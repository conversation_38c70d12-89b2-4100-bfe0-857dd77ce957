-- 添加拉取订单API权限的SQL脚本

-- 1. 添加API到sys_apis表
INSERT INTO sys_apis (created_at, updated_at, path, description, api_group, method) 
VALUES (NOW(), NOW(), '/od/pullOrders', '拉取订单', '订单管理', 'POST')
ON DUPLICATE KEY UPDATE 
    description = '拉取订单',
    api_group = '订单管理';

-- 2. 为管理员角色(888)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2) 
VALUES ('p', '888', '/od/pullOrders', 'POST')
ON DUPLICATE KEY UPDATE 
    ptype = 'p';

-- 3. 如果您的角色ID不是888，请将下面的888替换为您的实际角色ID
-- 查询您的角色ID可以使用：
-- SELECT authority_id FROM sys_users WHERE username = '您的用户名';

-- 然后添加权限（将YOUR_ROLE_ID替换为实际的角色ID）：
-- INSERT INTO casbin_rule (ptype, v0, v1, v2) 
-- VALUES ('p', 'YOUR_ROLE_ID', '/od/pullOrders', 'POST')
-- ON DUPLICATE KEY UPDATE 
--     ptype = 'p';
