#!/bin/bash

# XRT系统Docker部署脚本
# 作者: Augment Agent
# 日期: 2025-07-14

set -e

echo "🚀 开始部署XRT电商管理系统..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p /opt/xrt-docker/{server,web/dist,nginx/conf.d,mysql/{conf.d,init},uploads,logs}
    
    log_success "目录结构创建完成"
}

# 停止现有服务
stop_existing_services() {
    log_info "停止现有服务..."
    
    if [ -f "/opt/xrt-docker/docker-compose.yml" ]; then
        cd /opt/xrt-docker
        docker-compose down 2>/dev/null || docker compose down 2>/dev/null || true
    fi
    
    # 停止可能占用端口的服务
    pkill -f xrt-server 2>/dev/null || true
    
    log_success "现有服务已停止"
}

# 复制配置文件
copy_configs() {
    log_info "复制配置文件..."
    
    cp docker-compose.yml /opt/xrt-docker/
    cp config.yaml /opt/xrt-docker/server/
    cp nginx.conf /opt/xrt-docker/nginx/
    
    log_success "配置文件复制完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chmod +x /opt/xrt-docker/server/xrt-server 2>/dev/null || true
    chown -R root:root /opt/xrt-docker
    chmod -R 755 /opt/xrt-docker
    
    log_success "文件权限设置完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    cd /opt/xrt-docker
    
    # 拉取镜像
    docker-compose pull 2>/dev/null || docker compose pull
    
    # 启动服务
    docker-compose up -d 2>/dev/null || docker compose up -d
    
    log_success "Docker服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务启动..."
    
    # 等待MySQL启动
    for i in {1..30}; do
        if docker exec xrt-mysql mysqladmin ping -h localhost -u root -pAsdf1357 --silent 2>/dev/null; then
            log_success "MySQL服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "MySQL服务启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待后端服务
    for i in {1..30}; do
        if curl -s http://localhost:8888/health >/dev/null 2>&1; then
            log_success "后端服务已就绪"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "后端服务启动超时"
            exit 1
        fi
        sleep 2
    done
    
    # 等待前端服务
    for i in {1..15}; do
        if curl -s http://localhost >/dev/null 2>&1; then
            log_success "前端服务已就绪"
            break
        fi
        if [ $i -eq 15 ]; then
            log_error "前端服务启动超时"
            exit 1
        fi
        sleep 2
    done
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    cd /opt/xrt-docker
    docker-compose ps 2>/dev/null || docker compose ps
    
    # 检查数据库初始化状态
    init_status=$(curl -s -X POST http://localhost/api/init/checkdb | grep -o '"needInit":[^,]*' | cut -d':' -f2)
    
    if [ "$init_status" = "true" ]; then
        log_warning "数据库需要初始化，请访问 http://************** 进行初始化"
    else
        log_success "系统已完成初始化"
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 XRT系统部署完成！"
    echo ""
    echo "📋 访问信息："
    echo "   前端地址: http://**************"
    echo "   后端API: http://**************/api"
    echo ""
    echo "🔧 管理命令："
    echo "   查看日志: cd /opt/xrt-docker && docker-compose logs -f"
    echo "   重启服务: cd /opt/xrt-docker && docker-compose restart"
    echo "   停止服务: cd /opt/xrt-docker && docker-compose down"
    echo ""
    echo "📁 重要目录："
    echo "   配置文件: /opt/xrt-docker/server/config.yaml"
    echo "   上传文件: /opt/xrt-docker/uploads"
    echo "   日志文件: /opt/xrt-docker/logs"
    echo ""
}

# 主函数
main() {
    echo "🚀 XRT电商管理系统 Docker部署脚本"
    echo "========================================"
    
    check_dependencies
    stop_existing_services
    create_directories
    copy_configs
    set_permissions
    start_services
    wait_for_services
    check_services
    show_deployment_info
    
    log_success "部署完成！请访问 http://************** 开始使用系统"
}

# 执行主函数
main "$@"
