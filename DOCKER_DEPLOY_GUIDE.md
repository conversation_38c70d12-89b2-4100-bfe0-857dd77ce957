# XRT Docker 部署指南

## 问题描述
在 Alibaba Cloud Linux 3 上遇到 MySQL 依赖冲突问题：
```
Error: 
 Problem 1: cannot install the best update candidate for package mariadb-3:10.5.22-1.0.1.al8.x86_64
  - nothing provides libstdc++.so.6(GLIBCXX_3.4.29)(64bit) needed by mysql-community-client-8.0.42-1.el9.x86_64
  - nothing provides libc.so.6(GLIBC_2.34)(64bit) needed by mysql-community-client-8.0.42-1.el9.x86_64
```

## 解决方案
使用 Docker 部署避免系统依赖冲突问题。

## 快速部署步骤

### 1. 修复 MySQL 依赖冲突
```bash
# 运行修复脚本
sudo ./fix-mysql-deps.sh
```

### 2. 构建应用（如果还没有构建）

#### 构建前端
```bash
cd web
npm install
npm run build
cd ..
```

#### 构建后端
```bash
cd server
go build -o xrt-server main.go
cd ..
```

### 3. 完整 Docker 部署
```bash
# 运行完整部署脚本
sudo ./deploy-docker.sh
```

### 4. 手动部署（可选）
如果自动脚本有问题，可以手动执行：

```bash
# 创建必要目录
mkdir -p mysql/conf.d mysql/init nginx/conf.d uploads

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 服务配置

### Docker Compose 服务
- **xrt-mysql**: MySQL 8.0 数据库
- **xrt-server**: Go 后端服务 (端口 8888)
- **xrt-web**: Nginx 前端服务 (端口 80)

### 数据库配置
- 数据库名: `qmPlus`
- 用户名: `gva`
- 密码: `Aa@6447985`
- Root 密码: `Aa@6447985`

### 访问地址
- 前端: http://服务器IP
- 后端API: http://服务器IP:8889 (避免与宝塔面板端口冲突)
- MySQL: 服务器IP:3306
- 宝塔面板: http://服务器IP:8888

## 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f xrt-server
docker-compose logs -f xrt-mysql
docker-compose logs -f xrt-web
```

### 数据库管理
```bash
# 连接到 MySQL 容器
docker exec -it xrt-mysql mysql -u root -p

# 备份数据库
docker exec xrt-mysql mysqldump -u root -pAa@6447985 qmPlus > backup.sql

# 恢复数据库
docker exec -i xrt-mysql mysql -u root -pAa@6447985 qmPlus < backup.sql
```

### 应用更新
```bash
# 更新后端
cd server
go build -o xrt-server main.go
docker-compose restart xrt-server

# 更新前端
cd web
npm run build
docker-compose restart xrt-web
```

## 故障排除

### 1. 端口冲突
如果端口被占用，修改 `docker-compose.yml` 中的端口映射：
```yaml
ports:
  - "8080:80"    # 将前端端口改为 8080
  - "8889:8888"  # 将后端端口改为 8889
  - "3307:3306"  # 将 MySQL 端口改为 3307
```

### 2. 权限问题
```bash
# 确保文件权限正确
sudo chown -R $USER:$USER .
sudo chmod +x *.sh
```

### 3. 数据库连接问题
```bash
# 检查数据库是否启动
docker-compose logs xrt-mysql

# 测试数据库连接
docker exec xrt-mysql mysqladmin ping -h localhost -u root -pAa@6447985
```

### 4. 服务无法访问
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 如果防火墙开启，添加端口规则
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=8888/tcp
sudo firewall-cmd --reload
```

## 安全建议

1. **修改默认密码**: 部署完成后立即修改数据库密码
2. **配置防火墙**: 只开放必要的端口
3. **定期备份**: 设置定期数据库备份
4. **更新镜像**: 定期更新 Docker 镜像到最新版本

## 监控和维护

### 健康检查
```bash
# 检查所有服务健康状态
docker-compose ps

# 检查特定服务
curl http://localhost:8888/health
curl http://localhost/
```

### 日志轮转
```bash
# 配置 Docker 日志轮转
echo '{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}' | sudo tee /etc/docker/daemon.json

sudo systemctl restart docker
```

## 联系支持
如果遇到问题，请提供以下信息：
1. 系统版本: `cat /etc/os-release`
2. Docker 版本: `docker --version`
3. 服务状态: `docker-compose ps`
4. 错误日志: `docker-compose logs`
