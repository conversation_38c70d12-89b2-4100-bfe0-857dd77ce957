package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 测试Ozon API连接的脚本

type OzonFBSListRequest struct {
	Dir    string            `json:"dir"`
	Filter OzonFBSListFilter `json:"filter"`
	Limit  int               `json:"limit"`
	Offset int               `json:"offset"`
	With   OzonFBSListWith   `json:"with"`
}

type OzonFBSListFilter struct {
	Since  string   `json:"since"`
	To     string   `json:"to"`
	Status []string `json:"status"`
}

type OzonFBSListWith struct {
	AnalyticsData bool `json:"analytics_data"`
	FinancialData bool `json:"financial_data"`
}

type OzonFBSListResponse struct {
	Result OzonFBSListResult `json:"result"`
}

type OzonFBSListResult struct {
	Postings []OzonPosting `json:"postings"`
	HasNext  bool          `json:"has_next"`
}

type OzonPosting struct {
	PostingNumber  string        `json:"posting_number"`
	OrderID        int64         `json:"order_id"`
	OrderNumber    string        `json:"order_number"`
	Status         string        `json:"status"`
	InProcessAt    string        `json:"in_process_at"`
	ShipmentDate   string        `json:"shipment_date"`
	TrackingNumber string        `json:"tracking_number"`
	TplProvider    string        `json:"tpl_provider"`
	Products       []OzonProduct `json:"products"`
}

type OzonProduct struct {
	Name     string `json:"name"`
	OfferId  string `json:"offer_id"`
	Price    string `json:"price"`
	Quantity int    `json:"quantity"`
}

func testOzonAPI(clientID, apiKey string) {
	fmt.Printf("测试Ozon API连接...\n")
	fmt.Printf("Client-Id: %s\n", clientID)
	fmt.Printf("Api-Key: %s...\n", apiKey[:10]+"***") // 只显示前10位

	baseURL := "https://api-seller.ozon.ru"
	
	// 构建请求体
	requestBody := OzonFBSListRequest{
		Dir: "ASC",
		Filter: OzonFBSListFilter{
			Since:  time.Now().AddDate(0, 0, -30).Format("2006-01-02T15:04:05Z"),
			To:     time.Now().Format("2006-01-02T15:04:05Z"),
			Status: []string{"awaiting_packaging", "awaiting_deliver"},
		},
		Limit:  10, // 先测试少量数据
		Offset: 0,
		With: OzonFBSListWith{
			AnalyticsData: false,
			FinancialData: false,
		},
	}

	fmt.Printf("请求时间范围: %s 到 %s\n", requestBody.Filter.Since, requestBody.Filter.To)
	fmt.Printf("状态过滤: %v\n", requestBody.Filter.Status)

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		fmt.Printf("序列化请求失败: %v\n", err)
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", baseURL+"/v3/posting/fbs/list", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("发送请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	fmt.Printf("响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	if resp.StatusCode != http.StatusOK {
		fmt.Printf("API请求失败\n")
		return
	}

	// 解析响应
	var ozonResp OzonFBSListResponse
	err = json.Unmarshal(body, &ozonResp)
	if err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}

	fmt.Printf("成功获取订单数量: %d\n", len(ozonResp.Result.Postings))
	fmt.Printf("是否有更多数据: %t\n", ozonResp.Result.HasNext)

	for i, posting := range ozonResp.Result.Postings {
		fmt.Printf("订单 %d: %s - %s - %s\n", i+1, posting.PostingNumber, posting.OrderNumber, posting.Status)
	}
}

func main() {
	// 请替换为您的实际凭证
	clientID := "YOUR_CLIENT_ID"
	apiKey := "YOUR_API_KEY"
	
	if clientID == "YOUR_CLIENT_ID" || apiKey == "YOUR_API_KEY" {
		fmt.Println("请在代码中设置您的实际Client-Id和Api-Key")
		return
	}
	
	testOzonAPI(clientID, apiKey)
}
