#!/bin/bash

# 修复 MySQL 依赖冲突问题的脚本
# 适用于 Alibaba Cloud Linux 3

set -e

echo "=== 修复 MySQL 依赖冲突问题 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统版本
check_system() {
    log_info "检查系统版本..."
    cat /etc/os-release
    echo ""
}

# 移除有问题的 MySQL 仓库
remove_mysql_repos() {
    log_info "移除有问题的 MySQL 仓库..."
    
    # 移除 MySQL 仓库文件
    if [ -f /etc/yum.repos.d/mysql-community.repo ]; then
        mv /etc/yum.repos.d/mysql-community.repo /etc/yum.repos.d/mysql-community.repo.bak
        log_info "已备份并移除 MySQL 社区仓库"
    fi
    
    if [ -f /etc/yum.repos.d/mysql80-community.repo ]; then
        mv /etc/yum.repos.d/mysql80-community.repo /etc/yum.repos.d/mysql80-community.repo.bak
        log_info "已备份并移除 MySQL 8.0 仓库"
    fi
    
    # 移除 MySQL GPG 密钥
    rpm -e --allmatches mysql80-community-release 2>/dev/null || true
    rpm -e --allmatches mysql-community-release 2>/dev/null || true
    
    log_info "MySQL 仓库移除完成"
}

# 清理 yum 缓存
clean_yum_cache() {
    log_info "清理 yum 缓存..."
    yum clean all
    yum makecache
    log_info "yum 缓存清理完成"
}

# 跳过有问题的包进行系统更新
update_system_skip_mysql() {
    log_info "跳过 MySQL 相关包进行系统更新..."
    
    yum update -y --skip-broken --exclude=mysql* --exclude=mariadb*
    
    log_info "系统更新完成（跳过 MySQL 相关包）"
}

# 安装 Docker 和 Docker Compose
install_docker() {
    log_info "安装 Docker..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装，版本: $(docker --version)"
    else
        # 安装 Docker
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io
        
        # 启动 Docker
        systemctl start docker
        systemctl enable docker
        
        log_info "Docker 安装完成"
    fi
    
    # 检查 Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装，版本: $(docker-compose --version)"
    else
        log_info "安装 Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
        log_info "Docker Compose 安装完成"
    fi
}

# 验证 Docker 安装
verify_docker() {
    log_info "验证 Docker 安装..."
    
    docker --version
    docker-compose --version
    
    # 测试 Docker 运行
    if docker run --rm hello-world &>/dev/null; then
        log_info "Docker 运行正常"
    else
        log_error "Docker 运行测试失败"
        exit 1
    fi
}

# 显示后续步骤
show_next_steps() {
    log_info "=== 修复完成 ==="
    echo ""
    log_info "MySQL 依赖冲突已解决，现在可以使用 Docker 部署："
    echo ""
    log_info "1. 运行完整部署脚本："
    log_info "   chmod +x deploy-docker.sh"
    log_info "   ./deploy-docker.sh"
    echo ""
    log_info "2. 或者手动启动 Docker 服务："
    log_info "   docker-compose up -d"
    echo ""
    log_info "3. 查看服务状态："
    log_info "   docker-compose ps"
    echo ""
    log_info "4. 查看日志："
    log_info "   docker-compose logs -f"
    echo ""
}

# 主函数
main() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
    
    check_system
    remove_mysql_repos
    clean_yum_cache
    update_system_skip_mysql
    install_docker
    verify_docker
    show_next_steps
}

# 执行主函数
main "$@"
