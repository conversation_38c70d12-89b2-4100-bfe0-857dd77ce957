<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="店铺名称" prop="shopName">
          <el-input v-model="searchInfo.shopName" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="物流方式名称" prop="name">
          <el-input v-model="searchInfo.name" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="物流服务商" prop="provider">
          <el-input v-model="searchInfo.provider" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="是否启用" prop="isActive">
          <el-select v-model="searchInfo.isActive" clearable placeholder="请选择">
            <el-option label="启用" value="true" />
            <el-option label="禁用" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button type="success" icon="refresh" @click="syncOzonLogisticsFunc">同步Ozon物流</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="日期" prop="CreatedAt" width="180">
          <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        <el-table-column align="left" label="店铺名称" prop="shopName" width="120" />
        <el-table-column align="left" label="物流方式名称" prop="name" width="150" />
        <el-table-column align="left" label="物流服务商" prop="provider" width="120" />
        <el-table-column align="left" label="服务类型" prop="serviceType" width="100" />
        <el-table-column align="left" label="描述" prop="description" width="200" show-overflow-tooltip />
        <el-table-column align="left" label="是否启用" prop="isActive" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="基础价格" prop="basePrice" width="100">
          <template #default="scope">
            <span v-if="scope.row.basePrice">{{ scope.row.basePrice.toFixed(2) }}</span>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="每公斤价格" prop="pricePerKg" width="100">
          <template #default="scope">
            <span v-if="scope.row.pricePerKg">{{ scope.row.pricePerKg.toFixed(2) }}</span>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="预计天数" prop="estimatedDays" width="100" />
        <el-table-column align="left" label="计价公式" prop="pricingFormula" width="150" show-overflow-tooltip />
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <div class="flex flex-col gap-1">
              <el-button type="primary" link icon="view" @click="getDetails(scope.row)">查看</el-button>
              <el-button type="primary" link icon="edit" @click="updateLogisticsFunc(scope.row)">变更</el-button>
              <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 详情/编辑对话框 -->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="type === 'create' ? '新增物流方式' : type === 'update' ? '修改物流方式' : '查看物流方式'">
      <el-form :model="formData" label-position="right" label-width="120px" style="width: 90%">
        <el-form-item label="店铺名称">
          <el-input v-model="formData.shopName" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="物流方式名称">
          <el-input v-model="formData.name" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="物流服务商">
          <el-input v-model="formData.provider" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="服务类型">
          <el-input v-model="formData.serviceType" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="formData.description" type="textarea" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="formData.isActive" :disabled="type === 'look'" />
        </el-form-item>
        <el-form-item label="基础价格">
          <el-input-number v-model="formData.basePrice" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="每公斤价格">
          <el-input-number v-model="formData.pricePerKg" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="每立方米价格">
          <el-input-number v-model="formData.pricePerCubic" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="最小重量(kg)">
          <el-input-number v-model="formData.minWeight" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="最大重量(kg)">
          <el-input-number v-model="formData.maxWeight" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="预计送达天数">
          <el-input-number v-model="formData.estimatedDays" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="计价公式">
          <el-input v-model="formData.pricingFormula" clearable :readonly="type === 'look'" placeholder="例如: basePrice + weight * pricePerKg" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="formData.sortOrder" :readonly="type === 'look'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button v-show="type !== 'look'" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createLogistics,
  deleteLogistics,
  deleteLogisticsByIds,
  updateLogistics,
  findLogistics,
  getLogisticsList,
  syncOzonLogistics
} from '@/api/logistics'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict, filterDataSource, ReturnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { ref, reactive } from 'vue'

defineOptions({
  name: 'Logistics'
})

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
  shopName: '',
  shopClientID: '',
  ozonDeliveryID: undefined,
  ozonWarehouseID: undefined,
  name: '',
  provider: '',
  serviceType: '',
  description: '',
  isActive: true,
  basePrice: undefined,
  pricePerKg: undefined,
  pricePerCubic: undefined,
  minWeight: undefined,
  maxWeight: undefined,
  estimatedDays: undefined,
  pricingFormula: '',
  formulaParams: undefined,
  sortOrder: 0,
  ozonData: undefined,
})

// 验证规则
const rule = reactive({})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elSearchFormRef = ref()
const elFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getLogisticsList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{

}

// 获取需要的字典 可能为空 按需保留
setOptions()

// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteLogisticsFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteLogisticsByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateLogisticsFunc = async(row) => {
  const res = await findLogistics({ ID: row.ID })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data.relogistics
    dialogFormVisible.value = true
  }
}

// 删除行
const deleteLogisticsFunc = async (row) => {
  const res = await deleteLogistics({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    shopName: '',
    shopClientID: '',
    ozonDeliveryID: undefined,
    ozonWarehouseID: undefined,
    name: '',
    provider: '',
    serviceType: '',
    description: '',
    isActive: true,
    basePrice: undefined,
    pricePerKg: undefined,
    pricePerCubic: undefined,
    minWeight: undefined,
    maxWeight: undefined,
    estimatedDays: undefined,
    pricingFormula: '',
    formulaParams: undefined,
    sortOrder: 0,
    ozonData: undefined,
  }
}

// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createLogistics(formData.value)
        break
      case 'update':
        res = await updateLogistics(formData.value)
        break
      default:
        res = await createLogistics(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
  })
}

// 查看详情控制标记
const detailFrom = ref({})

// 查看详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findLogistics({ ID: row.ID })
  if (res.code === 0) {
    formData.value = res.data.relogistics
    type.value = 'look'
    dialogFormVisible.value = true
  }
}

// 同步Ozon物流信息
const syncOzonLogisticsFunc = async () => {
  ElMessageBox.confirm('确定要同步Ozon物流信息吗？这将从所有Ozon店铺拉取最新的物流方式信息。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在同步Ozon物流信息...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const res = await syncOzonLogistics()
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '同步成功'
        })
        getTableData()
      }
    } catch (error) {
      ElMessage({
        type: 'error',
        message: '同步失败: ' + error.message
      })
    } finally {
      loading.close()
    }
  })
}
</script>

<style>
.text-gray-400 {
  color: #9ca3af;
}
</style>