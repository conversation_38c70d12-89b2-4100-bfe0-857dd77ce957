# 生产环境配置文件
# 请根据您的实际服务器环境修改以下配置

# 系统配置
system:
  env: public
  addr: 8888
  db-type: mysql
  oss-type: local
  router-prefix: ""
  iplimit-count: 15000
  iplimit-time: 3600
  use-multipoint: false
  use-redis: false
  use-mongo: false
  use-strict-auth: false

# MySQL数据库配置 - 请修改为您的生产环境数据库信息
mysql:
  prefix: ""
  port: "3306"
  config: "charset=utf8mb4&parseTime=True&loc=Local"
  db-name: "xrt"  # 您的数据库名
  username: "root"  # 您的数据库用户名
  password: "Asdf1357"  # 您的数据库密码
  path: "127.0.0.1"  # 您的数据库服务器地址
  engine: ""
  log-mode: "error"
  max-idle-conns: 10
  max-open-conns: 100
  singular: false
  log-zap: false

# JWT配置
jwt:
  signing-key: "3ef3cc5e-f7ef-4673-b402-dd7e8e25367d"  # 生产环境请更换为随机密钥
  expires-time: "7d"
  buffer-time: "1d"
  issuer: "qmPlus"

# 日志配置
zap:
  level: "info"
  prefix: '[XRT-Server]'
  format: "console"
  director: "log"
  encode-level: "LowercaseColorLevelEncoder"
  stacktrace-key: "stacktrace"
  show-line: true
  log-in-console: false  # 生产环境建议关闭控制台日志
  retention-day: 30  # 日志保留30天

# 本地文件存储配置
local:
  path: "uploads/file"
  store-path: "uploads/file"

# Excel配置
excel:
  dir: "./resource/excel/"

# 验证码配置
captcha:
  key-long: 4
  img-width: 150
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600

# CORS配置 - 请根据您的前端域名修改
cors:
  mode: "strict-whitelist"
  whitelist:
    - allow-origin: "https://yourdomain.com"  # 请修改为您的前端域名
      allow-methods: "POST, GET, PUT, DELETE, OPTIONS"
      allow-headers: "Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id"
      expose-headers: "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type"
      allow-credentials: true

# 自动代码生成配置
autocode:
  web: "web/src"
  root: "/path/to/your/project"  # 请修改为您的项目路径
  server: "server"
  module: "github.com/flipped-aurora/gin-vue-admin/server"
  ai-path: ""
