# Gin-Vue-Admin Server 重启脚本使用说明

## 📁 文件说明

- `restart_server.sh` - Linux/macOS 重启脚本
- `restart_server.bat` - Windows 重启脚本
- `README_RESTART.md` - 本使用说明文档

## 🚀 使用方法

### Linux/macOS 系统

```bash
# 进入server目录
cd server

# 运行重启脚本
./restart_server.sh
```

### Windows 系统

```cmd
# 进入server目录
cd server

# 运行重启脚本
restart_server.bat
```

## 🔧 脚本功能

重启脚本会自动执行以下步骤：

1. **停止旧服务**
   - 停止所有gin-vue-admin相关进程
   - 释放8888端口

2. **编译最新代码**
   - 使用 `go build` 编译最新版本
   - 检查编译是否成功

3. **启动新的后端服务**
   - 后台启动新的服务进程
   - 将日志输出到 `server.log` 文件

4. **启动前端服务**
   - 检查前端是否已在运行
   - 如果未运行，自动启动前端开发服务器
   - 将前端日志输出到 `web.log` 文件

5. **验证服务状态**
   - 检查后端服务是否正常启动
   - 显示相关信息和链接

## 📊 服务信息

启动成功后，您可以访问：

- **前端界面**: http://127.0.0.1:8080 或 http://127.0.0.1:8093 (如果8080被占用)
- **API文档**: http://127.0.0.1:8888/swagger/index.html
- **健康检查**: http://127.0.0.1:8888/health

## 📝 日志查看

### 后端日志

#### 实时查看日志 (Linux/macOS)
```bash
tail -f server.log
```

#### 查看日志 (Windows)
```cmd
type server.log
```

#### 查看最新日志
```bash
# Linux/macOS
tail -n 100 server.log

# Windows
powershell "Get-Content server.log -Tail 100"
```

### 前端日志

#### 实时查看前端日志 (Linux/macOS)
```bash
tail -f web.log
```

#### 查看前端日志 (Windows)
```cmd
type web.log
```

## 🛑 停止服务

### Linux/macOS
```bash
# 方法1: 使用脚本显示的PID
kill [PID]

# 方法2: 停止所有相关进程
pkill -f "gin-vue-admin"

# 方法3: 停止占用8888端口的进程
lsof -ti:8888 | xargs kill -9
```

### Windows
```cmd
# 方法1: 使用任务管理器
# 找到 gin-vue-admin-server.exe 进程并结束

# 方法2: 使用命令行
taskkill /f /im gin-vue-admin-server.exe

# 方法3: 停止占用8888端口的进程
for /f "tokens=5" %a in ('netstat -aon ^| findstr :8888') do taskkill /f /pid %a
```

## ⚠️ 注意事项

1. **权限问题**: 确保脚本有执行权限
2. **端口占用**: 如果8888端口被其他程序占用，脚本会尝试停止
3. **编译失败**: 如果代码有错误，编译会失败，脚本会退出
4. **Go环境**: 确保系统已安装Go并配置好环境变量

## 🔍 故障排除

### 编译失败
```bash
# 手动检查编译错误
go build -o gin-vue-admin-server .
```

### 服务启动失败
```bash
# 查看详细日志
cat server.log

# 检查端口占用
lsof -i:8888  # Linux/macOS
netstat -ano | findstr :8888  # Windows
```

### 权限问题 (Linux/macOS)
```bash
# 给脚本添加执行权限
chmod +x restart_server.sh
```

## 📞 技术支持

如果遇到问题，请：

1. 查看 `server.log` 日志文件
2. 检查Go环境和依赖
3. 确认数据库连接正常
4. 检查配置文件 `config.yaml`

---

**提示**: 每次修改代码后，只需运行重启脚本即可应用最新更改！
