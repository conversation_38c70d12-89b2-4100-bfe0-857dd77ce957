-- 添加Ozon订单状态字典的SQL脚本
USE xrt;

-- 1. 检查是否已存在order_status字典
SELECT * FROM sys_dictionaries WHERE type = 'order_status';

-- 2. 如果不存在，则插入order_status字典
INSERT IGNORE INTO sys_dictionaries (created_at, updated_at, name, type, status, `desc`) 
VALUES (NOW(), NOW(), 'Ozon订单状态', 'order_status', 1, 'Ozon平台订单状态字典');

-- 3. 获取字典ID（取第一个）
SET @dict_id = (SELECT id FROM sys_dictionaries WHERE type = 'order_status' LIMIT 1);

-- 4. 删除现有的订单状态详情（如果有）
DELETE FROM sys_dictionary_details WHERE sys_dictionary_id = @dict_id;

-- 5. 插入完整的Ozon订单状态详情
INSERT INTO sys_dictionary_details (created_at, updated_at, label, value, extend, status, sort, sys_dictionary_id) VALUES
-- 等待相关状态
(NOW(), NOW(), '等待验收', 'acceptance_in_progress', '正在验收', 1, 1, @dict_id),
(NOW(), NOW(), '等待确认', 'awaiting_approve', '等待确认', 1, 2, @dict_id),
(NOW(), NOW(), '等待发运', 'awaiting_deliver', '等待装运/等待发货', 1, 3, @dict_id),
(NOW(), NOW(), '等待包装', 'awaiting_packaging', '等待包装/等待备货', 1, 4, @dict_id),
(NOW(), NOW(), '等待注册', 'awaiting_registration', '等待注册', 1, 5, @dict_id),
(NOW(), NOW(), '已创建', 'awaiting_verification', '已创建/等待验证', 1, 6, @dict_id),

-- 运输相关状态
(NOW(), NOW(), '运输中', 'delivering', '运输中/配送中', 1, 10, @dict_id),
(NOW(), NOW(), '司机取货', 'driver_pickup', '司机处/司机取货', 1, 11, @dict_id),
(NOW(), NOW(), '卖家发送', 'sent_by_seller', '由卖家发送', 1, 12, @dict_id),

-- 问题状态
(NOW(), NOW(), '未接受', 'not_accepted', '分拣中心未接受', 1, 20, @dict_id),
(NOW(), NOW(), '仲裁中', 'arbitration', '仲裁', 1, 21, @dict_id),
(NOW(), NOW(), '客户仲裁', 'client_arbitration', '快递客户仲裁', 1, 22, @dict_id),

-- 完成/取消状态
(NOW(), NOW(), '已取消', 'cancelled', '已取消', 1, 30, @dict_id),
(NOW(), NOW(), '拆分取消', 'cancelled_from_split_pending', '因货件拆分而取消', 1, 31, @dict_id),
(NOW(), NOW(), '已完成', 'delivered', '已送达/已完成', 1, 32, @dict_id),
(NOW(), NOW(), '已退回', 'returned', '已退回', 1, 33, @dict_id),

-- 其他状态
(NOW(), NOW(), '处理中', 'processing', '处理中', 1, 40, @dict_id),
(NOW(), NOW(), '已发货', 'shipped', '已发货', 1, 41, @dict_id),
(NOW(), NOW(), '待发货', 'pending', '待发货', 1, 42, @dict_id);

-- 6. 验证插入结果
SELECT 
    d.name as '字典名称',
    d.type as '字典类型', 
    dd.label as '状态名称',
    dd.value as '状态值',
    dd.extend as '说明',
    dd.sort as '排序'
FROM sys_dictionaries d 
LEFT JOIN sys_dictionary_details dd ON d.id = dd.sys_dictionary_id 
WHERE d.type = 'order_status' 
ORDER BY dd.sort;
