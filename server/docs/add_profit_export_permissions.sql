-- 添加利润管理导出权限的SQL脚本

-- 1. 为管理员角色(888)添加利润管理权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '888', '/profit/getProfitList', 'GET'),
('p', '888', '/profit/getProfitSummary', 'GET'),
('p', '888', '/profit/getProfitSummaryByCurrency', 'GET'),
('p', '888', '/profit/getProfitByShop', 'GET'),
('p', '888', '/profit/getMonthlyProfitList', 'GET'),
('p', '888', '/profit/getMonthlyAdvertising', 'GET'),
('p', '888', '/profit/exportUSDOrdersDetail', 'GET'),
('p', '888', '/profit/exportMonthlyOrdersDetail', 'GET'),
('p', '888', '/profit/calculateProfit', 'POST'),
('p', '888', '/profit/setMonthlyAdvertising', 'POST'),
('p', '888', '/profit/refreshProfitData', 'POST'),
('p', '888', '/profit/deleteAllProfitData', 'DELETE')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 2. 为子角色(8881)添加利润管理权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '8881', '/profit/getProfitList', 'GET'),
('p', '8881', '/profit/getProfitSummary', 'GET'),
('p', '8881', '/profit/getProfitSummaryByCurrency', 'GET'),
('p', '8881', '/profit/getProfitByShop', 'GET'),
('p', '8881', '/profit/getMonthlyProfitList', 'GET'),
('p', '8881', '/profit/getMonthlyAdvertising', 'GET'),
('p', '8881', '/profit/exportUSDOrdersDetail', 'GET'),
('p', '8881', '/profit/exportMonthlyOrdersDetail', 'GET'),
('p', '8881', '/profit/calculateProfit', 'POST'),
('p', '8881', '/profit/setMonthlyAdvertising', 'POST'),
('p', '8881', '/profit/refreshProfitData', 'POST'),
('p', '8881', '/profit/deleteAllProfitData', 'DELETE')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 3. 为测试角色(9528)添加利润管理权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '9528', '/profit/getProfitList', 'GET'),
('p', '9528', '/profit/getProfitSummary', 'GET'),
('p', '9528', '/profit/getProfitSummaryByCurrency', 'GET'),
('p', '9528', '/profit/getProfitByShop', 'GET'),
('p', '9528', '/profit/getMonthlyProfitList', 'GET'),
('p', '9528', '/profit/getMonthlyAdvertising', 'GET'),
('p', '9528', '/profit/exportUSDOrdersDetail', 'GET'),
('p', '9528', '/profit/exportMonthlyOrdersDetail', 'GET'),
('p', '9528', '/profit/calculateProfit', 'POST'),
('p', '9528', '/profit/setMonthlyAdvertising', 'POST'),
('p', '9528', '/profit/refreshProfitData', 'POST'),
('p', '9528', '/profit/deleteAllProfitData', 'DELETE')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 4. 查看当前用户的角色ID
-- SELECT authority_id FROM sys_users WHERE username = '您的用户名';

-- 5. 验证权限是否添加成功
-- SELECT * FROM casbin_rule WHERE v1 LIKE '/profit/%' ORDER BY v0, v1;
