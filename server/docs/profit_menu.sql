-- 添加利润统计菜单
-- 首先查找订单管理的父菜单ID
-- 假设订单管理菜单已存在，我们需要找到它的ID

-- 插入利润统计菜单
INSERT INTO `sys_base_menus` (`created_at`, `updated_at`, `deleted_at`, `menu_level`, `parent_id`, `path`, `name`, `hidden`, `component`, `sort`, `active_name`, `keep_alive`, `default_menu`, `title`, `icon`, `close_tab`, `transition_type`) 
VALUES (
    NOW(), 
    NOW(), 
    NULL, 
    0, 
    (SELECT id FROM (SELECT id FROM sys_base_menus WHERE name = 'order' AND component LIKE '%order%' LIMIT 1) AS temp), -- 订单管理的父菜单ID
    'profit', 
    'profit', 
    0, 
    'view/order/profit/profit.vue', 
    2, 
    '', 
    1, 
    0, 
    '利润统计', 
    'money', 
    0, 
    ''
);

-- 获取刚插入的菜单ID
SET @profit_menu_id = LAST_INSERT_ID();

-- 为管理员角色(authority_id = 888)添加菜单权限
INSERT INTO `sys_authority_menus` (`sys_authority_authority_id`, `sys_base_menu_id`, `created_at`, `updated_at`, `deleted_at`) 
VALUES (888, @profit_menu_id, NOW(), NOW(), NULL);

-- 为超级管理员角色(authority_id = 9528)添加菜单权限
INSERT INTO `sys_authority_menus` (`sys_authority_authority_id`, `sys_base_menu_id`, `created_at`, `updated_at`, `deleted_at`) 
VALUES (9528, @profit_menu_id, NOW(), NOW(), NULL);

-- 添加API权限
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) VALUES
(NOW(), NOW(), NULL, '/profit/getProfitList', '获取利润统计列表', 'profit', 'GET'),
(NOW(), NOW(), NULL, '/profit/getProfitSummary', '获取利润汇总统计', 'profit', 'GET'),
(NOW(), NOW(), NULL, '/profit/getProfitByShop', '按店铺统计利润', 'profit', 'GET'),
(NOW(), NOW(), NULL, '/profit/calculateProfit', '计算订单利润', 'profit', 'POST');

-- 为管理员角色添加API权限
INSERT INTO `casbin_rules` (`ptype`, `v0`, `v1`, `v2`) VALUES
('p', '888', '/profit/getProfitList', 'GET'),
('p', '888', '/profit/getProfitSummary', 'GET'),
('p', '888', '/profit/getProfitByShop', 'GET'),
('p', '888', '/profit/calculateProfit', 'POST');

-- 为超级管理员角色添加API权限
INSERT INTO `casbin_rules` (`ptype`, `v0`, `v1`, `v2`) VALUES
('p', '9528', '/profit/getProfitList', 'GET'),
('p', '9528', '/profit/getProfitSummary', 'GET'),
('p', '9528', '/profit/getProfitByShop', 'GET'),
('p', '9528', '/profit/calculateProfit', 'POST');
