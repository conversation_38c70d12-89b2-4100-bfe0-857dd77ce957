-- 添加月度利润统计API权限的SQL脚本

-- 1. 添加新的API到sys_apis表
INSERT INTO sys_apis (created_at, updated_at, path, description, api_group, method)
VALUES
(NOW(), NOW(), '/profit/getMonthlyProfitList', '获取月度利润统计列表', 'profit', 'GET'),
(NOW(), NOW(), '/profit/setMonthlyAdvertising', '设置月度广告费用', 'profit', 'POST'),
(NOW(), NOW(), '/profit/getMonthlyAdvertising', '获取月度广告费用', 'profit', 'GET'),
(NOW(), NOW(), '/profit/refreshProfitData', '刷新利润统计数据', 'profit', 'POST')
ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    api_group = VALUES(api_group);

-- 2. 为管理员角色(888)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '888', '/profit/getMonthlyProfitList', 'GET'),
('p', '888', '/profit/setMonthlyAdvertising', 'POST'),
('p', '888', '/profit/getMonthlyAdvertising', 'GET'),
('p', '888', '/profit/refreshProfitData', 'POST')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 3. 为超级管理员角色(9528)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '9528', '/profit/getMonthlyProfitList', 'GET'),
('p', '9528', '/profit/setMonthlyAdvertising', 'POST'),
('p', '9528', '/profit/getMonthlyAdvertising', 'GET'),
('p', '9528', '/profit/refreshProfitData', 'POST')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 4. 如果您的角色ID不是888或9528，请查询您的角色ID
-- SELECT authority_id FROM sys_users WHERE username = '您的用户名';

-- 然后为您的角色添加权限（将YOUR_ROLE_ID替换为实际的角色ID）：
-- INSERT INTO casbin_rule (ptype, v0, v1, v2) 
-- VALUES 
-- ('p', 'YOUR_ROLE_ID', '/profit/getMonthlyProfitList', 'GET'),
-- ('p', 'YOUR_ROLE_ID', '/profit/setMonthlyAdvertising', 'POST'),
-- ('p', 'YOUR_ROLE_ID', '/profit/getMonthlyAdvertising', 'GET')
-- ON DUPLICATE KEY UPDATE 
--     ptype = 'p';
