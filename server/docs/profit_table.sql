-- 创建订单利润统计表
CREATE TABLE IF NOT EXISTS `order_profits` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `posting_number` varchar(191) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '货件号',
  `order_number` varchar(191) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单号',
  `shop_name` varchar(191) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '店铺名称',
  `product_name` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '产品名称',
  `sku` varchar(191) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'SKU',
  `quantity` int DEFAULT NULL COMMENT '数量',
  `sale_price` decimal(10,2) DEFAULT NULL COMMENT '销售价格',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价格',
  `commission` decimal(10,2) DEFAULT NULL COMMENT '佣金',
  `shipping_cost` decimal(10,2) DEFAULT NULL COMMENT '运费',
  `other_costs` decimal(10,2) DEFAULT NULL COMMENT '其他费用',
  `gross_profit` decimal(10,2) DEFAULT NULL COMMENT '毛利润',
  `net_profit` decimal(10,2) DEFAULT NULL COMMENT '净利润',
  `profit_margin` decimal(5,2) DEFAULT NULL COMMENT '利润率',
  `order_date` datetime(3) DEFAULT NULL COMMENT '下单日期',
  `status` varchar(191) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '订单状态',
  PRIMARY KEY (`id`),
  KEY `idx_order_profits_deleted_at` (`deleted_at`),
  KEY `idx_order_profits_posting_number` (`posting_number`),
  KEY `idx_order_profits_order_number` (`order_number`),
  KEY `idx_order_profits_shop_name` (`shop_name`),
  KEY `idx_order_profits_sku` (`sku`),
  KEY `idx_order_profits_order_date` (`order_date`),
  UNIQUE KEY `uk_posting_number` (`posting_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='订单利润统计表';
