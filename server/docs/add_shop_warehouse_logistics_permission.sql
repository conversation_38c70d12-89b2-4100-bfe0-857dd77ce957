-- 添加getShopWarehouseLogistics API权限的SQL脚本

-- 1. 添加API到sys_apis表
INSERT INTO sys_apis (created_at, updated_at, path, description, api_group, method) 
VALUES (NOW(), NOW(), '/logistics/getShopWarehouseLogistics', '获取店铺仓库物流层级数据', 'logistics', 'GET')
ON DUPLICATE KEY UPDATE 
    description = '获取店铺仓库物流层级数据',
    api_group = 'logistics';

-- 2. 为管理员角色(888)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2) 
VALUES ('p', '888', '/logistics/getShopWarehouseLogistics', 'GET')
ON DUPLICATE KEY UPDATE 
    ptype = 'p';

-- 3. 为超级管理员角色(9528)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2) 
VALUES ('p', '9528', '/logistics/getShopWarehouseLogistics', 'GET')
ON DUPLICATE KEY UPDATE 
    ptype = 'p';

-- 4. 如果您的角色ID不是888或9528，请将下面的YOUR_ROLE_ID替换为您的实际角色ID
-- 查询您的角色ID可以使用：
-- SELECT authority_id FROM sys_users WHERE username = '您的用户名';

-- 然后添加权限（将YOUR_ROLE_ID替换为实际的角色ID）：
-- INSERT INTO casbin_rule (ptype, v0, v1, v2) 
-- VALUES ('p', 'YOUR_ROLE_ID', '/logistics/getShopWarehouseLogistics', 'GET')
-- ON DUPLICATE KEY UPDATE 
--     ptype = 'p';
