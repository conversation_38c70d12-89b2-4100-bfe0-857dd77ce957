-- 添加按货币分组获取利润汇总统计API权限的SQL脚本

-- 1. 添加新的API到sys_apis表
INSERT INTO sys_apis (created_at, updated_at, path, description, api_group, method)
VALUES
(NOW(), NOW(), '/profit/getProfitSummaryByCurrency', '按货币分组获取利润汇总统计', 'profit', 'GET')
ON DUPLICATE KEY UPDATE
    description = VALUES(description),
    api_group = VALUES(api_group);

-- 2. 为管理员角色(888)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '888', '/profit/getProfitSummaryByCurrency', 'GET')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 3. 为超级管理员角色(9528)添加权限到casbin_rule表
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES
('p', '9528', '/profit/getProfitSummaryByCurrency', 'GET')
ON DUPLICATE KEY UPDATE
    ptype = 'p';

-- 4. 如果您的角色ID不是888或9528，请查询您的角色ID
-- SELECT authority_id FROM sys_users WHERE username = '您的用户名';

-- 然后为您的角色添加权限（将YOUR_ROLE_ID替换为实际的角色ID）：
-- INSERT INTO casbin_rule (ptype, v0, v1, v2) 
-- VALUES 
-- ('p', 'YOUR_ROLE_ID', '/profit/getProfitSummaryByCurrency', 'GET')
-- ON DUPLICATE KEY UPDATE 
--     ptype = 'p';
