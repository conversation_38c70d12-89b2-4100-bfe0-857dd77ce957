-- 添加物流管理菜单
-- 首先查找订单管理的父菜单ID
-- 假设订单管理菜单已存在，我们需要找到它的ID

-- 插入物流管理菜单
INSERT INTO `sys_base_menus` (`created_at`, `updated_at`, `deleted_at`, `menu_level`, `parent_id`, `path`, `name`, `hidden`, `component`, `sort`, `active_name`, `keep_alive`, `default_menu`, `title`, `icon`, `close_tab`, `transition_type`) 
VALUES (
    NOW(), 
    NOW(), 
    NULL, 
    0, 
    (SELECT id FROM (SELECT id FROM sys_base_menus WHERE name = 'order' AND component LIKE '%order%' LIMIT 1) AS temp), -- 订单管理的父菜单ID
    'logistics', 
    'logistics', 
    0, 
    'view/order/logistics/logistics.vue', 
    4, 
    '', 
    1, 
    0, 
    '物流管理', 
    'truck', 
    0, 
    ''
);

-- 获取刚插入的菜单ID
SET @logistics_menu_id = LAST_INSERT_ID();

-- 为管理员角色(authority_id = 888)添加菜单权限
INSERT INTO `sys_authority_menus` (`sys_authority_authority_id`, `sys_base_menu_id`)
VALUES (888, @logistics_menu_id);

-- 为超级管理员角色(authority_id = 9528)添加菜单权限
INSERT INTO `sys_authority_menus` (`sys_authority_authority_id`, `sys_base_menu_id`)
VALUES (9528, @logistics_menu_id);

-- 添加API权限
INSERT INTO `sys_apis` (`created_at`, `updated_at`, `deleted_at`, `path`, `description`, `api_group`, `method`) VALUES
(NOW(), NOW(), NULL, '/logistics/getLogisticsList', '获取物流信息列表', 'logistics', 'GET'),
(NOW(), NOW(), NULL, '/logistics/getLogisticsSummary', '获取物流统计信息', 'logistics', 'GET'),
(NOW(), NOW(), NULL, '/logistics/syncLogistics', '同步物流信息', 'logistics', 'POST'),
(NOW(), NOW(), NULL, '/logistics/createLogistics', '创建物流信息', 'logistics', 'POST'),
(NOW(), NOW(), NULL, '/logistics/updateLogistics', '更新物流信息', 'logistics', 'PUT'),
(NOW(), NOW(), NULL, '/logistics/deleteLogistics', '删除物流信息', 'logistics', 'DELETE'),
(NOW(), NOW(), NULL, '/logistics/deleteLogisticsByIds', '批量删除物流信息', 'logistics', 'DELETE'),
(NOW(), NOW(), NULL, '/logistics/findLogistics', '查询物流信息', 'logistics', 'GET');

-- 为管理员角色添加API权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES
('p', '888', '/logistics/getLogisticsList', 'GET'),
('p', '888', '/logistics/getLogisticsSummary', 'GET'),
('p', '888', '/logistics/syncLogistics', 'POST'),
('p', '888', '/logistics/createLogistics', 'POST'),
('p', '888', '/logistics/updateLogistics', 'PUT'),
('p', '888', '/logistics/deleteLogistics', 'DELETE'),
('p', '888', '/logistics/deleteLogisticsByIds', 'DELETE'),
('p', '888', '/logistics/findLogistics', 'GET');

-- 为超级管理员角色添加API权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES
('p', '9528', '/logistics/getLogisticsList', 'GET'),
('p', '9528', '/logistics/getLogisticsSummary', 'GET'),
('p', '9528', '/logistics/syncLogistics', 'POST'),
('p', '9528', '/logistics/createLogistics', 'POST'),
('p', '9528', '/logistics/updateLogistics', 'PUT'),
('p', '9528', '/logistics/deleteLogistics', 'DELETE'),
('p', '9528', '/logistics/deleteLogisticsByIds', 'DELETE'),
('p', '9528', '/logistics/findLogistics', 'GET');
