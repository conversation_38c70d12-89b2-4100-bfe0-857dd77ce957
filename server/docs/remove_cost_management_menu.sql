-- 删除成本管理菜单及相关权限
-- 此脚本用于完全移除成本管理模块的菜单入口

-- 1. 查找成本管理菜单ID
SET @cost_menu_id = (SELECT id FROM sys_base_menus WHERE name = 'cost' OR title = '成本管理' OR component LIKE '%cost%' LIMIT 1);

-- 2. 删除菜单权限关联 (sys_authority_menus表)
DELETE FROM sys_authority_menus WHERE sys_base_menu_id = @cost_menu_id;

-- 3. 删除API权限 (casbin_rule表)
DELETE FROM casbin_rule WHERE v1 LIKE '/cost/%' OR v1 LIKE '%cost%';

-- 4. 删除菜单按钮权限 (sys_authority_btns表，如果存在)
DELETE FROM sys_authority_btns WHERE sys_menu_id = @cost_menu_id;

-- 5. 删除菜单参数 (sys_base_menu_parameters表，如果存在)
DELETE FROM sys_base_menu_parameters WHERE sys_base_menu_id = @cost_menu_id;

-- 6. 删除菜单按钮定义 (sys_base_menu_btns表，如果存在)
DELETE FROM sys_base_menu_btns WHERE sys_base_menu_id = @cost_menu_id;

-- 7. 最后删除菜单本身
DELETE FROM sys_base_menus WHERE id = @cost_menu_id;

-- 8. 验证删除结果
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '成本管理菜单已成功删除'
        ELSE CONCAT('仍有 ', COUNT(*), ' 个成本管理菜单记录')
    END AS result
FROM sys_base_menus 
WHERE name = 'cost' OR title = '成本管理' OR component LIKE '%cost%';

-- 9. 检查是否还有相关权限记录
SELECT 
    CASE 
        WHEN COUNT(*) = 0 THEN '成本管理相关权限已清理完毕'
        ELSE CONCAT('仍有 ', COUNT(*), ' 个相关权限记录')
    END AS permission_result
FROM casbin_rule 
WHERE v1 LIKE '/cost/%' OR v1 LIKE '%cost%';
