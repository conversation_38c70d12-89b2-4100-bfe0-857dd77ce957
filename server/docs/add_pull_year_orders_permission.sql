-- 为拉取今年订单功能添加API权限
INSERT INTO `sys_apis` (`path`, `description`, `api_group`, `method`) VALUES 
('/od/pullYearOrders', '拉取今年订单', 'order', 'POST');

-- 为管理员角色添加拉取今年订单的权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES 
('p', '888', '/od/pullYearOrders', 'POST');

-- 为开发者角色添加拉取今年订单的权限  
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES 
('p', '8881', '/od/pullYearOrders', 'POST');

-- 为普通用户角色添加拉取今年订单的权限
INSERT INTO `casbin_rule` (`ptype`, `v0`, `v1`, `v2`) VALUES 
('p', '9528', '/od/pullYearOrders', 'POST');
