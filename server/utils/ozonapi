package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 公共请求函数
func CallOzonAPI[T any](clientId, apiKey, path string, payload any) (*T, error) {
	url := "https://api-seller.ozon.ru" + path

	// 编码请求体
	bodyBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("请求体编码失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置 Header
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientId)
	req.Header.Set("Api-Key", apiKey)

	// 发起请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d，响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应体
	var result T
	if err := json.Unmarshal(respBody, &result); err != nil {
		return nil, fmt.Errorf("响应解析失败: %w", err)
	}

	return &result, nil
}


// 业务函数
func GetUnfulfilledOrders(clientId, apiKey string) (*UnfulfilledResponse, error) {
	ozonShopService = service.ServiceGroupApp.ShopsServiceGroup.ozonShopService
	ozonShopService.getOzonShopList
	req := UnfulfilledRequest{
		Dir:    "ASC",
		Limit:  1000,
		Offset: 0,
	}
	req.Filter.CutoffFrom = "2024-01-01T00:00:00.000Z"
	req.Filter.CutoffTo = time.Now().UTC().Format(time.RFC3339)
	req.With.AnalyticsData = true
	req.With.FinancialData = true

	return CallOzonAPI[UnfulfilledResponse](clientId, apiKey, "/v3/posting/fbs/unfulfilled/list", req)
}