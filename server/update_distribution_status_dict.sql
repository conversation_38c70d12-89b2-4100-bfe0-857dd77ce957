-- 更新配货状态字典的SQL脚本
USE xrt;

-- 1. 检查现有配货状态字典
SELECT * FROM sys_dictionaries WHERE type = 'distribution_status';

-- 2. 获取字典ID
SET @dict_id = (SELECT id FROM sys_dictionaries WHERE type = 'distribution_status' LIMIT 1);

-- 3. 删除现有的配货状态详情
DELETE FROM sys_dictionary_details WHERE sys_dictionary_id = @dict_id;

-- 4. 插入完善的配货状态详情
INSERT INTO sys_dictionary_details (created_at, updated_at, label, value, extend, status, sort, sys_dictionary_id) VALUES
-- 基础状态
(NOW(), NOW(), '待处理', 'pending', '订单刚创建，等待处理', 1, 1, @dict_id),
(NOW(), NOW(), '备货中', 'preparing', '正在准备商品', 1, 2, @dict_id),
(NOW(), NOW(), '已备货', 'prepared', '商品已准备完成', 1, 3, @dict_id),
(NOW(), NOW(), '打包中', 'packing', '正在打包', 1, 4, @dict_id),
(NOW(), NOW(), '已打包', 'packed', '已完成打包', 1, 5, @dict_id),
(NOW(), NOW(), '待发货', 'ready_to_ship', '准备发货', 1, 6, @dict_id),
(NOW(), NOW(), '已发货', 'shipped', '已发出', 1, 7, @dict_id),
(NOW(), NOW(), '运输中', 'in_transit', '运输途中', 1, 8, @dict_id),
(NOW(), NOW(), '已送达', 'delivered', '已送达客户', 1, 9, @dict_id),

-- 异常状态
(NOW(), NOW(), '缺货', 'out_of_stock', '商品缺货', 1, 20, @dict_id),
(NOW(), NOW(), '暂停', 'on_hold', '订单暂停处理', 1, 21, @dict_id),
(NOW(), NOW(), '已取消', 'cancelled', '订单已取消', 1, 22, @dict_id),
(NOW(), NOW(), '退货中', 'returning', '正在退货', 1, 23, @dict_id),
(NOW(), NOW(), '已退货', 'returned', '已完成退货', 1, 24, @dict_id),

-- 兼容旧数据
(NOW(), NOW(), '未配货', '0', '未开始配货（兼容旧数据）', 1, 30, @dict_id),
(NOW(), NOW(), '已配货', '1', '已完成配货（兼容旧数据）', 1, 31, @dict_id);

-- 5. 验证插入结果
SELECT 
    d.name as '字典名称',
    d.type as '字典类型', 
    dd.label as '状态名称',
    dd.value as '状态值',
    dd.extend as '说明',
    dd.sort as '排序'
FROM sys_dictionaries d 
LEFT JOIN sys_dictionary_details dd ON d.id = dd.sys_dictionary_id 
WHERE d.type = 'distribution_status' 
ORDER BY dd.sort;
