-- 更新现有订单的店铺信息
USE xrt;

-- 1. 查看当前订单的店铺信息分布
SELECT 
    '更新前店铺信息分布' as info,
    shop_name, 
    shop_client_id,
    COUNT(*) as count 
FROM `order` 
GROUP BY shop_name, shop_client_id;

-- 2. 为所有没有店铺信息的订单设置默认店铺（兴如通）
UPDATE `order` 
SET 
    shop_name = '兴如通', 
    shop_client_id = '1594601' 
WHERE shop_name IS NULL OR shop_name = '';

-- 3. 查看更新后的店铺信息分布
SELECT 
    '更新后店铺信息分布' as info,
    shop_name, 
    shop_client_id,
    COUNT(*) as count 
FROM `order` 
GROUP BY shop_name, shop_client_id;

-- 4. 验证更新结果
SELECT 
    '验证结果' as info,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN shop_name IS NOT NULL AND shop_name != '' THEN 1 END) as orders_with_shop,
    COUNT(CASE WHEN shop_name IS NULL OR shop_name = '' THEN 1 END) as orders_without_shop
FROM `order`;
