-- 更新订单状态值的SQL脚本
USE xrt;

-- 1. 查看当前状态分布
SELECT 
    '更新前状态分布' as info,
    distribution_status, 
    COUNT(*) as count 
FROM `order` 
GROUP BY distribution_status;

-- 2. 更新配货状态：0 -> unprocessed
UPDATE `order` 
SET distribution_status = 'unprocessed' 
WHERE distribution_status = '0';

-- 3. 更新配货状态：1 -> processed  
UPDATE `order` 
SET distribution_status = 'processed' 
WHERE distribution_status = '1';

-- 4. 更新配货状态：pending -> unprocessed
UPDATE `order` 
SET distribution_status = 'unprocessed' 
WHERE distribution_status = 'pending';

-- 5. 查看更新后状态分布
SELECT 
    '更新后状态分布' as info,
    distribution_status, 
    COUNT(*) as count 
FROM `order` 
GROUP BY distribution_status;
