// 自动生成模板OzoneOrderDetail
package ozoneOrder

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
	"time"
)

// ozone订单详情 结构体  OzoneOrderDetail
type OzoneOrderDetail struct {
	global.GVA_MODEL
	Addressee             datatypes.JSON `json:"addressee" form:"addressee" gorm:"column:addressee;comment:;type:text;" swaggertype:"object"`                   //收件人联系方式
	Analytics_data        datatypes.JSON `json:"analytics_data" form:"analytics_data" gorm:"column:analytics_data;comment:;type:text;" swaggertype:"object"`    //分析数据
	Customer              datatypes.JSON `json:"customer" form:"customer" gorm:"column:customer;comment:;type:text;" swaggertype:"object"`                      //买家信息
	Barcodes              datatypes.JSON `json:"barcodes" form:"barcodes" gorm:"column:barcodes;comment:;type:text;" swaggertype:"object"`                      //货件条码
	Delivering_date       datatypes.JSON `json:"delivering_date" form:"delivering_date" gorm:"column:delivering_date;comment:;type:text;" swaggertype:"object"` //货件交付物流的时间
	Delivery_method       datatypes.JSON `json:"delivery_method" form:"delivery_method" gorm:"column:delivery_method;comment:;type:text;" swaggertype:"object"` //快递方式
	Financial_data        datatypes.JSON `json:"financial_data" form:"financial_data" gorm:"column:financial_data;comment:;type:text;" swaggertype:"object"`    //有关商品成本、折扣幅度、付款和佣金的信息
	In_process_at         *time.Time     `json:"in_process_at" form:"in_process_at" gorm:"column:in_process_at;comment:;"`                                      //开始处理货件的日期和时间
	Is_express            *bool          `json:"is_express" form:"is_express" gorm:"column:is_express;comment:;"`                                               //如果使用快速物流 Ozon Express —— true
	Optional              datatypes.JSON `json:"optional" form:"optional" gorm:"column:optional;comment:;type:text;" swaggertype:"object"`                      //带有附加特征的商品列表
	Order_id              *int           `json:"order_id" form:"order_id" gorm:"column:order_id;comment:;"`                                                     //货件所属订单的ID
	Order_number          *string        `json:"order_number" form:"order_number" gorm:"column:order_number;comment:;"`                                         //货件所属的订单号
	Parent_posting_number *string        `json:"parent_posting_number" form:"parent_posting_number" gorm:"column:parent_posting_number;comment:;"`              //快递母件编号，从该母件中拆分出了当前货件
	Posting_number        *string        `json:"posting_number" form:"posting_number" gorm:"column:posting_number;comment:;"`                                   //货件号
	Products              datatypes.JSON `json:"products" form:"products" gorm:"column:products;comment:;type:text;" swaggertype:"array,object"`                //货运商品列表
	Requirements          datatypes.JSON `json:"requirements" form:"requirements" gorm:"column:requirements;comment:;type:text;" swaggertype:"object"`          //需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态
	Shipment_date         *time.Time     `json:"shipment_date" form:"shipment_date" gorm:"column:shipment_date;comment:;"`                                      //必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段
	Status                *string        `json:"status" form:"status" gorm:"column:status;comment:;"`                                                           //货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。
	Substatus             *string        `json:"substatus" form:"substatus" gorm:"column:substatus;comment:;"`                                                  //发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。
	Tpl_integration_type  *string        `json:"tpl_integration_type" form:"tpl_integration_type" gorm:"column:tpl_integration_type;comment:;"`                 //快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。
	Tracking_number       *string        `json:"tracking_number" form:"tracking_number" gorm:"column:tracking_number;comment:;"`                                //货件跟踪号
	Tariffication         datatypes.JSON `json:"tariffication" form:"tariffication" gorm:"column:tariffication;comment:;type:text;" swaggertype:"array,object"` //发运的计费信息
}

// TableName ozone订单详情 OzoneOrderDetail自定义表名 ozone_order_detail
func (OzoneOrderDetail) TableName() string {
	return "ozone_order_detail"
}
