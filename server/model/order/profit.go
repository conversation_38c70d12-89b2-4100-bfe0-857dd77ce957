package order

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// OrderProfit 订单利润统计结构体
type OrderProfit struct {
	global.GVA_MODEL
	PostingNumber *string    `json:"postingNumber" form:"postingNumber" gorm:"column:posting_number;comment:货件号;"`
	OrderNumber   *string    `json:"orderNumber" form:"orderNumber" gorm:"column:order_number;comment:订单号;"`
	ShopName      *string    `json:"shopName" form:"shopName" gorm:"column:shop_name;comment:店铺名称;"`
	Currency      *string    `json:"currency" form:"currency" gorm:"column:currency;comment:结算货币;size:10;default:'CNY';"`
	ProductName   *string    `json:"productName" form:"productName" gorm:"column:product_name;comment:产品名称;"`
	SKU           *string    `json:"sku" form:"sku" gorm:"column:sku;comment:SKU;"`
	Quantity      *int       `json:"quantity" form:"quantity" gorm:"column:quantity;comment:数量;"`
	SalePrice     *float64   `json:"salePrice" form:"salePrice" gorm:"column:sale_price;comment:销售价格;"`
	CostPrice     *float64   `json:"costPrice" form:"costPrice" gorm:"column:cost_price;comment:成本价格;"`
	Commission    *float64   `json:"commission" form:"commission" gorm:"column:commission;comment:佣金;"`
	ShippingCost  *float64   `json:"shippingCost" form:"shippingCost" gorm:"column:shipping_cost;comment:运费;"`
	OtherCosts    *float64   `json:"otherCosts" form:"otherCosts" gorm:"column:other_costs;comment:其他费用;"`
	GrossProfit   *float64   `json:"grossProfit" form:"grossProfit" gorm:"column:gross_profit;comment:毛利润;"`
	NetProfit     *float64   `json:"netProfit" form:"netProfit" gorm:"column:net_profit;comment:净利润;"`
	ProfitMargin  *float64   `json:"profitMargin" form:"profitMargin" gorm:"column:profit_margin;comment:利润率;"`
	OrderDate     *time.Time `json:"orderDate" form:"orderDate" gorm:"column:order_date;comment:下单日期;"`
	Status        *string    `json:"status" form:"status" gorm:"column:status;comment:订单状态;"`
}

// ProfitSummary 利润汇总统计
type ProfitSummary struct {
	TotalOrders      int     `json:"totalOrders"`      // 总订单数
	TotalSales       float64 `json:"totalSales"`       // 总销售额(人民币)
	TotalCosts       float64 `json:"totalCosts"`       // 总成本(人民币)
	TotalGrossProfit float64 `json:"totalGrossProfit"` // 总毛利润(人民币)
	TotalNetProfit   float64 `json:"totalNetProfit"`   // 总净利润(人民币)
	AvgProfitMargin  float64 `json:"avgProfitMargin"`  // 平均利润率
}

// ProfitSummaryByCurrency 按货币分组的利润汇总统计
type ProfitSummaryByCurrency struct {
	Currency         string  `json:"currency"`         // 货币类型
	TotalOrders      int     `json:"totalOrders"`      // 总订单数
	TotalSales       float64 `json:"totalSales"`       // 总销售额
	TotalCosts       float64 `json:"totalCosts"`       // 总成本
	TotalGrossProfit float64 `json:"totalGrossProfit"` // 总毛利润
	TotalNetProfit   float64 `json:"totalNetProfit"`   // 总净利润
	AvgProfitMargin  float64 `json:"avgProfitMargin"`  // 平均利润率
}

// ProfitByShop 按店铺统计利润
type ProfitByShop struct {
	ShopName    string  `json:"shopName"`
	Currency    string  `json:"currency"`
	OrderCount  int     `json:"orderCount"`
	TotalSales  float64 `json:"totalSales"`
	TotalProfit float64 `json:"totalProfit"`
	ProfitRate  float64 `json:"profitRate"`
}

// ProfitByProduct 按产品统计利润
type ProfitByProduct struct {
	SKU         string  `json:"sku"`
	ProductName string  `json:"productName"`
	OrderCount  int     `json:"orderCount"`
	TotalSales  float64 `json:"totalSales"`
	TotalProfit float64 `json:"totalProfit"`
	ProfitRate  float64 `json:"profitRate"`
}

// ProfitByDate 按日期统计利润
type ProfitByDate struct {
	Date        string  `json:"date"`
	OrderCount  int     `json:"orderCount"`
	TotalSales  float64 `json:"totalSales"`
	TotalProfit float64 `json:"totalProfit"`
	ProfitRate  float64 `json:"profitRate"`
}

// ProfitByMonth 按月统计利润
type ProfitByMonth struct {
	Month           string  `json:"month"`           // 月份 (YYYY-MM)
	Currency        string  `json:"currency"`        // 货币类型(统一CNY)
	OrderCount      int     `json:"orderCount"`      // 订单数量
	TotalQuantity   int     `json:"totalQuantity"`   // 总商品数量
	TotalSales      float64 `json:"totalSales"`      // 总销售额(人民币)
	TotalCosts      float64 `json:"totalCosts"`      // 总成本(人民币)
	TotalShipping   float64 `json:"totalShipping"`   // 总物流费用(人民币)
	TotalCommission float64 `json:"totalCommission"` // 总佣金(人民币)
	AdvertisingCost float64 `json:"advertisingCost"` // 广告费用(人民币)
	TotalProfit     float64 `json:"totalProfit"`     // 总利润(人民币)
	ProfitRate      float64 `json:"profitRate"`      // 利润率
}

// MonthlyAdvertising 月度广告费用管理
type MonthlyAdvertising struct {
	global.GVA_MODEL
	Month           string   `json:"month" form:"month" gorm:"column:month;comment:月份(YYYY-MM);uniqueIndex;"`
	AdvertisingCost *float64 `json:"advertisingCost" form:"advertisingCost" gorm:"column:advertising_cost;comment:广告费用;"`
	Notes           *string  `json:"notes" form:"notes" gorm:"column:notes;comment:备注;"`
}

func (OrderProfit) TableName() string {
	return "order_profits"
}

func (MonthlyAdvertising) TableName() string {
	return "monthly_advertising"
}
