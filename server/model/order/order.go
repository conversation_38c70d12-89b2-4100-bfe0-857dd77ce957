// 自动生成模板Order
package order

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// 订单 结构体  Order
type Order struct {
	global.GVA_MODEL
	ShopName           *string        `json:"shopName" form:"shopName" gorm:"column:shop_name;comment:店铺名称;"`                                     //店铺名称
	ShopClientID       *string        `json:"shopClientID" form:"shopClientID" gorm:"column:shop_client_id;comment:店铺ClientID;"`                  //店铺ClientID
	OrderImg           datatypes.JSON `json:"orderImg" form:"orderImg" gorm:"column:order_img;comment:;" swaggertype:"array,object"`              //产品图
	PostingNumber      *string        `json:"postingNumber" form:"postingNumber" gorm:"column:posting_number;comment:;"`                          //货件号
	InProcessAt        *time.Time     `json:"inProcessAt" form:"inProcessAt" gorm:"column:in_process_at;comment:;"`                               //下单时间
	OrderNumber        *string        `json:"orderNumber" form:"orderNumber" gorm:"column:order_number;comment:;"`                                //订单号
	TplProvider        *string        `json:"tplProvider" form:"tplProvider" gorm:"column:tpl_provider;comment:;"`                                //物流
	Status             *string        `json:"status" form:"status" gorm:"column:status;comment:;"`                                                //订单状态
	DistributionStatus *string        `json:"distributionStatus" form:"distributionStatus" gorm:"default:0;column:distribution_status;comment:;"` //配货状态
	TrackingNumber     *string        `json:"trackingNumber" form:"trackingNumber" gorm:"column:tracking_number;comment:;"`                       //国际单号
	ShipmentDate       *time.Time     `json:"shipmentDate" form:"shipmentDate" gorm:"column:shipment_date;comment:;"`                             //发货日期
	JsonData           datatypes.JSON `json:"jsonData" form:"jsonData" gorm:"column:json_data;comment:;type:text;" swaggertype:"object"`          //订单完整信息
}

// TableName 订单 Order自定义表名 order
func (Order) TableName() string {
	return "order"
}
