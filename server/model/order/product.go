package order

import (
	"time"

	"gorm.io/datatypes"
)

// Product 产品管理结构体（不使用软删除）
type Product struct {
	ID              uint           `json:"ID" gorm:"primarykey"`                                                                            // 主键ID
	CreatedAt       time.Time      `json:"CreatedAt"`                                                                                       // 创建时间
	UpdatedAt       time.Time      `json:"UpdatedAt"`                                                                                       // 更新时间
	SKU             *string        `json:"sku" form:"sku" gorm:"column:sku;comment:SKU;index;uniqueIndex"`                                  // SKU
	ProductName     *string        `json:"productName" form:"productName" gorm:"column:product_name;comment:产品名称;"`                         // 产品名称（内部使用，不在前端显示）
	ShopName        *string        `json:"shopName" form:"shopName" gorm:"column:shop_name;comment:店铺名称;index"`                             // 店铺名称
	ActualWeight    *float64       `json:"actualWeight" form:"actualWeight" gorm:"column:actual_weight;comment:实际重量(kg);"`                  // 实际重量(kg)
	OzonWeight      *float64       `json:"ozonWeight" form:"ozonWeight" gorm:"column:ozon_weight;comment:Ozon填写重量(kg);"`                    // Ozon填写重量(kg)
	Description     *string        `json:"description" form:"description" gorm:"column:description;comment:产品描述;"`                          // 产品描述
	Brand           *string        `json:"brand" form:"brand" gorm:"column:brand;comment:品牌;"`                                              // 品牌
	ImageURL        *string        `json:"imageUrl" form:"imageUrl" gorm:"column:image_url;comment:产品图片URL;"`                               // 产品图片URL
	IsActive        *bool          `json:"isActive" form:"isActive" gorm:"column:is_active;default:true;comment:是否启用;"`                     // 是否启用
	FirstOrderDate  *time.Time     `json:"firstOrderDate" form:"firstOrderDate" gorm:"column:first_order_date;comment:首次订单日期;"`             // 首次订单日期
	LastOrderDate   *time.Time     `json:"lastOrderDate" form:"lastOrderDate" gorm:"column:last_order_date;comment:最后订单日期;"`                // 最后订单日期
	TotalOrderCount *int           `json:"totalOrderCount" form:"totalOrderCount" gorm:"column:total_order_count;comment:总订单数;"`            // 总订单数
	CostPrice       *float64       `json:"costPrice" form:"costPrice" gorm:"column:cost_price;comment:成本价格;"`                               // 成本价格
	CommissionRate  *float64       `json:"commissionRate" form:"commissionRate" gorm:"column:commission_rate;comment:佣金比率;default:0.12;"`   // 佣金比率
	Notes           *string        `json:"notes" form:"notes" gorm:"column:notes;comment:备注;"`                                              // 备注
	JsonData        datatypes.JSON `json:"jsonData" form:"jsonData" gorm:"column:json_data;comment:产品完整信息;type:text;" swaggertype:"object"` // 产品完整信息
}

// ProductSummary 产品汇总统计
type ProductSummary struct {
	TotalProducts    int `json:"totalProducts"`    // 总产品数
	ActiveProducts   int `json:"activeProducts"`   // 启用产品数
	InactiveProducts int `json:"inactiveProducts"` // 禁用产品数
}

func (Product) TableName() string {
	return "products"
}
