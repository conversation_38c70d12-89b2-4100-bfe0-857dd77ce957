package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
)

// ProductSearch 产品搜索结构体
type ProductSearch struct {
	StartDate       *time.Time `json:"startDate" form:"startDate"`
	EndDate         *time.Time `json:"endDate" form:"endDate"`
	SKU             *string    `json:"sku" form:"sku"`
	ShopName        *string    `json:"shopName" form:"shopName"`
	Brand           *string    `json:"brand" form:"brand"`
	MinActualWeight *float64   `json:"minActualWeight" form:"minActualWeight"`
	MaxActualWeight *float64   `json:"maxActualWeight" form:"maxActualWeight"`
	MinOzonWeight   *float64   `json:"minOzonWeight" form:"minOzonWeight"`
	MaxOzonWeight   *float64   `json:"maxOzonWeight" form:"maxOzonWeight"`
	IsActive        *bool      `json:"isActive" form:"isActive"`
	MinOrderCount   *int       `json:"minOrderCount" form:"minOrderCount"`
	MaxOrderCount   *int       `json:"maxOrderCount" form:"maxOrderCount"`
	request.PageInfo
}

// CreateProductRequest 创建产品请求
type CreateProductRequest struct {
	order.Product
}

// UpdateProductRequest 更新产品请求
type UpdateProductRequest struct {
	ID uint `json:"id" form:"id"`
	order.Product
}

// DeleteProductRequest 删除产品请求
type DeleteProductRequest struct {
	ID uint `json:"id" form:"id"`
}

// GetProductRequest 获取产品请求
type GetProductRequest struct {
	ID uint `json:"id" form:"id"`
}

// BatchUpdateProductRequest 批量更新产品请求
type BatchUpdateProductRequest struct {
	IDs            []uint   `json:"ids" form:"ids"`
	ActualWeight   *float64 `json:"actualWeight" form:"actualWeight"`
	OzonWeight     *float64 `json:"ozonWeight" form:"ozonWeight"`
	CostPrice      *float64 `json:"costPrice" form:"costPrice"`
	CommissionRate *float64 `json:"commissionRate" form:"commissionRate"`
	ShopName       *string  `json:"shopName" form:"shopName"`
	Brand          *string  `json:"brand" form:"brand"`
	Description    *string  `json:"description" form:"description"`
	IsActive       *bool    `json:"isActive" form:"isActive"`
}

// SyncProductsFromOrdersRequest 从订单同步产品请求
type SyncProductsFromOrdersRequest struct {
	ForceUpdate bool `json:"forceUpdate" form:"forceUpdate"` // 是否强制更新已存在的产品
}
