package request

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	"time"
)

type LogisticsSearch struct {
	order.Logistics
	StartCreatedAt *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt   *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	request.PageInfo
}

// SyncLogisticsRequest 同步物流信息请求
type SyncLogisticsRequest struct {
	ShopID *uint `json:"shopId" form:"shopId"` // 店铺ID，可选，为空则同步所有店铺
}
