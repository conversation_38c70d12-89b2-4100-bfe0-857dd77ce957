package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

// OrderProfitSearch 利润统计搜索结构体
type OrderProfitSearch struct {
	StartDate     *time.Time `json:"startDate" form:"startDate"`
	EndDate       *time.Time `json:"endDate" form:"endDate"`
	ShopName      *string    `json:"shopName" form:"shopName"`
	ProductName   *string    `json:"productName" form:"productName"`
	SKU           *string    `json:"sku" form:"sku"`
	PostingNumber *string    `json:"postingNumber" form:"postingNumber"`
	OrderNumber   *string    `json:"orderNumber" form:"orderNumber"`
	MinProfit     *float64   `json:"minProfit" form:"minProfit"`
	MaxProfit     *float64   `json:"maxProfit" form:"maxProfit"`
	Status        *string    `json:"status" form:"status"`
	request.PageInfo
}

// ProfitAnalysisRequest 利润分析请求
type ProfitAnalysisRequest struct {
	StartDate *time.Time `json:"startDate" form:"startDate"`
	EndDate   *time.Time `json:"endDate" form:"endDate"`
	GroupBy   string     `json:"groupBy" form:"groupBy"` // shop, product, date
	ShopName  *string    `json:"shopName" form:"shopName"`
}

// ProfitCalculateRequest 利润计算请求
type ProfitCalculateRequest struct {
	PostingNumber *string  `json:"postingNumber" form:"postingNumber"`
	CostPrice     *float64 `json:"costPrice" form:"costPrice"`
	Commission    *float64 `json:"commission" form:"commission"`
	ShippingCost  *float64 `json:"shippingCost" form:"shippingCost"`
	OtherCosts    *float64 `json:"otherCosts" form:"otherCosts"`
}

// MonthlyProfitSearch 月度利润统计搜索
type MonthlyProfitSearch struct {
	Year     *int    `json:"year" form:"year"`         // 年份
	Month    *int    `json:"month" form:"month"`       // 月份
	ShopName *string `json:"shopName" form:"shopName"` // 店铺名称
	request.PageInfo
}

// MonthlyAdvertisingRequest 月度广告费用请求
type MonthlyAdvertisingRequest struct {
	Month           string   `json:"month" form:"month"`                     // 月份 (YYYY-MM)
	AdvertisingCost *float64 `json:"advertisingCost" form:"advertisingCost"` // 广告费用
	Notes           *string  `json:"notes" form:"notes"`                     // 备注
}

// GetMonthlyAdvertisingRequest 获取月度广告费用请求
type GetMonthlyAdvertisingRequest struct {
	Month string `json:"month" form:"month"` // 月份 (YYYY-MM)
}
