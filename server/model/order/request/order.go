package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
)

type OrderSearch struct {
	StartCreatedAt     *time.Time `json:"startCreatedAt" form:"startCreatedAt"`
	EndCreatedAt       *time.Time `json:"endCreatedAt" form:"endCreatedAt"`
	PostingNumber      *string    `json:"postingNumber" form:"postingNumber" `
	InProcessAt        *time.Time `json:"inProcessAt" form:"inProcessAt" `
	OrderNumber        *string    `json:"orderNumber" form:"orderNumber" `
	TplProvider        *string    `json:"tplProvider" form:"tplProvider" `
	Status             *string    `json:"status" form:"status" `
	DistributionStatus *string    `json:"distributionStatus" form:"distributionStatus" `
	TrackingNumber     *string    `json:"trackingNumber" form:"trackingNumber" `
	ShipmentDate       *time.Time `json:"shipmentDate" form:"shipmentDate" `
	request.PageInfo
}
