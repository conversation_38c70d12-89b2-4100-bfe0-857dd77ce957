// 物流管理模型
package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// Logistics 物流方式管理结构体
type Logistics struct {
	global.GVA_MODEL
	ShopName        *string        `json:"shopName" form:"shopName" gorm:"column:shop_name;comment:店铺名称;"`                                               //店铺名称
	ShopClientID    *string        `json:"shopClientID" form:"shopClientID" gorm:"column:shop_client_id;comment:店铺ClientID;"`                            //店铺ClientID
	OzonDeliveryID  *int64         `json:"ozonDeliveryID" form:"ozonDeliveryID" gorm:"column:ozon_delivery_id;comment:Ozon配送方式ID;index;"`                //Ozon配送方式ID
	OzonWarehouseID *int64         `json:"ozonWarehouseID" form:"ozonWarehouseID" gorm:"column:ozon_warehouse_id;comment:Ozon仓库ID;"`                     //Ozon仓库ID
	Name            *string        `json:"name" form:"name" gorm:"column:name;comment:物流方式名称;"`                                                          //物流方式名称
	Provider        *string        `json:"provider" form:"provider" gorm:"column:provider;comment:物流服务商;"`                                               //物流服务商
	ServiceType     *string        `json:"serviceType" form:"serviceType" gorm:"column:service_type;comment:服务类型;"`                                      //服务类型(如：标准快递、特快专递等)
	Description     *string        `json:"description" form:"description" gorm:"column:description;comment:描述;"`                                         //描述
	IsActive        *bool          `json:"isActive" form:"isActive" gorm:"column:is_active;comment:是否启用;default:true;"`                                  //是否启用
	BasePrice       *float64       `json:"basePrice" form:"basePrice" gorm:"column:base_price;comment:基础价格;"`                                            //基础价格
	PricePerKg      *float64       `json:"pricePerKg" form:"pricePerKg" gorm:"column:price_per_kg;comment:每公斤价格;"`                                       //每公斤价格
	PricePerCubic   *float64       `json:"pricePerCubic" form:"pricePerCubic" gorm:"column:price_per_cubic;comment:每立方米价格;"`                             //每立方米价格
	MinWeight       *float64       `json:"minWeight" form:"minWeight" gorm:"column:min_weight;comment:最小重量(kg);"`                                        //最小重量(kg)
	MaxWeight       *float64       `json:"maxWeight" form:"maxWeight" gorm:"column:max_weight;comment:最大重量(kg);"`                                        //最大重量(kg)
	EstimatedDays   *int           `json:"estimatedDays" form:"estimatedDays" gorm:"column:estimated_days;comment:预计送达天数;"`                              //预计送达天数
	CommissionRate  *float64       `json:"commissionRate" form:"commissionRate" gorm:"column:commission_rate;comment:佣金比率;"`                             //佣金比率(优先级高于产品佣金比率)
	PricingFormula  *string        `json:"pricingFormula" form:"pricingFormula" gorm:"column:pricing_formula;comment:计价公式;"`                             //计价公式
	FormulaParams   datatypes.JSON `json:"formulaParams" form:"formulaParams" gorm:"column:formula_params;comment:公式参数;type:text;" swaggertype:"object"` //公式参数
	SortOrder       *int           `json:"sortOrder" form:"sortOrder" gorm:"column:sort_order;comment:排序;default:0;"`                                    //排序
	OzonData        datatypes.JSON `json:"ozonData" form:"ozonData" gorm:"column:ozon_data;comment:Ozon原始数据;type:text;" swaggertype:"object"`            //Ozon原始数据
}

// TableName 物流信息 Logistics自定义表名 logistics
func (Logistics) TableName() string {
	return "logistics"
}
