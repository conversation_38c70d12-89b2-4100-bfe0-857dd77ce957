
// 自动生成模板OzonShop
package xrtozon
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// ozon商店 结构体  OzonShop
type OzonShop struct {
    global.GVA_MODEL
    Name  *string `json:"name" form:"name" gorm:"column:name;comment:;" binding:"required"`  //店铺名称
    ClientID  *string `json:"clientID" form:"clientID" gorm:"column:clientID;comment:;" binding:"required"`  //店铺ID
    APIKey  *string `json:"APIKey" form:"APIKey" gorm:"column:APIKey;comment:;" binding:"required"`  //APIKey
    AccountID  *string `json:"accountID" form:"accountID" gorm:"column:accountID;comment:;" binding:"required"`  //关联的账号
}


// TableName ozon商店 OzonShop自定义表名 ozon_shop
func (OzonShop) TableName() string {
    return "ozon_shop"
}





