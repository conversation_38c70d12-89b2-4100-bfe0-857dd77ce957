// 自动生成模板OzonShop
package shops

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 店铺授权 结构体  OzonShop
type OzonShop struct {
	global.GVA_MODEL
	Name        *string `json:"name" form:"name" gorm:"column:name;comment:店铺名字;size:191;"`                          //店铺名字
	ClientID    *string `json:"clientID" form:"clientID" gorm:"column:clientID;comment:店铺ID;size:191;"`              //店铺ID
	APIKey      *string `json:"APIKey" form:"APIKey" gorm:"column:APIKey;comment:;size:191;"`                        //APIKey字段
	AccountID   *string `json:"accountID" form:"accountID" gorm:"column:accountID;comment:;size:191;"`               //accountID字段
	AccountName *string `json:"accountName" form:"accountName" gorm:"column:account_name;comment:用户名;"`              //用户名
	ShopType    *string `json:"shopType" form:"shopType" gorm:"column:shop_type;comment:店铺类型;"`                      //店铺类型
	Currency    *string `json:"currency" form:"currency" gorm:"column:currency;comment:结算货币;size:10;default:'CNY';"` //结算货币(CNY/USD/EUR)
}

// TableName 店铺授权 OzonShop自定义表名 ozon_shop
func (OzonShop) TableName() string {
	return "ozon_shop"
}
