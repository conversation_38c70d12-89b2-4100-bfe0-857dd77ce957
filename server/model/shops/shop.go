
// 自动生成模板Shop
package shops
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 商店 结构体  Shop
type Shop struct {
    global.GVA_MODEL
    ShopName  *string `json:"shopName" form:"shopName" gorm:"column:shop_name;comment:;"`  //商店名字
    ShopID  *string `json:"shopID" form:"shopID" gorm:"column:shop_i_d;comment:;"`  //商店ID
    ShopAPIKey  *string `json:"shopAPIKey" form:"shopAPIKey" gorm:"column:shop_a_p_i_key;comment:;"`  //商店API Key
}


// TableName 商店 Shop自定义表名 shop
func (Shop) TableName() string {
    return "shop"
}





