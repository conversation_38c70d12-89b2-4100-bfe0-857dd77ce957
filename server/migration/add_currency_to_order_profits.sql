-- 为 order_profits 表添加货币字段
ALTER TABLE `order_profits` ADD COLUMN `currency` VARCHAR(10) DEFAULT 'CNY' COMMENT '结算货币(CNY/USD/EUR)' AFTER `shop_name`;

-- 更新现有记录的货币字段，根据店铺名称从 ozon_shop 表获取货币信息
UPDATE `order_profits` op 
JOIN `ozon_shop` os ON op.shop_name = os.name 
SET op.currency = COALESCE(os.currency, 'CNY') 
WHERE op.currency IS NULL OR op.currency = '';

-- 为没有匹配店铺的记录设置默认货币
UPDATE `order_profits` SET `currency` = 'CNY' WHERE `currency` IS NULL OR `currency` = '';

-- 添加索引以提高按货币查询的性能
CREATE INDEX `idx_order_profits_currency` ON `order_profits` (`currency`);
CREATE INDEX `idx_order_profits_currency_date` ON `order_profits` (`currency`, `order_date`);
