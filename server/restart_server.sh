#!/bin/bash

# Gin-Vue-Admin Server 重启脚本
# 用途：停止旧服务，编译最新代码，启动新服务

echo "🔄 开始重启 Gin-Vue-Admin Server..."

# 1. 停止当前运行的服务
echo "📛 停止当前运行的服务..."

# 停止所有gin-vue-admin相关进程
pkill -f "gin-vue-admin" 2>/dev/null || true

# 停止占用8888端口的进程
if lsof -ti:8888 >/dev/null 2>&1; then
    echo "   发现端口8888被占用，正在停止..."
    lsof -ti:8888 | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# 2. 编译最新版本
echo "🔨 编译最新版本..."
if go build -o gin-vue-admin-server .; then
    echo "   ✅ 编译成功"
else
    echo "   ❌ 编译失败，请检查代码"
    exit 1
fi

# 3. 启动新服务
echo "🚀 启动新服务..."
nohup ./gin-vue-admin-server > server.log 2>&1 &
SERVER_PID=$!

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 4. 验证服务状态
echo "🔍 验证服务状态..."
if curl -s http://localhost:8888/health >/dev/null 2>&1; then
    echo "   ✅ 服务启动成功！"
    echo "   📊 服务PID: $SERVER_PID"
    echo "   🌐 前端地址: http://127.0.0.1:8080"
    echo "   📖 API文档: http://127.0.0.1:8888/swagger/index.html"
    echo "   📝 日志文件: server.log"
    echo ""
    echo "💡 使用以下命令查看实时日志:"
    echo "   tail -f server.log"
    echo ""
    echo "💡 使用以下命令停止服务:"
    echo "   kill $SERVER_PID"
else
    echo "   ❌ 服务启动失败，请检查日志"
    echo "   📝 查看日志: tail -f server.log"
    exit 1
fi

echo "🎉 后端重启完成！"

# 5. 检查前端服务状态（不重启）
echo "🌐 检查前端服务状态..."

# 检查前端是否已经在运行
WEB_RUNNING=false
for port in 8080 8093 8081 8082 8083 8084 8085; do
    if lsof -ti:$port >/dev/null 2>&1; then
        echo "   ✅ 前端服务正在运行 (端口$port)"
        WEB_RUNNING=true
        WEB_PORT=$port
        break
    fi
done

if [ "$WEB_RUNNING" = false ]; then
    echo "   ⚠️  前端服务未运行"
    echo "   💡 请手动启动前端服务："
    echo "      cd ../web && npm run serve"
else
    echo "   💡 前端服务地址: http://localhost:$WEB_PORT"
fi
echo ""
echo "🎉 后端服务重启完成！"
echo ""
echo "📊 服务状态总结:"
echo "   🔧 后端服务: http://127.0.0.1:8888 (PID: $SERVER_PID)"
if [ -n "$WEB_PORT" ]; then
    echo "   🌐 前端服务: http://127.0.0.1:$WEB_PORT (保持运行)"
fi
echo "   📖 API文档: http://127.0.0.1:8888/swagger/index.html"
echo ""
if [ "$WEB_RUNNING" = true ]; then
    echo "💡 前端服务保持运行，无需重启"
    echo "💡 前端地址: http://127.0.0.1:${WEB_PORT}"
else
    echo "💡 请手动启动前端服务: cd ../web && npm run serve"
fi
