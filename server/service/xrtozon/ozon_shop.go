
package xrtozon

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/xrtozon"
    xrtozonReq "github.com/flipped-aurora/gin-vue-admin/server/model/xrtozon/request"
)

type OzonShopService struct {}
// CreateOzonShop 创建ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService) CreateOzonShop(ctx context.Context, oShop *xrtozon.OzonShop) (err error) {
	err = global.GVA_DB.Create(oShop).Error
	return err
}

// DeleteOzonShop 删除ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService)DeleteOzonShop(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&xrtozon.OzonShop{},"id = ?",ID).Error
	return err
}

// DeleteOzonShopByIds 批量删除ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService)DeleteOzonShopByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]xrtozon.OzonShop{},"id in ?",IDs).Error
	return err
}

// UpdateOzonShop 更新ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService)UpdateOzonShop(ctx context.Context, oShop xrtozon.OzonShop) (err error) {
	err = global.GVA_DB.Model(&xrtozon.OzonShop{}).Where("id = ?",oShop.ID).Updates(&oShop).Error
	return err
}

// GetOzonShop 根据ID获取ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService)GetOzonShop(ctx context.Context, ID string) (oShop xrtozon.OzonShop, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&oShop).Error
	return
}
// GetOzonShopInfoList 分页获取ozon商店记录
// Author [yourname](https://github.com/yourname)
func (oShopService *OzonShopService)GetOzonShopInfoList(ctx context.Context, info xrtozonReq.OzonShopSearch) (list []xrtozon.OzonShop, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&xrtozon.OzonShop{})
    var oShops []xrtozon.OzonShop
    // 如果有条件搜索 下方会自动创建搜索语句
    if info.StartCreatedAt !=nil && info.EndCreatedAt !=nil {
     db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
    }
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&oShops).Error
	return  oShops, total, err
}
func (oShopService *OzonShopService)GetOzonShopPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
