package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/service/example"
	"github.com/flipped-aurora/gin-vue-admin/server/service/order"
	"github.com/flipped-aurora/gin-vue-admin/server/service/ozoneOrder"
	"github.com/flipped-aurora/gin-vue-admin/server/service/shops"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"github.com/flipped-aurora/gin-vue-admin/server/service/xrtozon"
)

var ServiceGroupApp = &ServiceGroup{
	OrderServiceGroup: order.ServiceGroup{
		OrderService: order.NewOrderService(),
	},
}

type ServiceGroup struct {
	SystemServiceGroup     system.ServiceGroup
	ExampleServiceGroup    example.ServiceGroup
	XrtozonServiceGroup    xrtozon.ServiceGroup
	ShopsServiceGroup      shops.ServiceGroup
	OzoneOrderServiceGroup ozoneOrder.ServiceGroup
	OrderServiceGroup      order.ServiceGroup
}
