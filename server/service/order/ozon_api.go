package order

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
)

// OzonAPIService Ozon API服务
type OzonAPIService struct {
	baseURL string
	client  *http.Client
}

// NewOzonAPIService 创建新的Ozon API服务
func NewOzonAPIService() *OzonAPIService {
	return &OzonAPIService{
		baseURL: "https://api-seller.ozon.ru",
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// OzonFBSListRequest Ozon FBS订单列表请求结构
type OzonFBSListRequest struct {
	Dir    string            `json:"dir"`
	Filter OzonFBSListFilter `json:"filter"`
	Limit  int               `json:"limit"`
	Offset int               `json:"offset"`
	With   OzonFBSListWith   `json:"with"`
}

// OzonFBSListFilter Ozon FBS订单过滤器
type OzonFBSListFilter struct {
	Since  string   `json:"since,omitempty"`
	To     string   `json:"to,omitempty"`
	Status []string `json:"status,omitempty"`
}

// OzonFBSListWith Ozon FBS订单附加数据
type OzonFBSListWith struct {
	AnalyticsData bool `json:"analytics_data"`
	FinancialData bool `json:"financial_data"`
}

// OzonFBSListResponse Ozon FBS订单列表响应结构
type OzonFBSListResponse struct {
	Result OzonFBSListResult `json:"result"`
}

// OzonFBOListResponse Ozon FBO订单列表响应
type OzonFBOListResponse struct {
	Result []OzonPosting `json:"result"`
}

// OzonFBSListResult Ozon FBS订单列表结果
type OzonFBSListResult struct {
	Postings []OzonPosting `json:"postings"`
	HasNext  bool          `json:"has_next"`
}

// OzonPosting Ozon订单结构
type OzonPosting struct {
	PostingNumber      string        `json:"posting_number"`
	OrderNumber        string        `json:"order_number"`
	Status             string        `json:"status"`
	InProcessAt        string        `json:"in_process_at"`
	ShipmentDate       string        `json:"shipment_date"`
	DeliveryMethod     OzonDelivery  `json:"delivery_method"`
	TrackingNumber     string        `json:"tracking_number"`
	TplIntegrationCode string        `json:"tpl_integration_code"`
	Products           []OzonProduct `json:"products"`
}

// OzonDelivery Ozon配送信息
type OzonDelivery struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
	TPL  string `json:"tpl_integration_code"`
}

// OzonProduct Ozon产品信息
type OzonProduct struct {
	SKU      int64  `json:"sku"`
	Name     string `json:"name"`
	Quantity int    `json:"quantity"`
	OfferId  string `json:"offer_id"`
	Price    string `json:"price"`
}

// GetUnfulfilledOrders 获取未完成的FBS订单列表（基于错误信息添加cutoff参数）
func (api *OzonAPIService) GetUnfulfilledOrders(ctx context.Context, clientID, apiKey string, sinceTime, toTime time.Time, statuses []string) (*OzonFBSListResponse, error) {
	// 拉取当前时间前后14天的订单数据
	// cutoff_from: 14天前
	// cutoff_to: 14天后
	cutoffFrom := time.Now().AddDate(0, 0, -14).Format("2006-01-02T15:04:05.000Z")
	cutoffTo := time.Now().AddDate(0, 0, 14).Format("2006-01-02T15:04:05.000Z")

	global.GVA_LOG.Info("Ozon API时间参数",
		zap.String("cutoff_from", cutoffFrom),
		zap.String("cutoff_to", cutoffTo),
		zap.String("说明", "使用当前时间前后14天的时间范围，总共28天"))

	requestBody := map[string]interface{}{
		"dir":    "DESC", // 改为降序，优先获取最新订单
		"limit":  1000,
		"offset": 0,
		"filter": map[string]interface{}{
			"cutoff_from": cutoffFrom,
			"cutoff_to":   cutoffTo,
		},
		"with": map[string]interface{}{
			"analytics_data": false,
			"financial_data": false,
		},
	}

	global.GVA_LOG.Info("构建Ozon Unfulfilled API请求",
		zap.String("URL", api.baseURL+"/v3/posting/fbs/unfulfilled/list"),
		zap.String("说明", "基于错误信息：unfulfilled接口也需要cutoff参数"),
		zap.String("CutoffFrom", cutoffFrom),
		zap.String("CutoffTo", cutoffTo),
		zap.Int("限制数量", 1000))

	// 不在API请求中过滤，在后端处理

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 添加请求体调试信息
	global.GVA_LOG.Info("Ozon Unfulfilled API请求体", zap.String("请求体", string(jsonData)))

	// 创建HTTP请求 - 获取未完成订单
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v3/posting/fbs/unfulfilled/list", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	// 发送请求
	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("Ozon API请求失败",
			zap.Int("状态码", resp.StatusCode),
			zap.String("响应内容", string(body)))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	global.GVA_LOG.Info("Ozon Unfulfilled API请求成功",
		zap.Int("响应长度", len(body)),
		zap.String("响应内容", string(body)))

	// 解析响应
	var ozonResp OzonFBSListResponse
	err = json.Unmarshal(body, &ozonResp)
	if err != nil {
		global.GVA_LOG.Error("解析Ozon响应失败",
			zap.Error(err),
			zap.String("响应内容", string(body)))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon Unfulfilled响应解析成功",
		zap.Int("未完成订单数量", len(ozonResp.Result.Postings)),
		zap.Bool("有更多数据", ozonResp.Result.HasNext))

	return &ozonResp, nil
}

// GetAllFBSOrders 获取所有FBS订单列表（包括已完成的订单）
func (api *OzonAPIService) GetAllFBSOrders(ctx context.Context, clientID, apiKey string, startTime, endTime time.Time) (*OzonFBSListResponse, error) {
	// 使用指定的时间范围
	cutoffFrom := startTime.Format("2006-01-02T15:04:05.000Z")
	cutoffTo := endTime.Format("2006-01-02T15:04:05.000Z")

	global.GVA_LOG.Info("Ozon API获取所有FBS订单参数",
		zap.String("cutoff_from", cutoffFrom),
		zap.String("cutoff_to", cutoffTo),
		zap.String("说明", "使用v3/posting/fbs/list获取所有订单，包括已完成的"))

	requestBody := map[string]interface{}{
		"dir":    "DESC", // 降序，优先获取最新订单
		"limit":  1000,
		"offset": 0,
		"filter": map[string]interface{}{
			"cutoff_from": cutoffFrom,
			"cutoff_to":   cutoffTo,
		},
		"with": map[string]interface{}{
			"analytics_data": false,
			"financial_data": false,
		},
	}

	global.GVA_LOG.Info("构建Ozon所有FBS订单API请求",
		zap.String("URL", api.baseURL+"/v3/posting/fbs/list"),
		zap.String("CutoffFrom", cutoffFrom),
		zap.String("CutoffTo", cutoffTo),
		zap.Int("限制数量", 1000))

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 添加请求体调试信息
	global.GVA_LOG.Info("Ozon所有FBS订单API请求体", zap.String("请求体", string(jsonData)))

	// 创建HTTP请求 - 获取所有FBS订单
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v3/posting/fbs/list", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("Ozon所有FBS订单API请求失败",
			zap.Int("状态码", resp.StatusCode),
			zap.String("响应内容", string(body)))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	global.GVA_LOG.Info("Ozon所有FBS订单API请求成功",
		zap.Int("响应长度", len(body)),
		zap.String("响应内容", string(body)))

	// 解析响应
	var ozonResp OzonFBSListResponse
	err = json.Unmarshal(body, &ozonResp)
	if err != nil {
		global.GVA_LOG.Error("解析Ozon所有FBS订单响应失败",
			zap.Error(err),
			zap.String("响应内容", string(body)))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon所有FBS订单响应解析成功",
		zap.Int("订单数量", len(ozonResp.Result.Postings)),
		zap.Bool("有更多数据", ozonResp.Result.HasNext))

	return &ozonResp, nil
}

// GetUnfulfilledOrdersWithTimeRange 获取指定时间范围内的未完成FBS订单列表
func (api *OzonAPIService) GetUnfulfilledOrdersWithTimeRange(ctx context.Context, clientID, apiKey string, startTime, endTime time.Time) (*OzonFBSListResponse, error) {
	// 使用指定的时间范围
	cutoffFrom := startTime.Format("2006-01-02T15:04:05.000Z")
	cutoffTo := endTime.Format("2006-01-02T15:04:05.000Z")

	global.GVA_LOG.Info("Ozon API指定时间范围参数",
		zap.String("cutoff_from", cutoffFrom),
		zap.String("cutoff_to", cutoffTo),
		zap.String("说明", "使用指定的时间范围拉取订单"))

	requestBody := map[string]interface{}{
		"dir":    "DESC", // 降序，优先获取最新订单
		"limit":  1000,
		"offset": 0,
		"filter": map[string]interface{}{
			"cutoff_from": cutoffFrom,
			"cutoff_to":   cutoffTo,
		},
		"with": map[string]interface{}{
			"analytics_data": false,
			"financial_data": false,
		},
	}

	global.GVA_LOG.Info("构建Ozon指定时间范围API请求",
		zap.String("URL", api.baseURL+"/v3/posting/fbs/unfulfilled/list"),
		zap.String("CutoffFrom", cutoffFrom),
		zap.String("CutoffTo", cutoffTo),
		zap.Int("限制数量", 1000))

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 添加请求体调试信息
	global.GVA_LOG.Info("Ozon指定时间范围API请求体", zap.String("请求体", string(jsonData)))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v3/posting/fbs/unfulfilled/list", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon指定时间范围API响应",
		zap.Int("状态码", resp.StatusCode),
		zap.Int("响应长度", len(body)))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("Ozon API返回错误状态码",
			zap.Int("状态码", resp.StatusCode),
			zap.String("响应体", string(body)))
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var ozonResp OzonFBSListResponse
	if err := json.Unmarshal(body, &ozonResp); err != nil {
		global.GVA_LOG.Error("解析响应失败",
			zap.Error(err),
			zap.String("响应体", string(body)))
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon指定时间范围API解析成功",
		zap.Int("订单数量", len(ozonResp.Result.Postings)))

	return &ozonResp, nil
}

// GetFBOOrders 获取FBO订单列表
func (api *OzonAPIService) GetFBOOrders(ctx context.Context, clientID, apiKey string, since, to time.Time, statuses []string) (*OzonFBOListResponse, error) {
	requestBody := map[string]interface{}{
		"dir":    "ASC",
		"limit":  1000,
		"offset": 0,
		"filter": map[string]interface{}{
			"since": since.Format("2006-01-02T15:04:05Z"),
			"to":    to.Format("2006-01-02T15:04:05Z"),
		},
		"with": map[string]interface{}{
			"analytics_data": false,
			"financial_data": false,
		},
	}

	// 如果指定了状态过滤，添加到请求中
	if len(statuses) > 0 {
		requestBody["filter"].(map[string]interface{})["status"] = statuses
	}

	global.GVA_LOG.Info("构建Ozon FBO API请求",
		zap.String("URL", api.baseURL+"/v2/posting/fbo/list"),
		zap.String("时间范围", fmt.Sprintf("%s 到 %s", since.Format("2006-01-02T15:04:05Z"), to.Format("2006-01-02T15:04:05Z"))),
		zap.Int("限制数量", 1000))

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon FBO API请求体", zap.String("请求体", string(jsonData)))

	// 创建HTTP请求 - FBO订单
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v2/posting/fbo/list", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	// 发送请求
	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon FBO API请求成功",
		zap.Int("响应长度", len(body)),
		zap.String("响应内容", string(body)))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var ozonResp OzonFBOListResponse
	if err := json.Unmarshal(body, &ozonResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	global.GVA_LOG.Info("Ozon FBO响应解析成功",
		zap.Int("订单数量", len(ozonResp.Result)))

	return &ozonResp, nil
}

// FilterOrdersByStatus 按状态过滤订单
func (api *OzonAPIService) FilterOrdersByStatus(postings []OzonPosting, targetStatuses []string) []OzonPosting {
	// 记录所有订单的状态用于调试
	statusCount := make(map[string]int)
	for _, posting := range postings {
		statusCount[posting.Status]++
	}
	global.GVA_LOG.Info("订单状态分布", zap.Any("状态统计", statusCount))

	// 本地过滤：只保存等待装运和等待备货的订单
	originalCount := len(postings)
	filteredPostings := []OzonPosting{}

	for _, posting := range postings {
		for _, targetStatus := range targetStatuses {
			if posting.Status == targetStatus {
				filteredPostings = append(filteredPostings, posting)
				break
			}
		}
	}

	global.GVA_LOG.Info("状态过滤后的订单",
		zap.Int("原始数量", originalCount),
		zap.Int("过滤后数量", len(filteredPostings)),
		zap.Strings("目标状态", targetStatuses))

	// 记录每个订单的详细信息
	for i, posting := range filteredPostings {
		global.GVA_LOG.Info("订单详情",
			zap.Int("序号", i+1),
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.String("status", posting.Status),
			zap.String("in_process_at", posting.InProcessAt))
	}

	return filteredPostings
}

// OzonProductInfoRequest 产品信息请求结构（单个offer_id）
type OzonProductInfoRequest struct {
	OfferID string `json:"offer_id"`
}

// OzonProductInfoResponse 产品信息响应结构（单个产品）
type OzonProductInfoResponse struct {
	Result OzonProductInfo `json:"result"`
}

// OzonProductInfo 产品信息
type OzonProductInfo struct {
	ID      int64  `json:"id"`
	OfferID string `json:"offer_id"`
	SKU     int64  `json:"sku"`
}

// OzonProductDetailRequest 产品详细信息请求结构
type OzonProductDetailRequest struct {
	ProductID []int64  `json:"product_id"`
	OfferID   []string `json:"offer_id"`
	SKU       []int64  `json:"sku"`
}

// OzonProductDetailResponse 产品详细信息响应结构
type OzonProductDetailResponse struct {
	Result OzonProductDetailResult `json:"result"`
}

// OzonProductDetailResult 产品详细信息结果
type OzonProductDetailResult struct {
	Items []OzonProductDetail `json:"items"`
}

// OzonProductDetail 产品详细信息
type OzonProductDetail struct {
	ID                int64                 `json:"id"`
	Name              string                `json:"name"`
	OfferID           string                `json:"offer_id"`
	Barcode           string                `json:"barcode"`
	BuyboxPrice       string                `json:"buybox_price"`
	CategoryID        int64                 `json:"category_id"`
	CreatedAt         string                `json:"created_at"`
	Images            []OzonProductImage    `json:"images"`
	MarketingPrice    string                `json:"marketing_price"`
	MinPrice          string                `json:"min_price"`
	OldPrice          string                `json:"old_price"`
	PremiumPrice      string                `json:"premium_price"`
	Price             string                `json:"price"`
	RecommendedPrice  string                `json:"recommended_price"`
	Sources           []OzonProductSource   `json:"sources"`
	State             string                `json:"state"`
	Stocks            OzonProductStocks     `json:"stocks"`
	Errors            []string              `json:"errors"`
	Vat               string                `json:"vat"`
	Visible           bool                  `json:"visible"`
	VisibilityDetails OzonVisibilityDetails `json:"visibility_details"`
	PriceIndex        string                `json:"price_index"`
	Images360         []string              `json:"images360"`
	ColorImage        string                `json:"color_image"`
	PrimaryImage      string                `json:"primary_image"`
	Status            OzonProductStatus     `json:"status"`
	Weight            int                   `json:"weight"`     // 重量（克）
	Dimensions        OzonProductDimensions `json:"dimensions"` // 尺寸
}

// OzonProductImage 产品图片
type OzonProductImage struct {
	FileName string `json:"file_name"`
	Default  bool   `json:"default"`
	Index    int    `json:"index"`
}

// OzonProductStocks 产品库存
type OzonProductStocks struct {
	Coming   int `json:"coming"`
	Present  int `json:"present"`
	Reserved int `json:"reserved"`
}

// OzonProductStatus 产品状态
type OzonProductStatus struct {
	State            string   `json:"state"`
	StateFailed      string   `json:"state_failed"`
	ModerationStatus string   `json:"moderation_status"`
	DeclineReasons   []string `json:"decline_reasons"`
	ValidationState  string   `json:"validation_state"`
}

// OzonProductDimensions 产品尺寸
type OzonProductDimensions struct {
	Height int `json:"height"` // 高度（毫米）
	Length int `json:"length"` // 长度（毫米）
	Width  int `json:"width"`  // 宽度（毫米）
}

// OzonProductListRequest 产品列表请求结构
type OzonProductListRequest struct {
	Filter OzonProductListFilter `json:"filter"`
	Limit  int                   `json:"limit"`
	LastID string                `json:"last_id"`
}

// OzonProductListFilter 产品列表过滤器
type OzonProductListFilter struct {
	OfferID    []string `json:"offer_id,omitempty"`
	ProductID  []int64  `json:"product_id,omitempty"`
	Visibility string   `json:"visibility,omitempty"`
}

// OzonProductListResponse 产品列表响应结构
type OzonProductListResponse struct {
	Result OzonProductListResult `json:"result"`
}

// OzonProductListResult 产品列表结果
type OzonProductListResult struct {
	Items  []OzonProductListItem `json:"items"`
	Total  int                   `json:"total"`
	LastID string                `json:"last_id"`
}

// OzonProductListItem 产品列表项
type OzonProductListItem struct {
	ProductID            int64                 `json:"product_id"`
	OfferID              string                `json:"offer_id"`
	IsCreated            bool                  `json:"is_created"`
	Barcode              string                `json:"barcode"`
	Description          string                `json:"description"`
	CategoryID           int64                 `json:"category_id"`
	Name                 string                `json:"name"`
	OfferIDOzon          string                `json:"offer_id_ozon"`
	TimeCreate           string                `json:"time_create"`
	Vat                  string                `json:"vat"`
	Visible              bool                  `json:"visible"`
	VisibilityDetails    OzonVisibilityDetails `json:"visibility_details"`
	Price                string                `json:"price"`
	OldPrice             string                `json:"old_price"`
	PremiumPrice         string                `json:"premium_price"`
	RecommendedPrice     string                `json:"recommended_price"`
	BuyingPrice          string                `json:"buying_price"`
	MinPrice             string                `json:"min_price"`
	MarketingPrice       string                `json:"marketing_price"`
	MarketingSellerPrice string                `json:"marketing_seller_price"`
	MinOzonPrice         string                `json:"min_ozon_price"`
	Sources              []OzonProductSource   `json:"sources"`
	State                string                `json:"state"`
	StateFailed          string                `json:"state_failed"`
	ModerationStatus     string                `json:"moderation_status"`
	DeclineReasons       []string              `json:"decline_reasons"`
	UpdateAt             string                `json:"update_at"`
	IsKGT                bool                  `json:"is_kgt"`
	HasDiscountedItem    bool                  `json:"has_discounted_item"`
	IsDiscounted         bool                  `json:"is_discounted"`
	DiscountedStocks     OzonDiscountedStocks  `json:"discounted_stocks"`
	IsArchived           bool                  `json:"is_archived"`
	IsAutoarchived       bool                  `json:"is_autoarchived"`
}

// OzonVisibilityDetails 可见性详情
type OzonVisibilityDetails struct {
	HasPrice      bool     `json:"has_price"`
	HasStock      bool     `json:"has_stock"`
	ActiveProduct bool     `json:"active_product"`
	Reasons       []string `json:"reasons"`
}

// OzonProductSource 产品来源
type OzonProductSource struct {
	IsEnabled bool   `json:"is_enabled"`
	SKU       int64  `json:"sku"`
	Source    string `json:"source"`
}

// OzonDiscountedStocks 折扣库存
type OzonDiscountedStocks struct {
	Coming   int `json:"coming"`
	Present  int `json:"present"`
	Reserved int `json:"reserved"`
}

// OzonProductAttributesV4Response v4产品属性响应结构
type OzonProductAttributesV4Response struct {
	Result OzonProductAttributesV4Result `json:"result"`
}

// OzonProductAttributesV4Result v4产品属性结果
type OzonProductAttributesV4Result struct {
	Items  []OzonProductAttributesV4Item `json:"items"`
	LastID string                        `json:"last_id"`
}

// OzonProductAttributesV4Item v4产品属性项
type OzonProductAttributesV4Item struct {
	ID         int64                    `json:"id"`
	OfferID    string                   `json:"offer_id"`
	Attributes []OzonProductAttributeV4 `json:"attributes"`
}

// OzonProductAttributeV4 v4产品属性
type OzonProductAttributeV4 struct {
	AttributeID int64                       `json:"attribute_id"`
	ComplexID   int64                       `json:"complex_id"`
	Values      []OzonProductAttributeValue `json:"values"`
}

// OzonProductAttributeValue 产品属性值
type OzonProductAttributeValue struct {
	DictionaryValueID int64  `json:"dictionary_value_id"`
	Value             string `json:"value"`
}

// OzonProductPicturesRequest 产品图片请求结构
type OzonProductPicturesRequest struct {
	ProductID []int64 `json:"product_id"`
}

// OzonProductPicturesResponse 产品图片响应结构（匹配实际API格式）
type OzonProductPicturesResponse struct {
	Items []OzonProductPictures `json:"items"`
}

// OzonProductPictures 产品图片信息
type OzonProductPictures struct {
	ProductID    int64    `json:"product_id"`
	PrimaryPhoto []string `json:"primary_photo"`
	Photo        []string `json:"photo"`
	ColorPhoto   []string `json:"color_photo"`
	Photo360     []string `json:"photo_360"`
	Errors       []string `json:"errors"`
}

// GetProductImages 获取商品图片 - 使用v4 API
func (api *OzonAPIService) GetProductImages(ctx context.Context, clientID, apiKey string, products []OzonProduct) ([]string, error) {
	global.GVA_LOG.Info("开始获取产品图片信息",
		zap.Int("产品总数", len(products)))

	if len(products) == 0 {
		return []string{}, nil
	}

	// 使用v4 API获取产品信息（包含图片）
	productInfos, err := api.GetProductImagesAndWeights(ctx, clientID, apiKey, products)
	if err != nil {
		global.GVA_LOG.Error("获取产品信息失败", zap.Error(err))
		// 如果API调用失败，返回空数组但不返回错误，避免影响订单同步
		return make([]string, len(products)), nil
	}

	// 创建offer_id到图片URL的映射，同时更新产品重量到数据库
	offerToImage := make(map[string]string)
	for _, info := range productInfos {
		if info.ImageURL != "" {
			offerToImage[info.OfferID] = info.ImageURL
		}

		// 同时更新产品重量到数据库
		if info.Weight > 0 {
			err := global.GVA_DB.Model(&order.Product{}).
				Where("sku = ?", info.OfferID).
				Update("ozon_weight", info.Weight).Error
			if err != nil {
				global.GVA_LOG.Warn("更新产品重量失败",
					zap.String("offer_id", info.OfferID),
					zap.Float64("weight", info.Weight),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("更新产品重量成功",
					zap.String("offer_id", info.OfferID),
					zap.Float64("weight_kg", info.Weight))
			}
		}
	}

	// 按照原始产品顺序构建图片URL数组
	images := make([]string, len(products))
	for i, product := range products {
		if imageURL, exists := offerToImage[product.OfferId]; exists {
			images[i] = imageURL
			global.GVA_LOG.Info("获取到产品图片",
				zap.String("offer_id", product.OfferId),
				zap.String("image_url", imageURL))
		} else {
			global.GVA_LOG.Warn("未找到产品图片",
				zap.String("offer_id", product.OfferId))
		}
	}

	global.GVA_LOG.Info("图片获取完成",
		zap.Int("总产品数", len(products)),
		zap.Int("获取到的产品信息数", len(productInfos)))

	return images, nil
}

// ProductImageAndWeight 产品图片和重量信息
type ProductImageAndWeight struct {
	OfferID     string  `json:"offer_id"`
	ImageURL    string  `json:"image_url"`
	Weight      float64 `json:"weight"` // 重量（千克）
	ProductID   int64   `json:"product_id"`
	SKU         int64   `json:"sku"`
	Name        string  `json:"name"`
	APIResponse string  `json:"api_response"` // 完整的API响应
}

// GetProductImagesAndWeights 同时获取商品图片和重量信息 - 使用v4 attributes API
func (api *OzonAPIService) GetProductImagesAndWeights(ctx context.Context, clientID, apiKey string, products []OzonProduct) ([]ProductImageAndWeight, error) {
	global.GVA_LOG.Info("开始获取产品信息",
		zap.Int("产品总数", len(products)))

	if len(products) == 0 {
		return []ProductImageAndWeight{}, nil
	}

	// 提取有效的offer_id
	offerIDs := make([]string, 0, len(products))
	offerToProduct := make(map[string]OzonProduct)

	for _, product := range products {
		if product.OfferId != "" && len(strings.TrimSpace(product.OfferId)) > 0 {
			cleanOfferId := strings.TrimSpace(product.OfferId)
			offerIDs = append(offerIDs, cleanOfferId)
			offerToProduct[cleanOfferId] = product
		}
	}

	if len(offerIDs) == 0 {
		global.GVA_LOG.Warn("没有有效的offer_id")
		return []ProductImageAndWeight{}, nil
	}

	// 直接使用offer_id调用v4 attributes API，不再依赖SKU
	// 因为很多产品的SKU为0，导致无法正确匹配

	// 构建v4 attributes API请求，使用offer_id而不是sku
	request := map[string]interface{}{
		"filter": map[string]interface{}{
			"offer_id":   offerIDs,
			"visibility": "ALL",
		},
		"limit":    1000,
		"sort_dir": "ASC",
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化产品属性请求失败: %v", err)
	}

	global.GVA_LOG.Info("调用v4 attributes API获取图片和重量",
		zap.String("请求体", string(jsonData)))

	// 发送v4 attributes请求
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v4/product/info/attributes", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建产品属性请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送产品属性请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取产品属性响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		global.GVA_LOG.Error("获取产品属性失败",
			zap.Int("状态码", resp.StatusCode),
			zap.String("响应", string(body)))
		return []ProductImageAndWeight{}, nil
	}

	global.GVA_LOG.Info("v4 attributes API调用成功",
		zap.Int("响应长度", len(body)))

	// 解析v4 API响应
	var response struct {
		Result []struct {
			ID           int64    `json:"id"`
			OfferID      string   `json:"offer_id"`
			SKU          int64    `json:"sku"`
			Name         string   `json:"name"`
			Weight       int      `json:"weight"`
			WeightUnit   string   `json:"weight_unit"`
			PrimaryImage string   `json:"primary_image"`
			Images       []string `json:"images"` // 修正：images是字符串数组，不是对象数组
		} `json:"result"`
	}

	err = json.Unmarshal(body, &response)
	if err != nil {
		global.GVA_LOG.Error("解析产品属性响应失败",
			zap.Error(err),
			zap.String("响应", string(body)))
		return []ProductImageAndWeight{}, nil
	}

	// 解析结果
	results := make([]ProductImageAndWeight, 0, len(response.Result))

	for _, item := range response.Result {
		// 直接使用API返回的offer_id
		offerID := item.OfferID
		if offerID == "" {
			global.GVA_LOG.Warn("产品缺少offer_id",
				zap.Int64("product_id", item.ID),
				zap.String("name", item.Name))
			continue
		}

		// 检查这个offer_id是否在我们的请求列表中
		if _, exists := offerToProduct[offerID]; !exists {
			global.GVA_LOG.Warn("返回的offer_id不在请求列表中",
				zap.String("offer_id", offerID))
			continue
		}

		// 解析重量
		var weightKg float64
		if item.Weight > 0 {
			weightKg = float64(item.Weight) / 1000.0 // 转换为千克
		}

		// 获取图片URL
		var imageURL string
		if item.PrimaryImage != "" {
			imageURL = api.FormatOzonImageURL(item.PrimaryImage)
		} else if len(item.Images) > 0 {
			// 从images数组中获取第一个图片URL
			imageURL = api.FormatOzonImageURL(item.Images[0])
		}

		result := ProductImageAndWeight{
			OfferID:     offerID,
			ImageURL:    imageURL,
			Weight:      weightKg,
			ProductID:   item.ID,
			SKU:         item.SKU,
			Name:        item.Name,
			APIResponse: string(body), // 保存完整的API响应
		}

		results = append(results, result)

		global.GVA_LOG.Info("成功解析产品信息",
			zap.String("offer_id", offerID),
			zap.String("name", item.Name),
			zap.Float64("weight_kg", weightKg),
			zap.String("image_url", imageURL))
	}

	global.GVA_LOG.Info("获取产品信息完成",
		zap.Int("成功数量", len(results)))

	return results, nil
}

// FormatOzonImageURL 格式化Ozon图片URL，添加尺寸参数以确保图片可以正常显示
func (api *OzonAPIService) FormatOzonImageURL(originalURL string) string {
	if originalURL == "" {
		return ""
	}

	// 如果URL已经包含尺寸参数，直接返回
	if strings.Contains(originalURL, "wc250") || strings.Contains(originalURL, "wc300") {
		return originalURL
	}

	// 添加wc250尺寸参数（宽度250px）
	// Ozon图片URL格式通常是: https://cdn1.ozone.ru/s3/multimedia-x/xxx.jpg
	// 需要转换为: https://cdn1.ozone.ru/s3/multimedia-x/wc250/xxx.jpg
	if strings.Contains(originalURL, "ozone.ru") && strings.Contains(originalURL, "/s3/") {
		// 找到最后一个 "/" 的位置
		lastSlashIndex := strings.LastIndex(originalURL, "/")
		if lastSlashIndex > 0 {
			// 在文件名前插入 wc250/
			return originalURL[:lastSlashIndex] + "/wc250" + originalURL[lastSlashIndex:]
		}
	}

	// 如果不是标准的Ozon图片URL格式，直接返回原URL
	return originalURL
}

// getSkuFromOfferIDs 通过offer_id获取sku映射
func (api *OzonAPIService) getSkuFromOfferIDs(ctx context.Context, clientID, apiKey string, offerIDs []string) (map[string]string, error) {
	skuMap := make(map[string]string)

	// 使用现有的产品信息API获取SKU
	for _, offerID := range offerIDs {
		infoRequest := OzonProductInfoRequest{
			OfferID: offerID,
		}

		jsonData, err := json.Marshal(infoRequest)
		if err != nil {
			global.GVA_LOG.Error("序列化产品信息请求失败",
				zap.String("offer_id", offerID),
				zap.Error(err))
			continue
		}

		// 发送产品信息请求
		req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v1/product/info/description", bytes.NewBuffer(jsonData))
		if err != nil {
			global.GVA_LOG.Error("创建产品信息请求失败",
				zap.String("offer_id", offerID),
				zap.Error(err))
			continue
		}

		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Client-Id", clientID)
		req.Header.Set("Api-Key", apiKey)

		resp, err := api.client.Do(req)
		if err != nil {
			global.GVA_LOG.Error("发送产品信息请求失败",
				zap.String("offer_id", offerID),
				zap.Error(err))
			continue
		}

		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			global.GVA_LOG.Error("读取产品信息响应失败",
				zap.String("offer_id", offerID),
				zap.Error(err))
			continue
		}

		if resp.StatusCode != http.StatusOK {
			global.GVA_LOG.Error("获取产品信息失败",
				zap.String("offer_id", offerID),
				zap.Int("状态码", resp.StatusCode),
				zap.String("响应", string(body)))
			continue
		}

		var infoResp OzonProductInfoResponse
		err = json.Unmarshal(body, &infoResp)
		if err != nil {
			global.GVA_LOG.Error("解析产品信息响应失败",
				zap.String("offer_id", offerID),
				zap.Error(err),
				zap.String("响应", string(body)))
			continue
		}

		// 将SKU转换为字符串
		skuStr := fmt.Sprintf("%d", infoResp.Result.SKU)
		skuMap[offerID] = skuStr

		global.GVA_LOG.Info("成功获取产品SKU",
			zap.String("offer_id", offerID),
			zap.String("sku", skuStr))
	}

	return skuMap, nil
}

// OzonWarehouse Ozon仓库信息
type OzonWarehouse struct {
	ID                     int64                `json:"warehouse_id"` // 修改字段名
	Name                   string               `json:"name"`
	IsRFBS                 bool                 `json:"is_rfbs"`
	HasEntrustedAcceptance bool                 `json:"has_entrusted_acceptance"`
	CanPrintActAcceptance  bool                 `json:"can_print_act_acceptance"`
	Status                 string               `json:"status"`
	WorkingHours           []OzonWorkingHour    `json:"working_hours"`
	Address                OzonWarehouseAddress `json:"address"`
}

// OzonWorkingHour 工作时间
type OzonWorkingHour struct {
	DayFrom  int    `json:"day_from"`
	DayTo    int    `json:"day_to"`
	TimeFrom string `json:"time_from"`
	TimeTo   string `json:"time_to"`
}

// OzonWarehouseAddress 仓库地址
type OzonWarehouseAddress struct {
	City         string `json:"city"`
	Region       string `json:"region"`
	AddressLine1 string `json:"address_line1"`
	AddressLine2 string `json:"address_line2"`
	PostalCode   string `json:"postal_code"`
	Country      string `json:"country"`
}

// OzonWarehouseListResponse 仓库列表响应
type OzonWarehouseListResponse struct {
	Result []OzonWarehouse `json:"result"`
}

// OzonDeliveryMethod 配送方式信息
type OzonDeliveryMethod struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	CompanyName string `json:"company_name"`
	Status      string `json:"status"`
	WarehouseID int64  `json:"warehouse_id"`
	TemplateID  int64  `json:"template_id"`
	CutoffTime  string `json:"cutoff_time"`
	MinDays     int    `json:"min_days"`
	MaxDays     int    `json:"max_days"`
	WorkingDays []int  `json:"working_days"`
	IsActive    bool   `json:"is_active"`
}

// OzonDeliveryMethodListResponse 配送方式列表响应
type OzonDeliveryMethodListResponse struct {
	Result []OzonDeliveryMethod `json:"result"`
}

// GetWarehouseList 获取仓库列表
func (api *OzonAPIService) GetWarehouseList(ctx context.Context, clientID, apiKey string) ([]OzonWarehouse, error) {
	url := fmt.Sprintf("%s/v1/warehouse/list", api.baseURL)

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer([]byte("{}")))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 添加调试日志
	global.GVA_LOG.Info("仓库列表API响应", zap.String("response", string(body)))

	var response OzonWarehouseListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 添加调试日志
	global.GVA_LOG.Info("解析后的仓库数据", zap.Int("count", len(response.Result)))
	for i, warehouse := range response.Result {
		global.GVA_LOG.Info("仓库信息",
			zap.Int("index", i),
			zap.Int64("id", warehouse.ID),
			zap.String("name", warehouse.Name),
			zap.String("status", warehouse.Status))
	}

	return response.Result, nil
}

// GetDeliveryMethodList 获取配送方式列表
func (api *OzonAPIService) GetDeliveryMethodList(ctx context.Context, clientID, apiKey string, warehouseID int64) ([]OzonDeliveryMethod, error) {
	// 首先尝试原始API端点
	result, err := api.tryGetDeliveryMethods(ctx, clientID, apiKey, warehouseID, "/v1/delivery-method/list")
	if err == nil && len(result) > 0 {
		return result, nil
	}

	// 如果第一个端点失败或返回空，尝试其他可能的端点
	endpoints := []string{
		"/v2/delivery-method/list",
		"/v1/delivery/method/list",
		"/v1/logistics/delivery-method/list",
	}

	for _, endpoint := range endpoints {
		global.GVA_LOG.Info("尝试备用配送方式API端点",
			zap.Int64("warehouseID", warehouseID),
			zap.String("endpoint", endpoint))

		result, err := api.tryGetDeliveryMethods(ctx, clientID, apiKey, warehouseID, endpoint)
		if err == nil && len(result) > 0 {
			return result, nil
		}
	}

	// 如果所有端点都失败，返回空结果
	global.GVA_LOG.Warn("所有配送方式API端点都返回空结果",
		zap.Int64("warehouseID", warehouseID))
	return []OzonDeliveryMethod{}, nil
}

// tryGetDeliveryMethods 尝试从指定端点获取配送方式
func (api *OzonAPIService) tryGetDeliveryMethods(ctx context.Context, clientID, apiKey string, warehouseID int64, endpoint string) ([]OzonDeliveryMethod, error) {
	url := fmt.Sprintf("%s%s", api.baseURL, endpoint)

	// 尝试不同的请求体格式
	requestBody := map[string]interface{}{
		"warehouse_id": warehouseID,
		"limit":        100, // 添加限制参数
		"offset":       0,   // 添加偏移参数
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)

	resp, err := api.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 添加调试日志
	global.GVA_LOG.Info("配送方式API响应",
		zap.Int64("warehouseID", warehouseID),
		zap.String("endpoint", endpoint),
		zap.String("response", string(body)))

	var response OzonDeliveryMethodListResponse
	if err := json.Unmarshal(body, &response); err != nil {
		// 如果解析失败，尝试解析错误响应
		var errorResp map[string]interface{}
		if jsonErr := json.Unmarshal(body, &errorResp); jsonErr == nil {
			global.GVA_LOG.Error("配送方式API返回错误",
				zap.Int64("warehouseID", warehouseID),
				zap.String("endpoint", endpoint),
				zap.Any("error", errorResp))
		}
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	// 添加调试日志
	global.GVA_LOG.Info("解析后的配送方式数据",
		zap.Int64("warehouseID", warehouseID),
		zap.String("endpoint", endpoint),
		zap.Int("count", len(response.Result)))
	for i, method := range response.Result {
		global.GVA_LOG.Info("配送方式信息",
			zap.Int("index", i),
			zap.Int64("id", method.ID),
			zap.String("name", method.Name),
			zap.String("company", method.CompanyName),
			zap.String("status", method.Status),
			zap.Bool("isActive", method.IsActive))
	}

	return response.Result, nil
}
