package order

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProfitService struct {
	exchangeRateService *ExchangeRateService
}

// NewProfitService 创建利润服务实例
func NewProfitService() *ProfitService {
	return &ProfitService{
		exchangeRateService: NewExchangeRateService(),
	}
}

// GetProfitList 获取利润统计列表 - 从利润表获取数据
func (profitService *ProfitService) GetProfitList(info orderReq.OrderProfitSearch) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 从利润表获取数据
	db := global.GVA_DB.Model(&order.OrderProfit{})

	// 构建查询条件
	if info.StartDate != nil && info.EndDate != nil {
		db = db.Where("order_date BETWEEN ? AND ?", info.StartDate, info.EndDate)
	} else {
		// 如果没有提供日期范围，默认查询最近30天的数据
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		db = db.Where("order_date BETWEEN ? AND ?", startDate, endDate)
	}
	if info.ShopName != nil && *info.ShopName != "" {
		db = db.Where("shop_name LIKE ?", "%"+*info.ShopName+"%")
	}
	if info.PostingNumber != nil && *info.PostingNumber != "" {
		db = db.Where("posting_number LIKE ?", "%"+*info.PostingNumber+"%")
	}
	if info.OrderNumber != nil && *info.OrderNumber != "" {
		db = db.Where("order_number LIKE ?", "%"+*info.OrderNumber+"%")
	}
	if info.Status != nil && *info.Status != "" {
		db = db.Where("status = ?", *info.Status)
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取分页数据
	var profits []order.OrderProfit
	err = db.Limit(limit).Offset(offset).Order("order_date DESC").Find(&profits).Error
	if err != nil {
		return
	}

	// 统一转换为人民币计算利润
	for i := range profits {
		if profits[i].Currency != nil && profits[i].NetProfit != nil {
			if *profits[i].Currency == "USD" {
				// 美元订单只转换售价，其他费用保持原值
				var convertedSalePrice float64
				if profits[i].SalePrice != nil {
					convertedSalePrice = *profits[i].SalePrice * 7.0
					profits[i].SalePrice = &convertedSalePrice
				}

				// 重新计算净利润：转换后的售价 - 原始成本费用
				var costPrice, shippingCost, commission, otherCosts float64
				if profits[i].CostPrice != nil {
					costPrice = *profits[i].CostPrice
				}
				if profits[i].ShippingCost != nil {
					shippingCost = *profits[i].ShippingCost
				}
				if profits[i].Commission != nil {
					commission = *profits[i].Commission
				}
				if profits[i].OtherCosts != nil {
					otherCosts = *profits[i].OtherCosts
				}

				// 重新计算净利润
				newNetProfit := convertedSalePrice - (costPrice + shippingCost + commission + otherCosts)
				profits[i].NetProfit = &newNetProfit
			}

		}
	}

	return profits, total, nil
}

// GetProfitSummary 获取利润汇总统计 - 使用并发处理提高性能
func (profitService *ProfitService) GetProfitSummary(info orderReq.ProfitAnalysisRequest) (summary order.ProfitSummary, err error) {
	// 使用GetProfitList获取所有数据，然后汇总
	searchInfo := orderReq.OrderProfitSearch{
		StartDate: info.StartDate,
		EndDate:   info.EndDate,
		ShopName:  info.ShopName,
		PageInfo: request.PageInfo{
			Page:     1,
			PageSize: 10000, // 获取大量数据用于汇总
		},
	}

	profits, _, err := profitService.GetProfitList(searchInfo)
	if err != nil {
		return summary, err
	}

	profitList, ok := profits.([]order.OrderProfit)
	if !ok {
		return summary, errors.New("数据类型转换失败")
	}

	// 如果数据量小，直接处理
	if len(profitList) <= 100 {
		return profitService.calculateSummarySequential(profitList), nil
	}

	// 数据量大时使用并发处理
	return profitService.calculateSummaryConcurrent(profitList), nil
}

// calculateSummarySequential 顺序计算汇总（小数据量）
func (profitService *ProfitService) calculateSummarySequential(profitList []order.OrderProfit) order.ProfitSummary {
	var totalOrders int
	var totalSales, totalCosts, totalNetProfit float64
	var profitMargins []float64

	for _, profit := range profitList {
		totalOrders++

		// 统一按人民币计算，美元订单只转换售价
		if profit.SalePrice != nil {
			if profit.Currency != nil && *profit.Currency == "USD" {
				// 美元售价转换为人民币
				totalSales += *profit.SalePrice * 7.0
			} else {
				totalSales += *profit.SalePrice
			}
		}

		// 计算总成本（只包含成本价，运费佣金等由Ozon扣除）
		// 成本价不转换，保持原值
		if profit.CostPrice != nil && profit.Quantity != nil {
			totalCosts += *profit.CostPrice * float64(*profit.Quantity)
		}

		if profit.NetProfit != nil {
			// 净利润已在上面重新计算过，直接累计
			totalNetProfit += *profit.NetProfit
		}

		if profit.ProfitMargin != nil {
			profitMargins = append(profitMargins, *profit.ProfitMargin)
		}
	}

	// 计算平均利润率
	var avgProfitMargin float64
	if len(profitMargins) > 0 {
		var sum float64
		for _, margin := range profitMargins {
			sum += margin
		}
		avgProfitMargin = sum / float64(len(profitMargins))
	}

	return order.ProfitSummary{
		TotalOrders:      totalOrders,
		TotalSales:       totalSales,
		TotalCosts:       totalCosts,
		TotalGrossProfit: totalSales - totalCosts, // 毛利润 = 销售额 - 成本
		TotalNetProfit:   totalNetProfit,          // 统一人民币利润
		AvgProfitMargin:  avgProfitMargin,
	}
}

// calculateSummaryConcurrent 并发计算汇总（大数据量）
func (profitService *ProfitService) calculateSummaryConcurrent(profitList []order.OrderProfit) order.ProfitSummary {
	// 分批处理数据
	batchSize := 500 // 每批处理500条记录
	numBatches := (len(profitList) + batchSize - 1) / batchSize

	// 创建通道接收各批次的计算结果
	type batchResult struct {
		orders    int
		sales     float64
		costs     float64
		netProfit float64
		margins   []float64
	}

	resultChan := make(chan batchResult, numBatches)

	// 启动多个 goroutine 并发处理各批次
	for i := 0; i < numBatches; i++ {
		start := i * batchSize
		end := start + batchSize
		if end > len(profitList) {
			end = len(profitList)
		}

		batch := profitList[start:end]

		go func(batch []order.OrderProfit) {
			var batchOrders int
			var batchSales, batchCosts, batchNetProfit float64
			var batchMargins []float64

			for _, profit := range batch {
				batchOrders++

				// 统一按人民币计算，美元订单只转换售价
				if profit.SalePrice != nil {
					if profit.Currency != nil && *profit.Currency == "USD" {
						// 美元售价转换为人民币
						batchSales += *profit.SalePrice * 7.0
					} else {
						batchSales += *profit.SalePrice
					}
				}

				// 计算总成本（只包含成本价，运费佣金等由Ozon扣除）
				// 成本价不转换，保持原值
				if profit.CostPrice != nil && profit.Quantity != nil {
					batchCosts += *profit.CostPrice * float64(*profit.Quantity)
				}

				if profit.NetProfit != nil {
					// 净利润已在上面重新计算过，直接累计
					batchNetProfit += *profit.NetProfit
				}

				if profit.ProfitMargin != nil {
					batchMargins = append(batchMargins, *profit.ProfitMargin)
				}
			}

			resultChan <- batchResult{
				orders:    batchOrders,
				sales:     batchSales,
				costs:     batchCosts,
				netProfit: batchNetProfit,
				margins:   batchMargins,
			}
		}(batch)
	}

	// 收集所有批次的结果
	var totalOrders int
	var totalSales, totalCosts, totalNetProfit float64
	var allMargins []float64

	for i := 0; i < numBatches; i++ {
		result := <-resultChan
		totalOrders += result.orders
		totalSales += result.sales
		totalCosts += result.costs
		totalNetProfit += result.netProfit
		allMargins = append(allMargins, result.margins...)
	}

	// 计算平均利润率
	var avgProfitMargin float64
	if len(allMargins) > 0 {
		var sum float64
		for _, margin := range allMargins {
			sum += margin
		}
		avgProfitMargin = sum / float64(len(allMargins))
	}

	return order.ProfitSummary{
		TotalOrders:      totalOrders,
		TotalSales:       totalSales,
		TotalCosts:       totalCosts,
		TotalGrossProfit: totalSales - totalCosts, // 毛利润 = 销售额 - 成本
		TotalNetProfit:   totalNetProfit,          // 统一人民币利润
		AvgProfitMargin:  avgProfitMargin,
	}
}

// GetProfitByShop 按店铺统计利润
func (profitService *ProfitService) GetProfitByShop(info orderReq.ProfitAnalysisRequest) (list []order.ProfitByShop, err error) {
	db := global.GVA_DB.Model(&order.OrderProfit{})

	// 构建查询条件
	if info.StartDate != nil && info.EndDate != nil {
		db = db.Where("order_date BETWEEN ? AND ?", info.StartDate, info.EndDate)
	}

	err = db.Select(`
		shop_name,
		currency,
		COUNT(*) as order_count,
		COALESCE(SUM(sale_price * quantity), 0) as total_sales,
		COALESCE(SUM(net_profit), 0) as total_profit,
		CASE
			WHEN SUM(sale_price * quantity) > 0
			THEN (SUM(net_profit) / SUM(sale_price * quantity)) * 100
			ELSE 0
		END as profit_rate
	`).Group("shop_name, currency").Order("total_profit DESC").Scan(&list).Error

	return list, err
}

// GetProfitSummaryByCurrency 按货币分组获取利润汇总统计
func (profitService *ProfitService) GetProfitSummaryByCurrency(info orderReq.ProfitAnalysisRequest) (summaries []order.ProfitSummaryByCurrency, err error) {
	db := global.GVA_DB.Model(&order.OrderProfit{})

	// 构建查询条件
	if info.StartDate != nil && info.EndDate != nil {
		db = db.Where("order_date BETWEEN ? AND ?", info.StartDate, info.EndDate)
	}
	if info.ShopName != nil && *info.ShopName != "" {
		db = db.Where("shop_name LIKE ?", "%"+*info.ShopName+"%")
	}

	var results []struct {
		Currency        string  `json:"currency"`
		TotalOrders     int     `json:"totalOrders"`
		TotalSales      float64 `json:"totalSales"`
		TotalCosts      float64 `json:"totalCosts"`
		TotalNetProfit  float64 `json:"totalNetProfit"`
		AvgProfitMargin float64 `json:"avgProfitMargin"`
	}

	// 统一按人民币计算，美元订单只转换售价
	err = db.Select(`
		'CNY' as currency,
		COUNT(*) as total_orders,
		COALESCE(SUM(
			CASE
				WHEN currency = 'USD' THEN sale_price * quantity * 7.0
				ELSE sale_price * quantity
			END
		), 0) as total_sales,
		COALESCE(SUM(cost_price * quantity), 0) as total_costs,
		COALESCE(SUM(
			CASE
				WHEN currency = 'USD' THEN (sale_price * quantity * 7.0) - (cost_price * quantity + shipping_cost + commission + other_costs)
				ELSE net_profit
			END
		), 0) as total_net_profit,
		COALESCE(AVG(profit_margin), 0) as avg_profit_margin
	`).Scan(&results).Error

	if err != nil {
		return nil, err
	}

	for _, result := range results {
		summary := order.ProfitSummaryByCurrency{
			Currency:         result.Currency,
			TotalOrders:      result.TotalOrders,
			TotalSales:       result.TotalSales,
			TotalCosts:       result.TotalCosts,
			TotalGrossProfit: result.TotalSales - result.TotalCosts,
			TotalNetProfit:   result.TotalNetProfit,
			AvgProfitMargin:  result.AvgProfitMargin,
		}
		summaries = append(summaries, summary)
	}

	return summaries, nil
}

// CalculateOrderProfit 计算订单利润
func (profitService *ProfitService) CalculateOrderProfit(info orderReq.ProfitCalculateRequest) error {
	if info.PostingNumber == nil || *info.PostingNumber == "" {
		return errors.New("货件号不能为空")
	}

	// 查找对应的订单
	var existingOrder order.Order
	err := global.GVA_DB.Where("posting_number = ?", *info.PostingNumber).First(&existingOrder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("订单不存在")
		}
		return err
	}

	// 从订单JSON数据中提取产品信息
	// 这里需要根据实际的JSON结构来解析
	// 暂时使用示例数据
	salePrice := 100.0 // 从订单数据中获取
	quantity := 1      // 从订单数据中获取

	costPrice := 0.0
	commission := 0.0
	shippingCost := 0.0
	otherCosts := 0.0

	if info.CostPrice != nil {
		costPrice = *info.CostPrice
	}
	if info.Commission != nil {
		commission = *info.Commission
	}
	if info.ShippingCost != nil {
		shippingCost = *info.ShippingCost
	}
	if info.OtherCosts != nil {
		otherCosts = *info.OtherCosts
	}

	// 计算利润：售价 - (成本价 + 运费 + 佣金 + 其他费用)
	// 保留其他费用字段，用于额外成本计算
	totalCosts := costPrice + shippingCost + commission + otherCosts
	grossProfit := salePrice - costPrice // 毛利润 = 售价 - 成本价
	netProfit := salePrice - totalCosts  // 净利润 = 售价 - (成本价 + 运费 + 佣金)
	profitMargin := 0.0
	if salePrice > 0 {
		profitMargin = (netProfit / salePrice) * 100
	}

	// 创建或更新利润记录
	profit := order.OrderProfit{
		PostingNumber: info.PostingNumber,
		OrderNumber:   existingOrder.OrderNumber,
		ShopName:      existingOrder.ShopName,
		ProductName:   &[]string{"示例产品"}[0],  // 从订单数据中获取
		SKU:           &[]string{"示例SKU"}[0], // 从订单数据中获取
		Quantity:      &quantity,
		SalePrice:     &salePrice,
		CostPrice:     &costPrice,
		Commission:    &commission,
		ShippingCost:  &shippingCost,
		OtherCosts:    &otherCosts,
		GrossProfit:   &grossProfit,
		NetProfit:     &netProfit,
		ProfitMargin:  &profitMargin,
		// 使用订单真实下单时间而不是系统同步时间
		OrderDate: existingOrder.InProcessAt,
		Status:    existingOrder.Status,
	}

	// 检查是否已存在利润记录
	var existingProfit order.OrderProfit
	err = global.GVA_DB.Where("posting_number = ?", *info.PostingNumber).First(&existingProfit).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 创建新记录
			err = global.GVA_DB.Create(&profit).Error
		} else {
			return err
		}
	} else {
		// 更新现有记录
		err = global.GVA_DB.Model(&existingProfit).Updates(&profit).Error
	}

	if err != nil {
		global.GVA_LOG.Error("保存利润数据失败", zap.Error(err))
		return err
	}

	global.GVA_LOG.Info("利润计算完成",
		zap.String("posting_number", *info.PostingNumber),
		zap.Float64("net_profit", netProfit),
		zap.Float64("profit_margin", profitMargin))

	return nil
}

// GetMonthlyProfitList 获取月度利润统计列表
func (profitService *ProfitService) GetMonthlyProfitList(info orderReq.MonthlyProfitSearch) (list []order.ProfitByMonth, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	// 从利润表获取月度统计数据 - 按货币分组
	var results []struct {
		Month           string  `json:"month"`
		Currency        string  `json:"currency"`
		OrderCount      int     `json:"orderCount"`
		TotalQuantity   int     `json:"totalQuantity"`
		TotalSales      float64 `json:"totalSales"`
		TotalCosts      float64 `json:"totalCosts"`
		TotalShipping   float64 `json:"totalShipping"`
		TotalCommission float64 `json:"totalCommission"`
		TotalProfit     float64 `json:"totalProfit"`
	}

	// 构建WHERE条件
	whereConditions := []string{"deleted_at IS NULL"}
	var args []interface{}

	if info.Year != nil {
		whereConditions = append(whereConditions, "YEAR(order_date) = ?")
		args = append(args, *info.Year)
	}

	if info.Month != nil {
		whereConditions = append(whereConditions, "MONTH(order_date) = ?")
		args = append(args, *info.Month)
	}

	if info.ShopName != nil && *info.ShopName != "" {
		whereConditions = append(whereConditions, "shop_name LIKE ?")
		args = append(args, "%"+*info.ShopName+"%")
	}

	whereClause := strings.Join(whereConditions, " AND ")

	// 查询月度统计数据 - 统一按人民币计算，美元只转换售价
	query := fmt.Sprintf(`
		SELECT
			DATE_FORMAT(order_date, '%%Y-%%m') as month,
			'CNY' as currency,
			COUNT(*) as order_count,
			COALESCE(SUM(quantity), 0) as total_quantity,
			COALESCE(SUM(
				CASE
					WHEN currency = 'USD' THEN sale_price * quantity * 7.0
					ELSE sale_price * quantity
				END
			), 0) as total_sales,
			COALESCE(SUM(cost_price * quantity), 0) as total_costs,
			COALESCE(SUM(shipping_cost), 0) as total_shipping,
			COALESCE(SUM(commission), 0) as total_commission,
			COALESCE(SUM(
				CASE
					WHEN currency = 'USD' THEN (sale_price * quantity * 7.0) - (cost_price * quantity + shipping_cost + commission + other_costs)
					ELSE net_profit
				END
			), 0) as total_profit
		FROM order_profits
		WHERE %s
		GROUP BY DATE_FORMAT(order_date, '%%Y-%%m')
		ORDER BY month DESC
	`, whereClause)

	err = global.GVA_DB.Raw(query, args...).Scan(&results).Error

	if err != nil {
		return nil, 0, err
	}

	// 获取总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT DATE_FORMAT(order_date, '%%Y-%%m'))
		FROM order_profits
		WHERE %s
	`, whereClause)

	err = global.GVA_DB.Raw(countQuery, args...).Count(&total).Error

	if err != nil {
		return nil, 0, err
	}

	// 按月份合并不同货币的数据
	monthlyData := make(map[string]*order.ProfitByMonth)

	for _, result := range results {
		month := result.Month

		// 如果该月份还没有记录，创建新记录
		if monthlyData[month] == nil {
			// 获取该月的广告费用
			var advertising order.MonthlyAdvertising
			advertisingCost := 0.0
			err := global.GVA_DB.Where("month = ?", month).First(&advertising).Error
			if err == nil {
				if advertising.AdvertisingCost != nil {
					advertisingCost = *advertising.AdvertisingCost
				}
			} else if !errors.Is(err, gorm.ErrRecordNotFound) {
				// 只有在非"记录不存在"的错误时才记录日志
				global.GVA_LOG.Warn("查询月度广告费用失败", zap.String("month", month), zap.Error(err))
			}

			monthlyData[month] = &order.ProfitByMonth{
				Month:           month,
				Currency:        "CNY", // 统一使用人民币
				OrderCount:      0,
				TotalQuantity:   0,
				TotalSales:      0,
				TotalCosts:      0,
				TotalShipping:   0,
				TotalCommission: 0,
				AdvertisingCost: advertisingCost,
				TotalProfit:     0,
				ProfitRate:      0,
			}
		}

		// 累加订单数量和其他统计数据（已按人民币统一计算）
		monthlyData[month].OrderCount += result.OrderCount
		monthlyData[month].TotalQuantity += result.TotalQuantity
		monthlyData[month].TotalSales += result.TotalSales
		monthlyData[month].TotalCosts += result.TotalCosts
		monthlyData[month].TotalShipping += result.TotalShipping
		monthlyData[month].TotalCommission += result.TotalCommission
		monthlyData[month].TotalProfit += result.TotalProfit
	}

	// 转换为列表并计算最终数据
	for _, monthData := range monthlyData {
		// 扣除广告费用（从人民币利润中扣除）
		monthData.TotalProfit -= monthData.AdvertisingCost

		// 计算利润率：净利润 / 总销售额 * 100
		if monthData.TotalSales > 0 {
			monthData.ProfitRate = (monthData.TotalProfit / monthData.TotalSales) * 100
		}

		list = append(list, *monthData)
	}

	// 按月份排序（降序）
	for i := 0; i < len(list)-1; i++ {
		for j := i + 1; j < len(list); j++ {
			if list[i].Month < list[j].Month {
				list[i], list[j] = list[j], list[i]
			}
		}
	}

	// 应用分页
	start := offset
	end := offset + limit
	if start > len(list) {
		start = len(list)
	}
	if end > len(list) {
		end = len(list)
	}

	if start < end {
		list = list[start:end]
	} else {
		list = []order.ProfitByMonth{}
	}

	total = int64(len(monthlyData))

	return list, total, nil
}

// SetMonthlyAdvertising 设置月度广告费用
func (profitService *ProfitService) SetMonthlyAdvertising(req orderReq.MonthlyAdvertisingRequest) error {
	var advertising order.MonthlyAdvertising

	// 查找是否已存在该月的记录
	err := global.GVA_DB.Where("month = ?", req.Month).First(&advertising).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 创建新记录
		advertising = order.MonthlyAdvertising{
			Month:           req.Month,
			AdvertisingCost: req.AdvertisingCost,
			Notes:           req.Notes,
		}
		return global.GVA_DB.Create(&advertising).Error
	} else {
		// 更新现有记录
		return global.GVA_DB.Model(&advertising).Updates(map[string]interface{}{
			"advertising_cost": req.AdvertisingCost,
			"notes":            req.Notes,
		}).Error
	}
}

// GetMonthlyAdvertising 获取月度广告费用
func (profitService *ProfitService) GetMonthlyAdvertising(month string) (advertising order.MonthlyAdvertising, err error) {
	err = global.GVA_DB.Where("month = ?", month).First(&advertising).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 如果没有记录，返回默认值
		advertising = order.MonthlyAdvertising{
			Month:           month,
			AdvertisingCost: new(float64), // 指向0的指针
			Notes:           new(string),  // 指向空字符串的指针
		}
		*advertising.AdvertisingCost = 0
		*advertising.Notes = ""
		err = nil
	}
	return advertising, err
}

// DeleteAllProfitData 删除所有利润数据
func (profitService *ProfitService) DeleteAllProfitData() error {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除所有利润记录
	if err := tx.Exec("DELETE FROM order_profits").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除利润记录失败: %v", err)
	}

	// 删除所有月度广告费用记录
	if err := tx.Exec("DELETE FROM monthly_advertising").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除月度广告费用记录失败: %v", err)
	}

	// 提交事务
	return tx.Commit().Error
}

// RefreshProfitData 刷新利润统计数据 - 使用并发处理提高性能
func (profitService *ProfitService) RefreshProfitData() (count int64, err error) {
	// 获取所有订单
	var orders []order.Order
	err = global.GVA_DB.Find(&orders).Error
	if err != nil {
		return 0, err
	}

	global.GVA_LOG.Info("开始刷新利润数据", zap.Int("总订单数", len(orders)))

	// 如果订单数量少，使用顺序处理
	if len(orders) <= 100 {
		return profitService.refreshProfitDataSequential(orders)
	}

	// 订单数量多，使用并发处理
	return profitService.refreshProfitDataConcurrent(orders)
}

// refreshProfitDataSequential 顺序处理利润数据刷新（小数据量）
func (profitService *ProfitService) refreshProfitDataSequential(orders []order.Order) (count int64, err error) {
	count = 0
	for _, ord := range orders {
		// 检查是否已存在利润记录
		var existingProfit order.OrderProfit
		err = global.GVA_DB.Where("posting_number = ?", ord.PostingNumber).First(&existingProfit).Error

		// 如果已存在则跳过
		if err == nil {
			continue
		}

		// 如果不是记录不存在的错误，则返回错误
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return count, err
		}

		// 生成利润数据
		profitData, err := profitService.generateProfitDataFromOrder(ord)
		if err != nil {
			global.GVA_LOG.Error("生成利润数据失败", zap.String("posting_number", *ord.PostingNumber), zap.Error(err))
			continue
		}

		// 保存利润数据
		err = global.GVA_DB.Create(&profitData).Error
		if err != nil {
			global.GVA_LOG.Error("保存利润数据失败", zap.String("posting_number", *ord.PostingNumber), zap.Error(err))
			continue
		}

		count++
	}

	return count, nil
}

// refreshProfitDataConcurrent 并发处理利润数据刷新（大数据量）
func (profitService *ProfitService) refreshProfitDataConcurrent(orders []order.Order) (count int64, err error) {
	// 设置并发参数
	maxConcurrency := 20 // 最大并发数
	batchSize := 50      // 每批处理50个订单

	// 创建信号量控制并发数
	semaphore := make(chan struct{}, maxConcurrency)

	// 使用 WaitGroup 等待所有协程完成
	var wg sync.WaitGroup

	// 使用原子操作统计成功处理的订单数
	var successCount int64
	var errorCount int64

	// 分批处理订单
	for i := 0; i < len(orders); i += batchSize {
		end := i + batchSize
		if end > len(orders) {
			end = len(orders)
		}

		batch := orders[i:end]
		wg.Add(1)

		go func(orderBatch []order.Order, batchIndex int) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			global.GVA_LOG.Info("开始处理订单批次",
				zap.Int("批次索引", batchIndex),
				zap.Int("批次大小", len(orderBatch)))

			batchSuccessCount := 0
			batchErrorCount := 0

			for _, ord := range orderBatch {
				// 检查是否已存在利润记录
				var existingProfit order.OrderProfit
				err := global.GVA_DB.Where("posting_number = ?", ord.PostingNumber).First(&existingProfit).Error

				// 如果已存在则跳过
				if err == nil {
					continue
				}

				// 如果不是记录不存在的错误，记录错误并继续
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					global.GVA_LOG.Error("查询利润记录失败",
						zap.String("posting_number", *ord.PostingNumber),
						zap.Error(err))
					batchErrorCount++
					continue
				}

				// 生成利润数据
				profitData, err := profitService.generateProfitDataFromOrder(ord)
				if err != nil {
					global.GVA_LOG.Error("生成利润数据失败",
						zap.String("posting_number", *ord.PostingNumber),
						zap.Error(err))
					batchErrorCount++
					continue
				}

				// 保存利润数据
				err = global.GVA_DB.Create(&profitData).Error
				if err != nil {
					global.GVA_LOG.Error("保存利润数据失败",
						zap.String("posting_number", *ord.PostingNumber),
						zap.Error(err))
					batchErrorCount++
					continue
				}

				batchSuccessCount++
			}

			// 原子操作更新计数器
			atomic.AddInt64(&successCount, int64(batchSuccessCount))
			atomic.AddInt64(&errorCount, int64(batchErrorCount))

			global.GVA_LOG.Info("批次处理完成",
				zap.Int("批次索引", batchIndex),
				zap.Int("成功处理", batchSuccessCount),
				zap.Int("处理失败", batchErrorCount))

		}(batch, i/batchSize)
	}

	// 等待所有协程完成
	wg.Wait()

	global.GVA_LOG.Info("利润数据刷新完成",
		zap.Int64("成功处理", successCount),
		zap.Int64("处理失败", errorCount),
		zap.Int("总订单数", len(orders)))

	return successCount, nil
}

// generateProfitDataFromOrder 从订单生成利润数据
func (profitService *ProfitService) generateProfitDataFromOrder(ord order.Order) (order.OrderProfit, error) {
	var profitData order.OrderProfit

	// 基础订单信息
	profitData.PostingNumber = ord.PostingNumber
	profitData.OrderNumber = ord.OrderNumber
	profitData.ShopName = ord.ShopName
	// 使用订单真实下单时间而不是系统同步时间
	// InProcessAt 是从Ozon API获取的真实下单时间
	profitData.OrderDate = ord.InProcessAt
	profitData.Status = ord.Status

	// 获取店铺的结算货币信息
	var shopCurrency string = "CNY" // 默认人民币
	if ord.ShopName != nil {
		var shop struct {
			Currency *string `json:"currency"`
		}
		err := global.GVA_DB.Table("ozon_shop").
			Select("currency").
			Where("name = ?", *ord.ShopName).
			First(&shop).Error

		if err == nil && shop.Currency != nil && *shop.Currency != "" {
			shopCurrency = *shop.Currency
		}
	}

	// 从ozone_order_detail表获取更完整的订单信息
	var ozoneOrder struct {
		PostingNumber string          `json:"posting_number"`
		FinancialData json.RawMessage `json:"financial_data"`
		Products      json.RawMessage `json:"products"`
	}

	var totalSalePrice float64
	var totalCostPrice float64
	var totalCommission float64
	var totalShippingCost float64
	var totalWeight float64
	var productName string
	var sku string
	var quantity int = 1

	// 先尝试从ozone_order_detail获取数据，如果失败则使用订单表的JsonData
	err := global.GVA_DB.Table("ozone_order_detail").
		Select("posting_number, financial_data, products").
		Where("posting_number = ?", *ord.PostingNumber).
		First(&ozoneOrder).Error

	if err == nil && ozoneOrder.Products != nil {
		// 解析产品信息
		var products []map[string]interface{}
		if err := json.Unmarshal(ozoneOrder.Products, &products); err == nil {
			for _, product := range products {
				// 获取产品基本信息
				if name, ok := product["name"].(string); ok {
					productName = name
				}
				if offerID, ok := product["sku"].(string); ok {
					sku = offerID
				}
				if qty, ok := product["quantity"].(float64); ok {
					quantity = int(qty)
				}

				// 获取销售价格（单价 * 数量）
				if priceStr, ok := product["price"].(string); ok {
					if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
						totalSalePrice += price * float64(quantity)
					}
				}

				// 从产品表获取成本价格和佣金比率
				var productInfo struct {
					CostPrice      *float64 `json:"costPrice"`
					CommissionRate *float64 `json:"commissionRate"`
					ActualWeight   *float64 `json:"actualWeight"`
					OzonWeight     *float64 `json:"ozonWeight"`
				}

				if sku != "" {
					err := global.GVA_DB.Table("products").
						Select("cost_price, commission_rate, actual_weight, ozon_weight").
						Where("sku = ?", sku).
						First(&productInfo).Error

					if err == nil {
						// 计算成本价格
						if productInfo.CostPrice != nil {
							totalCostPrice += *productInfo.CostPrice * float64(quantity)
						}

						// 计算佣金 - 优先使用物流佣金比率，其次使用产品佣金比率
						commissionRate := profitService.getCommissionRate(ord.ShopName, productInfo.CommissionRate)
						totalCommission += totalSalePrice * commissionRate

						// 计算重量（优先使用实际重量）
						var weight float64
						if productInfo.ActualWeight != nil && *productInfo.ActualWeight > 0 {
							weight = *productInfo.ActualWeight
						} else if productInfo.OzonWeight != nil && *productInfo.OzonWeight > 0 {
							weight = *productInfo.OzonWeight
						} else {
							// 如果没有重量数据，使用默认重量
							weight = 0.5 // 默认500克
						}
						totalWeight += weight * float64(quantity)
					}
				}
			}
		}

		// 从financial_data获取销售价格
		if ozoneOrder.FinancialData != nil {
			var financialData map[string]interface{}
			if err := json.Unmarshal(ozoneOrder.FinancialData, &financialData); err == nil {
				if postingServices, ok := financialData["posting_services"].(map[string]interface{}); ok {
					if fulfillment, ok := postingServices["marketplace_service_item_fulfillment"].(string); ok {
						if price, err := strconv.ParseFloat(fulfillment, 64); err == nil {
							totalSalePrice += price
						}
					}
				}
			}
		}
	} else {
		// 如果从ozone_order_detail获取失败，回退到使用订单表的JsonData
		if ord.JsonData != nil {
			var jsonData map[string]interface{}
			if err := json.Unmarshal(ord.JsonData, &jsonData); err == nil {
				// 获取产品信息
				if products, ok := jsonData["products"].([]interface{}); ok && len(products) > 0 {
					for _, productInterface := range products {
						if product, ok := productInterface.(map[string]interface{}); ok {
							// 获取产品基本信息
							if name, ok := product["name"].(string); ok {
								productName = name
							}
							if offerID, ok := product["offer_id"].(string); ok {
								sku = offerID
							}
							if qty, ok := product["quantity"].(float64); ok {
								quantity = int(qty)
							}

							// 获取销售价格（单价 * 数量）
							if priceStr, ok := product["price"].(string); ok {
								if price, err := strconv.ParseFloat(priceStr, 64); err == nil {
									totalSalePrice += price * float64(quantity)
								}
							}

							// 从产品表获取成本价格和佣金比率
							var productInfo struct {
								CostPrice      *float64 `json:"costPrice"`
								CommissionRate *float64 `json:"commissionRate"`
								ActualWeight   *float64 `json:"actualWeight"`
								OzonWeight     *float64 `json:"ozonWeight"`
							}

							if sku != "" {
								err := global.GVA_DB.Table("products").
									Select("cost_price, commission_rate, actual_weight, ozon_weight").
									Where("sku = ?", sku).
									First(&productInfo).Error

								if err == nil {
									// 计算成本价格
									if productInfo.CostPrice != nil {
										totalCostPrice += *productInfo.CostPrice * float64(quantity)
									}

									// 计算佣金 - 优先使用物流佣金比率，其次使用产品佣金比率
									commissionRate := profitService.getCommissionRate(ord.ShopName, productInfo.CommissionRate)
									totalCommission += totalSalePrice * commissionRate

									// 计算重量（优先使用实际重量）
									var weight float64
									if productInfo.ActualWeight != nil && *productInfo.ActualWeight > 0 {
										weight = *productInfo.ActualWeight
									} else if productInfo.OzonWeight != nil && *productInfo.OzonWeight > 0 {
										weight = *productInfo.OzonWeight
									} else {
										// 如果没有重量数据，使用默认重量
										weight = 0.5 // 默认500克
									}
									totalWeight += weight * float64(quantity)
								}
							}
						}
					}
				}
			}
		}
	}

	// 计算物流费用
	if totalWeight > 0 {
		// 使用默认物流费用计算
		shippingCost, err := profitService.calculateDefaultShippingCost(totalWeight, ord.ShopName)
		if err == nil {
			totalShippingCost = shippingCost
		}
	}

	// 按原始货币计算利润，不进行汇率转换
	// 销售价格和佣金保持店铺原始货币
	// 成本价和物流费用需要根据情况处理（通常为人民币）

	// 如果店铺货币不是人民币，需要将成本价和物流费用转换为店铺货币
	var finalCostPrice, finalShippingCost float64
	if shopCurrency != "CNY" && shopCurrency != "RMB" {
		// 将人民币成本价转换为店铺货币
		if converted, err := profitService.exchangeRateService.ConvertFromBaseCurrency(totalCostPrice, shopCurrency); err == nil {
			finalCostPrice = converted
		} else {
			global.GVA_LOG.Warn("成本价汇率转换失败，使用原值",
				zap.String("shop_currency", shopCurrency),
				zap.Float64("original_cost", totalCostPrice),
				zap.Error(err))
			finalCostPrice = totalCostPrice
		}

		// 将人民币物流费用转换为店铺货币
		if converted, err := profitService.exchangeRateService.ConvertFromBaseCurrency(totalShippingCost, shopCurrency); err == nil {
			finalShippingCost = converted
		} else {
			finalShippingCost = totalShippingCost
		}

		global.GVA_LOG.Info("成本价汇率转换完成",
			zap.String("shop_currency", shopCurrency),
			zap.Float64("original_cost_price", totalCostPrice),
			zap.Float64("converted_cost_price", finalCostPrice),
			zap.Float64("original_shipping_cost", totalShippingCost),
			zap.Float64("converted_shipping_cost", finalShippingCost))
	} else {
		// 如果是人民币（CNY或RMB），直接使用原值，无需汇率转换
		finalCostPrice = totalCostPrice
		finalShippingCost = totalShippingCost
	}

	// 计算利润（使用统一货币）
	totalCosts := finalCostPrice + totalCommission + finalShippingCost
	netProfit := totalSalePrice - totalCosts
	profitMargin := 0.0
	if totalSalePrice > 0 {
		profitMargin = (netProfit / totalSalePrice) * 100
	}

	// 设置利润数据（使用店铺原始货币）
	profitData.ProductName = &productName
	profitData.SKU = &sku
	profitData.Quantity = &quantity
	profitData.Currency = &shopCurrency
	profitData.SalePrice = &totalSalePrice
	profitData.CostPrice = &finalCostPrice
	profitData.Commission = &totalCommission
	profitData.ShippingCost = &finalShippingCost
	profitData.NetProfit = &netProfit
	profitData.ProfitMargin = &profitMargin

	return profitData, nil
}

// calculateShippingCost 计算物流费用
func (profitService *ProfitService) calculateShippingCost(tplProvider string, weight float64, shopName *string) (float64, error) {
	// 从物流表获取物流信息
	var logistics order.Logistics

	// 构建查询条件
	query := global.GVA_DB.Where("name = ? OR provider = ?", tplProvider, tplProvider)
	if shopName != nil {
		query = query.Where("shop_name = ?", *shopName)
	}

	err := query.Where("is_active = ?", true).First(&logistics).Error
	if err != nil {
		// 如果找不到对应的物流信息，返回默认费用
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0.0, nil // 或者返回一个默认的物流费用
		}
		return 0.0, err
	}

	// 计算物流费用：基础价格 + (重量 * 每公斤价格)
	var shippingCost float64

	// 基础价格
	if logistics.BasePrice != nil {
		shippingCost += *logistics.BasePrice
	}

	// 重量费用
	if logistics.PricePerKg != nil {
		shippingCost += weight * (*logistics.PricePerKg)
	}

	return shippingCost, nil
}

// calculateDefaultShippingCost 计算默认物流费用
func (profitService *ProfitService) calculateDefaultShippingCost(weight float64, shopName *string) (float64, error) {
	// 从物流表获取默认物流信息
	var logistics order.Logistics

	// 构建查询条件 - 查找该店铺的活跃物流方式
	query := global.GVA_DB.Where("is_active = ?", true)
	if shopName != nil {
		query = query.Where("shop_name = ?", *shopName)
	}

	// 按优先级排序，获取第一个可用的物流方式
	err := query.Order("base_price ASC").First(&logistics).Error
	if err != nil {
		// 如果找不到对应的物流信息，使用默认费用
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 使用默认物流费用：基础费用11元 + 重量费用30元/公斤
			return 11.0 + (weight * 30.0), nil
		}
		return 0.0, err
	}

	// 计算物流费用：基础价格 + (重量 * 每公斤价格)
	var shippingCost float64

	// 基础价格
	if logistics.BasePrice != nil {
		shippingCost += *logistics.BasePrice
	} else {
		shippingCost += 11.0 // 默认基础价格
	}

	// 重量费用
	if logistics.PricePerKg != nil {
		shippingCost += weight * (*logistics.PricePerKg)
	} else {
		shippingCost += weight * 30.0 // 默认每公斤价格
	}

	return shippingCost, nil
}

// getCommissionRate 获取佣金比率，优先使用物流的佣金比率，其次使用产品的佣金比率
func (profitService *ProfitService) getCommissionRate(shopName *string, productCommissionRate *float64) float64 {
	// 默认佣金率
	defaultRate := 0.12

	// 尝试从物流表获取佣金比率
	if shopName != nil {
		var logistics order.Logistics
		err := global.GVA_DB.Where("shop_name = ? AND is_active = ? AND commission_rate IS NOT NULL",
			*shopName, true).
			Order("sort_order ASC").
			First(&logistics).Error

		if err == nil && logistics.CommissionRate != nil {
			global.GVA_LOG.Info("使用物流佣金比率",
				zap.String("shop_name", *shopName),
				zap.Float64("logistics_commission_rate", *logistics.CommissionRate))
			return *logistics.CommissionRate
		}
	}

	// 如果物流表没有佣金比率，使用产品的佣金比率
	if productCommissionRate != nil {
		global.GVA_LOG.Info("使用产品佣金比率",
			zap.Float64("product_commission_rate", *productCommissionRate))
		return *productCommissionRate
	}

	// 如果都没有，使用默认佣金比率
	global.GVA_LOG.Info("使用默认佣金比率",
		zap.Float64("default_commission_rate", defaultRate))
	return defaultRate
}
