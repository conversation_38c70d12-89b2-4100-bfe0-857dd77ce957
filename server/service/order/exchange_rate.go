package order

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// ExchangeRateService 汇率服务
type ExchangeRateService struct{}

// ExchangeRateResponse 汇率API响应结构
type ExchangeRateResponse struct {
	Success bool               `json:"success"`
	Rates   map[string]float64 `json:"rates"`
	Base    string             `json:"base"`
	Date    string             `json:"date"`
}

// CachedExchangeRate 缓存的汇率信息
type CachedExchangeRate struct {
	Rate      float64   `json:"rate"`
	UpdatedAt time.Time `json:"updated_at"`
}

var (
	// 汇率缓存，避免频繁调用API
	exchangeRateCache = make(map[string]CachedExchangeRate)
	// 缓存有效期（1小时）
	cacheValidDuration = time.Hour
)

// NewExchangeRateService 创建汇率服务实例
func NewExchangeRateService() *ExchangeRateService {
	return &ExchangeRateService{}
}

// GetExchangeRate 获取汇率（从fromCurrency到toCurrency）
func (ers *ExchangeRateService) GetExchangeRate(fromCurrency, toCurrency string) (float64, error) {
	// 如果是相同货币，汇率为1
	if fromCurrency == toCurrency {
		return 1.0, nil
	}

	// 标准化货币代码
	fromCurrency = ers.normalizeCurrency(fromCurrency)
	toCurrency = ers.normalizeCurrency(toCurrency)

	// 检查缓存
	cacheKey := fmt.Sprintf("%s_%s", fromCurrency, toCurrency)
	if cachedRate, exists := exchangeRateCache[cacheKey]; exists {
		if time.Since(cachedRate.UpdatedAt) < cacheValidDuration {
			global.GVA_LOG.Debug("使用缓存汇率",
				zap.String("from", fromCurrency),
				zap.String("to", toCurrency),
				zap.Float64("rate", cachedRate.Rate))
			return cachedRate.Rate, nil
		}
	}

	// 从API获取汇率
	rate, err := ers.fetchExchangeRateFromAPI(fromCurrency, toCurrency)
	if err != nil {
		global.GVA_LOG.Error("获取汇率失败，使用默认汇率",
			zap.String("from", fromCurrency),
			zap.String("to", toCurrency),
			zap.Error(err))

		// 如果API失败，使用默认汇率
		return ers.getDefaultExchangeRate(fromCurrency, toCurrency), nil
	}

	// 缓存汇率
	exchangeRateCache[cacheKey] = CachedExchangeRate{
		Rate:      rate,
		UpdatedAt: time.Now(),
	}

	global.GVA_LOG.Info("获取汇率成功",
		zap.String("from", fromCurrency),
		zap.String("to", toCurrency),
		zap.Float64("rate", rate))

	return rate, nil
}

// ConvertCurrency 货币转换
func (ers *ExchangeRateService) ConvertCurrency(amount float64, fromCurrency, toCurrency string) (float64, error) {
	rate, err := ers.GetExchangeRate(fromCurrency, toCurrency)
	if err != nil {
		return 0, err
	}

	convertedAmount := amount * rate

	global.GVA_LOG.Debug("货币转换",
		zap.Float64("原金额", amount),
		zap.String("原货币", fromCurrency),
		zap.Float64("转换后金额", convertedAmount),
		zap.String("目标货币", toCurrency),
		zap.Float64("汇率", rate))

	return convertedAmount, nil
}

// normalizeCurrency 标准化货币代码
func (ers *ExchangeRateService) normalizeCurrency(currency string) string {
	switch currency {
	case "RMB", "CNY", "人民币":
		return "CNY"
	case "USD", "美元", "$":
		return "USD"
	case "EUR", "欧元":
		return "EUR"
	default:
		return currency
	}
}

// fetchExchangeRateFromAPI 从API获取汇率
func (ers *ExchangeRateService) fetchExchangeRateFromAPI(fromCurrency, toCurrency string) (float64, error) {
	// 使用免费的汇率API（exchangerate-api.com）
	url := fmt.Sprintf("https://api.exchangerate-api.com/v4/latest/%s", fromCurrency)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return 0, fmt.Errorf("请求汇率API失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("汇率API返回错误状态: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("读取汇率API响应失败: %v", err)
	}

	var exchangeResp ExchangeRateResponse
	err = json.Unmarshal(body, &exchangeResp)
	if err != nil {
		return 0, fmt.Errorf("解析汇率API响应失败: %v", err)
	}

	if !exchangeResp.Success {
		return 0, fmt.Errorf("汇率API返回失败状态")
	}

	rate, exists := exchangeResp.Rates[toCurrency]
	if !exists {
		return 0, fmt.Errorf("未找到目标货币汇率: %s", toCurrency)
	}

	return rate, nil
}

// getDefaultExchangeRate 获取默认汇率（当API失败时使用）
func (ers *ExchangeRateService) getDefaultExchangeRate(fromCurrency, toCurrency string) float64 {
	// 默认汇率表（可以定期手动更新）
	defaultRates := map[string]map[string]float64{
		"USD": {
			"CNY": 7.2, // 1美元 = 7.2人民币（示例汇率）
		},
		"CNY": {
			"USD": 0.139, // 1人民币 = 0.139美元（示例汇率）
		},
	}

	if rates, exists := defaultRates[fromCurrency]; exists {
		if rate, exists := rates[toCurrency]; exists {
			global.GVA_LOG.Warn("使用默认汇率",
				zap.String("from", fromCurrency),
				zap.String("to", toCurrency),
				zap.Float64("rate", rate))
			return rate
		}
	}

	// 如果没有找到默认汇率，返回1（不转换）
	global.GVA_LOG.Warn("未找到默认汇率，使用1:1转换",
		zap.String("from", fromCurrency),
		zap.String("to", toCurrency))
	return 1.0
}

// ConvertToBaseCurrency 将金额转换为基准货币（人民币）
func (ers *ExchangeRateService) ConvertToBaseCurrency(amount float64, fromCurrency string) (float64, error) {
	return ers.ConvertCurrency(amount, fromCurrency, "CNY")
}

// ConvertFromBaseCurrency 从基准货币（人民币）转换为目标货币
func (ers *ExchangeRateService) ConvertFromBaseCurrency(amount float64, toCurrency string) (float64, error) {
	return ers.ConvertCurrency(amount, "CNY", toCurrency)
}

// UpdateDefaultRates 更新默认汇率（可以通过配置文件或管理界面调用）
func (ers *ExchangeRateService) UpdateDefaultRates(rates map[string]map[string]float64) {
	// 这个方法可以用来更新默认汇率
	// 实际实现中可以将汇率存储到数据库或配置文件中
	global.GVA_LOG.Info("更新默认汇率", zap.Any("rates", rates))
}

// ClearCache 清除汇率缓存
func (ers *ExchangeRateService) ClearCache() {
	exchangeRateCache = make(map[string]CachedExchangeRate)
	global.GVA_LOG.Info("汇率缓存已清除")
}

// GetCachedRates 获取所有缓存的汇率（用于调试）
func (ers *ExchangeRateService) GetCachedRates() map[string]CachedExchangeRate {
	return exchangeRateCache
}
