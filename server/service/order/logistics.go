package order

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

type LogisticsService struct{}

// CreateLogistics 创建物流方式
func (logisticsService *LogisticsService) CreateLogistics(logistics *order.Logistics) (err error) {
	err = global.GVA_DB.Create(logistics).Error
	return err
}

// DeleteLogistics 删除物流方式
func (logisticsService *LogisticsService) DeleteLogistics(ID string) (err error) {
	err = global.GVA_DB.Delete(&order.Logistics{}, "id = ?", ID).Error
	return err
}

// DeleteLogisticsByIds 批量删除物流方式
func (logisticsService *LogisticsService) DeleteLogisticsByIds(IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]order.Logistics{}, "id in ?", IDs).Error
	return err
}

// UpdateLogistics 更新物流方式
func (logisticsService *LogisticsService) UpdateLogistics(logistics order.Logistics) (err error) {
	err = global.GVA_DB.Model(&order.Logistics{}).Where("id = ?", logistics.ID).Updates(&logistics).Error
	return err
}

// GetLogistics 根据ID获取物流方式
func (logisticsService *LogisticsService) GetLogistics(ID string) (logistics order.Logistics, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&logistics).Error
	return
}

// GetLogisticsInfoList 分页获取物流方式列表
func (logisticsService *LogisticsService) GetLogisticsInfoList(info orderReq.LogisticsSearch) (list []order.Logistics, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&order.Logistics{})
	var logisticsList []order.Logistics
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.Name != nil && *info.Name != "" {
		db = db.Where("name LIKE ?", "%"+*info.Name+"%")
	}
	if info.Provider != nil && *info.Provider != "" {
		db = db.Where("provider LIKE ?", "%"+*info.Provider+"%")
	}
	if info.ServiceType != nil && *info.ServiceType != "" {
		db = db.Where("service_type LIKE ?", "%"+*info.ServiceType+"%")
	}
	if info.IsActive != nil {
		db = db.Where("is_active = ?", *info.IsActive)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Order("sort_order ASC, created_at DESC").Find(&logisticsList).Error
	return logisticsList, total, err
}

// GetLogisticsPublic 获取启用的物流方式列表（公开接口）
func (logisticsService *LogisticsService) GetLogisticsPublic() (list []order.Logistics, err error) {
	err = global.GVA_DB.Where("is_active = ?", true).Order("sort_order ASC, created_at DESC").Find(&list).Error
	return list, err
}

// CalculateShippingCost 计算物流费用
func (logisticsService *LogisticsService) CalculateShippingCost(logisticsID uint, weight float64, volume float64) (cost float64, err error) {
	var logistics order.Logistics
	err = global.GVA_DB.Where("id = ? AND is_active = ?", logisticsID, true).First(&logistics).Error
	if err != nil {
		return 0, err
	}

	// 基础计算逻辑
	cost = 0
	if logistics.BasePrice != nil {
		cost += *logistics.BasePrice
	}

	// 按重量计费
	if logistics.PricePerKg != nil && weight > 0 {
		// 如果有最小重量要求，使用最小重量或实际重量中的较大值
		chargeableWeight := weight
		if logistics.MinWeight != nil && weight < *logistics.MinWeight {
			chargeableWeight = *logistics.MinWeight
		}
		cost += *logistics.PricePerKg * chargeableWeight
	}

	// 按体积计费
	if logistics.PricePerCubic != nil && volume > 0 {
		cost += *logistics.PricePerCubic * volume
	}

	// TODO: 这里可以根据PricingFormula字段实现更复杂的计价公式
	// 例如：阶梯计费、区域计费等

	return cost, nil
}

// GetLogisticsSummary 获取物流方式统计信息
func (logisticsService *LogisticsService) GetLogisticsSummary() (summary map[string]interface{}, err error) {
	var total int64
	var activeCount int64

	// 总数
	err = global.GVA_DB.Model(&order.Logistics{}).Count(&total).Error
	if err != nil {
		return nil, err
	}

	// 启用数量
	err = global.GVA_DB.Model(&order.Logistics{}).Where("is_active = ?", true).Count(&activeCount).Error
	if err != nil {
		return nil, err
	}

	summary = map[string]interface{}{
		"total":         total,
		"activeCount":   activeCount,
		"inactiveCount": total - activeCount,
	}

	return summary, nil
}

// SyncOzonLogistics 同步Ozon物流信息
func (logisticsService *LogisticsService) SyncOzonLogistics() error {
	// 获取所有Ozon店铺
	var ozonShops []shops.OzonShop
	if err := global.GVA_DB.Find(&ozonShops).Error; err != nil {
		global.GVA_LOG.Error("获取Ozon店铺失败", zap.Error(err))
		return fmt.Errorf("获取Ozon店铺失败: %v", err)
	}

	global.GVA_LOG.Info("开始同步Ozon物流信息", zap.Int("shopCount", len(ozonShops)))

	ozonAPI := NewOzonAPIService()
	ctx := context.Background()
	totalSynced := 0

	for _, shop := range ozonShops {
		global.GVA_LOG.Info("处理店铺", zap.String("shopName", *shop.Name), zap.String("clientID", *shop.ClientID))

		if shop.ClientID == nil || shop.APIKey == nil {
			global.GVA_LOG.Warn("店铺缺少API凭证", zap.String("shopName", *shop.Name))
			continue
		}

		// 跳过明显的测试数据
		if *shop.ClientID == "sss" || *shop.APIKey == "sss" || *shop.APIKey == "eeee" {
			global.GVA_LOG.Warn("跳过测试数据", zap.String("shopName", *shop.Name))
			continue
		}

		// 获取仓库列表
		global.GVA_LOG.Info("获取仓库列表", zap.String("shopName", *shop.Name))
		warehouses, err := ozonAPI.GetWarehouseList(ctx, *shop.ClientID, *shop.APIKey)
		if err != nil {
			global.GVA_LOG.Error("获取仓库列表失败",
				zap.String("shopName", *shop.Name),
				zap.String("clientID", *shop.ClientID),
				zap.Error(err))
			continue
		}

		global.GVA_LOG.Info("获取到仓库", zap.String("shopName", *shop.Name), zap.Int("warehouseCount", len(warehouses)))

		// 为每个仓库创建协程处理
		var wg sync.WaitGroup
		var mu sync.Mutex

		for _, warehouse := range warehouses {
			// 过滤不可用的仓库
			if warehouse.Status != "created" {
				global.GVA_LOG.Info("跳过不可用仓库",
					zap.String("shopName", *shop.Name),
					zap.String("warehouseName", warehouse.Name),
					zap.String("status", warehouse.Status),
					zap.Int64("warehouseID", warehouse.ID))
				continue
			}

			wg.Add(1)
			go func(w OzonWarehouse) {
				defer wg.Done()

				global.GVA_LOG.Info("开始处理仓库",
					zap.String("shopName", *shop.Name),
					zap.String("warehouseName", w.Name),
					zap.String("status", w.Status),
					zap.Int64("warehouseID", w.ID))

				// 保存或更新仓库信息
				if err := logisticsService.saveOrUpdateWarehouse(shop, w); err != nil {
					global.GVA_LOG.Error("保存仓库失败",
						zap.String("shopName", *shop.Name),
						zap.Int64("warehouseID", w.ID),
						zap.Error(err))
				} else {
					mu.Lock()
					totalSynced++
					mu.Unlock()
				}

				// 获取该仓库的配送方式清单
				deliveryMethods, err := ozonAPI.GetDeliveryMethodList(ctx, *shop.ClientID, *shop.APIKey, w.ID)
				if err != nil {
					global.GVA_LOG.Error("获取配送方式失败",
						zap.String("shopName", *shop.Name),
						zap.Int64("warehouseID", w.ID),
						zap.Error(err))
					return
				}

				global.GVA_LOG.Info("获取到配送方式",
					zap.String("shopName", *shop.Name),
					zap.Int64("warehouseID", w.ID),
					zap.Int("methodCount", len(deliveryMethods)))

				// 验证并保存配送方式
				validMethodCount := 0
				for _, method := range deliveryMethods {
					// 验证配送方式是否属于当前仓库
					if method.WarehouseID != 0 && method.WarehouseID != w.ID {
						global.GVA_LOG.Warn("配送方式仓库ID不匹配，跳过",
							zap.String("shopName", *shop.Name),
							zap.Int64("warehouseID", w.ID),
							zap.Int64("methodWarehouseID", method.WarehouseID),
							zap.Int64("methodID", method.ID),
							zap.String("methodName", method.Name))
						continue
					}

					// 强制设置配送方式的仓库ID为当前仓库ID
					method.WarehouseID = w.ID

					if err := logisticsService.saveOrUpdateDeliveryMethod(shop, w, method); err != nil {
						global.GVA_LOG.Error("保存配送方式失败",
							zap.String("shopName", *shop.Name),
							zap.Int64("deliveryMethodID", method.ID),
							zap.Error(err))
					} else {
						validMethodCount++
						mu.Lock()
						totalSynced++
						mu.Unlock()
					}
				}

				global.GVA_LOG.Info("仓库处理完成",
					zap.String("shopName", *shop.Name),
					zap.Int64("warehouseID", w.ID),
					zap.Int("totalMethodCount", len(deliveryMethods)),
					zap.Int("validMethodCount", validMethodCount))
			}(warehouse)
		}

		// 等待所有仓库处理完成
		wg.Wait()
		global.GVA_LOG.Info("店铺同步完成",
			zap.String("shopName", *shop.Name),
			zap.Int("warehouseCount", len(warehouses)))
	}

	global.GVA_LOG.Info("同步完成", zap.Int("totalSynced", totalSynced))
	return nil
}

// saveOrUpdateWarehouse 保存或更新仓库信息
func (logisticsService *LogisticsService) saveOrUpdateWarehouse(shop shops.OzonShop, warehouse OzonWarehouse) error {
	// 检查是否已存在该仓库记录
	var existingLogistics order.Logistics
	err := global.GVA_DB.Where("shop_client_id = ? AND ozon_warehouse_id = ? AND ozon_delivery_id IS NULL",
		*shop.ClientID, warehouse.ID).First(&existingLogistics).Error

	// 准备Ozon数据
	ozonData := map[string]interface{}{
		"warehouse": warehouse,
	}
	ozonDataJSON, _ := json.Marshal(ozonData)

	// 构建仓库信息
	description := fmt.Sprintf("仓库: %s, 状态: %s", warehouse.Name, warehouse.Status)
	sortOrder := 0

	// 根据status字段判断是否启用，created表示启用，其他状态表示禁用
	isActive := warehouse.Status == "created"

	logistics := order.Logistics{
		ShopName:        shop.Name,
		ShopClientID:    shop.ClientID,
		OzonDeliveryID:  nil, // 仓库记录不关联配送方式
		OzonWarehouseID: &warehouse.ID,
		Name:            &warehouse.Name,
		Provider:        nil,
		ServiceType:     &warehouse.Status,
		Description:     &description,
		IsActive:        &isActive,
		EstimatedDays:   nil,
		SortOrder:       &sortOrder,
		OzonData:        datatypes.JSON(ozonDataJSON),
	}

	if err != nil {
		// 不存在，创建新记录
		if err := global.GVA_DB.Create(&logistics).Error; err != nil {
			return fmt.Errorf("创建仓库记录失败: %v", err)
		}
		global.GVA_LOG.Info("创建新仓库记录",
			zap.String("shopName", *shop.Name),
			zap.String("warehouseName", warehouse.Name))
	} else {
		// 已存在，更新记录
		logistics.ID = existingLogistics.ID
		if err := global.GVA_DB.Model(&existingLogistics).Updates(&logistics).Error; err != nil {
			return fmt.Errorf("更新仓库记录失败: %v", err)
		}
		global.GVA_LOG.Info("更新仓库记录",
			zap.String("shopName", *shop.Name),
			zap.String("warehouseName", warehouse.Name))
	}

	return nil
}

// saveOrUpdateDeliveryMethod 保存或更新配送方式
func (logisticsService *LogisticsService) saveOrUpdateDeliveryMethod(shop shops.OzonShop, warehouse OzonWarehouse, method OzonDeliveryMethod) error {
	// 验证配送方式是否属于当前仓库
	if method.WarehouseID != 0 && method.WarehouseID != warehouse.ID {
		return fmt.Errorf("配送方式仓库ID不匹配: method.WarehouseID=%d, warehouse.ID=%d", method.WarehouseID, warehouse.ID)
	}

	// 检查是否已存在（同时检查仓库ID和配送方式ID）
	var existingLogistics order.Logistics
	err := global.GVA_DB.Where("shop_client_id = ? AND ozon_delivery_id = ? AND ozon_warehouse_id = ?",
		*shop.ClientID, method.ID, warehouse.ID).First(&existingLogistics).Error

	// 准备Ozon数据
	ozonData := map[string]interface{}{
		"delivery_method": method,
		"warehouse":       warehouse,
	}
	ozonDataJSON, _ := json.Marshal(ozonData)

	// 构建物流信息
	description := fmt.Sprintf("仓库: %s, 配送时间: %d-%d天", warehouse.Name, method.MinDays, method.MaxDays)
	sortOrder := 0

	// 根据Status字段判断是否启用，ACTIVE表示启用，其他状态表示禁用
	isActive := method.Status == "ACTIVE"

	logistics := order.Logistics{
		ShopName:        shop.Name,
		ShopClientID:    shop.ClientID,
		OzonDeliveryID:  &method.ID,
		OzonWarehouseID: &warehouse.ID, // 确保使用当前仓库ID
		Name:            &method.Name,
		Provider:        &method.CompanyName,
		ServiceType:     &method.Status,
		Description:     &description,
		IsActive:        &isActive,
		EstimatedDays:   &method.MaxDays,
		SortOrder:       &sortOrder,
		OzonData:        datatypes.JSON(ozonDataJSON),
	}

	if err != nil {
		// 不存在，创建新记录
		if err := global.GVA_DB.Create(&logistics).Error; err != nil {
			return fmt.Errorf("创建物流方式失败: %v", err)
		}
		global.GVA_LOG.Info("创建新物流方式",
			zap.String("shopName", *shop.Name),
			zap.String("methodName", method.Name))
	} else {
		// 已存在，更新记录
		logistics.ID = existingLogistics.ID
		if err := global.GVA_DB.Model(&existingLogistics).Updates(&logistics).Error; err != nil {
			return fmt.Errorf("更新物流方式失败: %v", err)
		}
		global.GVA_LOG.Info("更新物流方式",
			zap.String("shopName", *shop.Name),
			zap.String("methodName", method.Name))
	}

	return nil
}

// ShopWarehouseData 店铺仓库数据结构
type ShopWarehouseData struct {
	Name       string          `json:"name"`
	ClientID   string          `json:"clientId"`
	Warehouses []WarehouseData `json:"warehouses"`
}

// WarehouseData 仓库数据结构
type WarehouseData struct {
	ID        int64             `json:"id"`
	Name      string            `json:"name"`
	Status    string            `json:"status"`
	Logistics []order.Logistics `json:"logistics"`
}

// GetShopWarehouseLogistics 获取店铺仓库物流层级数据
func (logisticsService *LogisticsService) GetShopWarehouseLogistics() ([]ShopWarehouseData, error) {
	// 获取所有未删除的Ozon店铺
	var shops []shops.OzonShop
	if err := global.GVA_DB.Where("deleted_at IS NULL").Find(&shops).Error; err != nil {
		return nil, fmt.Errorf("获取店铺列表失败: %v", err)
	}

	var result []ShopWarehouseData

	for _, shop := range shops {
		shopData := ShopWarehouseData{
			Name:       *shop.Name,
			ClientID:   *shop.ClientID,
			Warehouses: []WarehouseData{},
		}

		// 获取该店铺的所有物流方式
		var logistics []order.Logistics
		if err := global.GVA_DB.Where("shop_client_id = ?", shop.ClientID).Find(&logistics).Error; err != nil {
			global.GVA_LOG.Error("获取店铺物流方式失败",
				zap.String("shopName", *shop.Name),
				zap.Error(err))
			continue
		}

		// 按仓库ID分组物流方式
		warehouseMap := make(map[int64][]order.Logistics)
		warehouseInfoMap := make(map[int64]WarehouseData)

		for _, logistic := range logistics {
			if logistic.OzonWarehouseID != nil {
				warehouseID := *logistic.OzonWarehouseID

				// 如果这是仓库记录（没有配送方式ID），保存仓库信息
				if logistic.OzonDeliveryID == nil {
					// 从仓库记录中提取仓库信息
					warehouseData := WarehouseData{
						ID:        warehouseID,
						Name:      "未知仓库",
						Status:    "unknown",
						Logistics: []order.Logistics{},
					}

					if logistic.Name != nil {
						warehouseData.Name = *logistic.Name
					}
					if logistic.ServiceType != nil {
						warehouseData.Status = *logistic.ServiceType
					}

					warehouseInfoMap[warehouseID] = warehouseData
				} else {
					// 只有配送方式记录才加入到物流方式列表中
					warehouseMap[warehouseID] = append(warehouseMap[warehouseID], logistic)
				}
			}
		}

		// 构建仓库数据
		for warehouseID, warehouseLogistics := range warehouseMap {
			warehouseData := warehouseInfoMap[warehouseID]
			warehouseData.Logistics = warehouseLogistics
			shopData.Warehouses = append(shopData.Warehouses, warehouseData)
		}

		result = append(result, shopData)
	}

	return result, nil
}
