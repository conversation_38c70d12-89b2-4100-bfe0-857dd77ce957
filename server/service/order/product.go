package order

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ProductService struct{}

// CreateProduct 创建产品
func (productService *ProductService) CreateProduct(product orderReq.CreateProductRequest) (err error) {
	err = global.GVA_DB.Create(&product.Product).Error
	return err
}

// DeleteProduct 删除产品（硬删除）
func (productService *ProductService) DeleteProduct(ID uint) (err error) {
	err = global.GVA_DB.Unscoped().Delete(&order.Product{}, ID).Error
	return err
}

// DeleteProductByIds 批量删除产品（硬删除）
func (productService *ProductService) DeleteProductByIds(IDs []uint) (err error) {
	err = global.GVA_DB.Unscoped().Delete(&[]order.Product{}, "id in ?", IDs).Error
	return err
}

// UpdateProduct 更新产品
func (productService *ProductService) UpdateProduct(product orderReq.UpdateProductRequest) (err error) {
	// 确定使用哪个ID字段
	var updateID uint
	if product.ID != 0 {
		updateID = product.ID
	} else if product.Product.ID != 0 {
		updateID = product.Product.ID
	} else {
		global.GVA_LOG.Error("更新产品失败：ID为空")
		return errors.New("产品ID不能为空")
	}

	// 添加调试日志
	global.GVA_LOG.Info("开始更新产品", zap.Uint("request_id", product.ID), zap.Uint("product_id", product.Product.ID), zap.Uint("update_id", updateID))

	// 创建更新数据的map，避免ID字段冲突
	updateData := map[string]interface{}{
		"actual_weight":   product.Product.ActualWeight,
		"cost_price":      product.Product.CostPrice,
		"commission_rate": product.Product.CommissionRate,
		"brand":           product.Product.Brand,
		"description":     product.Product.Description,
		"image_url":       product.Product.ImageURL,
		"notes":           product.Product.Notes,
		"is_active":       product.Product.IsActive,
	}

	result := global.GVA_DB.Model(&order.Product{}).Where("id = ?", updateID).Updates(updateData)
	if result.Error != nil {
		global.GVA_LOG.Error("更新产品失败", zap.Error(result.Error))
		return result.Error
	}

	global.GVA_LOG.Info("更新产品成功", zap.Uint("id", updateID), zap.Int64("affected_rows", result.RowsAffected))
	return nil
}

// GetProduct 根据ID获取产品
func (productService *ProductService) GetProduct(ID uint) (product order.Product, err error) {
	err = global.GVA_DB.Unscoped().Where("id = ?", ID).First(&product).Error
	return
}

// GetProductInfoList 分页获取产品列表
func (productService *ProductService) GetProductInfoList(info orderReq.ProductSearch) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Unscoped().Model(&order.Product{})
	var products []order.Product

	// 构建查询条件
	if info.StartDate != nil && info.EndDate != nil {
		db = db.Where("first_order_date BETWEEN ? AND ?", info.StartDate, info.EndDate)
	}
	if info.SKU != nil && *info.SKU != "" {
		db = db.Where("sku LIKE ?", "%"+*info.SKU+"%")
	}
	if info.ShopName != nil && *info.ShopName != "" {
		db = db.Where("shop_name LIKE ?", "%"+*info.ShopName+"%")
	}
	if info.Brand != nil && *info.Brand != "" {
		db = db.Where("brand LIKE ?", "%"+*info.Brand+"%")
	}
	if info.MinActualWeight != nil {
		db = db.Where("actual_weight >= ?", *info.MinActualWeight)
	}
	if info.MaxActualWeight != nil {
		db = db.Where("actual_weight <= ?", *info.MaxActualWeight)
	}
	if info.MinOzonWeight != nil {
		db = db.Where("ozon_weight >= ?", *info.MinOzonWeight)
	}
	if info.MaxOzonWeight != nil {
		db = db.Where("ozon_weight <= ?", *info.MaxOzonWeight)
	}
	if info.IsActive != nil {
		db = db.Where("is_active = ?", *info.IsActive)
	}
	if info.MinOrderCount != nil {
		db = db.Where("total_order_count >= ?", *info.MinOrderCount)
	}
	if info.MaxOrderCount != nil {
		db = db.Where("total_order_count <= ?", *info.MaxOrderCount)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("updated_at DESC").Find(&products).Error
	return products, total, err
}

// GetProductSummary 获取产品汇总统计
func (productService *ProductService) GetProductSummary() (summary order.ProductSummary, err error) {
	var result struct {
		TotalProducts    int `json:"total_products"`
		ActiveProducts   int `json:"active_products"`
		InactiveProducts int `json:"inactive_products"`
	}

	err = global.GVA_DB.Unscoped().Model(&order.Product{}).
		Select(`
			COUNT(*) as total_products,
			SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active_products,
			SUM(CASE WHEN is_active = false THEN 1 ELSE 0 END) as inactive_products
		`).Scan(&result).Error

	if err != nil {
		return summary, err
	}

	summary = order.ProductSummary{
		TotalProducts:    result.TotalProducts,
		ActiveProducts:   result.ActiveProducts,
		InactiveProducts: result.InactiveProducts,
	}

	return summary, nil
}

// GetProductBySKU 根据SKU获取产品信息
func (productService *ProductService) GetProductBySKU(sku string) (product order.Product, err error) {
	err = global.GVA_DB.Unscoped().Where("sku = ?", sku).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return product, errors.New("未找到该SKU的产品信息")
		}
		return product, err
	}
	return product, nil
}

// BatchUpdateProduct 批量更新产品
func (productService *ProductService) BatchUpdateProduct(req orderReq.BatchUpdateProductRequest) (err error) {
	if len(req.IDs) == 0 {
		return errors.New("请选择要更新的记录")
	}

	updates := make(map[string]interface{})

	if req.ActualWeight != nil {
		updates["actual_weight"] = *req.ActualWeight
	}
	if req.OzonWeight != nil {
		updates["ozon_weight"] = *req.OzonWeight
	}
	if req.CostPrice != nil {
		updates["cost_price"] = *req.CostPrice
	}
	if req.CommissionRate != nil {
		updates["commission_rate"] = *req.CommissionRate
	}
	if req.ShopName != nil {
		updates["shop_name"] = *req.ShopName
	}
	if req.Brand != nil {
		updates["brand"] = *req.Brand
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) == 0 {
		return errors.New("没有要更新的字段")
	}

	err = global.GVA_DB.Unscoped().Model(&order.Product{}).Where("id IN ?", req.IDs).Updates(updates).Error
	return err
}

// SyncProductsFromOrders 从订单同步产品信息
func (productService *ProductService) SyncProductsFromOrders(req orderReq.SyncProductsFromOrdersRequest) (syncCount int, err error) {
	// 获取所有有JsonData的订单
	var orders []order.Order
	err = global.GVA_DB.Where("json_data IS NOT NULL AND json_data != 'null'").Find(&orders).Error
	if err != nil {
		return 0, err
	}

	// 用于统计每个SKU的订单信息
	productStats := make(map[string]struct {
		ProductName     string
		ShopName        *string
		FirstOrderDate  *time.Time
		LastOrderDate   *time.Time
		TotalOrderCount int
	})

	// 遍历所有订单，统计产品信息
	for _, ord := range orders {
		// 解析JsonData获取产品信息
		if ord.JsonData != nil {
			var jsonData map[string]interface{}
			if err := json.Unmarshal(ord.JsonData, &jsonData); err == nil {
				// 根据实际的JSON结构提取产品信息
				if products, ok := jsonData["products"].([]interface{}); ok && len(products) > 0 {
					for _, productInterface := range products {
						if productData, ok := productInterface.(map[string]interface{}); ok {
							var productName, sku string

							// 提取产品名称
							if name, ok := productData["name"].(string); ok {
								productName = name
							}
							// 提取SKU (offer_id)
							if offerID, ok := productData["offer_id"].(string); ok {
								sku = offerID
							}

							if sku != "" && productName != "" {
								// 统计产品信息
								if stats, exists := productStats[sku]; exists {
									// 更新统计信息
									stats.TotalOrderCount++
									if ord.InProcessAt != nil {
										if stats.FirstOrderDate == nil || ord.InProcessAt.Before(*stats.FirstOrderDate) {
											stats.FirstOrderDate = ord.InProcessAt
										}
										if stats.LastOrderDate == nil || ord.InProcessAt.After(*stats.LastOrderDate) {
											stats.LastOrderDate = ord.InProcessAt
										}
									}
									productStats[sku] = stats
								} else {
									// 新产品
									productStats[sku] = struct {
										ProductName     string
										ShopName        *string
										FirstOrderDate  *time.Time
										LastOrderDate   *time.Time
										TotalOrderCount int
									}{
										ProductName:     productName,
										ShopName:        ord.ShopName,
										FirstOrderDate:  ord.InProcessAt,
										LastOrderDate:   ord.InProcessAt,
										TotalOrderCount: 1,
									}
								}
							}
						}
					}
				}
			}
		}
	}

	// 根据统计结果创建或更新产品
	syncCount = 0
	for sku, stats := range productStats {
		var existingProduct order.Product
		err = global.GVA_DB.Unscoped().Where("sku = ?", sku).First(&existingProduct).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 产品不存在，创建新产品
			newProduct := order.Product{
				SKU:             &sku,
				ProductName:     &stats.ProductName,
				ShopName:        stats.ShopName,
				ActualWeight:    nil,
				OzonWeight:      nil,
				IsActive:        func() *bool { b := true; return &b }(),
				FirstOrderDate:  stats.FirstOrderDate,
				LastOrderDate:   stats.LastOrderDate,
				TotalOrderCount: &stats.TotalOrderCount,
			}

			err = global.GVA_DB.Create(&newProduct).Error
			if err != nil {
				global.GVA_LOG.Error("创建产品失败", zap.String("sku", sku), zap.Error(err))
				continue
			}
			global.GVA_LOG.Info("成功创建产品",
				zap.String("sku", sku),
				zap.String("name", stats.ProductName),
				zap.Int("order_count", stats.TotalOrderCount))
			syncCount++
		} else if err == nil {
			// 产品已存在，更新订单统计信息
			updates := make(map[string]interface{})

			// 更新产品名称（如果强制更新）
			if req.ForceUpdate && stats.ProductName != "" {
				updates["product_name"] = stats.ProductName
			}

			// 更新统计信息
			updates["first_order_date"] = stats.FirstOrderDate
			updates["last_order_date"] = stats.LastOrderDate
			updates["total_order_count"] = stats.TotalOrderCount

			err = global.GVA_DB.Unscoped().Model(&order.Product{}).Where("sku = ?", sku).Updates(updates).Error
			if err != nil {
				global.GVA_LOG.Error("更新产品统计失败", zap.String("sku", sku), zap.Error(err))
				continue
			}
			global.GVA_LOG.Info("成功更新产品统计",
				zap.String("sku", sku),
				zap.Int("order_count", stats.TotalOrderCount))
		} else {
			global.GVA_LOG.Error("查询产品失败", zap.String("sku", sku), zap.Error(err))
			continue
		}
	}

	return syncCount, nil
}

// SyncProductImagesFromOrders 从订单同步产品图片
func (productService *ProductService) SyncProductImagesFromOrders() (syncCount int, err error) {
	// 获取所有有图片的订单
	var orders []order.Order
	err = global.GVA_DB.Unscoped().Where("order_img IS NOT NULL AND order_img != 'null' AND order_img != ''").Find(&orders).Error
	if err != nil {
		return 0, err
	}

	syncCount = 0
	for _, ord := range orders {
		// 解析JsonData获取产品信息
		if ord.JsonData != nil {
			var jsonData map[string]interface{}
			if err := json.Unmarshal(ord.JsonData, &jsonData); err == nil {
				// 解析订单图片
				var orderImages []string
				if ord.OrderImg != nil {
					if err := json.Unmarshal(ord.OrderImg, &orderImages); err == nil && len(orderImages) > 0 {
						// 根据实际的JSON结构提取产品信息
						if products, ok := jsonData["products"].([]interface{}); ok && len(products) > 0 {
							// 遍历产品，更新图片
							for i, productData := range products {
								if productMap, ok := productData.(map[string]interface{}); ok {
									var sku string

									// 提取SKU (offer_id)
									if offerID, ok := productMap["offer_id"].(string); ok {
										sku = offerID
									}

									if sku != "" && i < len(orderImages) && orderImages[i] != "" {
										// 更新产品表中的图片URL
										err = global.GVA_DB.Unscoped().Model(&order.Product{}).
											Where("sku = ?", sku).
											Update("image_url", orderImages[i]).Error

										if err != nil {
											global.GVA_LOG.Error("更新产品图片失败", zap.String("sku", sku), zap.Error(err))
											continue
										}

										global.GVA_LOG.Info("产品图片同步成功",
											zap.String("sku", sku),
											zap.String("image_url", orderImages[i]))
										syncCount++
									}
								}
							}
						}
					}
				}
			}
		}
	}

	return syncCount, nil
}
