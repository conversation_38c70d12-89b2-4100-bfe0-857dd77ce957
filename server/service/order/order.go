package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type OrderService struct {
	ozonAPI *OzonAPIService
}

// NewOrderService 创建新的订单服务
func NewOrderService() *OrderService {
	return &OrderService{
		ozonAPI: NewOzonAPIService(),
	}
}

// CreateOrder 创建订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) CreateOrder(ctx context.Context, od *order.Order) (err error) {
	err = global.GVA_DB.Create(od).Error
	return err
}

// DeleteOrder 删除订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) DeleteOrder(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&order.Order{}, "id = ?", ID).Error
	return err
}

// DeleteOrderByIds 批量删除订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) DeleteOrderByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]order.Order{}, "id in ?", IDs).Error
	return err
}

// UpdateOrder 更新订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) UpdateOrder(ctx context.Context, od order.Order) (err error) {
	err = global.GVA_DB.Model(&order.Order{}).Where("id = ?", od.ID).Updates(&od).Error
	return err
}

// GetOrder 根据ID获取订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) GetOrder(ctx context.Context, ID string) (od order.Order, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&od).Error
	return
}

// GetOrderInfoList 分页获取订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) GetOrderInfoList(ctx context.Context, info orderReq.OrderSearch) (list []order.Order, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&order.Order{})
	var ods []order.Order
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	if info.PostingNumber != nil && *info.PostingNumber != "" {
		db = db.Where("posting_number LIKE ?", "%"+*info.PostingNumber+"%")
	}
	if info.InProcessAt != nil {
		db = db.Where("in_process_at <> ?", *info.InProcessAt)
	}
	if info.OrderNumber != nil && *info.OrderNumber != "" {
		db = db.Where("order_number LIKE ?", "%"+*info.OrderNumber+"%")
	}
	if info.TplProvider != nil && *info.TplProvider != "" {
		db = db.Where("tpl_provider LIKE ?", "%"+*info.TplProvider+"%")
	}
	if info.Status != nil && *info.Status != "" {
		db = db.Where("status = ?", *info.Status)
	}
	if info.DistributionStatus != nil && *info.DistributionStatus != "" {
		db = db.Where("distribution_status = ?", *info.DistributionStatus)
	}
	if info.TrackingNumber != nil && *info.TrackingNumber != "" {
		db = db.Where("tracking_number LIKE ?", "%"+*info.TrackingNumber+"%")
	}
	if info.ShipmentDate != nil {
		db = db.Where("shipment_date <> ?", *info.ShipmentDate)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 按下单时间倒序排列，最新下单的订单显示在前面
	// 如果下单时间为空，则按创建时间排序
	err = db.Order("COALESCE(in_process_at, created_at) DESC").Find(&ods).Error
	return ods, total, err
}
func (odService *OrderService) GetOrderPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// PullOrders 拉取订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) PullOrders(ctx context.Context) (count int, err error) {
	// 1. 获取所有Ozon店铺的认证信息
	var ozonShops []shops.OzonShop
	err = global.GVA_DB.Find(&ozonShops).Error
	if err != nil {
		global.GVA_LOG.Error("获取Ozon店铺信息失败", zap.Error(err))
		return 0, fmt.Errorf("获取Ozon店铺信息失败: %v", err)
	}

	global.GVA_LOG.Info("开始拉取订单", zap.Int("店铺数量", len(ozonShops)))

	if len(ozonShops) == 0 {
		global.GVA_LOG.Warn("未找到Ozon店铺配置")
		return 0, fmt.Errorf("未找到Ozon店铺配置")
	}

	totalCount := 0

	// 2. 遍历每个店铺拉取订单
	for _, shop := range ozonShops {
		if shop.ClientID == nil || shop.APIKey == nil {
			global.GVA_LOG.Warn("店铺缺少认证信息",
				zap.String("店铺名称", func() string {
					if shop.Name != nil {
						return *shop.Name
					}
					return "未知"
				}()),
				zap.Bool("有ClientID", shop.ClientID != nil),
				zap.Bool("有APIKey", shop.APIKey != nil))
			continue
		}

		global.GVA_LOG.Info("开始从店铺拉取订单",
			zap.String("店铺名称", *shop.Name),
			zap.String("ClientID", *shop.ClientID))

		shopCount, err := odService.pullOrdersFromOzonShop(ctx, *shop.ClientID, *shop.APIKey)
		if err != nil {
			global.GVA_LOG.Error("从店铺拉取订单失败",
				zap.String("店铺名称", *shop.Name),
				zap.Error(err))
			continue
		}

		global.GVA_LOG.Info("店铺订单拉取完成",
			zap.String("店铺名称", *shop.Name),
			zap.Int("订单数量", shopCount))

		totalCount += shopCount
	}

	global.GVA_LOG.Info("订单拉取总结", zap.Int("总订单数量", totalCount))
	return totalCount, nil
}

// PullYearOrders 拉取今年一整年的订单记录
// Author [yourname](https://github.com/yourname)
func (odService *OrderService) PullYearOrders(ctx context.Context) (count int, err error) {
	// 1. 获取所有Ozon店铺的认证信息
	var ozonShops []shops.OzonShop
	err = global.GVA_DB.Find(&ozonShops).Error
	if err != nil {
		global.GVA_LOG.Error("获取Ozon店铺信息失败", zap.Error(err))
		return 0, fmt.Errorf("获取Ozon店铺信息失败: %v", err)
	}

	global.GVA_LOG.Info("开始拉取今年一整年订单", zap.Int("店铺数量", len(ozonShops)))

	if len(ozonShops) == 0 {
		global.GVA_LOG.Warn("未找到Ozon店铺配置")
		return 0, fmt.Errorf("未找到Ozon店铺配置")
	}

	totalCount := 0

	// 2. 遍历每个店铺拉取订单
	for _, shop := range ozonShops {
		if shop.ClientID == nil || shop.APIKey == nil {
			global.GVA_LOG.Warn("店铺认证信息不完整，跳过",
				zap.String("店铺名称", func() string {
					if shop.Name != nil {
						return *shop.Name
					}
					return "未知店铺"
				}()))
			continue
		}

		global.GVA_LOG.Info("开始从店铺拉取今年订单",
			zap.String("店铺名称", *shop.Name),
			zap.String("ClientID", *shop.ClientID))

		shopCount, err := odService.pullYearOrdersFromOzonShop(ctx, *shop.ClientID, *shop.APIKey)
		if err != nil {
			global.GVA_LOG.Error("从店铺拉取今年订单失败",
				zap.String("店铺名称", *shop.Name),
				zap.Error(err))
			continue
		}

		global.GVA_LOG.Info("店铺今年订单拉取完成",
			zap.String("店铺名称", *shop.Name),
			zap.Int("订单数量", shopCount))

		totalCount += shopCount
	}

	global.GVA_LOG.Info("今年订单拉取总结", zap.Int("总订单数量", totalCount))
	return totalCount, nil
}

// pullOrdersFromOzonShop 从单个Ozon店铺拉取订单
func (odService *OrderService) pullOrdersFromOzonShop(ctx context.Context, clientID, apiKey string) (count int, err error) {
	// Ozon API 基础URL
	baseURL := "https://api-seller.ozon.ru"

	// 只使用不带状态过滤的方式，因为状态过滤会导致API错误
	count, err = odService.fetchOzonOrders(ctx, baseURL, clientID, apiKey, nil)
	if err != nil {
		global.GVA_LOG.Error("获取订单失败", zap.Error(err))
		return 0, err
	}

	return count, nil
}

// pullYearOrdersFromOzonShop 从单个Ozon店铺拉取今年一整年的订单
func (odService *OrderService) pullYearOrdersFromOzonShop(ctx context.Context, clientID, apiKey string) (count int, err error) {
	// Ozon API 基础URL
	baseURL := "https://api-seller.ozon.ru"

	// 拉取今年一整年的订单
	count, err = odService.fetchYearOzonOrders(ctx, baseURL, clientID, apiKey)
	if err != nil {
		global.GVA_LOG.Error("获取今年订单失败", zap.Error(err))
		return 0, err
	}

	return count, nil
}

// fetchYearOzonOrders 拉取今年一整年的订单
func (odService *OrderService) fetchYearOzonOrders(ctx context.Context, baseURL, clientID, apiKey string) (count int, err error) {
	// 获取店铺名称
	var shopName string
	err = global.GVA_DB.Table("ozon_shop").Select("name").Where("clientID = ?", clientID).Scan(&shopName).Error
	if err != nil {
		global.GVA_LOG.Warn("获取店铺名称失败，使用ClientID作为店铺名",
			zap.String("ClientID", clientID),
			zap.Error(err))
		shopName = clientID
	}

	// 计算今年的时间范围
	now := time.Now()
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	yearEnd := time.Date(now.Year(), 12, 31, 23, 59, 59, 999999999, now.Location())

	global.GVA_LOG.Info("拉取今年全部订单数据（包括已完成的）",
		zap.String("店铺", shopName),
		zap.String("开始时间", yearStart.Format("2006-01-02")),
		zap.String("结束时间", yearEnd.Format("2006-01-02")),
		zap.String("说明", "使用v3/posting/fbs/list获取所有订单，包括已完成的"))

	// 创建Ozon API服务实例
	ozonAPI := NewOzonAPIService()

	// 获取今年的所有FBS订单（包括已完成的）
	response, err := ozonAPI.GetAllFBSOrders(ctx, clientID, apiKey, yearStart, yearEnd)
	if err != nil {
		global.GVA_LOG.Error("调用Ozon所有FBS订单API失败",
			zap.String("店铺", shopName),
			zap.Error(err))
		return 0, fmt.Errorf("调用Ozon API失败: %v", err)
	}

	if response == nil {
		global.GVA_LOG.Warn("API返回空结果",
			zap.String("店铺", shopName))
		return 0, nil
	}

	global.GVA_LOG.Info("API返回订单数据",
		zap.String("店铺", shopName),
		zap.Int("订单数量", len(response.Result.Postings)))

	// 第一阶段：快速保存订单（不获取图片）
	savedCount := 0
	var ordersForImageSync []OzonPosting // 需要异步获取图片的订单列表

	for _, posting := range response.Result.Postings {
		global.GVA_LOG.Debug("快速保存年度订单",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.String("status", posting.Status))

		// 快速保存订单（不获取图片）
		err := odService.saveOzonOrderFast(ctx, posting, shopName, clientID)
		if err != nil {
			global.GVA_LOG.Error("快速保存订单失败",
				zap.String("posting_number", posting.PostingNumber),
				zap.Error(err))
			continue
		}

		// 检查产品是否都已存在于数据库中
		allProductsExist, hasExistingImages := odService.checkProductsExistence(posting)

		if allProductsExist && hasExistingImages {
			// 所有产品都存在且有图片，使用数据库中已有的图片数据
			odService.useExistingProductImages(ctx, posting)
			global.GVA_LOG.Info("使用已有产品图片数据",
				zap.String("posting_number", posting.PostingNumber))
		} else {
			// 有新产品或产品没有图片，添加到异步图片获取队列
			ordersForImageSync = append(ordersForImageSync, posting)
			global.GVA_LOG.Info("订单包含新产品或缺少图片，加入异步获取队列",
				zap.String("posting_number", posting.PostingNumber),
				zap.Bool("all_products_exist", allProductsExist),
				zap.Bool("has_existing_images", hasExistingImages))
		}
		savedCount++
	}

	global.GVA_LOG.Info("年度订单快速保存完成",
		zap.String("店铺", shopName),
		zap.Int("API返回数量", len(response.Result.Postings)),
		zap.Int("成功保存数量", savedCount),
		zap.Int("待获取图片", len(ordersForImageSync)))

	// 第二阶段：异步获取图片
	if len(ordersForImageSync) > 0 {
		go func() {
			global.GVA_LOG.Info("开始异步获取年度订单图片",
				zap.String("店铺", shopName),
				zap.Int("订单数量", len(ordersForImageSync)))

			odService.asyncUpdateOrderImages(context.Background(), ordersForImageSync, clientID, apiKey)

			global.GVA_LOG.Info("年度订单图片获取完成",
				zap.String("店铺", shopName),
				zap.Int("处理订单数", len(ordersForImageSync)))
		}()
	}

	return savedCount, nil
}

// fetchOzonOrders 实际获取订单的方法
func (odService *OrderService) fetchOzonOrders(ctx context.Context, _ /* baseURL */, clientID, apiKey string, statuses []string) (count int, err error) {
	// 获取店铺名称
	var shopName string
	err = global.GVA_DB.Table("ozon_shop").Select("name").Where("clientID = ?", clientID).Scan(&shopName).Error
	if err != nil {
		global.GVA_LOG.Warn("获取店铺名称失败，使用ClientID作为店铺名",
			zap.String("clientID", clientID),
			zap.Error(err))
		shopName = clientID // 如果获取失败，使用ClientID作为店铺名
	}

	// 固定拉取当前时间前后14天数据，不依赖数据库时间计算
	global.GVA_LOG.Info("拉取当前时间前后14天订单数据",
		zap.String("店铺ClientID", clientID),
		zap.String("店铺名称", shopName),
		zap.String("说明", "每次都拉取当前时间前后14天的订单，总共28天范围"))

	// 检查ozonAPI是否已初始化
	if odService.ozonAPI == nil {
		return 0, fmt.Errorf("OzonAPIService未初始化")
	}

	// 使用OzonAPIService获取未完成订单（基于研究的简化版本）
	unfulfilledResp, err := odService.ozonAPI.GetUnfulfilledOrders(ctx, clientID, apiKey, time.Time{}, time.Time{}, []string{})
	if err != nil {
		global.GVA_LOG.Error("调用Ozon Unfulfilled API失败",
			zap.String("店铺ClientID", clientID),
			zap.Error(err))
		return 0, err
	}

	// 直接使用unfulfilled订单响应
	ozonResp := unfulfilledResp

	// 记录所有订单状态用于调试
	global.GVA_LOG.Info("所有订单状态详情", zap.Int("总订单数", len(ozonResp.Result.Postings)))
	for i, posting := range ozonResp.Result.Postings {
		global.GVA_LOG.Info("订单状态",
			zap.Int("序号", i+1),
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.String("status", posting.Status),
			zap.String("in_process_at", posting.InProcessAt),
			zap.String("shipment_date", posting.ShipmentDate),
			zap.String("tracking_number", posting.TrackingNumber))
	}

	// 在后端进行状态过滤，拉取所有需要关注的订单状态
	var filteredPostings []OzonPosting

	// 目标状态：拉取所有未完成的订单，前端再进行具体过滤
	targetStatuses := []string{
		"awaiting_packaging", // 等待备货/等待打包
		"awaiting_deliver",   // 等待发运/等待发货
		"delivering",         // 配送中（已发货但未完成）
		"not_accepted",       // 未接受
		"awaiting_approve",   // 等待审批
		"arbitration",        // 仲裁中
		"client_arbitration", // 客户仲裁
		"driver_pickup",      // 司机取货
	}

	for _, posting := range ozonResp.Result.Postings {
		// 只检查状态，不检查时间
		for _, status := range targetStatuses {
			if posting.Status == status {
				filteredPostings = append(filteredPostings, posting)
				break
			}
		}
	}

	global.GVA_LOG.Info("后端状态过滤完成",
		zap.Int("原始订单数量", len(ozonResp.Result.Postings)),
		zap.Int("过滤后订单数量", len(filteredPostings)),
		zap.Strings("目标状态", targetStatuses),
		zap.String("说明", "固定拉取7天数据，只按状态过滤"))

	// 根据货件号（posting_number）进行去重
	postingMap := make(map[string]OzonPosting)
	for _, posting := range filteredPostings {
		// 使用货件号作为唯一标识
		if existingPosting, exists := postingMap[posting.PostingNumber]; exists {
			// 如果已存在，保留更新时间较新的订单
			// 这里可以根据需要调整比较逻辑
			global.GVA_LOG.Info("发现重复货件号，保留最新订单",
				zap.String("posting_number", posting.PostingNumber),
				zap.String("existing_order", existingPosting.OrderNumber),
				zap.String("new_order", posting.OrderNumber))
		}
		postingMap[posting.PostingNumber] = posting
	}

	// 转换map为slice
	uniquePostings := make([]OzonPosting, 0, len(postingMap))
	for _, posting := range postingMap {
		uniquePostings = append(uniquePostings, posting)
	}

	global.GVA_LOG.Info("订单去重完成",
		zap.Int("去重前数量", len(filteredPostings)),
		zap.Int("去重后数量", len(uniquePostings)),
		zap.String("去重依据", "货件号(posting_number)"))

	// 第一阶段：快速保存订单（不获取图片）
	count = 0
	var ordersForImageSync []OzonPosting // 需要异步获取图片的订单列表

	for _, posting := range uniquePostings {
		global.GVA_LOG.Debug("快速保存订单",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.String("status", posting.Status))

		// 快速保存订单（不获取图片）
		err = odService.saveOzonOrderFast(ctx, posting, shopName, clientID)
		if err != nil {
			global.GVA_LOG.Error("快速保存订单失败",
				zap.String("posting_number", posting.PostingNumber),
				zap.Error(err))
			continue
		}

		// 检查产品是否都已存在于数据库中
		allProductsExist, hasExistingImages := odService.checkProductsExistence(posting)

		if allProductsExist && hasExistingImages {
			// 所有产品都存在且有图片，使用数据库中已有的图片数据
			odService.useExistingProductImages(ctx, posting)
			global.GVA_LOG.Info("使用已有产品图片数据",
				zap.String("posting_number", posting.PostingNumber))
		} else {
			// 有新产品或产品没有图片，添加到异步图片获取队列
			ordersForImageSync = append(ordersForImageSync, posting)
			global.GVA_LOG.Info("订单包含新产品或缺少图片，加入异步获取队列",
				zap.String("posting_number", posting.PostingNumber),
				zap.Bool("all_products_exist", allProductsExist),
				zap.Bool("has_existing_images", hasExistingImages))
		}
		count++
	}

	global.GVA_LOG.Info("订单快速保存完成",
		zap.Int("成功保存", count),
		zap.Int("待获取图片", len(ordersForImageSync)))

	// 第二阶段：异步获取图片
	if len(ordersForImageSync) > 0 {
		go func() {
			global.GVA_LOG.Info("开始异步获取订单图片",
				zap.Int("订单数量", len(ordersForImageSync)))

			odService.asyncUpdateOrderImages(context.Background(), ordersForImageSync, clientID, apiKey)
		}()
	}

	global.GVA_LOG.Info("订单处理完成",
		zap.Int("成功保存", count),
		zap.Int("去重后总数量", len(uniquePostings)))

	// 更新拉取时间记录
	odService.updateLastPullTime(ctx, clientID)

	return count, nil
}

// checkProductsExistence 检查订单中的产品是否都已存在于数据库中，以及是否有图片
func (odService *OrderService) checkProductsExistence(posting OzonPosting) (allProductsExist bool, hasExistingImages bool) {
	allProductsExist = true
	hasExistingImages = false

	for _, product := range posting.Products {
		if product.OfferId == "" {
			// SKU为空，视为新产品
			allProductsExist = false
			continue
		}

		// 检查产品是否存在于数据库中
		var existingProduct order.Product
		err := global.GVA_DB.Where("sku = ?", product.OfferId).First(&existingProduct).Error

		if err != nil {
			// 产品不存在
			allProductsExist = false
			global.GVA_LOG.Debug("产品不存在于数据库",
				zap.String("sku", product.OfferId))
		} else {
			// 产品存在，检查是否有图片
			if existingProduct.ImageURL != nil && *existingProduct.ImageURL != "" {
				hasExistingImages = true
				global.GVA_LOG.Debug("找到已存在产品及图片",
					zap.String("sku", product.OfferId),
					zap.String("image_url", *existingProduct.ImageURL))
			} else {
				global.GVA_LOG.Debug("产品存在但无图片",
					zap.String("sku", product.OfferId))
			}
		}
	}

	global.GVA_LOG.Info("产品存在性检查完成",
		zap.String("posting_number", posting.PostingNumber),
		zap.Bool("all_products_exist", allProductsExist),
		zap.Bool("has_existing_images", hasExistingImages),
		zap.Int("total_products", len(posting.Products)))

	return allProductsExist, hasExistingImages
}

// useExistingProductImages 使用数据库中已有的产品图片数据填充订单图片
func (odService *OrderService) useExistingProductImages(ctx context.Context, posting OzonPosting) {
	global.GVA_LOG.Info("开始使用已有产品图片",
		zap.String("posting_number", posting.PostingNumber),
		zap.Int("产品数量", len(posting.Products)))

	// 收集所有产品的图片URL
	var productImages []string

	for _, product := range posting.Products {
		if product.OfferId == "" {
			productImages = append(productImages, "")
			continue
		}

		// 从产品表中查询图片URL
		var existingProduct order.Product
		err := global.GVA_DB.Where("sku = ?", product.OfferId).First(&existingProduct).Error

		if err == nil && existingProduct.ImageURL != nil && *existingProduct.ImageURL != "" {
			// 找到产品图片，应用格式转换
			formattedURL := odService.ozonAPI.FormatOzonImageURL(*existingProduct.ImageURL)
			productImages = append(productImages, formattedURL)
			global.GVA_LOG.Info("找到已有产品图片",
				zap.String("sku", product.OfferId),
				zap.String("original_url", *existingProduct.ImageURL),
				zap.String("formatted_url", formattedURL))
		} else {
			// 没有找到图片，使用空字符串
			productImages = append(productImages, "")
			global.GVA_LOG.Warn("未找到产品图片",
				zap.String("sku", product.OfferId))
		}
	}

	// 如果有图片数据，更新订单的图片字段
	if len(productImages) > 0 {
		// 检查是否有非空图片
		hasImages := false
		for _, img := range productImages {
			if img != "" {
				hasImages = true
				break
			}
		}

		if hasImages {
			// 转换图片数组为JSON
			var orderImgJSON datatypes.JSON
			imgBytes, err := json.Marshal(productImages)
			if err != nil {
				global.GVA_LOG.Error("序列化已有图片数组失败",
					zap.String("posting_number", posting.PostingNumber),
					zap.Error(err))
				return
			}
			orderImgJSON.UnmarshalJSON(imgBytes)

			// 更新数据库中的图片字段
			err = global.GVA_DB.Model(&order.Order{}).
				Where("posting_number = ?", posting.PostingNumber).
				Update("order_img", orderImgJSON).Error

			if err != nil {
				global.GVA_LOG.Error("更新订单已有图片失败",
					zap.String("posting_number", posting.PostingNumber),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("订单已有图片更新成功",
					zap.String("posting_number", posting.PostingNumber),
					zap.Int("图片数量", len(productImages)))
			}
		} else {
			global.GVA_LOG.Info("所有产品都没有图片，跳过更新",
				zap.String("posting_number", posting.PostingNumber))
		}
	}
}

// getDistributionStatusByOrderStatus 根据订单状态判断配货状态
func getDistributionStatusByOrderStatus(orderStatus string) string {
	// 已发运的状态列表
	shippedStatuses := []string{
		"delivering",     // 配送中
		"delivered",      // 已送达
		"driver_pickup",  // 司机取货
		"sent_by_seller", // 卖家已发货
	}

	// 检查是否为已发运状态
	for _, status := range shippedStatuses {
		if orderStatus == status {
			return "processed" // 已处理
		}
	}

	// 其他状态都认为是未发运，配货状态为未处理
	return "unprocessed" // 未处理
}

// saveOzonOrderFast 快速保存Ozon订单（不获取图片）
func (odService *OrderService) saveOzonOrderFast(ctx context.Context, posting OzonPosting, shopName, shopClientID string) error {
	// 解析时间
	inProcessAt, err := time.Parse("2006-01-02T15:04:05Z", posting.InProcessAt)
	if err != nil {
		global.GVA_LOG.Warn("解析订单时间失败，使用当前时间", zap.Error(err))
		inProcessAt = time.Now()
	}

	// 解析发货时间
	var shipmentDate *time.Time
	if posting.ShipmentDate != "" {
		if parsed, err := time.Parse("2006-01-02T15:04:05Z", posting.ShipmentDate); err == nil {
			shipmentDate = &parsed
		}
	}

	// 保存产品信息到JsonData
	jsonData := map[string]interface{}{
		"products":        posting.Products,
		"delivery_method": posting.DeliveryMethod,
		"status":          posting.Status,
		"posting_number":  posting.PostingNumber,
		"order_number":    posting.OrderNumber,
	}

	jsonBytes, err := json.Marshal(jsonData)
	if err != nil {
		return fmt.Errorf("序列化订单数据失败: %v", err)
	}

	// 转换为datatypes.JSON类型
	var jsonDataType datatypes.JSON
	err = jsonDataType.UnmarshalJSON(jsonBytes)
	if err != nil {
		return fmt.Errorf("转换JSON数据类型失败: %v", err)
	}

	// 创建空图片数组
	var orderImgJSON datatypes.JSON
	emptyImgBytes, _ := json.Marshal([]string{})
	orderImgJSON.UnmarshalJSON(emptyImgBytes)

	// 根据订单状态自动判断配货状态
	distributionStatus := getDistributionStatusByOrderStatus(posting.Status)

	global.GVA_LOG.Debug("自动设置配货状态",
		zap.String("posting_number", posting.PostingNumber),
		zap.String("order_status", posting.Status),
		zap.String("distribution_status", distributionStatus))

	// 转换为本地订单格式
	newOrder := order.Order{
		ShopName:           &shopName,     // 店铺名称
		ShopClientID:       &shopClientID, // 店铺ClientID
		PostingNumber:      &posting.PostingNumber,
		OrderNumber:        &posting.OrderNumber,
		TrackingNumber:     &posting.TrackingNumber,
		InProcessAt:        &inProcessAt,
		ShipmentDate:       shipmentDate,
		TplProvider:        &posting.DeliveryMethod.Name,
		Status:             &posting.Status,     // 保存Ozon订单状态
		DistributionStatus: &distributionStatus, // 根据订单状态自动设置配货状态
		OrderImg:           orderImgJSON,        // 空图片数组，后续异步更新
		JsonData:           jsonDataType,
	}

	// 检查订单是否已存在（基于posting_number）
	var existingOrder order.Order
	err = global.GVA_DB.Where("posting_number = ?", posting.PostingNumber).First(&existingOrder).Error

	if err == nil {
		// 订单已存在，更新而不是创建新的
		global.GVA_LOG.Info("订单已存在，执行快速更新",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.Uint("existing_id", existingOrder.ID))

		// 更新现有订单（不更新图片字段）
		err = global.GVA_DB.Model(&existingOrder).Updates(map[string]interface{}{
			"shop_name":           newOrder.ShopName,     // 更新店铺名称
			"shop_client_id":      newOrder.ShopClientID, // 更新店铺ClientID
			"order_number":        newOrder.OrderNumber,
			"tracking_number":     newOrder.TrackingNumber,
			"in_process_at":       newOrder.InProcessAt,
			"shipment_date":       newOrder.ShipmentDate,
			"json_data":           newOrder.JsonData,
			"tpl_provider":        newOrder.TplProvider,
			"status":              newOrder.Status, // 更新Ozon订单状态
			"distribution_status": newOrder.DistributionStatus,
		}).Error

		if err != nil {
			return fmt.Errorf("快速更新订单失败: %v", err)
		}

		global.GVA_LOG.Info("订单快速更新成功",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber))

		return nil
	}

	// 如果是其他错误（非记录不存在），返回错误
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("检查订单是否存在时出错: %v", err)
	}

	// 订单不存在，创建新订单
	err = global.GVA_DB.Create(&newOrder).Error
	if err != nil {
		return fmt.Errorf("快速保存订单到数据库失败: %v", err)
	}

	global.GVA_LOG.Info("新订单快速保存成功",
		zap.String("posting_number", posting.PostingNumber),
		zap.String("order_number", posting.OrderNumber))

	// 创建产品记录
	odService.createProductsFromOrder(posting, shopName, &inProcessAt)

	return nil
}

// createProductsFromOrder 从订单创建产品记录
func (odService *OrderService) createProductsFromOrder(posting OzonPosting, shopName string, orderTime *time.Time) {
	for _, product := range posting.Products {
		if product.OfferId == "" {
			continue
		}

		// 检查产品是否已存在
		var existingProduct order.Product
		err := global.GVA_DB.Unscoped().Where("sku = ?", product.OfferId).First(&existingProduct).Error

		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 产品不存在，创建新产品
			newProduct := order.Product{
				SKU:             &product.OfferId,
				ProductName:     &product.Name,
				ShopName:        &shopName,
				ActualWeight:    nil,
				OzonWeight:      nil, // 重量将在异步获取图片时更新
				IsActive:        func() *bool { b := true; return &b }(),
				FirstOrderDate:  orderTime, // 设置首次订单日期
				LastOrderDate:   orderTime, // 设置最后订单日期
				TotalOrderCount: func() *int { i := 1; return &i }(),
			}

			err = global.GVA_DB.Create(&newProduct).Error
			if err != nil {
				global.GVA_LOG.Error("创建产品失败",
					zap.String("sku", product.OfferId),
					zap.String("name", product.Name),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("成功创建产品",
					zap.String("sku", product.OfferId),
					zap.String("name", product.Name),
					zap.String("shop", shopName),
					zap.String("order_time", orderTime.Format("2006-01-02 15:04:05")))
			}
		} else if err == nil {
			// 产品已存在，更新订单计数和日期
			updates := map[string]interface{}{
				"total_order_count": gorm.Expr("total_order_count + 1"),
			}

			// 更新首次订单日期（如果当前订单时间更早）
			if existingProduct.FirstOrderDate == nil || orderTime.Before(*existingProduct.FirstOrderDate) {
				updates["first_order_date"] = orderTime
			}

			// 更新最后订单日期（如果当前订单时间更晚）
			if existingProduct.LastOrderDate == nil || orderTime.After(*existingProduct.LastOrderDate) {
				updates["last_order_date"] = orderTime
			}

			err = global.GVA_DB.Unscoped().Model(&order.Product{}).
				Where("sku = ?", product.OfferId).
				Updates(updates).Error

			if err != nil {
				global.GVA_LOG.Error("更新产品信息失败",
					zap.String("sku", product.OfferId),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("更新产品信息成功",
					zap.String("sku", product.OfferId),
					zap.String("order_time", orderTime.Format("2006-01-02 15:04:05")))
			}
		} else {
			global.GVA_LOG.Error("查询产品失败",
				zap.String("sku", product.OfferId),
				zap.Error(err))
		}
	}
}

// asyncUpdateOrderImages 异步更新订单图片 - 智能混合处理版
func (odService *OrderService) asyncUpdateOrderImages(ctx context.Context, postings []OzonPosting, clientID, apiKey string) {
	global.GVA_LOG.Info("开始异步更新订单图片（智能混合版）",
		zap.Int("订单数量", len(postings)))

	// 使用并发处理，但限制并发数量避免过载
	maxConcurrency := 10 // 最大并发数
	semaphore := make(chan struct{}, maxConcurrency)

	// 使用WaitGroup等待所有协程完成
	var wg sync.WaitGroup

	// 统计成功和失败的数量
	var successCount, failCount int64

	for _, posting := range postings {
		wg.Add(1)

		// 为每个订单启动一个协程
		go func(p OzonPosting) {
			defer wg.Done()

			// 获取信号量，限制并发数
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			global.GVA_LOG.Info("开始智能处理订单图片",
				zap.String("posting_number", p.PostingNumber))

			// 智能混合处理：结合已有图片和新获取的图片
			finalImages, err := odService.smartProcessOrderImages(ctx, p, clientID, apiKey)
			if err != nil {
				atomic.AddInt64(&failCount, 1)
				global.GVA_LOG.Error("智能处理订单图片失败",
					zap.String("posting_number", p.PostingNumber),
					zap.Error(err))
				return
			}

			// 如果有图片数据，更新数据库
			if len(finalImages) > 0 {
				// 转换图片数组为JSON
				var orderImgJSON datatypes.JSON
				imgBytes, err := json.Marshal(finalImages)
				if err != nil {
					atomic.AddInt64(&failCount, 1)
					global.GVA_LOG.Error("序列化图片数组失败",
						zap.String("posting_number", p.PostingNumber),
						zap.Error(err))
					return
				}
				orderImgJSON.UnmarshalJSON(imgBytes)

				// 更新数据库中的图片字段
				err = global.GVA_DB.Model(&order.Order{}).
					Where("posting_number = ?", p.PostingNumber).
					Update("order_img", orderImgJSON).Error

				if err != nil {
					atomic.AddInt64(&failCount, 1)
					global.GVA_LOG.Error("更新订单图片失败",
						zap.String("posting_number", p.PostingNumber),
						zap.Error(err))
				} else {
					atomic.AddInt64(&successCount, 1)
					global.GVA_LOG.Info("订单图片更新成功",
						zap.String("posting_number", p.PostingNumber),
						zap.Int("图片数量", len(finalImages)))
				}
			} else {
				global.GVA_LOG.Warn("未获取到任何图片",
					zap.String("posting_number", p.PostingNumber))
			}
		}(posting)
	}

	// 等待所有协程完成
	wg.Wait()

	global.GVA_LOG.Info("智能图片更新完成",
		zap.Int("总订单数", len(postings)),
		zap.Int64("成功数量", successCount),
		zap.Int64("失败数量", failCount))
}

// smartProcessOrderImages 智能处理订单图片：结合已有图片和新获取的图片
func (odService *OrderService) smartProcessOrderImages(ctx context.Context, posting OzonPosting, clientID, apiKey string) ([]string, error) {
	global.GVA_LOG.Info("开始智能处理订单图片",
		zap.String("posting_number", posting.PostingNumber),
		zap.Int("产品数量", len(posting.Products)))

	var finalImages []string
	var newProducts []OzonProduct // 需要从API获取图片的新产品

	// 第一步：检查每个产品，收集已有图片或标记需要获取的新产品
	for _, product := range posting.Products {
		if product.OfferId == "" {
			finalImages = append(finalImages, "")
			continue
		}

		// 检查产品是否存在于数据库中
		var existingProduct order.Product
		err := global.GVA_DB.Where("sku = ?", product.OfferId).First(&existingProduct).Error

		if err == nil && existingProduct.ImageURL != nil && *existingProduct.ImageURL != "" {
			// 产品存在且有图片，使用已有图片并应用格式转换
			formattedURL := odService.ozonAPI.FormatOzonImageURL(*existingProduct.ImageURL)
			finalImages = append(finalImages, formattedURL)
			global.GVA_LOG.Debug("使用已有产品图片",
				zap.String("sku", product.OfferId),
				zap.String("original_url", *existingProduct.ImageURL),
				zap.String("formatted_url", formattedURL))
		} else {
			// 产品不存在或没有图片，需要从API获取
			newProducts = append(newProducts, product)
			finalImages = append(finalImages, "") // 先占位，稍后填充
			global.GVA_LOG.Debug("产品需要获取新图片",
				zap.String("sku", product.OfferId))
		}
	}

	// 第二步：如果有新产品，从API获取图片
	if len(newProducts) > 0 {
		global.GVA_LOG.Info("从API获取新产品图片",
			zap.String("posting_number", posting.PostingNumber),
			zap.Int("新产品数量", len(newProducts)))

		newImages, err := odService.ozonAPI.GetProductImages(ctx, clientID, apiKey, newProducts)
		if err != nil {
			global.GVA_LOG.Error("获取新产品图片失败",
				zap.String("posting_number", posting.PostingNumber),
				zap.Error(err))
			return finalImages, err
		}

		// 第三步：将新获取的图片填充到对应位置，并更新产品表
		newImageIndex := 0
		for i, product := range posting.Products {
			if product.OfferId == "" {
				continue
			}

			// 如果这个位置是空的（需要新图片的产品）
			if finalImages[i] == "" {
				if newImageIndex < len(newImages) && newImages[newImageIndex] != "" {
					finalImages[i] = newImages[newImageIndex]

					// 同时更新产品表中的图片URL
					err := global.GVA_DB.Model(&order.Product{}).
						Where("sku = ?", product.OfferId).
						Update("image_url", newImages[newImageIndex]).Error

					if err != nil {
						global.GVA_LOG.Error("更新产品图片URL失败",
							zap.String("sku", product.OfferId),
							zap.String("image_url", newImages[newImageIndex]),
							zap.Error(err))
					} else {
						global.GVA_LOG.Info("更新产品图片URL成功",
							zap.String("sku", product.OfferId),
							zap.String("image_url", newImages[newImageIndex]))
					}
				}
				newImageIndex++
			}
		}
	}

	// 统计最终结果
	nonEmptyCount := 0
	for _, img := range finalImages {
		if img != "" {
			nonEmptyCount++
		}
	}

	global.GVA_LOG.Info("智能图片处理完成",
		zap.String("posting_number", posting.PostingNumber),
		zap.Int("总产品数", len(posting.Products)),
		zap.Int("有图片产品数", nonEmptyCount),
		zap.Int("新获取图片数", len(newProducts)))

	return finalImages, nil
}

// updateProductImages 更新产品表中的图片URL
func (odService *OrderService) updateProductImages(products []OzonProduct, productImages []string) {
	if len(products) != len(productImages) {
		global.GVA_LOG.Warn("产品数量与图片数量不匹配",
			zap.Int("产品数量", len(products)),
			zap.Int("图片数量", len(productImages)))
		return
	}

	for i, product := range products {
		if i >= len(productImages) || productImages[i] == "" {
			continue
		}

		imageURL := productImages[i]
		sku := product.OfferId

		// 更新产品表中的图片URL
		err := global.GVA_DB.Unscoped().Model(&order.Product{}).
			Where("sku = ?", sku).
			Update("image_url", imageURL).Error

		if err != nil {
			global.GVA_LOG.Error("更新产品图片失败",
				zap.String("sku", sku),
				zap.String("image_url", imageURL),
				zap.Error(err))
		} else {
			global.GVA_LOG.Info("产品图片更新成功",
				zap.String("sku", sku),
				zap.String("image_url", imageURL))
		}
	}
}

// getLastPullTime 获取指定店铺上次拉取的时间点
func (odService *OrderService) getLastPullTime(ctx context.Context, clientID string) time.Time {
	// 查询该店铺最新的订单下单时间
	var lastOrder order.Order

	// 需要先查询ozon_shop表获取该clientID对应的店铺信息
	var shop shops.OzonShop
	err := global.GVA_DB.Where("clientID = ?", clientID).First(&shop).Error
	if err != nil {
		global.GVA_LOG.Error("未找到店铺信息",
			zap.String("ClientID", clientID),
			zap.Error(err))
		// 如果找不到店铺，返回2小时前（保守策略）
		return time.Now().Add(-2 * time.Hour)
	}

	// 首先尝试通过JsonData查询该店铺的订单
	err = global.GVA_DB.Where("in_process_at IS NOT NULL").
		Where("JSON_EXTRACT(json_data, '$.shop_info.client_id') = ?", clientID).
		Order("in_process_at DESC").
		First(&lastOrder).Error

	if err != nil {
		// 如果通过JsonData没找到，可能是老数据，尝试查询所有订单中最新的
		err = global.GVA_DB.Where("in_process_at IS NOT NULL").
			Order("in_process_at DESC").
			First(&lastOrder).Error

		if err != nil {
			// 如果完全没有订单，使用30天前作为起始时间（确保能拉取到更多订单）
			defaultTime := time.Now().Add(-30 * 24 * time.Hour)
			global.GVA_LOG.Info("未找到任何历史订单，使用30天前作为起始时间",
				zap.String("店铺名称", *shop.Name),
				zap.String("ClientID", clientID),
				zap.String("默认开始时间", defaultTime.Format("2006-01-02T15:04:05Z")))
			return defaultTime
		}

		// 找到了订单但不是该店铺的，使用30天前作为起始点（确保不遗漏新订单）
		defaultTime := time.Now().Add(-30 * 24 * time.Hour)
		global.GVA_LOG.Info("未找到该店铺的历史订单，使用30天前作为起始时间",
			zap.String("店铺名称", *shop.Name),
			zap.String("ClientID", clientID),
			zap.String("起始时间", defaultTime.Format("2006-01-02T15:04:05Z")))
		return defaultTime
	}

	// 从最后一个订单的下单时间开始，但减去2小时以确保不遗漏
	lastPullTime := lastOrder.InProcessAt.Add(-2 * time.Hour)

	global.GVA_LOG.Info("找到该店铺上次拉取时间点",
		zap.String("店铺名称", *shop.Name),
		zap.String("ClientID", clientID),
		zap.String("最后订单下单时间", lastOrder.InProcessAt.Format("2006-01-02T15:04:05Z")),
		zap.String("拉取起始时间", lastPullTime.Format("2006-01-02T15:04:05Z")))

	return lastPullTime
}

// updateLastPullTime 更新拉取时间记录
func (odService *OrderService) updateLastPullTime(ctx context.Context, clientID string) {
	// 这个方法可以用来记录拉取时间，目前由于我们使用订单创建时间来判断，所以暂时不需要额外的表
	global.GVA_LOG.Info("拉取完成，时间点已通过订单记录更新",
		zap.String("店铺ClientID", clientID),
		zap.String("当前时间", time.Now().Format("2006-01-02T15:04:05Z")))
}

// saveOzonOrder 保存Ozon订单到数据库
func (odService *OrderService) saveOzonOrder(ctx context.Context, posting OzonPosting, clientID, apiKey string) error {
	// 创建包含店铺信息的扩展数据结构
	type ExtendedOrderData struct {
		OzonPosting
		ShopInfo struct {
			ClientID string `json:"client_id"`
			APIKey   string `json:"api_key"`
		} `json:"shop_info"`
	}

	// 构建扩展的订单数据
	extendedData := ExtendedOrderData{
		OzonPosting: posting,
	}
	extendedData.ShopInfo.ClientID = clientID
	extendedData.ShopInfo.APIKey = apiKey

	// 将扩展数据序列化为JSON
	jsonBytes, err := json.Marshal(extendedData)
	if err != nil {
		return fmt.Errorf("序列化扩展订单数据失败: %v", err)
	}

	// 转换为datatypes.JSON类型
	var jsonData datatypes.JSON
	err = jsonData.UnmarshalJSON(jsonBytes)
	if err != nil {
		return fmt.Errorf("转换JSON数据类型失败: %v", err)
	}

	// 获取商品图片
	productImages, err := odService.ozonAPI.GetProductImages(ctx, clientID, apiKey, posting.Products)
	if err != nil {
		global.GVA_LOG.Error("获取商品图片失败", zap.Error(err))
		// 继续处理，不因为图片获取失败而中断订单保存
		productImages = make([]string, len(posting.Products))
	}

	// 解析时间
	inProcessAt, err := time.Parse("2006-01-02T15:04:05Z", posting.InProcessAt)
	if err != nil {
		global.GVA_LOG.Warn("解析订单时间失败，使用当前时间", zap.Error(err))
		inProcessAt = time.Now()
	}

	// 解析发货时间
	var shipmentDate *time.Time
	if posting.ShipmentDate != "" {
		if parsed, err := time.Parse("2006-01-02T15:04:05Z", posting.ShipmentDate); err == nil {
			shipmentDate = &parsed
		}
	}
	// 转换图片数组为JSON
	var orderImgJSON datatypes.JSON
	if len(productImages) > 0 {
		imgBytes, err := json.Marshal(productImages)
		if err != nil {
			global.GVA_LOG.Error("序列化图片数组失败", zap.Error(err))
		} else {
			orderImgJSON.UnmarshalJSON(imgBytes)
		}
	}

	// 创建订单对象
	newOrder := order.Order{
		OrderNumber:    &posting.OrderNumber,
		PostingNumber:  &posting.PostingNumber,
		TrackingNumber: &posting.TrackingNumber,
		InProcessAt:    &inProcessAt,
		ShipmentDate:   shipmentDate,
		JsonData:       jsonData,
		OrderImg:       orderImgJSON,
	}

	// 设置TplProvider
	if posting.TplIntegrationCode != "" {
		newOrder.TplProvider = &posting.TplIntegrationCode
	}

	// 设置分发状态
	distributionStatus := "pending"
	newOrder.DistributionStatus = &distributionStatus

	// 检查订单是否已存在（基于posting_number）
	var existingOrder order.Order
	err = global.GVA_DB.Where("posting_number = ?", posting.PostingNumber).First(&existingOrder).Error

	if err == nil {
		// 订单已存在，更新而不是创建新的
		global.GVA_LOG.Info("订单已存在，执行更新",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber),
			zap.Uint("existing_id", existingOrder.ID))

		// 更新现有订单
		err = global.GVA_DB.Model(&existingOrder).Updates(map[string]interface{}{
			"order_number":        newOrder.OrderNumber,
			"tracking_number":     newOrder.TrackingNumber,
			"in_process_at":       newOrder.InProcessAt,
			"shipment_date":       newOrder.ShipmentDate,
			"json_data":           newOrder.JsonData,
			"order_img":           newOrder.OrderImg,
			"tpl_provider":        newOrder.TplProvider,
			"distribution_status": newOrder.DistributionStatus,
		}).Error

		if err != nil {
			return fmt.Errorf("更新订单失败: %v", err)
		}

		global.GVA_LOG.Info("订单更新成功",
			zap.String("posting_number", posting.PostingNumber),
			zap.String("order_number", posting.OrderNumber))

		return nil
	}

	// 如果是其他错误（非记录不存在），返回错误
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("检查订单是否存在时出错: %v", err)
	}

	// 订单不存在，创建新订单
	err = global.GVA_DB.Create(&newOrder).Error
	if err != nil {
		return fmt.Errorf("保存订单到数据库失败: %v", err)
	}

	global.GVA_LOG.Info("新订单保存成功",
		zap.String("posting_number", posting.PostingNumber),
		zap.String("order_number", posting.OrderNumber))

	return nil
}
