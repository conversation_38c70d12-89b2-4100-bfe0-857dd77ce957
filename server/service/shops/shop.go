
package shops

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
    shopsReq "github.com/flipped-aurora/gin-vue-admin/server/model/shops/request"
)

type ShopService struct {}
// CreateShop 创建商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService) CreateShop(ctx context.Context, shop *shops.Shop) (err error) {
	err = global.GVA_DB.Create(shop).Error
	return err
}

// DeleteShop 删除商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService)DeleteShop(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&shops.Shop{},"id = ?",ID).Error
	return err
}

// DeleteShopByIds 批量删除商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService)DeleteShopByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]shops.Shop{},"id in ?",IDs).Error
	return err
}

// UpdateShop 更新商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService)UpdateShop(ctx context.Context, shop shops.Shop) (err error) {
	err = global.GVA_DB.Model(&shops.Shop{}).Where("id = ?",shop.ID).Updates(&shop).Error
	return err
}

// GetShop 根据ID获取商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService)GetShop(ctx context.Context, ID string) (shop shops.Shop, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&shop).Error
	return
}
// GetShopInfoList 分页获取商店记录
// Author [yourname](https://github.com/yourname)
func (shopService *ShopService)GetShopInfoList(ctx context.Context, info shopsReq.ShopSearch) (list []shops.Shop, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&shops.Shop{})
    var shops []shops.Shop
    // 如果有条件搜索 下方会自动创建搜索语句
    if info.StartCreatedAt !=nil && info.EndCreatedAt !=nil {
     db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
    }
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&shops).Error
	return  shops, total, err
}
func (shopService *ShopService)GetShopPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
