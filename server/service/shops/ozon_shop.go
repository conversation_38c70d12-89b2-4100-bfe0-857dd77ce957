
package shops

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
    shopsReq "github.com/flipped-aurora/gin-vue-admin/server/model/shops/request"
)

type OzonShopService struct {}
// CreateOzonShop 创建店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService) CreateOzonShop(ctx context.Context, ozonShop *shops.OzonShop) (err error) {
	err = global.GVA_DB.Create(ozonShop).Error
	return err
}

// DeleteOzonShop 删除店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService)DeleteOzonShop(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&shops.OzonShop{},"id = ?",ID).Error
	return err
}

// DeleteOzonShopByIds 批量删除店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService)DeleteOzonShopByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]shops.OzonShop{},"id in ?",IDs).Error
	return err
}

// UpdateOzonShop 更新店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService)UpdateOzonShop(ctx context.Context, ozonShop shops.OzonShop) (err error) {
	err = global.GVA_DB.Model(&shops.OzonShop{}).Where("id = ?",ozonShop.ID).Updates(&ozonShop).Error
	return err
}

// GetOzonShop 根据ID获取店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService)GetOzonShop(ctx context.Context, ID string) (ozonShop shops.OzonShop, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&ozonShop).Error
	return
}
// GetOzonShopInfoList 分页获取店铺授权记录
// Author [yourname](https://github.com/yourname)
func (ozonShopService *OzonShopService)GetOzonShopInfoList(ctx context.Context, info shopsReq.OzonShopSearch) (list []shops.OzonShop, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&shops.OzonShop{})
    var ozonShops []shops.OzonShop
    // 如果有条件搜索 下方会自动创建搜索语句
    if info.StartCreatedAt !=nil && info.EndCreatedAt !=nil {
     db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
    }
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&ozonShops).Error
	return  ozonShops, total, err
}
func (ozonShopService *OzonShopService)GetOzonShopPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
