package ozoneOrder

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ozoneOrder"
	ozoneOrderReq "github.com/flipped-aurora/gin-vue-admin/server/model/ozoneOrder/request"
)

type OzoneOrderDetailService struct{}

// CreateOzoneOrderDetail 创建ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) CreateOzoneOrderDetail(ctx context.Context, ozoneOrderDetail *[]ozoneOrder.OzoneOrderDetail) (err error) {
	err = global.GVA_DB.Create(ozoneOrderDetail).Error
	return err
}

// DeleteOzoneOrderDetail 删除ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) DeleteOzoneOrderDetail(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&ozoneOrder.OzoneOrderDetail{}, "id = ?", ID).Error
	return err
}

// DeleteOzoneOrderDetailByIds 批量删除ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) DeleteOzoneOrderDetailByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]ozoneOrder.OzoneOrderDetail{}, "id in ?", IDs).Error
	return err
}

// UpdateOzoneOrderDetail 更新ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) UpdateOzoneOrderDetail(ctx context.Context, ozoneOrderDetail ozoneOrder.OzoneOrderDetail) (err error) {
	err = global.GVA_DB.Model(&ozoneOrder.OzoneOrderDetail{}).Where("id = ?", ozoneOrderDetail.ID).Updates(&ozoneOrderDetail).Error
	return err
}

// GetOzoneOrderDetail 根据ID获取ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) GetOzoneOrderDetail(ctx context.Context, ID string) (ozoneOrderDetail ozoneOrder.OzoneOrderDetail, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&ozoneOrderDetail).Error
	return
}

// GetOzoneOrderDetailInfoList 分页获取ozone订单详情记录
// Author [yourname](https://github.com/yourname)
func (ozoneOrderDetailService *OzoneOrderDetailService) GetOzoneOrderDetailInfoList(ctx context.Context, info ozoneOrderReq.OzoneOrderDetailSearch) (list []ozoneOrder.OzoneOrderDetail, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&ozoneOrder.OzoneOrderDetail{})
	var ozoneOrderDetails []ozoneOrder.OzoneOrderDetail
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.StartCreatedAt != nil && info.EndCreatedAt != nil {
		db = db.Where("created_at BETWEEN ? AND ?", info.StartCreatedAt, info.EndCreatedAt)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&ozoneOrderDetails).Error
	return ozoneOrderDetails, total, err
}
func (ozoneOrderDetailService *OzoneOrderDetailService) GetOzoneOrderDetailPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
