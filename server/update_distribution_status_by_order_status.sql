-- 根据订单状态更新配货状态的SQL脚本
USE xrt;

-- 1. 查看当前订单状态和配货状态分布
SELECT 
    '更新前状态分布' as info,
    status as order_status,
    distribution_status,
    COUNT(*) as count 
FROM `order` 
GROUP BY status, distribution_status
ORDER BY status, distribution_status;

-- 2. 更新已发运订单的配货状态为"已处理"
UPDATE `order` 
SET distribution_status = 'processed' 
WHERE status IN ('delivering', 'delivered', 'driver_pickup', 'sent_by_seller');

-- 3. 更新未发运订单的配货状态为"未处理"
UPDATE `order` 
SET distribution_status = 'unprocessed' 
WHERE status NOT IN ('delivering', 'delivered', 'driver_pickup', 'sent_by_seller');

-- 4. 查看更新后的状态分布
SELECT 
    '更新后状态分布' as info,
    status as order_status,
    distribution_status,
    COUNT(*) as count 
FROM `order` 
GROUP BY status, distribution_status
ORDER BY status, distribution_status;

-- 5. 验证更新结果
SELECT 
    '验证结果' as info,
    COUNT(*) as total_orders,
    COUNT(CASE WHEN distribution_status = 'processed' THEN 1 END) as processed_orders,
    COUNT(CASE WHEN distribution_status = 'unprocessed' THEN 1 END) as unprocessed_orders
FROM `order`;
