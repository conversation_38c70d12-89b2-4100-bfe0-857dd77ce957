package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ozoneOrder"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
	"github.com/flipped-aurora/gin-vue-admin/server/model/xrtozon"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(xrtozon.OzonShop{}, shops.Shop{}, ozoneOrder.OzoneOrderDetail{}, shops.OzonShop{}, order.Order{}, order.Logistics{})
	if err != nil {
		return err
	}
	return nil
}
