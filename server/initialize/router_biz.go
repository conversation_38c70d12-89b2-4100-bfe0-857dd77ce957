package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
	}
	{
		shopsRouter := router.RouterGroupApp.Shops
		shopsRouter.InitShopRouter(privateGroup, publicGroup)
		shopsRouter.InitOzonShopRouter(privateGroup, publicGroup)
	}
	{
		ozoneOrderRouter := router.RouterGroupApp.OzoneOrder
		ozoneOrderRouter.InitOzoneOrderDetailRouter(privateGroup, publicGroup)
	} // 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
	{
		orderRouter := router.RouterGroupApp.Order
		orderRouter.InitOrderRouter(privateGroup, publicGroup)
		orderRouter.InitProfitRouter(privateGroup)                 // 利润统计路由
		orderRouter.InitProductRouter(privateGroup)                // 产品管理路由
		orderRouter.InitLogisticsRouter(privateGroup, publicGroup) // 物流管理路由
	}
}
