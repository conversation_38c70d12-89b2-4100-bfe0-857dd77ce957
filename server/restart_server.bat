@echo off
chcp 65001 >nul
echo 🔄 开始重启 Gin-Vue-Admin Server...

REM 1. 停止当前运行的服务
echo 📛 停止当前运行的服务...

REM 停止所有gin-vue-admin相关进程
taskkill /f /im gin-vue-admin-server.exe >nul 2>&1

REM 停止占用8888端口的进程
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8888') do (
    taskkill /f /pid %%a >nul 2>&1
)

timeout /t 2 >nul

REM 2. 编译最新版本
echo 🔨 编译最新版本...
go build -o gin-vue-admin-server.exe .
if %errorlevel% neq 0 (
    echo    ❌ 编译失败，请检查代码
    pause
    exit /b 1
)
echo    ✅ 编译成功

REM 3. 启动新服务
echo 🚀 启动新服务...
start /b gin-vue-admin-server.exe > server.log 2>&1

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 3 >nul

REM 4. 验证服务状态
echo 🔍 验证服务状态...
curl -s http://localhost:8888/health >nul 2>&1
if %errorlevel% equ 0 (
    echo    ✅ 服务启动成功！
    echo    🌐 前端地址: http://127.0.0.1:8080
    echo    📖 API文档: http://127.0.0.1:8888/swagger/index.html
    echo    📝 日志文件: server.log
    echo.
    echo 💡 使用以下命令查看实时日志:
    echo    type server.log
    echo.
    echo 💡 使用任务管理器或以下命令停止服务:
    echo    taskkill /f /im gin-vue-admin-server.exe
) else (
    echo    ❌ 服务启动失败，请检查日志
    echo    📝 查看日志: type server.log
    pause
    exit /b 1
)

echo 🎉 后端重启完成！

REM 5. 启动前端服务（如果需要）
echo 🌐 启动前端服务...
cd ..\web

REM 检查前端是否已经在运行
netstat -ano | findstr :8080 >nul 2>&1
if %errorlevel% equ 0 (
    echo    ℹ️  前端服务已在运行 (端口8080)
) else (
    netstat -ano | findstr :8093 >nul 2>&1
    if %errorlevel% equ 0 (
        echo    ℹ️  前端服务已在运行 (端口8093)
    ) else (
        echo    🚀 启动前端开发服务器...
        start /b npm run serve > ..\server\web.log 2>&1
        timeout /t 3 >nul
        echo    ✅ 前端服务启动中...
        echo    📝 前端日志文件: web.log
    )
)

cd ..\server
echo.
echo 🎉 全部服务重启完成！
pause
