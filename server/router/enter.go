package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router/example"
	"github.com/flipped-aurora/gin-vue-admin/server/router/order"
	"github.com/flipped-aurora/gin-vue-admin/server/router/ozoneOrder"
	"github.com/flipped-aurora/gin-vue-admin/server/router/shops"
	"github.com/flipped-aurora/gin-vue-admin/server/router/system"
)

var RouterGroupApp = new(RouterGroup)

type RouterGroup struct {
	System     system.RouterGroup
	Example    example.RouterGroup
	Shops      shops.RouterGroup
	OzoneOrder ozoneOrder.RouterGroup
	Order      order.RouterGroup
}
