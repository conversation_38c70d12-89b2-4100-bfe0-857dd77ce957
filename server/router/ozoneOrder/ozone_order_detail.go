package ozoneOrder

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type OzoneOrderDetailRouter struct{}

// InitOzoneOrderDetailRouter 初始化 ozone订单详情 路由信息
func (s *OzoneOrderDetailRouter) InitOzoneOrderDetailRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	ozoneOrderDetailRouter := Router.Group("ozoneOrderDetail").Use(middleware.OperationRecord())
	ozoneOrderDetailRouterWithoutRecord := Router.Group("ozoneOrderDetail")
	ozoneOrderDetailRouterWithoutAuth := PublicRouter.Group("ozoneOrderDetail")
	{
		ozoneOrderDetailRouter.POST("synchronizeOrders", ozoneOrderDetailApi.SynchronizeOrders)                       // 新建ozone订单详情
		ozoneOrderDetailRouter.POST("createOzoneOrderDetail", ozoneOrderDetailApi.CreateOzoneOrderDetail)             // 新建ozone订单详情
		ozoneOrderDetailRouter.DELETE("deleteOzoneOrderDetail", ozoneOrderDetailApi.DeleteOzoneOrderDetail)           // 删除ozone订单详情
		ozoneOrderDetailRouter.DELETE("deleteOzoneOrderDetailByIds", ozoneOrderDetailApi.DeleteOzoneOrderDetailByIds) // 批量删除ozone订单详情
		ozoneOrderDetailRouter.PUT("updateOzoneOrderDetail", ozoneOrderDetailApi.UpdateOzoneOrderDetail)              // 更新ozone订单详情
	}
	{
		ozoneOrderDetailRouterWithoutRecord.GET("findOzoneOrderDetail", ozoneOrderDetailApi.FindOzoneOrderDetail)       // 根据ID获取ozone订单详情
		ozoneOrderDetailRouterWithoutRecord.GET("getOzoneOrderDetailList", ozoneOrderDetailApi.GetOzoneOrderDetailList) // 获取ozone订单详情列表
	}
	{
		ozoneOrderDetailRouterWithoutAuth.GET("getOzoneOrderDetailPublic", ozoneOrderDetailApi.GetOzoneOrderDetailPublic) // ozone订单详情开放接口
	}
}
