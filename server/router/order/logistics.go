package order

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type LogisticsRouter struct{}

// InitLogisticsRouter 初始化 物流方式 路由信息
func (s *LogisticsRouter) InitLogisticsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	logisticsRouter := Router.Group("logistics").Use(middleware.OperationRecord())
	logisticsRouterWithoutRecord := Router.Group("logistics")
	logisticsRouterWithoutAuth := PublicRouter.Group("logistics")

	var logisticsApi = v1.ApiGroupApp.OrderApiGroup.LogisticsApi
	{
		logisticsRouter.POST("createLogistics", logisticsApi.CreateLogistics)             // 新建物流方式
		logisticsRouter.DELETE("deleteLogistics", logisticsApi.DeleteLogistics)           // 删除物流方式
		logisticsRouter.DELETE("deleteLogisticsByIds", logisticsApi.DeleteLogisticsByIds) // 批量删除物流方式
		logisticsRouter.PUT("updateLogistics", logisticsApi.UpdateLogistics)              // 更新物流方式
		logisticsRouter.POST("syncOzonLogistics", logisticsApi.SyncOzonLogistics)         // 同步Ozon物流信息
	}
	{
		logisticsRouterWithoutRecord.GET("findLogistics", logisticsApi.FindLogistics)                         // 根据ID获取物流方式
		logisticsRouterWithoutRecord.GET("getLogisticsList", logisticsApi.GetLogisticsList)                   // 获取物流方式列表
		logisticsRouterWithoutRecord.GET("getLogisticsSummary", logisticsApi.GetLogisticsSummary)             // 获取物流统计信息
		logisticsRouterWithoutRecord.GET("getShopWarehouseLogistics", logisticsApi.GetShopWarehouseLogistics) // 获取店铺仓库物流层级数据
	}
	{
		logisticsRouterWithoutAuth.GET("getLogisticsPublic", logisticsApi.GetLogisticsList) // 获取物流方式列表
	}
}
