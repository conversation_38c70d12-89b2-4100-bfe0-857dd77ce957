package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type OrderRouter struct{}

// InitOrderRouter 初始化 订单 路由信息
func (s *OrderRouter) InitOrderRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	odRouter := Router.Group("od").Use(middleware.OperationRecord())
	odRouterWithoutRecord := Router.Group("od")
	odRouterWithoutAuth := PublicRouter.Group("od")
	{
		odRouter.POST("createOrder", odApi.CreateOrder)             // 新建订单
		odRouter.DELETE("deleteOrder", odApi.DeleteOrder)           // 删除订单
		odRouter.DELETE("deleteOrderByIds", odApi.DeleteOrderByIds) // 批量删除订单
		odRouter.PUT("updateOrder", odApi.UpdateOrder)              // 更新订单
		odRouter.POST("pullOrders", odApi.PullOrders)               // 拉取订单
		odRouter.POST("pullYearOrders", odApi.PullYearOrders)       // 拉取今年订单
	}
	{
		odRouterWithoutRecord.GET("findOrder", odApi.FindOrder)       // 根据ID获取订单
		odRouterWithoutRecord.GET("getOrderList", odApi.GetOrderList) // 获取订单列表
	}
	{
		odRouterWithoutAuth.GET("getOrderPublic", odApi.GetOrderPublic) // 订单开放接口
	}
}
