package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProductRouter struct{}

// InitProductRouter 初始化产品管理路由信息
func (s *ProductRouter) InitProductRouter(Router *gin.RouterGroup) {
	productRouter := Router.Group("product").Use(middleware.OperationRecord())
	productRouterWithoutRecord := Router.Group("product")
	{
		productRouter.POST("createProduct", productApi.CreateProduct)                             // 新建产品
		productRouter.DELETE("deleteProduct", productApi.DeleteProduct)                           // 删除产品
		productRouter.DELETE("deleteProductByIds", productApi.DeleteProductByIds)                 // 批量删除产品
		productRouter.PUT("updateProduct", productApi.UpdateProduct)                              // 更新产品
		productRouter.PUT("batchUpdateProduct", productApi.BatchUpdateProduct)                    // 批量更新产品
		productRouter.POST("syncProductsFromOrders", productApi.SyncProductsFromOrders)           // 从订单同步产品
		productRouter.POST("syncProductImagesFromOrders", productApi.SyncProductImagesFromOrders) // 从订单同步产品图片
	}
	{
		productRouterWithoutRecord.GET("findProduct", productApi.FindProduct)             // 根据ID获取产品
		productRouterWithoutRecord.GET("getProductList", productApi.GetProductList)       // 获取产品列表
		productRouterWithoutRecord.GET("getProductSummary", productApi.GetProductSummary) // 获取产品汇总统计
		productRouterWithoutRecord.GET("getProductBySKU", productApi.GetProductBySKU)     // 根据SKU获取产品信息
	}
}
