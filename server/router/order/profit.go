package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ProfitRouter struct{}

// InitProfitRouter 初始化利润统计路由信息
func (s *ProfitRouter) InitProfitRouter(Router *gin.RouterGroup) {
	profitRouter := Router.Group("profit").Use(middleware.OperationRecord())
	profitRouterWithoutRecord := Router.Group("profit")
	{
		profitRouter.POST("calculateProfit", profitApi.CalculateOrderProfit)        // 计算订单利润
		profitRouter.POST("setMonthlyAdvertising", profitApi.SetMonthlyAdvertising) // 设置月度广告费用
		profitRouter.POST("refreshProfitData", profitApi.RefreshProfitData)         // 刷新利润统计数据
		profitRouter.DELETE("deleteAllProfitData", profitApi.DeleteAllProfitData)   // 删除所有利润数据
	}
	{
		profitRouterWithoutRecord.GET("getProfitList", profitApi.GetProfitList)                              // 获取利润统计列表
		profitRouterWithoutRecord.GET("getProfitSummary", profitApi.GetProfitSummary)                        // 获取利润汇总统计
		profitRouterWithoutRecord.GET("getProfitSummaryByCurrency", profitApi.GetProfitSummaryByCurrency)    // 按货币分组获取利润汇总统计
		profitRouterWithoutRecord.GET("getProfitByShop", profitApi.GetProfitByShop)                          // 按店铺统计利润
		profitRouterWithoutRecord.GET("getMonthlyProfitList", profitApi.GetMonthlyProfitList)                // 获取月度利润统计列表
		profitRouterWithoutRecord.GET("getMonthlyAdvertising", profitApi.GetMonthlyAdvertising)              // 获取月度广告费用
		profitRouterWithoutRecord.GET("exportUSDOrdersDetail", profitApi.ExportUSDOrdersDetail)              // 导出美元订单详细数据
		profitRouterWithoutRecord.GET("exportMonthlyOrdersDetail", profitApi.ExportMonthlyOrdersDetailFixed) // 导出月度订单详细数据
	}
}
