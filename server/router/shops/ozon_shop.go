package shops

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type OzonShopRouter struct {}

// InitOzonShopRouter 初始化 店铺授权 路由信息
func (s *OzonShopRouter) InitOzonShopRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	ozonShopRouter := Router.Group("ozonShop").Use(middleware.OperationRecord())
	ozonShopRouterWithoutRecord := Router.Group("ozonShop")
	ozonShopRouterWithoutAuth := PublicRouter.Group("ozonShop")
	{
		ozonShopRouter.POST("createOzonShop", ozonShopApi.CreateOzonShop)   // 新建店铺授权
		ozonShopRouter.DELETE("deleteOzonShop", ozonShopApi.DeleteOzonShop) // 删除店铺授权
		ozonShopRouter.DELETE("deleteOzonShopByIds", ozonShopApi.DeleteOzonShopByIds) // 批量删除店铺授权
		ozonShopRouter.PUT("updateOzonShop", ozonShopApi.UpdateOzonShop)    // 更新店铺授权
	}
	{
		ozonShopRouterWithoutRecord.GET("findOzonShop", ozonShopApi.FindOzonShop)        // 根据ID获取店铺授权
		ozonShopRouterWithoutRecord.GET("getOzonShopList", ozonShopApi.GetOzonShopList)  // 获取店铺授权列表
	}
	{
	    ozonShopRouterWithoutAuth.GET("getOzonShopPublic", ozonShopApi.GetOzonShopPublic)  // 店铺授权开放接口
	}
}
