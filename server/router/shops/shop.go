package shops

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ShopRouter struct {}

// InitShopRouter 初始化 商店 路由信息
func (s *ShopRouter) InitShopRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	shopRouter := Router.Group("shop").Use(middleware.OperationRecord())
	shopRouterWithoutRecord := Router.Group("shop")
	shopRouterWithoutAuth := PublicRouter.Group("shop")
	{
		shopRouter.POST("createShop", shopApi.CreateShop)   // 新建商店
		shopRouter.DELETE("deleteShop", shopApi.DeleteShop) // 删除商店
		shopRouter.DELETE("deleteShopByIds", shopApi.DeleteShopByIds) // 批量删除商店
		shopRouter.PUT("updateShop", shopApi.UpdateShop)    // 更新商店
	}
	{
		shopRouterWithoutRecord.GET("findShop", shopApi.FindShop)        // 根据ID获取商店
		shopRouterWithoutRecord.GET("getShopList", shopApi.GetShopList)  // 获取商店列表
	}
	{
	    shopRouterWithoutAuth.GET("getShopPublic", shopApi.GetShopPublic)  // 商店开放接口
	}
}
