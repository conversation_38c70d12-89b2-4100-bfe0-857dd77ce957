package v1

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/example"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/order"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/ozoneOrder"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/shops"
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1/system"
)

var ApiGroupApp = new(ApiGroup)

type ApiGroup struct {
	SystemApiGroup     system.ApiGroup
	ExampleApiGroup    example.ApiGroup
	ShopsApiGroup      shops.ApiGroup
	OzoneOrderApiGroup ozoneOrder.ApiGroup
	OrderApiGroup      order.ApiGroup
}
