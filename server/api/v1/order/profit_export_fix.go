package order

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

// ExportMonthlyOrdersDetailFixed 导出月度订单详细数据(修复版本)
func (profitApi *ProfitApi) ExportMonthlyOrdersDetailFixed(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		response.FailWithMessage("月份参数不能为空", c)
		return
	}

	global.GVA_LOG.Info("开始导出月度订单详细数据", zap.String("month", month))

	// 查询RMB订单数据
	var rmbOrders []OrderDetail
	rmbQuery := `
		SELECT
			posting_number, order_number, shop_name, product_name, sku, quantity,
			sale_price, cost_price, shipping_cost, commission, other_costs,
			net_profit, currency, order_date
		FROM order_profits
		WHERE currency = 'RMB'
		AND DATE_FORMAT(order_date, '%Y-%m') = ?
		ORDER BY order_date DESC
	`

	if err := global.GVA_DB.Raw(rmbQuery, month).Scan(&rmbOrders).Error; err != nil {
		global.GVA_LOG.Error("查询RMB订单失败", zap.Error(err))
		response.FailWithMessage("查询RMB订单失败", c)
		return
	}

	// 查询USD订单数据
	var usdOrders []OrderDetail
	usdQuery := `
		SELECT
			posting_number, order_number, shop_name, product_name, sku, quantity,
			sale_price, cost_price, shipping_cost, commission, other_costs,
			net_profit, currency, order_date
		FROM order_profits
		WHERE currency = 'USD'
		AND DATE_FORMAT(order_date, '%Y-%m') = ?
		ORDER BY order_date DESC
	`

	if err := global.GVA_DB.Raw(usdQuery, month).Scan(&usdOrders).Error; err != nil {
		global.GVA_LOG.Error("查询USD订单失败", zap.Error(err))
		response.FailWithMessage("查询USD订单失败", c)
		return
	}

	global.GVA_LOG.Info("查询订单完成", zap.Int("RMB订单数", len(rmbOrders)), zap.Int("USD订单数", len(usdOrders)))

	// 添加详细的调试信息
	global.GVA_LOG.Info("调试信息", zap.String("查询月份", month))

	// 查询总订单数量进行调试
	var totalCount int64
	global.GVA_DB.Table("order_profits").Count(&totalCount)
	global.GVA_LOG.Info("总订单数", zap.Int64("count", totalCount))

	// 查询该月份的订单数量
	var monthCount int64
	global.GVA_DB.Table("order_profits").Where("DATE_FORMAT(order_date, '%Y-%m') = ?", month).Count(&monthCount)
	global.GVA_LOG.Info("该月份订单数量", zap.String("月份", month), zap.Int64("订单数", monthCount))

	// 查询RMB订单数量
	var rmbCount int64
	global.GVA_DB.Table("order_profits").Where("currency = 'RMB' AND DATE_FORMAT(order_date, '%Y-%m') = ?", month).Count(&rmbCount)
	global.GVA_LOG.Info("RMB订单数量", zap.String("月份", month), zap.Int64("订单数", rmbCount))

	// 查询USD订单数量
	var usdCount int64
	global.GVA_DB.Table("order_profits").Where("currency = 'USD' AND DATE_FORMAT(order_date, '%Y-%m') = ?", month).Count(&usdCount)
	global.GVA_LOG.Info("USD订单数量", zap.String("月份", month), zap.Int64("订单数", usdCount))

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			global.GVA_LOG.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 表头定义
	headers := []string{"货件号", "订单号", "店铺名称", "产品名称", "SKU", "数量", "售价", "成本价", "运费", "佣金", "其他费用", "净利润", "利润率", "订单日期", "货币"}

	// 创建RMB工作表
	rmbSheetName := "RMB订单"
	f.SetSheetName("Sheet1", rmbSheetName)

	// 创建USD工作表
	usdSheetName := "USD订单(已转换为RMB)"
	_, err := f.NewSheet(usdSheetName)
	if err != nil {
		global.GVA_LOG.Error("创建USD工作表失败", zap.Error(err))
		response.FailWithMessage("创建Excel工作表失败", c)
		return
	}

	// 填充RMB工作表
	if err := profitApi.fillOrderSheetFixed(f, rmbSheetName, headers, rmbOrders, false); err != nil {
		global.GVA_LOG.Error("填充RMB工作表失败", zap.Error(err))
		response.FailWithMessage("生成RMB工作表失败", c)
		return
	}

	// 填充USD工作表（转换为RMB）
	if err := profitApi.fillOrderSheetFixed(f, usdSheetName, headers, usdOrders, true); err != nil {
		global.GVA_LOG.Error("填充USD工作表失败", zap.Error(err))
		response.FailWithMessage("生成USD工作表失败", c)
		return
	}

	// 设置文件名
	filename := fmt.Sprintf("monthly_orders_detail_%s.xlsx", month)

	// 直接保存到临时文件然后读取
	tempFile := fmt.Sprintf("/tmp/%s", filename)
	err = f.SaveAs(tempFile)
	if err != nil {
		global.GVA_LOG.Error("保存Excel文件失败", zap.Error(err))
		response.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	global.GVA_LOG.Info("Excel文件保存成功", zap.String("tempFile", tempFile))

	// 设置响应头并发送文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.File(tempFile)

	global.GVA_LOG.Info("Excel文件发送完成")
}

// fillOrderSheetFixed 填充订单数据到工作表(修复版本)
func (profitApi *ProfitApi) fillOrderSheetFixed(f *excelize.File, sheetName string, headers []string, orders []OrderDetail, convertToRMB bool) error {
	// 设置表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		if err := f.SetCellValue(sheetName, cell, header); err != nil {
			return err
		}
	}

	// 设置表头样式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6E6FA"}, Pattern: 1},
	})
	f.SetRowStyle(sheetName, 1, 1, headerStyle)

	// 填充数据
	for i, order := range orders {
		row := i + 2

		// 如果需要转换为RMB，则应用7.0汇率
		salePrice := order.SalePrice
		costPrice := order.CostPrice
		shippingCost := order.ShippingCost
		commission := order.Commission
		otherCosts := order.OtherCosts
		netProfit := order.NetProfit
		currency := order.Currency

		if convertToRMB && order.Currency == "USD" {
			// 只转换售价，其他费用保持原值
			salePrice *= 7.0
			// 重新计算净利润：转换后的售价 - 原始成本费用
			netProfit = salePrice - (costPrice + shippingCost + commission + otherCosts)
			currency = "RMB(转换)"
		}

		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), order.PostingNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), order.OrderNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), order.ShopName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), order.ProductName)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), order.SKU)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), order.Quantity)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), salePrice)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), costPrice)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), shippingCost)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), commission)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), otherCosts)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), netProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), order.ProfitMargin)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), order.OrderDate.Format("2006-01-02"))
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", row), currency)

		// 如果利润为负数，设置红色背景
		if netProfit < 0 {
			negativeStyle, _ := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFE4E1"}, Pattern: 1},
			})
			f.SetRowStyle(sheetName, row, row, negativeStyle)
		}
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c", 'A'+i)
		f.SetColWidth(sheetName, col, col, 15)
	}

	return nil
}
