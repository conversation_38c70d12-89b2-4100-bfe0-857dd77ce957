package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OrderApi struct{}

// CreateOrder 创建订单
// @Tags Order
// @Summary 创建订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body order.Order true "创建订单"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /od/createOrder [post]
func (odApi *OrderApi) CreateOrder(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()
	var od order.Order
	err := c.ShouldBindJSON(&od)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = getOrderService().CreateOrder(ctx, &od)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteOrder 删除订单
// @Tags Order
// @Summary 删除订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body order.Order true "删除订单"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /od/deleteOrder [delete]
func (odApi *OrderApi) DeleteOrder(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := getOrderService().DeleteOrder(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteOrderByIds 批量删除订单
// @Tags Order
// @Summary 批量删除订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /od/deleteOrderByIds [delete]
func (odApi *OrderApi) DeleteOrderByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := getOrderService().DeleteOrderByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateOrder 更新订单
// @Tags Order
// @Summary 更新订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body order.Order true "更新订单"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /od/updateOrder [put]
func (odApi *OrderApi) UpdateOrder(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var od order.Order
	err := c.ShouldBindJSON(&od)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = getOrderService().UpdateOrder(ctx, od)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindOrder 用id查询订单
// @Tags Order
// @Summary 用id查询订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询订单"
// @Success 200 {object} response.Response{data=order.Order,msg=string} "查询成功"
// @Router /od/findOrder [get]
func (odApi *OrderApi) FindOrder(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reod, err := getOrderService().GetOrder(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reod, c)
}

// GetOrderList 分页获取订单列表
// @Tags Order
// @Summary 分页获取订单列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query orderReq.OrderSearch true "分页获取订单列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /od/getOrderList [get]
func (odApi *OrderApi) GetOrderList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo orderReq.OrderSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := getOrderService().GetOrderInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetOrderPublic 不需要鉴权的订单接口
// @Tags Order
// @Summary 不需要鉴权的订单接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /od/getOrderPublic [get]
func (odApi *OrderApi) GetOrderPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	getOrderService().GetOrderPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的订单接口信息",
	}, "获取成功", c)
}

// PullOrders 拉取订单
// @Tags Order
// @Summary 拉取订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "拉取成功"
// @Router /od/pullOrders [post]
func (odApi *OrderApi) PullOrders(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	count, err := getOrderService().PullOrders(ctx)
	if err != nil {
		global.GVA_LOG.Error("拉取订单失败!", zap.Error(err))
		response.FailWithMessage("拉取订单失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"count": count,
	}, "拉取订单成功", c)
}

// PullYearOrders 拉取今年一整年的订单
// @Tags Order
// @Summary 拉取今年一整年的订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "拉取成功"
// @Router /od/pullYearOrders [post]
func (odApi *OrderApi) PullYearOrders(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	count, err := getOrderService().PullYearOrders(ctx)
	if err != nil {
		global.GVA_LOG.Error("拉取今年订单失败!", zap.Error(err))
		response.FailWithMessage("拉取今年订单失败:"+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"count": count,
	}, "拉取今年订单成功", c)
}
