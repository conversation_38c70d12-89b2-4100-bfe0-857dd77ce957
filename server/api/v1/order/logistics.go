package order

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/order"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type LogisticsApi struct{}

// CreateLogistics 创建物流方式
// @Tags Logistics
// @Summary 创建物流方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body order.Logistics true "创建物流方式"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /logistics/createLogistics [post]
func (logisticsApi *LogisticsApi) CreateLogistics(c *gin.Context) {
	var logistics order.Logistics
	err := c.ShouldBindJSON(&logistics)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.LogisticsService.CreateLogistics(&logistics)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteLogistics 删除物流信息
// @Tags Logistics
// @Summary 删除物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body order.Logistics true "删除物流信息"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /logistics/deleteLogistics [delete]
func (logisticsApi *LogisticsApi) DeleteLogistics(c *gin.Context) {
	ID := c.Query("ID")
	err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.DeleteLogistics(ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteLogisticsByIds 批量删除物流信息
// @Tags Logistics
// @Summary 批量删除物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /logistics/deleteLogisticsByIds [delete]
func (logisticsApi *LogisticsApi) DeleteLogisticsByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.DeleteLogisticsByIds(IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateLogistics 更新物流信息
// @Tags Logistics
// @Summary 更新物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body order.Logistics true "更新物流信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /logistics/updateLogistics [put]
func (logisticsApi *LogisticsApi) UpdateLogistics(c *gin.Context) {
	var logistics order.Logistics
	err := c.ShouldBindJSON(&logistics)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.LogisticsService.UpdateLogistics(logistics)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindLogistics 用id查询物流信息
// @Tags Logistics
// @Summary 用id查询物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query order.Logistics true "用id查询物流信息"
// @Success 200 {object} response.Response{data=order.Logistics,msg=string} "查询成功"
// @Router /logistics/findLogistics [get]
func (logisticsApi *LogisticsApi) FindLogistics(c *gin.Context) {
	ID := c.Query("ID")
	relogistics, err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.GetLogistics(ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
		return
	}
	response.OkWithData(relogistics, c)
}

// GetLogisticsList 分页获取物流信息列表
// @Tags Logistics
// @Summary 分页获取物流信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.LogisticsSearch true "分页获取物流信息列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /logistics/getLogisticsList [get]
func (logisticsApi *LogisticsApi) GetLogisticsList(c *gin.Context) {
	var pageInfo orderReq.LogisticsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.GetLogisticsInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetLogisticsSummary 获取物流统计信息
// @Tags Logistics
// @Summary 获取物流统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /logistics/getLogisticsSummary [get]
func (logisticsApi *LogisticsApi) GetLogisticsSummary(c *gin.Context) {
	summary, err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.GetLogisticsSummary()
	if err != nil {
		global.GVA_LOG.Error("获取物流统计信息失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithData(summary, c)
}

// SyncOzonLogistics 同步Ozon物流信息
// @Tags Logistics
// @Summary 同步Ozon物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "同步成功"
// @Router /logistics/syncOzonLogistics [post]
func (logisticsApi *LogisticsApi) SyncOzonLogistics(c *gin.Context) {
	err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.SyncOzonLogistics()
	if err != nil {
		global.GVA_LOG.Error("同步Ozon物流信息失败!", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("同步成功", c)
}

// GetShopWarehouseLogistics 获取店铺仓库物流层级数据
// @Tags Logistics
// @Summary 获取店铺仓库物流层级数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=interface{},msg=string} "获取成功"
// @Router /logistics/getShopWarehouseLogistics [get]
func (logisticsApi *LogisticsApi) GetShopWarehouseLogistics(c *gin.Context) {
	data, err := service.ServiceGroupApp.OrderServiceGroup.LogisticsService.GetShopWarehouseLogistics()
	if err != nil {
		global.GVA_LOG.Error("获取店铺仓库物流数据失败!", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}
	response.OkWithData(data, c)
}
