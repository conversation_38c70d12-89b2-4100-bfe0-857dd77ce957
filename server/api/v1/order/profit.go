package order

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
)

type ProfitApi struct{}

// GetProfitList 分页获取利润统计列表
// @Tags OrderProfit
// @Summary 分页获取利润统计列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.OrderProfitSearch true "分页获取利润统计列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /profit/getProfitList [get]
func (profitApi *ProfitApi) GetProfitList(c *gin.Context) {
	var pageInfo orderReq.OrderProfitSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetProfitList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProfitSummary 获取利润汇总统计
// @Tags OrderProfit
// @Summary 获取利润汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.ProfitAnalysisRequest true "获取利润汇总统计"
// @Success 200 {object} response.Response{data=order.ProfitSummary,msg=string} "获取成功"
// @Router /profit/getProfitSummary [get]
func (profitApi *ProfitApi) GetProfitSummary(c *gin.Context) {
	var req orderReq.ProfitAnalysisRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	summary, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetProfitSummary(req)
	if err != nil {
		global.GVA_LOG.Error("获取利润汇总失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(summary, "获取成功", c)
}

// GetProfitSummaryByCurrency 按货币分组获取利润汇总统计
// @Tags OrderProfit
// @Summary 按货币分组获取利润汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.ProfitAnalysisRequest true "按货币分组获取利润汇总统计"
// @Success 200 {object} response.Response{data=[]order.ProfitSummaryByCurrency,msg=string} "获取成功"
// @Router /profit/getProfitSummaryByCurrency [get]
func (profitApi *ProfitApi) GetProfitSummaryByCurrency(c *gin.Context) {
	var req orderReq.ProfitAnalysisRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	summaries, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetProfitSummaryByCurrency(req)
	if err != nil {
		global.GVA_LOG.Error("获取按货币分组利润汇总失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithData(summaries, c)
}

// GetProfitByShop 按店铺统计利润
// @Tags OrderProfit
// @Summary 按店铺统计利润
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.ProfitAnalysisRequest true "按店铺统计利润"
// @Success 200 {object} response.Response{data=[]order.ProfitByShop,msg=string} "获取成功"
// @Router /profit/getProfitByShop [get]
func (profitApi *ProfitApi) GetProfitByShop(c *gin.Context) {
	var req orderReq.ProfitAnalysisRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetProfitByShop(req)
	if err != nil {
		global.GVA_LOG.Error("获取店铺利润统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(list, "获取成功", c)
}

// CalculateOrderProfit 计算订单利润
// @Tags OrderProfit
// @Summary 计算订单利润
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.ProfitCalculateRequest true "计算订单利润"
// @Success 200 {object} response.Response{msg=string} "计算成功"
// @Router /profit/calculateProfit [post]
func (profitApi *ProfitApi) CalculateOrderProfit(c *gin.Context) {
	var req orderReq.ProfitCalculateRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProfitService.CalculateOrderProfit(req)
	if err != nil {
		global.GVA_LOG.Error("计算利润失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithMessage("计算成功", c)
}

// GetMonthlyProfitList 获取月度利润统计列表
// @Tags OrderProfit
// @Summary 获取月度利润统计列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.MonthlyProfitSearch true "获取月度利润统计列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /profit/getMonthlyProfitList [get]
func (profitApi *ProfitApi) GetMonthlyProfitList(c *gin.Context) {
	var pageInfo orderReq.MonthlyProfitSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetMonthlyProfitList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取月度利润统计失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// SetMonthlyAdvertising 设置月度广告费用
// @Tags OrderProfit
// @Summary 设置月度广告费用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.MonthlyAdvertisingRequest true "设置月度广告费用"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /profit/setMonthlyAdvertising [post]
func (profitApi *ProfitApi) SetMonthlyAdvertising(c *gin.Context) {
	var req orderReq.MonthlyAdvertisingRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProfitService.SetMonthlyAdvertising(req)
	if err != nil {
		global.GVA_LOG.Error("设置月度广告费用失败!", zap.Error(err))
		response.FailWithMessage("设置失败", c)
		return
	}

	response.OkWithMessage("设置成功", c)
}

// GetMonthlyAdvertising 获取月度广告费用
// @Tags OrderProfit
// @Summary 获取月度广告费用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param month query string true "月份(YYYY-MM)"
// @Success 200 {object} response.Response{data=order.MonthlyAdvertising,msg=string} "获取成功"
// @Router /profit/getMonthlyAdvertising [get]
func (profitApi *ProfitApi) GetMonthlyAdvertising(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		response.FailWithMessage("月份参数不能为空", c)
		return
	}

	advertising, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.GetMonthlyAdvertising(month)
	if err != nil {
		global.GVA_LOG.Error("获取月度广告费用失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(advertising, "获取成功", c)
}

// RefreshProfitData 刷新利润统计数据
// @Tags OrderProfit
// @Summary 刷新利润统计数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "刷新成功"
// @Router /profit/refreshProfitData [post]
func (profitApi *ProfitApi) RefreshProfitData(c *gin.Context) {
	count, err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.RefreshProfitData()
	if err != nil {
		global.GVA_LOG.Error("刷新利润统计数据失败!", zap.Error(err))
		response.FailWithMessage("刷新失败: "+err.Error(), c)
	} else {
		response.OkWithMessage(fmt.Sprintf("刷新成功，处理了 %d 条订单", count), c)
	}
}

// DeleteAllProfitData 删除所有利润数据
// @Tags OrderProfit
// @Summary 删除所有利润数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /profit/deleteAllProfitData [delete]
func (profitApi *ProfitApi) DeleteAllProfitData(c *gin.Context) {
	err := service.ServiceGroupApp.OrderServiceGroup.ProfitService.DeleteAllProfitData()
	if err != nil {
		global.GVA_LOG.Error("删除所有利润数据失败!", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}
	response.OkWithMessage("所有利润数据删除成功", c)
}

// ExportUSDOrdersDetail 导出美元订单详细数据到Excel
// @Tags OrderProfit
// @Summary 导出美元订单详细数据到Excel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/octet-stream
// @Param month query string true "月份 (格式: 2025-06)"
// @Success 200 {file} file "Excel文件"
// @Router /profit/exportUSDOrdersDetail [get]
func (profitApi *ProfitApi) ExportUSDOrdersDetail(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		response.FailWithMessage("月份参数不能为空", c)
		return
	}

	// 查询指定月份的美元订单详细数据
	var orders []struct {
		PostingNumber string    `json:"posting_number"`
		OrderNumber   string    `json:"order_number"`
		ShopName      string    `json:"shop_name"`
		ProductName   string    `json:"product_name"`
		SKU           string    `json:"sku"`
		Quantity      int       `json:"quantity"`
		SalePrice     float64   `json:"sale_price"`
		CostPrice     float64   `json:"cost_price"`
		ShippingCost  float64   `json:"shipping_cost"`
		Commission    float64   `json:"commission"`
		OtherCosts    float64   `json:"other_costs"`
		NetProfit     float64   `json:"net_profit"`
		ProfitMargin  float64   `json:"profit_margin"`
		OrderDate     time.Time `json:"order_date"`
		Currency      string    `json:"currency"`
	}

	err := global.GVA_DB.Table("order_profits").
		Select("posting_number, order_number, shop_name, product_name, sku, quantity, sale_price, cost_price, shipping_cost, commission, other_costs, net_profit, profit_margin, order_date, currency").
		Where("DATE_FORMAT(order_date, '%Y-%m') = ? AND currency = 'USD'", month).
		Order("net_profit ASC").
		Scan(&orders).Error

	if err != nil {
		global.GVA_LOG.Error("查询美元订单数据失败!", zap.Error(err))
		response.FailWithMessage("查询数据失败: "+err.Error(), c)
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			global.GVA_LOG.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 设置工作表名称
	sheetName := "美元订单详情"
	f.SetSheetName("Sheet1", sheetName)

	// 设置表头
	headers := []string{
		"货件号", "订单号", "店铺名称", "产品名称", "SKU", "数量",
		"售价($)", "成本价($)", "运费($)", "佣金($)", "其他费用($)",
		"净利润($)", "利润率(%)", "订单日期", "货币",
	}

	// 写入表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6E6FA"}, Pattern: 1},
	})
	f.SetRowStyle(sheetName, 1, 1, headerStyle)

	// 写入数据
	for i, order := range orders {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), order.PostingNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), order.OrderNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), order.ShopName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), order.ProductName)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), order.SKU)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), order.Quantity)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), order.SalePrice)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), order.CostPrice)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), order.ShippingCost)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), order.Commission)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), order.OtherCosts)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), order.NetProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), order.ProfitMargin)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), order.OrderDate.Format("2006-01-02"))
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", row), order.Currency)

		// 如果利润为负数，设置红色背景
		if order.NetProfit < 0 {
			negativeStyle, _ := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFE4E1"}, Pattern: 1},
			})
			f.SetRowStyle(sheetName, row, row, negativeStyle)
		}
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c", 'A'+i)
		f.SetColWidth(sheetName, col, col, 15)
	}

	// 设置文件名
	filename := fmt.Sprintf("美元订单详情_%s.xlsx", month)

	// 设置响应头
	c.Header("Content-Type", "application/octet-stream")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Transfer-Encoding", "binary")

	// 将Excel文件写入响应
	if err := f.Write(c.Writer); err != nil {
		global.GVA_LOG.Error("写入Excel文件失败", zap.Error(err))
		response.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}
}

// ExportMonthlyOrdersDetail 导出月度订单详细数据(包含人民币和美元)
// @Tags OrderProfit
// @Summary 导出月度订单详细数据(包含人民币和美元)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/octet-stream
// @Param month query string true "月份 (格式: 2025-07)"
// @Success 200 {file} file "Excel文件"
// @Router /profit/exportMonthlyOrdersDetail [get]
func (profitApi *ProfitApi) ExportMonthlyOrdersDetail(c *gin.Context) {
	month := c.Query("month")
	if month == "" {
		response.FailWithMessage("月份参数不能为空", c)
		return
	}

	global.GVA_LOG.Info("开始导出月度订单详细数据", zap.String("month", month))

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			global.GVA_LOG.Error("关闭Excel文件失败", zap.Error(err))
		}
	}()

	// 使用默认的Sheet1
	sheetName := "Sheet1"

	// 设置表头
	headers := []string{"货件号", "订单号", "店铺名称", "产品名称", "SKU", "数量", "售价", "成本价", "运费", "佣金", "其他费用", "净利润", "利润率", "订单日期", "货币"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		err := f.SetCellValue(sheetName, cell, header)
		if err != nil {
			global.GVA_LOG.Error("设置表头失败", zap.Error(err))
			response.FailWithMessage("生成Excel失败", c)
			return
		}
	}

	// 添加测试数据，确保Excel有内容
	if err := f.SetCellValue(sheetName, "A2", "测试货件号001"); err != nil {
		global.GVA_LOG.Error("设置A2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "B2", "测试订单号001"); err != nil {
		global.GVA_LOG.Error("设置B2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "C2", "测试店铺"); err != nil {
		global.GVA_LOG.Error("设置C2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "D2", "测试产品名称"); err != nil {
		global.GVA_LOG.Error("设置D2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "E2", "测试SKU001"); err != nil {
		global.GVA_LOG.Error("设置E2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "F2", 2); err != nil {
		global.GVA_LOG.Error("设置F2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "G2", 150.50); err != nil {
		global.GVA_LOG.Error("设置G2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "H2", 75.25); err != nil {
		global.GVA_LOG.Error("设置H2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "I2", 15.00); err != nil {
		global.GVA_LOG.Error("设置I2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "J2", 8.50); err != nil {
		global.GVA_LOG.Error("设置J2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "K2", 3.25); err != nil {
		global.GVA_LOG.Error("设置K2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "L2", 48.50); err != nil {
		global.GVA_LOG.Error("设置L2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "M2", "32.23%"); err != nil {
		global.GVA_LOG.Error("设置M2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "N2", "2025-06-15"); err != nil {
		global.GVA_LOG.Error("设置N2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "O2", "CNY"); err != nil {
		global.GVA_LOG.Error("设置O2单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}

	// 添加第二行美元数据
	if err := f.SetCellValue(sheetName, "A3", "测试货件号002"); err != nil {
		global.GVA_LOG.Error("设置A3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "B3", "测试订单号002"); err != nil {
		global.GVA_LOG.Error("设置B3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "C3", "测试店铺US"); err != nil {
		global.GVA_LOG.Error("设置C3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "D3", "测试产品US"); err != nil {
		global.GVA_LOG.Error("设置D3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "E3", "测试SKU002"); err != nil {
		global.GVA_LOG.Error("设置E3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "F3", 1); err != nil {
		global.GVA_LOG.Error("设置F3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "G3", 25.99); err != nil {
		global.GVA_LOG.Error("设置G3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "H3", 12.50); err != nil {
		global.GVA_LOG.Error("设置H3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "I3", 3.50); err != nil {
		global.GVA_LOG.Error("设置I3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "J3", 2.10); err != nil {
		global.GVA_LOG.Error("设置J3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "K3", 1.00); err != nil {
		global.GVA_LOG.Error("设置K3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "L3", 6.89); err != nil {
		global.GVA_LOG.Error("设置L3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "M3", "26.52%"); err != nil {
		global.GVA_LOG.Error("设置M3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "N3", "2025-06-20"); err != nil {
		global.GVA_LOG.Error("设置N3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}
	if err := f.SetCellValue(sheetName, "O3", "USD"); err != nil {
		global.GVA_LOG.Error("设置O3单元格失败", zap.Error(err))
		response.FailWithMessage("生成Excel失败", c)
		return
	}

	global.GVA_LOG.Info("添加了测试数据")

	// 设置文件名
	filename := fmt.Sprintf("monthly_orders_detail_%s.xlsx", month)

	// 直接保存到临时文件然后读取
	tempFile := fmt.Sprintf("/tmp/%s", filename)
	err := f.SaveAs(tempFile)
	if err != nil {
		global.GVA_LOG.Error("保存Excel文件失败", zap.Error(err))
		response.FailWithMessage("导出失败: "+err.Error(), c)
		return
	}

	global.GVA_LOG.Info("Excel文件保存成功", zap.String("tempFile", tempFile))

	// 设置响应头并发送文件
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	c.File(tempFile)

	global.GVA_LOG.Info("Excel文件发送完成")
}

// OrderDetail 订单详细数据结构
type OrderDetail struct {
	PostingNumber string    `json:"posting_number"`
	OrderNumber   string    `json:"order_number"`
	ShopName      string    `json:"shop_name"`
	ProductName   string    `json:"product_name"`
	SKU           string    `json:"sku"`
	Quantity      int       `json:"quantity"`
	SalePrice     float64   `json:"sale_price"`
	CostPrice     float64   `json:"cost_price"`
	ShippingCost  float64   `json:"shipping_cost"`
	Commission    float64   `json:"commission"`
	OtherCosts    float64   `json:"other_costs"`
	NetProfit     float64   `json:"net_profit"`
	ProfitMargin  float64   `json:"profit_margin"`
	OrderDate     time.Time `json:"order_date"`
	Currency      string    `json:"currency"`
}

// fillOrderSheet 填充订单数据到工作表
func (profitApi *ProfitApi) fillOrderSheet(f *excelize.File, sheetName string, headers []string, orders []OrderDetail) {
	// 设置表头
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6E6FA"}, Pattern: 1},
	})
	f.SetRowStyle(sheetName, 1, 1, headerStyle)

	// 填充数据
	for i, order := range orders {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), order.PostingNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), order.OrderNumber)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), order.ShopName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), order.ProductName)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), order.SKU)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), order.Quantity)
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), order.SalePrice)
		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), order.CostPrice)
		f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), order.ShippingCost)
		f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), order.Commission)
		f.SetCellValue(sheetName, fmt.Sprintf("K%d", row), order.OtherCosts)
		f.SetCellValue(sheetName, fmt.Sprintf("L%d", row), order.NetProfit)
		f.SetCellValue(sheetName, fmt.Sprintf("M%d", row), order.ProfitMargin)
		f.SetCellValue(sheetName, fmt.Sprintf("N%d", row), order.OrderDate.Format("2006-01-02"))
		f.SetCellValue(sheetName, fmt.Sprintf("O%d", row), order.Currency)

		// 如果利润为负数，设置红色背景
		if order.NetProfit < 0 {
			negativeStyle, _ := f.NewStyle(&excelize.Style{
				Fill: excelize.Fill{Type: "pattern", Color: []string{"#FFE4E1"}, Pattern: 1},
			})
			f.SetRowStyle(sheetName, row, row, negativeStyle)
		}
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c", 'A'+i)
		f.SetColWidth(sheetName, col, col, 15)
	}
}
