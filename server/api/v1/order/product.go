package order

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	orderReq "github.com/flipped-aurora/gin-vue-admin/server/model/order/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProductApi struct{}

// CreateProduct 创建产品
// @Tags Product
// @Summary 创建产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.CreateProductRequest true "创建产品"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /product/createProduct [post]
func (productApi *ProductApi) CreateProduct(c *gin.Context) {
	var product orderReq.CreateProductRequest
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProductService.CreateProduct(product)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败", c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteProduct 删除产品
// @Tags Product
// @Summary 删除产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.DeleteProductRequest true "删除产品"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /product/deleteProduct [delete]
func (productApi *ProductApi) DeleteProduct(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProductService.DeleteProduct(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteProductByIds 批量删除产品
// @Tags Product
// @Summary 批量删除产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /product/deleteProductByIds [delete]
func (productApi *ProductApi) DeleteProductByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	var ids []uint
	for _, id := range IDs {
		ID, err := strconv.Atoi(id)
		if err != nil {
			response.FailWithMessage("ID格式错误", c)
			return
		}
		ids = append(ids, uint(ID))
	}

	err := service.ServiceGroupApp.OrderServiceGroup.ProductService.DeleteProductByIds(ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateProduct 更新产品
// @Tags Product
// @Summary 更新产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.UpdateProductRequest true "更新产品"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /product/updateProduct [put]
func (productApi *ProductApi) UpdateProduct(c *gin.Context) {
	var product orderReq.UpdateProductRequest
	err := c.ShouldBindJSON(&product)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProductService.UpdateProduct(product)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindProduct 用id查询产品
// @Tags Product
// @Summary 用id查询产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.GetProductRequest true "用id查询产品"
// @Success 200 {object} response.Response{data=order.Product,msg=string} "查询成功"
// @Router /product/findProduct [get]
func (productApi *ProductApi) FindProduct(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}

	product, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.GetProduct(uint(id))
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
		return
	}
	response.OkWithDetailed(product, "查询成功", c)
}

// GetProductList 分页获取产品列表
// @Tags Product
// @Summary 分页获取产品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query orderReq.ProductSearch true "分页获取产品列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /product/getProductList [get]
func (productApi *ProductApi) GetProductList(c *gin.Context) {
	var pageInfo orderReq.ProductSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.GetProductInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetProductSummary 获取产品汇总统计
// @Tags Product
// @Summary 获取产品汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=order.ProductSummary,msg=string} "获取成功"
// @Router /product/getProductSummary [get]
func (productApi *ProductApi) GetProductSummary(c *gin.Context) {
	summary, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.GetProductSummary()
	if err != nil {
		global.GVA_LOG.Error("获取产品汇总失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(summary, "获取成功", c)
}

// GetProductBySKU 根据SKU获取产品信息
// @Tags Product
// @Summary 根据SKU获取产品信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param sku query string true "SKU"
// @Success 200 {object} response.Response{data=order.Product,msg=string} "获取成功"
// @Router /product/getProductBySKU [get]
func (productApi *ProductApi) GetProductBySKU(c *gin.Context) {
	sku := c.Query("sku")
	if sku == "" {
		response.FailWithMessage("SKU不能为空", c)
		return
	}

	product, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.GetProductBySKU(sku)
	if err != nil {
		global.GVA_LOG.Error("获取产品信息失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithDetailed(product, "获取成功", c)
}

// BatchUpdateProduct 批量更新产品
// @Tags Product
// @Summary 批量更新产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.BatchUpdateProductRequest true "批量更新产品"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /product/batchUpdateProduct [put]
func (productApi *ProductApi) BatchUpdateProduct(c *gin.Context) {
	var req orderReq.BatchUpdateProductRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ServiceGroupApp.OrderServiceGroup.ProductService.BatchUpdateProduct(req)
	if err != nil {
		global.GVA_LOG.Error("批量更新失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("批量更新成功", c)
}

// SyncProductsFromOrders 从订单同步产品
// @Tags Product
// @Summary 从订单同步产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body orderReq.SyncProductsFromOrdersRequest true "从订单同步产品"
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "同步成功"
// @Router /product/syncProductsFromOrders [post]
func (productApi *ProductApi) SyncProductsFromOrders(c *gin.Context) {
	var req orderReq.SyncProductsFromOrdersRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	syncCount, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.SyncProductsFromOrders(req)
	if err != nil {
		global.GVA_LOG.Error("同步产品失败!", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(map[string]interface{}{
		"syncCount": syncCount,
	}, "同步成功，共同步了 "+strconv.Itoa(syncCount)+" 个产品", c)
}

// SyncProductImagesFromOrders 从订单同步产品图片
// @Tags Product
// @Summary 从订单同步产品图片
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "同步成功"
// @Router /product/syncProductImagesFromOrders [post]
func (productApi *ProductApi) SyncProductImagesFromOrders(c *gin.Context) {
	syncCount, err := service.ServiceGroupApp.OrderServiceGroup.ProductService.SyncProductImagesFromOrders()
	if err != nil {
		global.GVA_LOG.Error("同步产品图片失败!", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(map[string]interface{}{
		"syncCount": syncCount,
	}, "同步成功，共同步了 "+strconv.Itoa(syncCount)+" 个产品图片", c)
}
