package shops

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/shops"
    shopsReq "github.com/flipped-aurora/gin-vue-admin/server/model/shops/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type ShopApi struct {}



// CreateShop 创建商店
// @Tags Shop
// @Summary 创建商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.Shop true "创建商店"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /shop/createShop [post]
func (shopApi *ShopApi) CreateShop(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var shop shops.Shop
	err := c.ShouldBindJSON(&shop)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = shopService.CreateShop(ctx,&shop)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteShop 删除商店
// @Tags Shop
// @Summary 删除商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.Shop true "删除商店"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /shop/deleteShop [delete]
func (shopApi *ShopApi) DeleteShop(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	err := shopService.DeleteShop(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteShopByIds 批量删除商店
// @Tags Shop
// @Summary 批量删除商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /shop/deleteShopByIds [delete]
func (shopApi *ShopApi) DeleteShopByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := shopService.DeleteShopByIds(ctx,IDs)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateShop 更新商店
// @Tags Shop
// @Summary 更新商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.Shop true "更新商店"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /shop/updateShop [put]
func (shopApi *ShopApi) UpdateShop(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var shop shops.Shop
	err := c.ShouldBindJSON(&shop)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = shopService.UpdateShop(ctx,shop)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindShop 用id查询商店
// @Tags Shop
// @Summary 用id查询商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询商店"
// @Success 200 {object} response.Response{data=shops.Shop,msg=string} "查询成功"
// @Router /shop/findShop [get]
func (shopApi *ShopApi) FindShop(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	reshop, err := shopService.GetShop(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(reshop, c)
}
// GetShopList 分页获取商店列表
// @Tags Shop
// @Summary 分页获取商店列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query shopsReq.ShopSearch true "分页获取商店列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /shop/getShopList [get]
func (shopApi *ShopApi) GetShopList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo shopsReq.ShopSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := shopService.GetShopInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetShopPublic 不需要鉴权的商店接口
// @Tags Shop
// @Summary 不需要鉴权的商店接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /shop/getShopPublic [get]
func (shopApi *ShopApi) GetShopPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    shopService.GetShopPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的商店接口信息",
    }, "获取成功", c)
}
