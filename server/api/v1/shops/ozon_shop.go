package shops

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/shops"
	shopsReq "github.com/flipped-aurora/gin-vue-admin/server/model/shops/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type OzonShopApi struct{}

// CreateOzonShop 创建店铺授权
// @Tags OzonShop
// @Summary 创建店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.OzonShop true "创建店铺授权"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /ozonShop/createOzonShop [post]
func (ozonShopApi *OzonShopApi) CreateOzonShop(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var ozonShop shops.OzonShop
	err := c.ShouldBindJSON(&ozonShop)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	_, err1 := IsOzonAuthValid(*ozonShop.ClientID, *ozonShop.APIKey)
	if err1 != nil {
		global.GVA_LOG.Error("创建失败:"+err1.Error(), zap.Error(err))
		response.FailWithMessage("创建失败:请检查你的授权参数", c)
		return
	}
	err = ozonShopService.CreateOzonShop(ctx, &ozonShop)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// IsOzonAuthValid 验证 Ozon API 授权是否有效
func IsOzonAuthValid(clientID, apiKey string) (bool, error) {
	url := "https://api-seller.ozon.ru/v1/description-category/tree"

	// 请求体示例（尽量简单）
	payload := map[string]interface{}{
		"language": "DEFAULT",
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return false, fmt.Errorf("请求体编码失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return false, fmt.Errorf("构建请求失败: %w", err)
	}
	req.Header.Set("Client-Id", clientID)
	req.Header.Set("Api-Key", apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return false, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)

	if resp.StatusCode == http.StatusOK {
		return true, nil
	}

	// 输出 403 或其他错误信息
	return false, fmt.Errorf("状态码: %d (%s)\n响应内容: %s",
		resp.StatusCode, http.StatusText(resp.StatusCode), string(body))
}

// stringPtr 是一个辅助函数，用于创建 string 指针
func stringPtr(s string) *string {
	return &s
}

// DeleteOzonShop 删除店铺授权
// @Tags OzonShop
// @Summary 删除店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.OzonShop true "删除店铺授权"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /ozonShop/deleteOzonShop [delete]
func (ozonShopApi *OzonShopApi) DeleteOzonShop(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := ozonShopService.DeleteOzonShop(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteOzonShopByIds 批量删除店铺授权
// @Tags OzonShop
// @Summary 批量删除店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /ozonShop/deleteOzonShopByIds [delete]
func (ozonShopApi *OzonShopApi) DeleteOzonShopByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := ozonShopService.DeleteOzonShopByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateOzonShop 更新店铺授权
// @Tags OzonShop
// @Summary 更新店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body shops.OzonShop true "更新店铺授权"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /ozonShop/updateOzonShop [put]
func (ozonShopApi *OzonShopApi) UpdateOzonShop(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var ozonShop shops.OzonShop
	err := c.ShouldBindJSON(&ozonShop)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = ozonShopService.UpdateOzonShop(ctx, ozonShop)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindOzonShop 用id查询店铺授权
// @Tags OzonShop
// @Summary 用id查询店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询店铺授权"
// @Success 200 {object} response.Response{data=shops.OzonShop,msg=string} "查询成功"
// @Router /ozonShop/findOzonShop [get]
func (ozonShopApi *OzonShopApi) FindOzonShop(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reozonShop, err := ozonShopService.GetOzonShop(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reozonShop, c)
}

// GetOzonShopList 分页获取店铺授权列表
// @Tags OzonShop
// @Summary 分页获取店铺授权列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query shopsReq.OzonShopSearch true "分页获取店铺授权列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /ozonShop/getOzonShopList [get]
func (ozonShopApi *OzonShopApi) GetOzonShopList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo shopsReq.OzonShopSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := ozonShopService.GetOzonShopInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetOzonShopPublic 不需要鉴权的店铺授权接口
// @Tags OzonShop
// @Summary 不需要鉴权的店铺授权接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /ozonShop/getOzonShopPublic [get]
func (ozonShopApi *OzonShopApi) GetOzonShopPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	ozonShopService.GetOzonShopPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的店铺授权接口信息",
	}, "获取成功", c)
}
