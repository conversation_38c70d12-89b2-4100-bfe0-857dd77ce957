package ozoneOrder

import (
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/ozoneOrder"
	ozoneOrderReq "github.com/flipped-aurora/gin-vue-admin/server/model/ozoneOrder/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"github.com/goccy/go-json"
	"go.uber.org/zap"
)

type OzoneOrderDetailApi struct{}

func (ozoneOrderDetailApi *OzoneOrderDetailApi) SynchronizeOrders(c *gin.Context) {

	client := utils.NewHttpClient()

	client.SetHeader("Client-Id", "2208200")
	client.SetHeader("Api-Key", "8a80f827-26bc-43c3-9121-ec283b852db5")

	//// GET 示例
	//resp, err := client.Get("https://httpbin.org/get")
	//if err != nil {
	//	fmt.Println("GET 错误:", err)
	//} else {
	//	fmt.Println("GET 响应:", resp)
	//}

	// POST 示例
	jsonData := []byte(`{
    "dir": "ASC",
    "filter": {
        "cutoff_from": "2025-01-01T00:00:00Z",
        "cutoff_to": "2025-12-31T23:59:59Z",
        "delivery_method_id": [],
        "provider_id": [],
        "status": "awaiting_deliver",
        "warehouse_id": []
    },
    "limit": 100,
    "offset": 0,
    "with": {
        "analytics_data": true,
        "barcodes": true,
        "financial_data": true,
        "translit": true
    }
}`)
	resp, err := client.Post("https://api-seller.ozon.ru/v3/posting/fbs/unfulfilled/list", jsonData)
	if err != nil {
		fmt.Println("POST 错误:", err)
	} else {
		fmt.Println("POST 响应:", resp)
	}
	response.OkWithMessage("创建成功", c)

}

// CreateOzoneOrderDetail 创建ozone订单详情
// @Tags OzoneOrderDetail
// @Summary 创建ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ozoneOrder.OzoneOrderDetail true "创建ozone订单详情"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /ozoneOrderDetail/createOzoneOrderDetail [post]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) CreateOzoneOrderDetail(c *gin.Context) {

	client := utils.NewHttpClient()

	client.SetHeader("Client-Id", "2208200")
	client.SetHeader("Api-Key", "8a80f827-26bc-43c3-9121-ec283b852db5")

	// POST 示例
	jsonData := []byte(`{
    "dir": "ASC",
    "filter": {
        "cutoff_from": "2025-01-01T00:00:00Z",
        "cutoff_to": "2025-12-31T23:59:59Z",
        "delivery_method_id": [],
        "provider_id": [],
        "status": "awaiting_deliver",
        "warehouse_id": []
    },
    "limit": 100,
    "offset": 0,
    "with": {
        "analytics_data": true,
        "barcodes": true,
        "financial_data": true,
        "translit": true
    }
}`)
	resp, err := client.Post("https://api-seller.ozon.ru/v3/posting/fbs/unfulfilled/list", jsonData)
	if err != nil {
		fmt.Println("POST 错误:", err)
	} else {
		//fmt.Println("POST 响应:", resp)
	}

	type OzonPostingsResponse struct {
		Result struct {
			Postings []ozoneOrder.OzoneOrderDetail `json:"postings"`
		} `json:"result"`
	}
	var result OzonPostingsResponse
	err = json.Unmarshal([]byte(resp), &result)
	if err != nil {
		fmt.Println("解析响应失败:", err)
		return
	}

	// 创建业务用Context
	ctx := c.Request.Context()

	err = ozoneOrderDetailService.CreateOzoneOrderDetail(ctx, &result.Result.Postings)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteOzoneOrderDetail 删除ozone订单详情
// @Tags OzoneOrderDetail
// @Summary 删除ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ozoneOrder.OzoneOrderDetail true "删除ozone订单详情"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /ozoneOrderDetail/deleteOzoneOrderDetail [delete]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) DeleteOzoneOrderDetail(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := ozoneOrderDetailService.DeleteOzoneOrderDetail(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteOzoneOrderDetailByIds 批量删除ozone订单详情
// @Tags OzoneOrderDetail
// @Summary 批量删除ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /ozoneOrderDetail/deleteOzoneOrderDetailByIds [delete]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) DeleteOzoneOrderDetailByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := ozoneOrderDetailService.DeleteOzoneOrderDetailByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateOzoneOrderDetail 更新ozone订单详情
// @Tags OzoneOrderDetail
// @Summary 更新ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body ozoneOrder.OzoneOrderDetail true "更新ozone订单详情"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /ozoneOrderDetail/updateOzoneOrderDetail [put]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) UpdateOzoneOrderDetail(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var ozoneOrderDetail ozoneOrder.OzoneOrderDetail
	err := c.ShouldBindJSON(&ozoneOrderDetail)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = ozoneOrderDetailService.UpdateOzoneOrderDetail(ctx, ozoneOrderDetail)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindOzoneOrderDetail 用id查询ozone订单详情
// @Tags OzoneOrderDetail
// @Summary 用id查询ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询ozone订单详情"
// @Success 200 {object} response.Response{data=ozoneOrder.OzoneOrderDetail,msg=string} "查询成功"
// @Router /ozoneOrderDetail/findOzoneOrderDetail [get]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) FindOzoneOrderDetail(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reozoneOrderDetail, err := ozoneOrderDetailService.GetOzoneOrderDetail(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reozoneOrderDetail, c)
}

// GetOzoneOrderDetailList 分页获取ozone订单详情列表
// @Tags OzoneOrderDetail
// @Summary 分页获取ozone订单详情列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query ozoneOrderReq.OzoneOrderDetailSearch true "分页获取ozone订单详情列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /ozoneOrderDetail/getOzoneOrderDetailList [get]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) GetOzoneOrderDetailList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo ozoneOrderReq.OzoneOrderDetailSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := ozoneOrderDetailService.GetOzoneOrderDetailInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetOzoneOrderDetailPublic 不需要鉴权的ozone订单详情接口
// @Tags OzoneOrderDetail
// @Summary 不需要鉴权的ozone订单详情接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /ozoneOrderDetail/getOzoneOrderDetailPublic [get]
func (ozoneOrderDetailApi *OzoneOrderDetailApi) GetOzoneOrderDetailPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	ozoneOrderDetailService.GetOzoneOrderDetailPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的ozone订单详情接口信息",
	}, "获取成功", c)
}
