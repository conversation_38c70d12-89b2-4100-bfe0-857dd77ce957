-- 清理重复订单数据的SQL脚本
USE xrt;

-- 1. 查看重复数据统计
SELECT 
    '重复订单统计' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT posting_number) as unique_orders,
    COUNT(*) - COUNT(DISTINCT posting_number) as duplicate_count
FROM `order`;

-- 2. 显示重复最多的订单
SELECT posting_number, COUNT(*) as count 
FROM `order`
GROUP BY posting_number
HAVING COUNT(*) > 1
ORDER BY count DESC
LIMIT 5;

-- 3. 创建临时表保存唯一订单（保留最新的记录）
CREATE TEMPORARY TABLE temp_unique_orders AS
SELECT
    MIN(id) as keep_id,
    posting_number,
    COUNT(*) as duplicate_count
FROM `order`
GROUP BY posting_number;

-- 4. 删除重复记录（保留每个posting_number的最新记录）
DELETE o1 FROM `order` o1
INNER JOIN `order` o2
WHERE o1.posting_number = o2.posting_number
AND o1.id > o2.id;

-- 5. 验证清理结果
SELECT
    '清理后统计' as info,
    COUNT(*) as total_records,
    COUNT(DISTINCT posting_number) as unique_orders,
    COUNT(*) - COUNT(DISTINCT posting_number) as should_be_zero
FROM `order`;

-- 6. 添加唯一性约束（防止未来重复）
ALTER TABLE `order`
ADD CONSTRAINT uk_posting_number 
UNIQUE (posting_number);
