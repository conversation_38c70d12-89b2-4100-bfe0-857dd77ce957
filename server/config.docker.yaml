# github.com/flipped-aurora/gin-vue-admin/server Global Configuration

# jwt configuration
jwt:
  signing-key: qmPlus
  expires-time: 7d
  buffer-time: 1d
  issuer: qmPlus
# zap logger configuration
zap:
  level: info
  format: console
  prefix: "[github.com/flipped-aurora/gin-vue-admin/server]"
  director: log
  show-line: true
  encode-level: LowercaseColorLevelEncoder
  stacktrace-key: stacktrace
  log-in-console: true

# redis configuration
redis:
  db: 0
  addr: **********:6379
  password: ""

# mongo configuration
mongo:
  coll: ''
  options: ''
  database: ''
  username: ''
  password: ''
  min-pool-size: 0
  max-pool-size: 100
  socket-timeout-ms: 0
  connect-timeout-ms: 0
  is-zap: false
  hosts:
    - host: ''
      port: ''

# email configuration
email:
  to: <EMAIL>
  port: 465
  from: <EMAIL>
  host: smtp.163.com
  is-ssl: true
  secret: xxx
  nickname: test

# system configuration
system:
  env: public  # Change to "develop" to skip authentication for development mode
  addr: 8888
  db-type: mysql
  oss-type: local    # 控制oss选择走本地还是 七牛等其他仓 自行增加其他oss仓可以在 server/utils/upload/upload.go 中 NewOss函数配置
  use-redis: false     # 使用redis
  use-mongo: false     # 使用mongo
  use-multipoint: false
  # IP限制次数 一个小时15000次
  iplimit-count: 15000
  #  IP限制一个小时
  iplimit-time: 3600

# captcha configuration
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0 # 0代表一直开启，大于0代表限制次数
  open-captcha-timeout: 3600 # open-captcha大于0时才生效

# mysql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
mysql:
  path: "xrt-mysql"  # Docker容器内的MySQL主机名
  port: "3306"
  config: "charset=utf8mb4&parseTime=True&loc=Local"
  db-name: "qmPlus"  # 注意：这里使用Docker默认数据库名
  username: "gva"
  password: "Aa@6447985"
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

# pgsql connect configuration
# 未初始化之前请勿手动修改数据库信息！！！如果一定要手动初始化请看（https://gin-vue-admin.com/docs/first_master）
pgsql:
  path: ""
  port: ""
  config: ""
  db-name: ""
  username: ""
  password: ""
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

db-list:
  - disable: true # 是否禁用
    type: "" # 数据库的类型,目前支持mysql、pgsql
    alias-name: "" # 数据库的名称,注意: alias-name 需要在db-list中唯一
    path: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    max-idle-conns: 10
    max-open-conns: 100
    log-mode: ""
    log-zap: false


# local configuration
local:
  path: uploads/file
  store-path: uploads/file

# autocode configuration
autocode:
  transfer-restart: true
  # root 自动适配项目根目录
  # 请不要手动配置,他会在项目加载的时候识别出根路径
  root: ""
  server: /server
  server-plug: /plugin/%s
  server-api: /api/v1/%s
  server-initialize: /initialize
  server-model: /model/%s
  server-request: /model/%s/request/
  server-router: /router/%s
  server-service: /service/%s
  web: /web/src
  web-api: /api
  web-form: /view
  web-table: /view

# qiniu configuration (请自行七牛申请对应的 公钥 私钥 bucket 和 域名地址)
qiniu:
  zone: ZoneHuaDong
  bucket: ""
  img-path: ""
  use-https: false
  access-key: ""
  secret-key: ""
  use-cdn-domains: false

# aliyun oss configuration
aliyun-oss:
  endpoint: yourEndpoint
  access-key-id: yourAccessKeyId
  access-key-secret: yourAccessKeySecret
  bucket-name: yourBucketName
  bucket-url: yourBucketUrl
  base-path: yourBasePath

# tencent cos configuration
tencent-cos:
  bucket: xxxxx-10005608
  region: ap-shanghai
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# aws s3 configuration (minio compatible)
aws-s3:
  bucket: xxxxx-10005608
  region: ap-shanghai
  endpoint: ""
  s3-force-path-style: false
  disable-ssl: false
  secret-id: your-secret-id
  secret-key: your-secret-key
  base-url: https://gin.vue.admin
  path-prefix: github.com/flipped-aurora/gin-vue-admin/server

# huawei obs configuration
hua-wei-obs:
  path: you-path
  bucket: you-bucket
  endpoint: you-endpoint
  access-key: you-access-key
  secret-key: you-secret-key

# excel configuration
excel:
  dir: ./resource/excel/

# timer task db clear table
Timer:
  start: true
  spec: "@daily"  # 定时任务详细配置参考 https://pkg.go.dev/github.com/robfig/cron/v3
  detail:
    - tableName: sys_operation_records
      compareField: created_at
      interval: 2160h
    - tableName: jwt_blacklists
      compareField: created_at
      interval: 168h

# 跨域配置
# 需要配合 server/initialize/router.go -> `Router.Use(middleware.CorsByRules())` 使用
cors:
  mode: whitelist # 放行模式: allow-all, 放行全部; whitelist, 白名单模式, 来自白名单内域名的请求添加 cors 头; strict-whitelist 严格白名单模式, 白名单外的请求一律拒绝
  whitelist:
    - allow-origin: example1.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值
    - allow-origin: example2.com
      allow-headers: content-type
      allow-methods: GET, POST
      expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
      allow-credentials: true # 布尔值