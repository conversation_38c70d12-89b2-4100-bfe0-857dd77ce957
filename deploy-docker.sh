#!/bin/bash

# XRT Docker 部署脚本
# 解决 MySQL 依赖冲突问题，使用 Docker 完整部署

set -e

echo "=== XRT Docker 部署开始 ==="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要 root 权限运行"
        exit 1
    fi
}

# 清理有问题的 MySQL 仓库
cleanup_mysql_repos() {
    log_info "清理有问题的 MySQL 仓库..."
    
    # 移除 MySQL 仓库文件
    if [ -f /etc/yum.repos.d/mysql-community.repo ]; then
        rm -f /etc/yum.repos.d/mysql-community.repo
        log_info "已移除 MySQL 社区仓库"
    fi
    
    # 清理 yum 缓存
    yum clean all
    log_info "已清理 yum 缓存"
}

# 检查并安装 Docker
install_docker() {
    log_info "检查 Docker 安装状态..."
    
    if command -v docker &> /dev/null; then
        log_info "Docker 已安装"
        docker --version
    else
        log_info "安装 Docker..."
        yum install -y yum-utils
        yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
        yum install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
        
        # 启动 Docker 服务
        systemctl start docker
        systemctl enable docker
        log_info "Docker 安装完成"
    fi
    
    # 检查 Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose 已安装"
        docker-compose --version
    else
        log_info "安装 Docker Compose..."
        curl -L "https://github.com/docker/compose/releases/download/v2.20.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
        log_info "Docker Compose 安装完成"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p mysql/conf.d
    mkdir -p mysql/init
    mkdir -p nginx/conf.d
    mkdir -p uploads
    
    log_info "目录创建完成"
}

# 创建 MySQL 配置文件
create_mysql_config() {
    log_info "创建 MySQL 配置文件..."
    
    cat > mysql/conf.d/my.cnf << 'EOF'
[mysqld]
# 基本设置
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
default-time-zone='+8:00'

# 性能优化
max_connections=1000
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_flush_log_at_trx_commit=2
innodb_flush_method=O_DIRECT

# 日志设置
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
EOF

    log_info "MySQL 配置文件创建完成"
}

# 创建 Nginx 配置
create_nginx_config() {
    log_info "创建 Nginx 配置..."
    
    # 确保使用正确的 nginx.conf 路径
    if [ ! -f nginx.conf ]; then
        log_warn "nginx.conf 不存在，创建默认配置"
        cp nginx.conf nginx/nginx.conf 2>/dev/null || true
    fi
    
    log_info "Nginx 配置完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端应用..."
    
    if [ ! -d "web/dist" ]; then
        log_warn "前端 dist 目录不存在，请先构建前端应用"
        log_info "运行以下命令构建前端："
        log_info "cd web && npm install && npm run build"
        exit 1
    fi
    
    log_info "前端应用已构建"
}

# 构建后端
build_backend() {
    log_info "构建后端应用..."
    
    if [ ! -f "server/xrt-server" ]; then
        log_warn "后端可执行文件不存在，请先构建后端应用"
        log_info "运行以下命令构建后端："
        log_info "cd server && go build -o xrt-server main.go"
        exit 1
    fi
    
    log_info "后端应用已构建"
}

# 启动服务
start_services() {
    log_info "启动 Docker 服务..."
    
    # 停止现有服务
    docker-compose down 2>/dev/null || true
    
    # 启动服务
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose ps
    
    log_info "服务启动完成"
}

# 检查服务健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    # 检查 MySQL
    log_info "检查 MySQL 连接..."
    for i in {1..30}; do
        if docker exec xrt-mysql mysqladmin ping -h localhost -u root -pAa@6447985 &>/dev/null; then
            log_info "MySQL 连接正常"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "MySQL 连接失败"
            exit 1
        fi
        sleep 2
    done
    
    # 检查后端服务
    log_info "检查后端服务..."
    for i in {1..30}; do
        if curl -s http://localhost:8888/health &>/dev/null; then
            log_info "后端服务正常"
            break
        fi
        if [ $i -eq 30 ]; then
            log_error "后端服务连接失败"
            exit 1
        fi
        sleep 2
    done
    
    # 检查前端服务
    log_info "检查前端服务..."
    if curl -s http://localhost &>/dev/null; then
        log_info "前端服务正常"
    else
        log_error "前端服务连接失败"
        exit 1
    fi
    
    log_info "所有服务健康检查通过"
}

# 显示部署信息
show_deployment_info() {
    log_info "=== 部署完成 ==="
    echo ""
    log_info "访问信息："
    log_info "前端地址: http://$(hostname -I | awk '{print $1}')"
    log_info "后端API: http://$(hostname -I | awk '{print $1}'):8888"
    log_info "MySQL: $(hostname -I | awk '{print $1}'):3306"
    echo ""
    log_info "默认管理员账号："
    log_info "用户名: admin"
    log_info "密码: 123456"
    echo ""
    log_info "常用命令："
    log_info "查看日志: docker-compose logs -f"
    log_info "重启服务: docker-compose restart"
    log_info "停止服务: docker-compose down"
    echo ""
}

# 主函数
main() {
    check_root
    cleanup_mysql_repos
    install_docker
    create_directories
    create_mysql_config
    create_nginx_config
    build_frontend
    build_backend
    start_services
    check_health
    show_deployment_info
}

# 执行主函数
main "$@"
