version: '3.8'

services:
  xrt-server:
    image: golang:1.21-alpine
    container_name: xrt-server
    restart: always
    ports:
      - "8888:8888"
    environment:
      - TZ=Asia/Shanghai
      - GIN_MODE=release
    volumes:
      - ./server:/app
      - ./uploads:/app/uploads
    working_dir: /app
    command: sh -c "apk add --no-cache ca-certificates tzdata && ./xrt-server -c config.yaml"
    networks:
      - xrt-network
    depends_on:
      - xrt-mysql
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8888/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  xrt-web:
    image: nginx:alpine
    container_name: xrt-web
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./web/dist:/usr/share/nginx/html
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
    networks:
      - xrt-network
    depends_on:
      - xrt-server
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  xrt-mysql:
    image: mysql:8.0
    container_name: xrt-mysql
    restart: always
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=Aa@6447985
      - MYSQL_DATABASE=qmPlus
      - MYSQL_USER=gva
      - MYSQL_PASSWORD=Aa@6447985
      - TZ=Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - xrt-network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-pAa@6447985"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  mysql_data:
    driver: local

networks:
  xrt-network:
    driver: bridge
