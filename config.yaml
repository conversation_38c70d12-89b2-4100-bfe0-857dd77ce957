# jwt配置
jwt:
  signing-key: 'qmPlus'
  expires-time: 7d
  buffer-time: 1d
  issuer: 'qmPlus'

# zap logger配置
zap:
  level: 'info'
  format: 'console'
  prefix: '[gin-vue-admin]'
  director: 'log'
  show-line: true
  encode-level: 'LowercaseColorLevelEncoder'
  stacktrace-key: 'stacktrace'
  log-in-console: true

# 系统配置
system:
  env: 'public'
  addr: 8888
  db-type: 'mysql'
  oss-type: 'local'
  use-multipoint: false
  use-redis: false
  use-mongo: false
  use-strict-auth: false
  iplimit-count: 15000
  iplimit-time: 3600
  router-prefix: '/api'

# mysql配置
mysql:
  path: 'xrt-mysql'
  port: '3306'
  config: 'charset=utf8mb4&parseTime=True&loc=Local'
  db-name: 'xrt'
  username: 'root'
  password: 'Asdf1357'
  prefix: ''
  singular: false
  engine: ''
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: 'error'
  log-zap: false

# captcha配置
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600

# local configuration
local:
  path: 'uploads/file'
  store-path: 'uploads/file'

# autocode configuration
autocode:
  transfer-restart: true
  root: ""
  server: /server
  server-plug: /plugin/%s
  server-api: /api/v1/%s
  server-initialize: /initialize
  server-model: /model/%s
  server-request: /model/%s/request/
  server-router: /router/%s
  server-service: /service/%s
  web: /web/src
  web-api: /api
  web-form: /view
  web-table: /view

# 跨域配置
cors:
  mode: 'strict-whitelist'
  whitelist:
    - allow-origin: 'http://**************'
      allow-headers: 'Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id'
      allow-methods: 'POST, GET, OPTIONS, PUT, DELETE'
      expose-headers: 'Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type'
      allow-credentials: true
    - allow-origin: 'http://localhost'
      allow-headers: 'Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id'
      allow-methods: 'POST, GET, OPTIONS, PUT, DELETE'
      expose-headers: 'Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type'
      allow-credentials: true
