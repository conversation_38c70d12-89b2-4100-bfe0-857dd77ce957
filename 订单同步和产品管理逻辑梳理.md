# 订单同步和产品管理逻辑梳理

## 当前系统架构

### 0. 使用的Ozon API列表

#### 0.1 订单相关API

**API 1**: `POST /v3/posting/fbs/unfulfilled/list` ⭐ **主要订单API**
- **用途**: 获取未发货的FBS订单列表
- **调用位置**: `fetchOzonOrders()` 方法
- **请求参数**:
  ```json
  {
    "dir": "ASC",
    "filter": {
      "cutoff_from": "2024-01-01T00:00:00.000Z",
      "cutoff_to": "2024-12-31T23:59:59.999Z"
    },
    "limit": 1000,
    "offset": 0
  }
  ```
- **返回数据**: FBS订单列表，包含产品信息（offer_id, name等）

**API 2**: `POST /v2/posting/fbo/list`
- **用途**: 获取FBO订单列表（备用）
- **调用位置**: `fetchOzonOrders()` 方法
- **请求参数**:
  ```json
  {
    "dir": "ASC",
    "filter": {
      "since": "2024-01-01T00:00:00.000Z",
      "to": "2024-12-31T23:59:59.999Z"
    },
    "limit": 1000,
    "offset": 0
  }
  ```
- **返回数据**: FBO订单列表

#### 0.2 产品信息API
**API**: `POST /v1/product/info/description`
- **用途**: 通过offer_id获取产品详细信息（主要获取product_id）
- **调用位置**: `getSkuFromOfferIDs()` 方法
- **请求参数**:
  ```json
  {
    "offer_id": "产品的offer_id"
  }
  ```
- **返回数据**: 产品ID、SKU、名称等基本信息

#### 0.3 产品图片API
**API**: `POST /v2/product/pictures/info`
- **用途**: 通过product_id获取产品图片
- **调用位置**: 原来的`GetProductImages()` 方法（已废弃）
- **请求参数**:
  ```json
  {
    "product_id": [123456, 789012]
  }
  ```
- **返回数据**: 产品图片URL列表

#### 0.4 产品属性API ⭐ **核心API**
**API**: `POST /v4/product/info/attributes`
- **用途**: 获取产品完整属性信息（图片+重量+其他属性）
- **调用位置**: `GetProductImagesAndWeights()` 方法
- **请求参数**:
  ```json
  {
    "filter": {
      "sku": ["sku1", "sku2"],
      "visibility": "ALL"
    },
    "limit": 1000,
    "sort_dir": "ASC"
  }
  ```
- **返回数据**:
  ```json
  {
    "result": [
      {
        "id": 123456,
        "offer_id": "product-001",
        "sku": 789012,
        "name": "产品名称",
        "weight": 200,
        "weight_unit": "g",
        "primary_image": "https://cdn.ozon.ru/image.jpg",
        "images": [
          {
            "file_name": "https://cdn.ozon.ru/image.jpg",
            "default": true
          }
        ]
      }
    ]
  }
  ```

#### 0.5 API调用流程图
```
订单同步流程:
1. /v3/posting/fbs/unfulfilled/list → 获取FBS订单数据
2. /v2/posting/fbo/list → 获取FBO订单数据（可选）

产品信息同步流程:
1. offer_id → /v1/product/info/description → 获取product_id和SKU
2. SKU → /v4/product/info/attributes → 获取图片(primary_image)和重量(weight)

旧的图片获取流程（已废弃）:
1. offer_id → /v1/product/info/description → 获取product_id
2. product_id → /v2/product/pictures/info → 获取图片
```

#### 0.6 API认证
- **认证方式**: Header认证
- **必需Headers**:
  - `Client-Id`: 店铺的客户端ID
  - `Api-Key`: 店铺的API密钥
  - `Content-Type`: application/json

#### 0.7 API基础URL
- **生产环境**: `https://api-seller.ozon.ru`
- **配置位置**: `OzonAPIService.baseURL`

#### 0.8 API使用状态总结

| API端点 | 版本 | 用途 | 状态 | 调用位置 |
|---------|------|------|------|----------|
| `/v3/posting/fbs/unfulfilled/list` | v3 | 获取FBS订单 | ✅ 使用中 | `fetchOzonOrders()` |
| `/v2/posting/fbo/list` | v2 | 获取FBO订单 | ✅ 使用中 | `fetchOzonOrders()` |
| `/v1/product/info/description` | v1 | 获取产品基本信息 | ✅ 使用中 | `getSkuFromOfferIDs()` |
| `/v4/product/info/attributes` | v4 | 获取产品属性(图片+重量) | ✅ 使用中 | `GetProductImagesAndWeights()` |
| `/v2/product/pictures/info` | v2 | 获取产品图片 | ❌ 已废弃 | 无 |

#### 0.9 关键数据字段映射

**订单数据字段**:
- `posting_number`: 订单号
- `order_number`: 内部订单号
- `products[].offer_id`: 产品SKU标识
- `products[].name`: 产品名称
- `products[].quantity`: 产品数量

**产品属性字段**:
- `primary_image`: 产品主图URL
- `weight`: 产品重量（克）
- `sku`: 产品SKU编号
- `offer_id`: 产品标识符

#### 0.10 API调用性能分析

**单次订单同步的API调用次数**:
```
假设同步10个订单，每个订单包含2个产品：

1. 订单获取: 1次 (/v3/posting/fbs/unfulfilled/list)
2. SKU获取: 20次 (/v1/product/info/description) - 每个产品1次
3. 属性获取: 1次 (/v4/product/info/attributes) - 批量获取所有产品

总计: 22次API调用
```

**性能瓶颈**:
- ❌ **最大瓶颈**: `getSkuFromOfferIDs()` 中的循环调用v1 API
- ⚠️ **次要瓶颈**: 复杂的API调用链
- ✅ **优化点**: v4 API支持批量获取，性能较好

**API限流风险**:
- Ozon API通常有频率限制
- 大量产品同步时可能触发限流
- 建议添加请求间隔和重试机制

### 1. 订单同步流程

#### 1.1 入口点
- **前端操作**: 订单列表页面点击"同步订单"按钮
- **后端API**: `/order/pullOrders` 
- **服务方法**: `OrderService.PullOrders()`

#### 1.2 核心流程
```
用户点击"同步订单" 
    ↓
OrderService.PullOrders()
    ↓
fetchOzonOrders() - 获取Ozon订单数据
    ↓
saveOzonOrderFast() - 保存订单到数据库
    ↓
createProductsFromOrder() - 创建产品记录 [新增]
    ↓
asyncUpdateOrderImages() - 异步更新订单图片
    ↓
GetProductImages() - 获取图片并更新产品重量 [修改]
```

### 2. 产品管理流程

#### 2.1 产品创建逻辑 (createProductsFromOrder)
**位置**: `server/service/order/order.go`
**触发时机**: 每次保存新订单时自动调用

```go
func (odService *OrderService) createProductsFromOrder(posting OzonPosting, shopName string) {
    for _, product := range posting.Products {
        // 检查产品是否已存在
        var existingProduct order.Product
        err := global.GVA_DB.Where("sku = ?", product.OfferId).First(&existingProduct)
        
        if errors.Is(err, gorm.ErrRecordNotFound) {
            // 创建新产品
            newProduct := order.Product{
                SKU:             &product.OfferId,
                ProductName:     &product.Name,
                ShopName:        &shopName,
                OzonWeight:      nil, // 重量将在异步获取图片时更新
                // ... 其他字段
            }
            global.GVA_DB.Create(&newProduct)
        } else {
            // 更新订单计数
            global.GVA_DB.Model(&order.Product{}).
                Where("sku = ?", product.OfferId).
                Update("total_order_count", gorm.Expr("total_order_count + 1"))
        }
    }
}
```

#### 2.2 图片和重量同步逻辑 (GetProductImages)
**位置**: `server/service/order/ozon_api.go`
**触发时机**: 订单同步时异步调用

```go
func (api *OzonAPIService) GetProductImages(ctx context.Context, clientID, apiKey string, products []OzonProduct) ([]string, error) {
    // 1. 调用v4 API获取产品信息
    productInfos, err := api.GetProductImagesAndWeights(ctx, clientID, apiKey, products)
    
    // 2. 处理图片和重量
    offerToImage := make(map[string]string)
    for _, info := range productInfos {
        if info.ImageURL != "" {
            offerToImage[info.OfferID] = info.ImageURL
        }
        
        // 3. 同时更新产品重量到数据库
        if info.Weight > 0 {
            global.GVA_DB.Model(&order.Product{}).
                Where("sku = ?", info.OfferID).
                Update("ozon_weight", info.Weight)
        }
    }
    
    // 4. 返回图片URL数组
    return images, nil
}
```

#### 2.3 v4 API调用逻辑 (GetProductImagesAndWeights)
**API端点**: `https://api-seller.ozon.ru/v4/product/info/attributes`
**返回数据**: 
- `primary_image`: 产品主图
- `weight`: 产品重量（克，需转换为千克）

```go
func (api *OzonAPIService) GetProductImagesAndWeights(ctx context.Context, clientID, apiKey string, products []OzonProduct) ([]ProductImageAndWeight, error) {
    // 1. 通过offer_id获取sku
    skuMap, err := api.getSkuFromOfferIDs(ctx, clientID, apiKey, offerIDs)
    
    // 2. 构建v4 API请求
    request := map[string]interface{}{
        "filter": map[string]interface{}{
            "sku":        skus,
            "visibility": "ALL",
        },
    }
    
    // 3. 调用v4 API
    resp, err := api.client.Do(req)
    
    // 4. 解析响应
    var response struct {
        Result []struct {
            PrimaryImage string `json:"primary_image"`
            Weight       int    `json:"weight"`
            // ... 其他字段
        } `json:"result"`
    }
    
    // 5. 处理结果
    for _, item := range response.Result {
        weightKg := float64(item.Weight) / 1000.0 // 克转千克
        imageURL := item.PrimaryImage
        // ...
    }
    
    return results, nil
}
```

### 3. 数据流向

#### 3.1 订单数据流
```
Ozon API → Order表 → 触发产品创建 → Product表
```

#### 3.2 产品图片数据流
```
v4 API → primary_image → Order表的order_img字段
```

#### 3.3 产品重量数据流
```
v4 API → weight字段 → Product表的ozon_weight字段
```

### 4. 数据库表结构

#### 4.1 Order表相关字段
- `json_data`: 存储原始订单JSON数据
- `order_img`: 存储产品图片URL数组

#### 4.2 Product表相关字段
- `sku`: 产品SKU (对应offer_id)
- `product_name`: 产品名称
- `shop_name`: 店铺名称
- `ozon_weight`: Ozon平台重量（千克）
- `actual_weight`: 实际重量（千克）
- `total_order_count`: 订单总数

### 5. 潜在问题分析

#### 5.1 时序问题 ⚠️ **关键问题**
- **问题**: 产品创建和重量更新是异步的
- **具体表现**:
  1. `createProductsFromOrder()` 在 `saveOzonOrderFast()` 中同步执行
  2. `GetProductImages()` 在 `asyncUpdateOrderImages()` 中异步执行
  3. 产品创建时 `OzonWeight` 字段为 `nil`
  4. 重量更新在图片获取时才执行
- **影响**: 产品管理页面显示重量为空的时间窗口
- **表现**: 新创建的产品重量显示"未设置"

#### 5.2 API依赖链问题 ⚠️ **关键问题**
- **问题**: 复杂的API调用链
- **调用链**: `GetProductImages` → `GetProductImagesAndWeights` → `getSkuFromOfferIDs` → `v1/product/info/description` → `v4/product/info/attributes`
- **风险点**: 任何一个环节失败都会导致整个流程失败
- **影响**: 图片和重量都无法获取
- **表现**: 订单图片不显示，产品重量为空

#### 5.3 错误处理不一致 ⚠️ **关键问题**
- **问题**: GetProductImages方法中的错误处理策略
- **当前逻辑**:
  ```go
  if err != nil {
      // 如果API调用失败，返回空数组但不返回错误
      return make([]string, len(products)), nil
  }
  ```
- **影响**: API失败时图片为空，但不会报错
- **表现**: 订单列表图片不显示，但同步显示"成功"

#### 5.4 数据一致性问题
- **问题**: 图片和重量更新分离
- **具体表现**:
  1. 图片保存到 `Order.order_img` 字段
  2. 重量保存到 `Product.ozon_weight` 字段
  3. 两个操作独立进行，可能出现不一致
- **影响**: 可能出现有图片但无重量，或有重量但无图片的情况

#### 5.5 SKU映射问题 ⚠️ **新发现问题**
- **问题**: `getSkuFromOfferIDs` 方法的可靠性
- **风险**:
  1. offer_id 到 SKU 的映射可能失败
  2. v1 API 可能返回不同的 offer_id
  3. SKU 格式转换问题
- **影响**: 即使产品存在，也可能无法获取图片和重量
- **表现**: 日志显示"没有找到有效的SKU"

#### 5.6 代码不一致问题 🐛 **代码Bug**
- **问题**: 日志和实际API调用不一致
- **具体位置**: `server/service/order/ozon_api.go:848`
- **错误代码**:
  ```go
  global.GVA_LOG.Info("获取产品属性信息",
      zap.String("API端点", api.baseURL+"/v2/product/info/attributes"), // ❌ 日志显示v2
      // ...
  req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+"/v4/product/info/attributes", ...) // ✅ 实际调用v4
  ```
- **影响**: 调试时可能产生误导
- **修复**: 统一日志和实际调用的API版本

#### 5.7 重复代码问题 🔄 **代码重复**
- **问题**: `/v4/product/info/attributes` API被调用了两次
- **位置**:
  1. `GetProductImagesAndWeights()` 方法 (第685行)
  2. `GetProductWeights()` 方法 (第854行)
- **影响**: 代码维护困难，可能导致不一致的行为
- **建议**: 合并重复的API调用逻辑

### 6. 建议的修复方案

#### 6.1 简化API调用链 🔧 **优先级：高**
**方案**: 直接在 `createProductsFromOrder` 中调用 v4 API
```go
func (odService *OrderService) createProductsFromOrder(posting OzonPosting, shopName string) {
    // 1. 创建产品记录
    // 2. 立即调用 v4 API 获取重量
    // 3. 更新产品重量
}
```
**优点**:
- 减少异步操作
- 简化调用链
- 提高数据一致性

#### 6.2 改进错误处理 🔧 **优先级：高**
**方案**: 分离图片获取和重量更新的错误处理
```go
// 图片获取失败时使用默认图片
// 重量获取失败时记录日志但不影响流程
```

#### 6.3 优化SKU映射 🔧 **优先级：中**
**方案**:
1. 缓存 offer_id 到 SKU 的映射
2. 添加映射失败的重试机制
3. 使用更可靠的 SKU 获取方法

#### 6.4 数据一致性保证 🔧 **优先级：中**
**方案**:
1. 使用数据库事务确保数据一致性
2. 添加数据验证和修复机制
3. 定期检查和修复不一致的数据

### 7. 当前问题的根本原因

#### 7.1 架构设计问题
- **异步处理过度**: 产品创建和属性更新分离
- **API调用链复杂**: 多个API依赖，容错性差
- **数据流分散**: 图片和重量存储在不同表中

#### 7.2 实现细节问题
- **错误处理不当**: API失败时静默返回空结果
- **时序依赖**: 产品必须先创建才能更新重量
- **缺少验证**: 没有检查数据完整性

### 8. 测试验证点

#### 8.1 当前状态验证 ❌
- [ ] 点击"同步订单"后，订单列表显示图片
- [ ] 产品管理页面自动创建产品记录
- [ ] 产品重量字段有数据 ❌ **主要问题**

#### 8.2 API调用验证
- [ ] v4 API能够正常调用
- [ ] primary_image字段有值
- [ ] weight字段有值且格式正确
- [ ] SKU映射成功率

#### 8.3 数据库状态验证
- [ ] Product表记录创建成功
- [ ] ozon_weight字段更新成功 ❌ **主要问题**
- [ ] Order表order_img字段有数据

#### 8.4 日志分析验证
- [ ] "开始获取产品图片和重量信息" 日志
- [ ] "v4 attributes API调用成功" 日志
- [ ] "同步更新产品重量成功" 日志 ❌ **关键日志**
- [ ] 错误日志分析

### 9. 下一步行动建议

#### 9.1 立即诊断 🚨
1. 检查日志输出，确认API调用是否成功
2. 验证v4 API响应格式是否正确
3. 检查数据库更新是否执行

#### 9.2 快速修复 ⚡
1. 在 `createProductsFromOrder` 中同步调用重量更新
2. 简化API调用链，减少失败点
3. 改进错误处理和日志记录

#### 9.3 长期优化 📈
1. 重构整个产品同步架构
2. 实现更可靠的数据一致性机制
3. 添加完整的监控和告警
