# gin-vue-admin全局配置

# jwt configuration
jwt:
  signing-key: 'qmPlus'
  expires-time: 7d
  buffer-time: 1d
  issuer: 'qmPlus'

# zap logger configuration
zap:
  level: 'info'
  format: 'console'
  prefix: '[gin-vue-admin]'
  director: 'log'
  show-line: true
  encode-level: 'LowercaseColorLevelEncoder'
  stacktrace-key: 'stacktrace'
  log-in-console: true

# system configuration
system:
  env: 'public'
  addr: 8888
  db-type: 'mysql'
  oss-type: 'local'
  use-multipoint: false
  use-redis: false
  iplimit-count: 15000
  iplimit-time: 3600

# captcha configuration
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600

# mysql connect configuration
mysql:
  path: 'gva-mysql'
  port: '3306'
  config: 'charset=utf8mb4&parseTime=True&loc=Local'
  db-name: 'qmPlus'
  username: 'root'
  password: 'Aa@6447985'
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: ""
  log-zap: false

# local configuration
local:
  path: 'uploads/file'
  store-path: 'uploads/file'

# Timer task db clear table
Timer:
  start: true
  spec: "@daily"
  detail:
    - tableName: "sys_operation_records"
      compareField: "created_at"
      interval: "2160h"
    - tableName: "jwt_blacklists"
      compareField: "created_at"
      interval: "168h"

# 跨域配置
cors:
  mode: 'whitelist'
  whitelist:
    - allow-origin: '*'
      allow-headers: 'content-type'
      allow-methods: 'GET, POST'
      expose-headers: 'Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type'
      allow-credentials: true
