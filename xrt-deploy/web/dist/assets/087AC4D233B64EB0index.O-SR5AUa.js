/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{l as e}from"./087AC4D233B64EB0logo.D8P6F9wK.js";import{C as a,_ as l,a as s,g as t,c as i,o as r,b as u,f as m,w as d,h as o,F as n,D as c,t as f,i as g,G as p}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const h=a.create();const v={class:"mt-2"},b={class:"flex flex-col md:flex-row gap-4"},x={class:"w-full md:w-1/2"},w={class:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-4 mt-4"},_=["href"],y=["src"],k={class:"w-full md:w-1/2"},j={class:"h-[calc(100vh-300px)] overflow-y-auto"},A={class:"w-full flex items-center justify-center"},D=l(Object.assign({name:"About"},{__name:"index",setup(a){const l=s(0),D=()=>{l.value++,C()},B=s([]),C=()=>{(function(e){return h({url:"https://api.github.com/repos/flipped-aurora/gin-vue-admin/commits?page="+e,method:"get"})})(l.value).then((({data:e})=>{e.forEach((e=>{e.commit.message&&B.value.push({from:p(e.commit.author.date,"yyyy-MM-dd"),title:e.commit.author.name,showDayAndMonth:!0,message:e.commit.message})}))}))},E=s([]);return C(),h({url:"https://api.github.com/orgs/FLIPPED-AURORA/members",method:"get"}).then((({data:e})=>{E.value=e,E.value.sort()})),(a,l)=>{const s=t("el-divider"),p=t("el-card"),h=t("el-link"),C=t("el-timeline-item"),F=t("el-timeline"),L=t("el-button");return r(),i("div",v,[u("div",b,[u("div",x,[m(p,{class:"min-w-96"},{header:d((()=>[m(s,null,{default:d((()=>l[0]||(l[0]=[o("gin-vue-admin")]))),_:1})])),default:d((()=>[l[1]||(l[1]=u("div",null,[u("div",{class:"w-full flex items-center justify-center"},[u("a",{href:"https://github.com/flipped-aurora/gin-vue-admin"},[u("img",{class:"org-img dom-center",src:e,alt:"gin-vue-admin"})])]),u("div",{class:"w-full flex items-center justify-around"},[u("a",{href:"https://github.com/flipped-aurora/gin-vue-admin"},[u("img",{class:"dom-center",src:"https://img.shields.io/github/watchers/flipped-aurora/gin-vue-admin.svg?label=Watch",alt:""})]),u("a",{href:"https://github.com/flipped-aurora/gin-vue-admin"},[u("img",{class:"dom-center",src:"https://img.shields.io/github/stars/flipped-aurora/gin-vue-admin.svg?style=social",alt:""})]),u("a",{href:"https://github.com/flipped-aurora/gin-vue-admin"},[u("img",{class:"dom-center",src:"https://img.shields.io/github/forks/flipped-aurora/gin-vue-admin.svg?label=Fork",alt:""})])])],-1))])),_:1}),m(p,{class:"min-w-96 mt-5"},{header:d((()=>l[2]||(l[2]=[u("div",null,"flipped-aurora团队",-1)]))),default:d((()=>[u("div",null,[l[3]||(l[3]=u("div",{class:"w-full flex items-center justify-center"},[u("a",{href:"https://github.com/flipped-aurora"},[u("img",{class:"org-img dom-center",src:"/assets/087AC4D233B64EB0flipped-aurora.DnmL0vNJ.png",alt:"flipped-aurora"})])],-1)),u("div",w,[(r(!0),i(n,null,c(E.value,((e,a)=>(r(),i("div",{key:a,class:"min-h-10 flex items-center"},[u("a",{href:e.html_url,class:"flex items-center group"},[u("img",{class:"w-8 h-8 rounded-full",src:e.avatar_url},null,8,y),m(h,{class:"text-blue-700 ml-2 text-lg font-bold font-sans break-all"},{default:d((()=>[o(f(e.login),1)])),_:2},1024)],8,_)])))),128))])])])),_:1})]),u("div",k,[m(p,null,{header:d((()=>l[4]||(l[4]=[u("div",null,"提交记录",-1)]))),default:d((()=>[u("div",j,[m(F,null,{default:d((()=>[(r(!0),i(n,null,c(B.value,((e,a)=>(r(),g(C,{key:a,timestamp:e.from,placement:"top"},{default:d((()=>[m(p,null,{default:d((()=>[u("h4",null,f(e.title),1),u("p",null,f(e.message),1)])),_:2},1024)])),_:2},1032,["timestamp"])))),128))])),_:1})]),u("div",A,[m(L,{class:"load-more",type:"primary",link:"",onClick:D},{default:d((()=>l[5]||(l[5]=[o(" Load more ")]))),_:1})])])),_:1})])])])}}}),[["__scopeId","data-v-8b552a9a"]]);export{D as default};
