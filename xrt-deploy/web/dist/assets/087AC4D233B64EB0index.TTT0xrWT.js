/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,I as l,J as a,K as t,ap as s,a as o,g as n,i as d,o as i,w as u,b as m,f as r,v as c,c as g,F as f,D as v,X as p,d as b,h,aq as y,E as V}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import w from"./087AC4D233B64EB0title.noh5aIFt.js";const x={class:"flex justify-between items-center"},_={class:"flex flex-col"},C={class:"mb-8"},k={class:"mt-2 text-sm p-2 flex items-center justify-center gap-2"},j={class:"mb-8"},B={class:"mt-2 text-sm p-2 flex items-center gap-2 justify-center"},U=["onClick"],E={class:"mb-8"},F={class:"mt-2 text-md p-2 flex flex-col gap-2"},S={class:"flex items-center justify-between"},z={class:"flex items-center justify-between"},D={class:"flex items-center justify-between"},M={class:"flex items-center justify-between"},T={class:"flex items-center justify-between"},I={class:"flex items-center justify-between gap-2"},A={class:"mb-8"},G={class:"mt-2 text-md p-2 flex flex-col gap-2"},J={class:"flex items-center justify-between mb-2"},O={class:"flex items-center justify-between mb-2"},P={class:"flex items-center justify-between mb-2"},W=e(Object.assign({name:"GvaSetting"},{__name:"index",props:{drawer:{default:!0,type:Boolean},drawerModifiers:{}},emits:["update:drawer"],setup(e){const W=l(),{config:q,device:K}=a(W),N=t((()=>"mobile"===K.value?"100%":"500px")),X=["#EB2F96","#3b82f6","#2FEB54","#EBEB2F","#EB2F2F","#2FEBEB"],H=s(e,"drawer"),L=["dark","light","auto"],Q=[{label:"正常模式",value:"normal"},{label:"顶部菜单栏模式",value:"head"},{label:"组合模式",value:"combination"}],R=async()=>{0===(await y(q.value)).code&&(localStorage.setItem("originSetting",JSON.stringify(q.value)),V.success("保存成功"),H.value=!1)},Y=o("");return(e,l)=>{const a=n("el-button"),t=n("el-segmented"),s=n("Select"),o=n("el-icon"),y=n("el-color-picker"),V=n("el-switch"),K=n("el-option"),Z=n("el-select"),$=n("el-input-number"),ee=n("el-drawer");return i(),d(ee,{modelValue:H.value,"onUpdate:modelValue":l[11]||(l[11]=e=>H.value=e),title:"系统配置",direction:"rtl",size:N.value,"show-close":!1},{header:u((()=>[m("div",x,[l[13]||(l[13]=m("span",{class:"text-lg"},"系统配置",-1)),r(a,{type:"primary",onClick:R},{default:u((()=>l[12]||(l[12]=[h("保存配置")]))),_:1})])])),default:u((()=>[m("div",_,[m("div",C,[r(w,{title:"默认主题"}),m("div",k,[r(t,{modelValue:c(q).darkMode,"onUpdate:modelValue":l[0]||(l[0]=e=>c(q).darkMode=e),options:L,size:"default",onChange:c(W).toggleDarkMode},null,8,["modelValue","onChange"])])]),m("div",j,[r(w,{title:"主题色"}),m("div",B,[(i(),g(f,null,v(X,(e=>m("div",{key:e,class:"w-5 h-5 rounded cursor-pointer flex items-center justify-center",style:p("background:".concat(e)),onClick:l=>c(W).togglePrimaryColor(e)},[c(q).primaryColor===e?(i(),d(o,{key:0},{default:u((()=>[r(s)])),_:1})):b("",!0)],12,U))),64)),r(y,{modelValue:Y.value,"onUpdate:modelValue":l[1]||(l[1]=e=>Y.value=e),onChange:c(W).togglePrimaryColor},null,8,["modelValue","onChange"])])]),m("div",E,[r(w,{title:"主题配置"}),m("div",F,[m("div",S,[l[14]||(l[14]=m("div",null,"展示水印",-1)),r(V,{modelValue:c(q).show_watermark,"onUpdate:modelValue":l[2]||(l[2]=e=>c(q).show_watermark=e),onChange:c(W).toggleConfigWatermark},null,8,["modelValue","onChange"])]),m("div",z,[l[15]||(l[15]=m("div",null,"灰色模式",-1)),r(V,{modelValue:c(q).grey,"onUpdate:modelValue":l[3]||(l[3]=e=>c(q).grey=e),onChange:c(W).toggleGrey},null,8,["modelValue","onChange"])]),m("div",D,[l[16]||(l[16]=m("div",null,"色弱模式",-1)),r(V,{modelValue:c(q).weakness,"onUpdate:modelValue":l[4]||(l[4]=e=>c(q).weakness=e),onChange:c(W).toggleWeakness},null,8,["modelValue","onChange"])]),m("div",M,[l[17]||(l[17]=m("div",null,"菜单模式",-1)),r(t,{modelValue:c(q).side_mode,"onUpdate:modelValue":l[5]||(l[5]=e=>c(q).side_mode=e),options:Q,size:"default",onChange:c(W).toggleSideMode},null,8,["modelValue","onChange"])]),m("div",T,[l[18]||(l[18]=m("div",null,"显示标签页",-1)),r(V,{modelValue:c(q).showTabs,"onUpdate:modelValue":l[6]||(l[6]=e=>c(q).showTabs=e),onChange:c(W).toggleTabs},null,8,["modelValue","onChange"])]),m("div",I,[l[19]||(l[19]=m("div",{class:"flex-shrink-0"},"页面切换动画",-1)),r(Z,{modelValue:c(q).transition_type,"onUpdate:modelValue":l[7]||(l[7]=e=>c(q).transition_type=e),onChange:c(W).toggleTransition,class:"w-40"},{default:u((()=>[r(K,{value:"fade",label:"淡入淡出"}),r(K,{value:"slide",label:"滑动"}),r(K,{value:"zoom",label:"缩放"}),r(K,{value:"none",label:"无动画"})])),_:1},8,["modelValue","onChange"])])])]),m("div",A,[r(w,{title:"layout 大小配置"}),m("div",G,[m("div",J,[l[20]||(l[20]=m("div",null,"侧边栏展开宽度",-1)),r($,{modelValue:c(q).layout_side_width,"onUpdate:modelValue":l[8]||(l[8]=e=>c(q).layout_side_width=e),min:150,max:400,step:10},null,8,["modelValue"])]),m("div",O,[l[21]||(l[21]=m("div",null,"侧边栏收缩宽度",-1)),r($,{modelValue:c(q).layout_side_collapsed_width,"onUpdate:modelValue":l[9]||(l[9]=e=>c(q).layout_side_collapsed_width=e),min:60,max:100},null,8,["modelValue"])]),m("div",P,[l[22]||(l[22]=m("div",null,"侧边栏子项高度",-1)),r($,{modelValue:c(q).layout_side_item_height,"onUpdate:modelValue":l[10]||(l[10]=e=>c(q).layout_side_item_height=e),min:30,max:50},null,8,["modelValue"])])])])])])),_:1},8,["modelValue","size"])}}}),[["__scopeId","data-v-eb18a363"]]);export{W as default};
