/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{a,r as e,k as s,c as l,b as t,f as r,t as n,w as c,l as o,g as i,m as d,u,o as m,i as p,d as f,h,E as g}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as w,c as b}from"./087AC4D233B64EB0initdb.P7dovK8r.js";import{_ as v}from"./087AC4D233B64EB0bottomInfo.BkiNnDsM.js";const y={id:"userLayout",class:"w-full h-full relative"},_={class:"rounded-lg flex items-center justify-evenly w-full h-full md:w-screen md:h-screen md:bg-[#194bfb] bg-white"},x={class:"md:w-3/5 w-10/12 h-full flex items-center justify-evenly"},C={class:"z-[999] pt-12 pb-10 md:w-96 w-full rounded-lg flex flex-col justify-between box-border"},k={class:"flex items-center justify-center"},B=["src"],E={class:"mb-9"},I={class:"text-center text-4xl font-bold"},V={class:"flex w-full justify-between"},D={class:"w-1/3 h-11 bg-[#c3d4f2] rounded"},j=["src"],A=Object.assign({name:"Login"},{__name:"index",setup(A){const L=u(),z=async()=>{const a=await d();U.captcha.push({max:a.data.captchaLength,min:a.data.captchaLength,message:"请输入".concat(a.data.captchaLength,"位验证码"),trigger:"blur"}),q.value=a.data.picPath,G.captchaId=a.data.captchaId,G.openCaptcha=a.data.openCaptcha};z();const N=a(null),q=a(""),G=e({username:"admin",password:"",captcha:"",captchaId:"",openCaptcha:!1}),U=e({username:[{validator:(a,e,s)=>{if(e.length<5)return s(new Error("请输入正确的用户名"));s()},trigger:"blur"}],password:[{validator:(a,e,s)=>{if(e.length<6)return s(new Error("请输入正确的密码"));s()},trigger:"blur"}],captcha:[{message:"验证码格式不正确",trigger:"blur"}]}),M=s(),O=()=>{N.value.validate((async a=>{if(!a)return g({type:"error",message:"请正确填写登录信息",showClose:!0}),await z(),!1;return!!(await(async()=>await M.LoginIn(G))())||(await z(),!1)}))},J=async()=>{var a;const e=await b();0===e.code&&((null==(a=e.data)?void 0:a.needInit)?(M.NeedInit(),await L.push({name:"Init"})):g({type:"info",message:"已配置数据库信息，无法初始化"}))};return(a,e)=>{const s=i("el-input"),d=i("el-form-item"),u=i("el-button"),g=i("el-form");return m(),l("div",y,[t("div",_,[t("div",x,[e[7]||(e[7]=t("div",{class:"oblique h-[130%] w-3/5 bg-white dark:bg-slate-900 transform -rotate-12 absolute -ml-52"},null,-1)),t("div",C,[t("div",null,[t("div",k,[t("img",{class:"w-24",src:a.$GIN_VUE_ADMIN.appLogo,alt:""},null,8,B)]),t("div",E,[t("p",I,n(a.$GIN_VUE_ADMIN.appName),1),e[4]||(e[4]=t("p",{class:"text-center text-sm font-normal text-gray-500 mt-2.5"}," A management platform using Golang and Vue ",-1))]),r(g,{ref_key:"loginForm",ref:N,model:G,rules:U,"validate-on-rule-change":!1,onKeyup:o(O,["enter"])},{default:c((()=>[r(d,{prop:"username",class:"mb-6"},{default:c((()=>[r(s,{modelValue:G.username,"onUpdate:modelValue":e[0]||(e[0]=a=>G.username=a),size:"large",placeholder:"请输入用户名","suffix-icon":"user"},null,8,["modelValue"])])),_:1}),r(d,{prop:"password",class:"mb-6"},{default:c((()=>[r(s,{modelValue:G.password,"onUpdate:modelValue":e[1]||(e[1]=a=>G.password=a),"show-password":"",size:"large",type:"password",placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),G.openCaptcha?(m(),p(d,{key:0,prop:"captcha",class:"mb-6"},{default:c((()=>[t("div",V,[r(s,{modelValue:G.captcha,"onUpdate:modelValue":e[2]||(e[2]=a=>G.captcha=a),placeholder:"请输入验证码",size:"large",class:"flex-1 mr-5"},null,8,["modelValue"]),t("div",D,[q.value?(m(),l("img",{key:0,class:"w-full h-full",src:q.value,alt:"请输入验证码",onClick:e[3]||(e[3]=a=>z())},null,8,j)):f("",!0)])])])),_:1})):f("",!0),r(d,{class:"mb-6"},{default:c((()=>[r(u,{class:"shadow shadow-active h-11 w-full",type:"primary",size:"large",onClick:O},{default:c((()=>e[5]||(e[5]=[h("登 录")]))),_:1})])),_:1}),r(d,{class:"mb-6"},{default:c((()=>[r(u,{class:"shadow shadow-active h-11 w-full",type:"primary",size:"large",onClick:J},{default:c((()=>e[6]||(e[6]=[h("前往初始化")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])]),e[8]||(e[8]=t("div",{class:"hidden md:block w-1/2 h-full float-right bg-[#194bfb]"},[t("img",{class:"h-full",src:w,alt:"banner"})],-1))]),r(v,{class:"left-0 right-0 absolute bottom-3 mx-auto w-full z-20"},{default:c((()=>e[9]||(e[9]=[t("div",{class:"links items-center justify-center gap-2 hidden md:flex"},[t("a",{href:"https://www.gin-vue-admin.com/",target:"_blank"},[t("img",{src:"/assets/087AC4D233B64EB0docs.DHdLpnBP.png",class:"w-8 h-8",alt:"文档"})]),t("a",{href:"https://support.qq.com/product/371961",target:"_blank"},[t("img",{src:"/assets/087AC4D233B64EB0kefu.DNqTOiJW.png",class:"w-8 h-8",alt:"客服"})]),t("a",{href:"https://github.com/flipped-aurora/gin-vue-admin",target:"_blank"},[t("img",{src:"/assets/087AC4D233B64EB0github.4gfhYJGc.png",class:"w-8 h-8",alt:"github"})]),t("a",{href:"https://space.bilibili.com/322210472",target:"_blank"},[t("img",{src:"/assets/087AC4D233B64EB0video.CGOnQqiM.png",class:"w-8 h-8",alt:"视频站"})])],-1)]))),_:1})])}}});export{A as default};
