/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var t,l,u,o,c=[],i=!0,a=!1;try{if(u=(r=r.call(e)).next,0===n){if(Object(r)!==r)return;i=!1}else for(;!(i=(t=u.call(r)).done)&&(c.push(t.value),c.length!==n);i=!0);}catch(e){a=!0,l=e}finally{try{if(!i&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(a)throw l}}return c}}(e,n)||r(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(e,n){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=r(e))||n&&e&&"number"==typeof e.length){t&&(e=t);var l=0,u=function(){};return{s:u,n:function(){return l>=e.length?{done:!0}:{done:!1,value:e[l++]}},e:function(e){throw e},f:u}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,i=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return c=e.done,e},e:function(e){i=!0,o=e},f:function(){try{c||null==t.return||t.return()}finally{if(i)throw o}}}}function r(e,n){if(e){if("string"==typeof e)return t(e,n);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?t(e,n):void 0}}function t(e,n){(null==n||n>e.length)&&(n=e.length);for(var r=0,t=Array(n);r<n;r++)t[r]=e[r];return t}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(r,t){"use strict";var l,u,o,c,i,a;return{setters:[function(e){l=e._,u=e.p,o=e.at,c=e.a,i=e.c,a=e.o}],execute:function(){var t=document.createElement("style");t.textContent=".screenfull-svg[data-v-c1eb3148]{width:16px;height:16px;cursor:pointer;vertical-align:middle;margin-right:32px;fill:rgba(0,0,0,.45)}\n/*$vite$:1*/",document.head.appendChild(t);var f=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],s=function(){if("undefined"==typeof document)return!1;for(var r=f[0],t={},l=0,u=f;l<u.length;l++){var o=u[l];if((null==o?void 0:o[1])in document){var c,i=n(o.entries());try{for(i.s();!(c=i.n()).done;){var a=e(c.value,2),s=a[0],d=a[1];t[r[s]]=d}}catch(m){i.e(m)}finally{i.f()}return t}}return!1}(),d={change:s.fullscreenchange,error:s.fullscreenerror},m={request:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.documentElement,n=arguments.length>1?arguments[1]:void 0;return new Promise((function(r,t){var l=function(){m.off("change",l),r()};m.on("change",l);var u=e[s.requestFullscreen](n);u instanceof Promise&&u.then(l).catch(t)}))},exit:function(){return new Promise((function(e,n){if(m.isFullscreen){var r=function(){m.off("change",r),e()};m.on("change",r);var t=document[s.exitFullscreen]();t instanceof Promise&&t.then(r).catch(n)}else e()}))},toggle:function(e,n){return m.isFullscreen?m.exit():m.request(e,n)},onchange:function(e){m.on("change",e)},onerror:function(e){m.on("error",e)},on:function(e,n){var r=d[e];r&&document.addEventListener(r,n,!1)},off:function(e,n){var r=d[e];r&&document.removeEventListener(r,n,!1)},raw:s};Object.defineProperties(m,{isFullscreen:{get:function(){return Boolean(document[s.fullscreenElement])}},element:{enumerable:!0,get:function(){var e;return null!==(e=document[s.fullscreenElement])&&void 0!==e?e:void 0}},isEnabled:{enumerable:!0,get:function(){return Boolean(document[s.fullscreenEnabled])}}}),s||(m={isEnabled:!1});var v={key:0,class:"gvaIcon gvaIcon-fullscreen-expand"},h={key:1,class:"gvaIcon gvaIcon-fullscreen-shrink"};r("default",l(Object.assign({name:"Screenfull"},{__name:"index",props:{width:{type:Number,default:22},height:{type:Number,default:22},fill:{type:String,default:"#48576a"}},setup:function(e){u((function(){m.isEnabled&&m.on("change",t)})),o((function(){m.off("change")}));var n=function(){m.isEnabled&&m.toggle()},r=c(!0),t=function(){r.value=!m.isFullscreen};return function(e,t){return a(),i("div",{onClick:n},[r.value?(a(),i("div",v)):(a(),i("div",h))])}}}),[["__scopeId","data-v-c1eb3148"]]))}}}))}();
