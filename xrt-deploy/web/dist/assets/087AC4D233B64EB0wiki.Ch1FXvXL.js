/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{c as t,o as e,F as i,D as r,b as a,t as l}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const s={class:"grid grid-cols-2 gap-2"},u=["href"],n={__name:"wiki",setup(n){const o=[{title:"Vue3",url:"https://v3.cn.vuejs.org/guide/introduction.html"},{title:"GIN 文档",url:"https://gin-gonic.com/"},{title:"GVA 文档",url:"https://www.gin-vue-admin.com/"},{title:"插件市场",url:"https://plugin.gin-vue-admin.com/"},{title:"github 仓库",url:"https://github.com/flipped-aurora/gin-vue-admin"}];return(n,g)=>(e(),t("div",s,[(e(),t(i,null,r(o,(t=>a("a",{key:t.url,href:t.url,class:"text-sm text-gray-700 dark:text-gray-300 no-underline hover:text-active",target:"_blank"},l(t.title),9,u))),64))]))}};export{n as default};
