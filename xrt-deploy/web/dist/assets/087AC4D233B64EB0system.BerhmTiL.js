/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{a as e,s as l,r as a}from"./087AC4D233B64EB0system.CvtIEjYM.js";import{s as o,_ as u,a as d,g as m,c as t,o as s,f as n,b as i,w as r,i as p,d as c,h as V,F as v,D as f,v as b,B as _,y as h,af as y,E as U,ab as g}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const q={class:"system"},M={class:"mt-4"},w=u(Object.assign({name:"Config"},{__name:"system",setup(u){const w=d("1"),k=d({system:{"iplimit-count":0,"iplimit-time":0},jwt:{},mysql:{},mssql:{},sqlite:{},pgsql:{},oracle:{},excel:{},autocode:{},redis:{},mongo:{coll:"",options:"",database:"",username:"",password:"","min-pool-size":"","max-pool-size":"","socket-timeout-ms":"","connect-timeout-ms":"","is-zap":!1,hosts:[{host:"",port:""}]},qiniu:{},"tencent-cos":{},"aliyun-oss":{},"hua-wei-obs":{},"cloudflare-r2":{},captcha:{},zap:{},local:{},email:{},timer:{detail:{}}}),x=async()=>{const l=await e();0===l.code&&(k.value=l.data.config)};x();const z=()=>{g.confirm("确定要重启服务?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await a()).code&&U({type:"success",message:"操作成功"})})).catch((()=>{U({type:"info",message:"取消重启"})}))},C=async()=>{0===(await l({config:k.value})).code&&(U({type:"success",message:"配置文件设置成功"}),await x())},S=async()=>{var e;0===(await o({url:"/email/emailTest",method:"post",data:e})).code?(U({type:"success",message:"邮件发送成功"}),await x()):U({type:"error",message:"邮件发送失败"})},I=()=>{k.value.jwt["signing-key"]=y()},K=()=>{k.value.mongo.hosts.push({host:"",port:""})};return(e,l)=>{const a=m("el-input-number"),o=m("el-form-item"),u=m("el-option"),d=m("el-select"),y=m("el-switch"),U=m("el-input"),g=m("el-tooltip"),x=m("el-tab-pane"),j=m("el-button"),L=m("el-tabs"),B=m("el-form");return s(),t("div",q,[n(B,{ref:"form",model:k.value,"label-width":"240px"},{default:r((()=>[n(L,{modelValue:w.value,"onUpdate:modelValue":l[144]||(l[144]=e=>w.value=e)},{default:r((()=>[n(x,{label:"系统配置",name:"1",class:"mt-3.5"},{default:r((()=>[n(o,{label:"端口值"},{default:r((()=>[n(a,{modelValue:k.value.system.addr,"onUpdate:modelValue":l[0]||(l[0]=e=>k.value.system.addr=e),placeholder:"请输入端口值"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库类型"},{default:r((()=>[n(d,{modelValue:k.value.system["db-type"],"onUpdate:modelValue":l[1]||(l[1]=e=>k.value.system["db-type"]=e),class:"w-full"},{default:r((()=>[n(u,{value:"mysql"}),n(u,{value:"pgsql"}),n(u,{value:"mssql"}),n(u,{value:"sqlite"}),n(u,{value:"oracle"})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"Oss类型"},{default:r((()=>[n(d,{modelValue:k.value.system["oss-type"],"onUpdate:modelValue":l[2]||(l[2]=e=>k.value.system["oss-type"]=e),class:"w-full"},{default:r((()=>[n(u,{value:"local"},{default:r((()=>l[145]||(l[145]=[V("本地")]))),_:1}),n(u,{value:"qiniu"},{default:r((()=>l[146]||(l[146]=[V("七牛")]))),_:1}),n(u,{value:"tencent-cos"},{default:r((()=>l[147]||(l[147]=[V("腾讯云COS")]))),_:1}),n(u,{value:"aliyun-oss"},{default:r((()=>l[148]||(l[148]=[V("阿里云OSS")]))),_:1}),n(u,{value:"huawei-obs"},{default:r((()=>l[149]||(l[149]=[V("华为云OBS")]))),_:1}),n(u,{value:"cloudflare-r2"},{default:r((()=>l[150]||(l[150]=[V("cloudflare R2")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"多点登录拦截"},{default:r((()=>[n(y,{modelValue:k.value.system["use-multipoint"],"onUpdate:modelValue":l[3]||(l[3]=e=>k.value.system["use-multipoint"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"开启redis"},{default:r((()=>[n(y,{modelValue:k.value.system["use-redis"],"onUpdate:modelValue":l[4]||(l[4]=e=>k.value.system["use-redis"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"开启Mongo"},{default:r((()=>[n(y,{modelValue:k.value.system["use-mongo"],"onUpdate:modelValue":l[5]||(l[5]=e=>k.value.system["use-mongo"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"严格角色模式"},{default:r((()=>[n(y,{modelValue:k.value.system["use-strict-auth"],"onUpdate:modelValue":l[6]||(l[6]=e=>k.value.system["use-strict-auth"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"限流次数"},{default:r((()=>[n(a,{modelValue:k.value.system["iplimit-count"],"onUpdate:modelValue":l[7]||(l[7]=e=>k.value.system["iplimit-count"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),n(o,{label:"限流时间"},{default:r((()=>[n(a,{modelValue:k.value.system["iplimit-time"],"onUpdate:modelValue":l[8]||(l[8]=e=>k.value.system["iplimit-time"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),n(g,{content:"请修改完成后，注意一并修改前端env环境下的VITE_BASE_PATH",placement:"top-start"},{default:r((()=>[n(o,{label:"全局路由前缀"},{default:r((()=>[n(U,{modelValue:k.value.system["router-prefix"],"onUpdate:modelValue":l[9]||(l[9]=e=>k.value.system["router-prefix"]=e),modelModifiers:{trim:!0},placeholder:"请输入全局路由前缀"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),n(x,{label:"jwt签名",name:"2",class:"mt-3.5"},{default:r((()=>[n(o,{label:"jwt签名"},{default:r((()=>[n(U,{modelValue:k.value.jwt["signing-key"],"onUpdate:modelValue":l[10]||(l[10]=e=>k.value.jwt["signing-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入jwt签名"},{append:r((()=>[n(j,{onClick:I},{default:r((()=>l[151]||(l[151]=[V("生成")]))),_:1})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"有效期"},{default:r((()=>[n(U,{modelValue:k.value.jwt["expires-time"],"onUpdate:modelValue":l[11]||(l[11]=e=>k.value.jwt["expires-time"]=e),modelModifiers:{trim:!0},placeholder:"请输入有效期"},null,8,["modelValue"])])),_:1}),n(o,{label:"缓冲期"},{default:r((()=>[n(U,{modelValue:k.value.jwt["buffer-time"],"onUpdate:modelValue":l[12]||(l[12]=e=>k.value.jwt["buffer-time"]=e),modelModifiers:{trim:!0},placeholder:"请输入缓冲期"},null,8,["modelValue"])])),_:1}),n(o,{label:"签发者"},{default:r((()=>[n(U,{modelValue:k.value.jwt.issuer,"onUpdate:modelValue":l[13]||(l[13]=e=>k.value.jwt.issuer=e),modelModifiers:{trim:!0},placeholder:"请输入签发者"},null,8,["modelValue"])])),_:1})])),_:1}),n(x,{label:"Zap日志配置",name:"3",class:"mt-3.5"},{default:r((()=>[n(o,{label:"级别"},{default:r((()=>[n(d,{modelValue:k.value.zap.level,"onUpdate:modelValue":l[14]||(l[14]=e=>k.value.zap.level=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"输出"},{default:r((()=>[n(d,{modelValue:k.value.zap.format,"onUpdate:modelValue":l[15]||(l[15]=e=>k.value.zap.format=e)},{default:r((()=>[n(u,{value:"console",label:"console"}),n(u,{value:"json",label:"json"})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"日志前缀"},{default:r((()=>[n(U,{modelValue:k.value.zap.prefix,"onUpdate:modelValue":l[16]||(l[16]=e=>k.value.zap.prefix=e),modelModifiers:{trim:!0},placeholder:"请输入日志前缀"},null,8,["modelValue"])])),_:1}),n(o,{label:"日志文件夹"},{default:r((()=>[n(U,{modelValue:k.value.zap.director,"onUpdate:modelValue":l[17]||(l[17]=e=>k.value.zap.director=e),modelModifiers:{trim:!0},placeholder:"请输入日志文件夹"},null,8,["modelValue"])])),_:1}),n(o,{label:"编码级"},{default:r((()=>[n(d,{modelValue:k.value.zap["encode-level"],"onUpdate:modelValue":l[18]||(l[18]=e=>k.value.zap["encode-level"]=e),class:"w-6/12"},{default:r((()=>[n(u,{value:"LowercaseLevelEncoder",label:"LowercaseLevelEncoder"}),n(u,{value:"LowercaseColorLevelEncoder",label:"LowercaseColorLevelEncoder"}),n(u,{value:"CapitalLevelEncoder",label:"CapitalLevelEncoder"}),n(u,{value:"CapitalColorLevelEncoder",label:"CapitalColorLevelEncoder"})])),_:1},8,["modelValue"])])),_:1}),n(o,{label:"栈名"},{default:r((()=>[n(U,{modelValue:k.value.zap["stacktrace-key"],"onUpdate:modelValue":l[19]||(l[19]=e=>k.value.zap["stacktrace-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入栈名"},null,8,["modelValue"])])),_:1}),n(o,{label:"日志留存时间(默认以天为单位)"},{default:r((()=>[n(a,{modelValue:k.value.zap["retention-day"],"onUpdate:modelValue":l[20]||(l[20]=e=>k.value.zap["retention-day"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"显示行"},{default:r((()=>[n(y,{modelValue:k.value.zap["show-line"],"onUpdate:modelValue":l[21]||(l[21]=e=>k.value.zap["show-line"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"输出控制台"},{default:r((()=>[n(y,{modelValue:k.value.zap["log-in-console"],"onUpdate:modelValue":l[22]||(l[22]=e=>k.value.zap["log-in-console"]=e)},null,8,["modelValue"])])),_:1})])),_:1}),k.value.system["use-redis"]?(s(),p(x,{key:0,label:"Redis",name:"4",class:"mt-3.5"},{default:r((()=>[n(o,{label:"库"},{default:r((()=>[n(a,{modelValue:k.value.redis.db,"onUpdate:modelValue":l[23]||(l[23]=e=>k.value.redis.db=e),min:"0",max:"16"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.redis.addr,"onUpdate:modelValue":l[24]||(l[24]=e=>k.value.redis.addr=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.redis.password,"onUpdate:modelValue":l[25]||(l[25]=e=>k.value.redis.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1})])),_:1})):c("",!0),n(x,{label:"邮箱配置",name:"5",class:"mt-3.5"},{default:r((()=>[n(o,{label:"接收者邮箱"},{default:r((()=>[n(U,{modelValue:k.value.email.to,"onUpdate:modelValue":l[26]||(l[26]=e=>k.value.email.to=e),placeholder:"可多个，以逗号分隔"},null,8,["modelValue"])])),_:1}),n(o,{label:"端口"},{default:r((()=>[n(a,{modelValue:k.value.email.port,"onUpdate:modelValue":l[27]||(l[27]=e=>k.value.email.port=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"发送者邮箱"},{default:r((()=>[n(U,{modelValue:k.value.email.from,"onUpdate:modelValue":l[28]||(l[28]=e=>k.value.email.from=e),modelModifiers:{trim:!0},placeholder:"请输入发送者邮箱"},null,8,["modelValue"])])),_:1}),n(o,{label:"host"},{default:r((()=>[n(U,{modelValue:k.value.email.host,"onUpdate:modelValue":l[29]||(l[29]=e=>k.value.email.host=e),modelModifiers:{trim:!0},placeholder:"请输入host"},null,8,["modelValue"])])),_:1}),n(o,{label:"是否为ssl"},{default:r((()=>[n(y,{modelValue:k.value.email["is-ssl"],"onUpdate:modelValue":l[30]||(l[30]=e=>k.value.email["is-ssl"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"secret"},{default:r((()=>[n(U,{modelValue:k.value.email.secret,"onUpdate:modelValue":l[31]||(l[31]=e=>k.value.email.secret=e),modelModifiers:{trim:!0},placeholder:"请输入secret"},null,8,["modelValue"])])),_:1}),n(o,{label:"测试邮件"},{default:r((()=>[n(j,{onClick:S},{default:r((()=>l[152]||(l[152]=[V("测试邮件")]))),_:1})])),_:1})])),_:1}),k.value.system["use-mongo"]?(s(),p(x,{key:1,label:"Mongo 数据库配置",name:"14",class:"mt-3.5"},{default:r((()=>[n(o,{label:"collection name(表名,一般不写)"},{default:r((()=>[n(U,{modelValue:k.value.mongo.coll,"onUpdate:modelValue":l[32]||(l[32]=e=>k.value.mongo.coll=e),modelModifiers:{trim:!0},placeholder:"请输入collection name"},null,8,["modelValue"])])),_:1}),n(o,{label:"mongodb 选项"},{default:r((()=>[n(U,{modelValue:k.value.mongo.options,"onUpdate:modelValue":l[33]||(l[33]=e=>k.value.mongo.options=e),modelModifiers:{trim:!0},placeholder:"请输入mongodb 选项"},null,8,["modelValue"])])),_:1}),n(o,{label:"database name(数据库名)"},{default:r((()=>[n(U,{modelValue:k.value.mongo.database,"onUpdate:modelValue":l[34]||(l[34]=e=>k.value.mongo.database=e),modelModifiers:{trim:!0},placeholder:"请输入数据库名"},null,8,["modelValue"])])),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.mongo.username,"onUpdate:modelValue":l[35]||(l[35]=e=>k.value.mongo.username=e),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.mongo.password,"onUpdate:modelValue":l[36]||(l[36]=e=>k.value.mongo.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"最小连接池"},{default:r((()=>[n(a,{modelValue:k.value.mongo["min-pool-size"],"onUpdate:modelValue":l[37]||(l[37]=e=>k.value.mongo["min-pool-size"]=e),min:"0"},null,8,["modelValue"])])),_:1}),n(o,{label:"最大连接池"},{default:r((()=>[n(a,{modelValue:k.value.mongo["max-pool-size"],"onUpdate:modelValue":l[38]||(l[38]=e=>k.value.mongo["max-pool-size"]=e),min:"100"},null,8,["modelValue"])])),_:1}),n(o,{label:"socket超时时间"},{default:r((()=>[n(a,{modelValue:k.value.mongo["socket-timeout-ms"],"onUpdate:modelValue":l[39]||(l[39]=e=>k.value.mongo["socket-timeout-ms"]=e),min:"0"},null,8,["modelValue"])])),_:1}),n(o,{label:"连接超时时间"},{default:r((()=>[n(a,{modelValue:k.value.mongo["socket-timeout-ms"],"onUpdate:modelValue":l[40]||(l[40]=e=>k.value.mongo["socket-timeout-ms"]=e),min:"0"},null,8,["modelValue"])])),_:1}),n(o,{label:"是否开启zap日志"},{default:r((()=>[n(y,{modelValue:k.value.mongo["is-zap"],"onUpdate:modelValue":l[41]||(l[41]=e=>k.value.mongo["is-zap"]=e)},null,8,["modelValue"])])),_:1}),(s(!0),t(v,null,f(k.value.mongo.hosts,((e,l)=>(s(),p(o,{key:l,label:"节点 ".concat(l+1)},{default:r((()=>[(s(!0),t(v,null,f(e,((a,u)=>(s(),t("div",{key:u},[(s(),p(o,{key:l+u,label:u,"label-width":"60"},{default:r((()=>[n(U,{modelValue:e[u],"onUpdate:modelValue":l=>e[u]=l,modelModifiers:{trim:!0},placeholder:"host"===u?"请输入地址":"请输入端口"},null,8,["modelValue","onUpdate:modelValue","placeholder"])])),_:2},1032,["label"]))])))),128)),l>0?(s(),p(o,{key:0},{default:r((()=>[n(j,{type:"danger",size:"small",plain:"",icon:b(_),onClick:e=>{return a=l,void k.value.mongo.hosts.splice(a,1);var a},class:"ml-3"},null,8,["icon","onClick"])])),_:2},1024)):c("",!0)])),_:2},1032,["label"])))),128)),n(o,null,{default:r((()=>[n(j,{type:"primary",size:"small",plain:"",icon:b(h),onClick:K},null,8,["icon"])])),_:1})])),_:1})):c("",!0),n(x,{label:"验证码配置",name:"7",class:"mt-3.5"},{default:r((()=>[n(o,{label:"字符长度"},{default:r((()=>[n(a,{modelValue:k.value.captcha["key-long"],"onUpdate:modelValue":l[42]||(l[42]=e=>k.value.captcha["key-long"]=e),min:4,max:6},null,8,["modelValue"])])),_:1}),n(o,{label:"图片宽度"},{default:r((()=>[n(a,{modelValue:k.value.captcha["img-width"],"onUpdate:modelValue":l[43]||(l[43]=e=>k.value.captcha["img-width"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1}),n(o,{label:"图片高度"},{default:r((()=>[n(a,{modelValue:k.value.captcha["img-height"],"onUpdate:modelValue":l[44]||(l[44]=e=>k.value.captcha["img-height"]=e),modelModifiers:{number:!0}},null,8,["modelValue"])])),_:1})])),_:1}),n(x,{label:"数据库配置",name:"9",class:"mt-3.5"},{default:r((()=>["mysql"===k.value.system["db-type"]?(s(),t(v,{key:0},[n(o,{label:""},{default:r((()=>l[153]||(l[153]=[i("h3",null,"MySQL",-1)]))),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.mysql.username,"onUpdate:modelValue":l[45]||(l[45]=e=>k.value.mysql.username=e),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.mysql.password,"onUpdate:modelValue":l[46]||(l[46]=e=>k.value.mysql.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.mysql.path,"onUpdate:modelValue":l[47]||(l[47]=e=>k.value.mysql.path=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库名称"},{default:r((()=>[n(U,{modelValue:k.value.mysql["db-name"],"onUpdate:modelValue":l[48]||(l[48]=e=>k.value.mysql["db-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入数据库名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"前缀"},{default:r((()=>[n(U,{modelValue:k.value.mysql.prefix,"onUpdate:modelValue":l[49]||(l[49]=e=>k.value.mysql.prefix=e),modelModifiers:{trim:!0},placeholder:"默认为空"},null,8,["modelValue"])])),_:1}),n(o,{label:"复数表"},{default:r((()=>[n(y,{modelValue:k.value.mysql.singular,"onUpdate:modelValue":l[50]||(l[50]=e=>k.value.mysql.singular=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"引擎"},{default:r((()=>[n(U,{modelValue:k.value.mysql.engine,"onUpdate:modelValue":l[51]||(l[51]=e=>k.value.mysql.engine=e),modelModifiers:{trim:!0},placeholder:"默认为InnoDB"},null,8,["modelValue"])])),_:1}),n(o,{label:"maxIdleConns"},{default:r((()=>[n(a,{modelValue:k.value.mysql["max-idle-conns"],"onUpdate:modelValue":l[52]||(l[52]=e=>k.value.mysql["max-idle-conns"]=e),min:1},null,8,["modelValue"])])),_:1}),n(o,{label:"maxOpenConns"},{default:r((()=>[n(a,{modelValue:k.value.mysql["max-open-conns"],"onUpdate:modelValue":l[53]||(l[53]=e=>k.value.mysql["max-open-conns"]=e),min:1},null,8,["modelValue"])])),_:1}),n(o,{label:"写入日志"},{default:r((()=>[n(y,{modelValue:k.value.mysql["log-zap"],"onUpdate:modelValue":l[54]||(l[54]=e=>k.value.mysql["log-zap"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"日志模式"},{default:r((()=>[n(d,{modelValue:k.value.mysql["log-mode"],"onUpdate:modelValue":l[55]||(l[55]=e=>k.value.mysql["log-mode"]=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1})],64)):c("",!0),"pgsql"===k.value.system["db-type"]?(s(),t(v,{key:1},[n(o,{label:""},{default:r((()=>l[154]||(l[154]=[i("h3",null,"PostgreSQL",-1)]))),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.pgsql.username,"onUpdate:modelValue":l[56]||(l[56]=e=>k.value.pgsql.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.pgsql.password,"onUpdate:modelValue":l[57]||(l[57]=e=>k.value.pgsql.password=e),placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.pgsql.path,"onUpdate:modelValue":l[58]||(l[58]=e=>k.value.pgsql.path=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库"},{default:r((()=>[n(U,{modelValue:k.value.pgsql["db-name"],"onUpdate:modelValue":l[59]||(l[59]=e=>k.value.pgsql["db-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])])),_:1}),n(o,{label:"前缀"},{default:r((()=>[n(U,{modelValue:k.value.pgsql.prefix,"onUpdate:modelValue":l[60]||(l[60]=e=>k.value.pgsql.prefix=e),modelModifiers:{trim:!0},placeholder:"请输入前缀"},null,8,["modelValue"])])),_:1}),n(o,{label:"复数表"},{default:r((()=>[n(y,{modelValue:k.value.pgsql.singular,"onUpdate:modelValue":l[61]||(l[61]=e=>k.value.pgsql.singular=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"引擎"},{default:r((()=>[n(U,{modelValue:k.value.pgsql.engine,"onUpdate:modelValue":l[62]||(l[62]=e=>k.value.pgsql.engine=e),modelModifiers:{trim:!0},placeholder:"请输入引擎"},null,8,["modelValue"])])),_:1}),n(o,{label:"maxIdleConns"},{default:r((()=>[n(a,{modelValue:k.value.pgsql["max-idle-conns"],"onUpdate:modelValue":l[63]||(l[63]=e=>k.value.pgsql["max-idle-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"maxOpenConns"},{default:r((()=>[n(a,{modelValue:k.value.pgsql["max-open-conns"],"onUpdate:modelValue":l[64]||(l[64]=e=>k.value.pgsql["max-open-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"写入日志"},{default:r((()=>[n(y,{modelValue:k.value.pgsql["log-zap"],"onUpdate:modelValue":l[65]||(l[65]=e=>k.value.pgsql["log-zap"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"日志模式"},{default:r((()=>[n(d,{modelValue:k.value.pgsql["log-mode"],"onUpdate:modelValue":l[66]||(l[66]=e=>k.value.pgsql["log-mode"]=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1})],64)):c("",!0),"mssql"===k.value.system["db-type"]?(s(),t(v,{key:2},[n(o,{label:""},{default:r((()=>l[155]||(l[155]=[i("h3",null,"MsSQL",-1)]))),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.mssql.username,"onUpdate:modelValue":l[67]||(l[67]=e=>k.value.mssql.username=e),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{"label.trim":"密码"},{default:r((()=>[n(U,{modelValue:k.value.mssql.password,"onUpdate:modelValue":l[68]||(l[68]=e=>k.value.mssql.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.mssql.path,"onUpdate:modelValue":l[69]||(l[69]=e=>k.value.mssql.path=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"端口"},{default:r((()=>[n(U,{modelValue:k.value.mssql.port,"onUpdate:modelValue":l[70]||(l[70]=e=>k.value.mssql.port=e),modelModifiers:{trim:!0},placeholder:"请输入端口"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库"},{default:r((()=>[n(U,{modelValue:k.value.mssql["db-name"],"onUpdate:modelValue":l[71]||(l[71]=e=>k.value.mssql["db-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])])),_:1}),n(o,{label:"前缀"},{default:r((()=>[n(U,{modelValue:k.value.mssql.prefix,"onUpdate:modelValue":l[72]||(l[72]=e=>k.value.mssql.prefix=e),modelModifiers:{trim:!0},placeholder:"请输入前缀"},null,8,["modelValue"])])),_:1}),n(o,{label:"复数表"},{default:r((()=>[n(y,{modelValue:k.value.mssql.singular,"onUpdate:modelValue":l[73]||(l[73]=e=>k.value.mssql.singular=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"引擎"},{default:r((()=>[n(U,{modelValue:k.value.mssql.engine,"onUpdate:modelValue":l[74]||(l[74]=e=>k.value.mssql.engine=e),modelModifiers:{trim:!0},placeholder:"请输入引擎"},null,8,["modelValue"])])),_:1}),n(o,{label:"maxIdleConns"},{default:r((()=>[n(a,{modelValue:k.value.mssql["max-idle-conns"],"onUpdate:modelValue":l[75]||(l[75]=e=>k.value.mssql["max-idle-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"maxOpenConns"},{default:r((()=>[n(a,{modelValue:k.value.mssql["max-open-conns"],"onUpdate:modelValue":l[76]||(l[76]=e=>k.value.mssql["max-open-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"写入日志"},{default:r((()=>[n(y,{modelValue:k.value.mssql["log-zap"],"onUpdate:modelValue":l[77]||(l[77]=e=>k.value.mssql["log-zap"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"日志模式"},{default:r((()=>[n(d,{modelValue:k.value.mssql["log-mode"],"onUpdate:modelValue":l[78]||(l[78]=e=>k.value.mssql["log-mode"]=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1})],64)):c("",!0),"sqlite"===k.value.system["db-type"]?(s(),t(v,{key:3},[n(o,{label:""},{default:r((()=>l[156]||(l[156]=[i("h3",null,"sqlite",-1)]))),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.sqlite.username,"onUpdate:modelValue":l[79]||(l[79]=e=>k.value.sqlite.username=e),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.sqlite.password,"onUpdate:modelValue":l[80]||(l[80]=e=>k.value.sqlite.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.sqlite.path,"onUpdate:modelValue":l[81]||(l[81]=e=>k.value.sqlite.path=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"端口"},{default:r((()=>[n(U,{modelValue:k.value.sqlite.port,"onUpdate:modelValue":l[82]||(l[82]=e=>k.value.sqlite.port=e),modelModifiers:{trim:!0},placeholder:"请输入端口"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库"},{default:r((()=>[n(U,{modelValue:k.value.sqlite["db-name"],"onUpdate:modelValue":l[83]||(l[83]=e=>k.value.sqlite["db-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])])),_:1}),n(o,{label:"maxIdleConns"},{default:r((()=>[n(a,{modelValue:k.value.sqlite["max-idle-conns"],"onUpdate:modelValue":l[84]||(l[84]=e=>k.value.sqlite["max-idle-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"maxOpenConns"},{default:r((()=>[n(a,{modelValue:k.value.sqlite["max-open-conns"],"onUpdate:modelValue":l[85]||(l[85]=e=>k.value.sqlite["max-open-conns"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"写入日志"},{default:r((()=>[n(y,{modelValue:k.value.sqlite["log-zap"],"onUpdate:modelValue":l[86]||(l[86]=e=>k.value.sqlite["log-zap"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"日志模式"},{default:r((()=>[n(d,{modelValue:k.value.sqlite["log-mode"],"onUpdate:modelValue":l[87]||(l[87]=e=>k.value.sqlite["log-mode"]=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1})],64)):c("",!0),"oracle"===k.value.system["db-type"]?(s(),t(v,{key:4},[n(o,{label:""},{default:r((()=>l[157]||(l[157]=[i("h3",null,"oracle",-1)]))),_:1}),n(o,{label:"用户名"},{default:r((()=>[n(U,{modelValue:k.value.oracle.username,"onUpdate:modelValue":l[88]||(l[88]=e=>k.value.oracle.username=e),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),n(o,{label:"密码"},{default:r((()=>[n(U,{modelValue:k.value.oracle.password,"onUpdate:modelValue":l[89]||(l[89]=e=>k.value.oracle.password=e),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])])),_:1}),n(o,{label:"地址"},{default:r((()=>[n(U,{modelValue:k.value.oracle.path,"onUpdate:modelValue":l[90]||(l[90]=e=>k.value.oracle.path=e),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"数据库名称"},{default:r((()=>[n(U,{modelValue:k.value.oracle["db-name"],"onUpdate:modelValue":l[91]||(l[91]=e=>k.value.oracle["db-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入数据库名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"前缀"},{default:r((()=>[n(U,{modelValue:k.value.oracle.prefix,"onUpdate:modelValue":l[92]||(l[92]=e=>k.value.oracle.prefix=e),modelModifiers:{trim:!0},placeholder:"默认为空"},null,8,["modelValue"])])),_:1}),n(o,{label:"复数表"},{default:r((()=>[n(y,{modelValue:k.value.oracle.singular,"onUpdate:modelValue":l[93]||(l[93]=e=>k.value.oracle.singular=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"引擎"},{default:r((()=>[n(U,{modelValue:k.value.oracle.engine,"onUpdate:modelValue":l[94]||(l[94]=e=>k.value.oracle.engine=e),modelModifiers:{trim:!0},placeholder:"默认为InnoDB"},null,8,["modelValue"])])),_:1}),n(o,{label:"maxIdleConns"},{default:r((()=>[n(a,{modelValue:k.value.oracle["max-idle-conns"],"onUpdate:modelValue":l[95]||(l[95]=e=>k.value.oracle["max-idle-conns"]=e),min:1},null,8,["modelValue"])])),_:1}),n(o,{label:"maxOpenConns"},{default:r((()=>[n(a,{modelValue:k.value.oracle["max-open-conns"],"onUpdate:modelValue":l[96]||(l[96]=e=>k.value.oracle["max-open-conns"]=e),min:1},null,8,["modelValue"])])),_:1}),n(o,{label:"写入日志"},{default:r((()=>[n(y,{modelValue:k.value.oracle["log-zap"],"onUpdate:modelValue":l[97]||(l[97]=e=>k.value.oracle["log-zap"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"日志模式"},{default:r((()=>[n(d,{modelValue:k.value.oracle["log-mode"],"onUpdate:modelValue":l[98]||(l[98]=e=>k.value.oracle["log-mode"]=e)},{default:r((()=>[n(u,{value:"off",label:"关闭"}),n(u,{value:"fatal",label:"致命"}),n(u,{value:"error",label:"错误"}),n(u,{value:"warn",label:"警告"}),n(u,{value:"info",label:"信息"}),n(u,{value:"debug",label:"调试"}),n(u,{value:"trace",label:"跟踪"})])),_:1},8,["modelValue"])])),_:1})],64)):c("",!0)])),_:1}),n(x,{label:"oss配置",name:"10",class:"mt-3.5"},{default:r((()=>["local"===k.value.system["oss-type"]?(s(),t(v,{key:0},[l[158]||(l[158]=i("h2",null,"本地配置",-1)),n(o,{label:"本地文件访问路径"},{default:r((()=>[n(U,{modelValue:k.value.local.path,"onUpdate:modelValue":l[99]||(l[99]=e=>k.value.local.path=e),modelModifiers:{trim:!0},placeholder:"请输入本地文件访问路径"},null,8,["modelValue"])])),_:1}),n(o,{label:"本地文件存储路径"},{default:r((()=>[n(U,{modelValue:k.value.local["store-path"],"onUpdate:modelValue":l[100]||(l[100]=e=>k.value.local["store-path"]=e),modelModifiers:{trim:!0},placeholder:"请输入本地文件存储路径"},null,8,["modelValue"])])),_:1})],64)):c("",!0),"qiniu"===k.value.system["oss-type"]?(s(),t(v,{key:1},[l[160]||(l[160]=i("h2",null,"七牛上传配置",-1)),n(o,{label:"存储区域"},{default:r((()=>[n(U,{modelValue:k.value.qiniu.zone,"onUpdate:modelValue":l[101]||(l[101]=e=>k.value.qiniu.zone=e),modelModifiers:{trim:!0},placeholder:"请输入存储区域"},null,8,["modelValue"])])),_:1}),n(o,{label:"空间名称"},{default:r((()=>[n(U,{modelValue:k.value.qiniu.bucket,"onUpdate:modelValue":l[102]||(l[102]=e=>k.value.qiniu.bucket=e),modelModifiers:{trim:!0},placeholder:"请输入空间名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"CDN加速域名"},{default:r((()=>[n(U,{modelValue:k.value.qiniu["img-path"],"onUpdate:modelValue":l[103]||(l[103]=e=>k.value.qiniu["img-path"]=e),modelModifiers:{trim:!0},placeholder:"请输入CDN加速域名"},null,8,["modelValue"])])),_:1}),n(o,{label:"是否使用https"},{default:r((()=>[n(y,{modelValue:k.value.qiniu["use-https"],"onUpdate:modelValue":l[104]||(l[104]=e=>k.value.qiniu["use-https"]=e)},{default:r((()=>l[159]||(l[159]=[V("开启")]))),_:1},8,["modelValue"])])),_:1}),n(o,{label:"accessKey"},{default:r((()=>[n(U,{modelValue:k.value.qiniu["access-key"],"onUpdate:modelValue":l[105]||(l[105]=e=>k.value.qiniu["access-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入accessKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"secretKey"},{default:r((()=>[n(U,{modelValue:k.value.qiniu["secret-key"],"onUpdate:modelValue":l[106]||(l[106]=e=>k.value.qiniu["secret-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"上传是否使用CDN上传加速"},{default:r((()=>[n(y,{modelValue:k.value.qiniu["use-cdn-domains"],"onUpdate:modelValue":l[107]||(l[107]=e=>k.value.qiniu["use-cdn-domains"]=e)},null,8,["modelValue"])])),_:1})],64)):c("",!0),"tencent-cos"===k.value.system["oss-type"]?(s(),t(v,{key:2},[l[161]||(l[161]=i("h2",null,"腾讯云COS上传配置",-1)),n(o,{label:"存储桶名称"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"].bucket,"onUpdate:modelValue":l[108]||(l[108]=e=>k.value["tencent-cos"].bucket=e),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"所属地域"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"].region,"onUpdate:modelValue":l[109]||(l[109]=e=>k.value["tencent-cos"].region=e),modelModifiers:{trim:!0},placeholder:"请输入所属地域"},null,8,["modelValue"])])),_:1}),n(o,{label:"secretID"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"]["secret-id"],"onUpdate:modelValue":l[110]||(l[110]=e=>k.value["tencent-cos"]["secret-id"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretID"},null,8,["modelValue"])])),_:1}),n(o,{label:"secretKey"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"]["secret-key"],"onUpdate:modelValue":l[111]||(l[111]=e=>k.value["tencent-cos"]["secret-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"路径前缀"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"]["path-prefix"],"onUpdate:modelValue":l[112]||(l[112]=e=>k.value["tencent-cos"]["path-prefix"]=e),modelModifiers:{trim:!0},placeholder:"请输入路径前缀"},null,8,["modelValue"])])),_:1}),n(o,{label:"访问域名"},{default:r((()=>[n(U,{modelValue:k.value["tencent-cos"]["base-url"],"onUpdate:modelValue":l[113]||(l[113]=e=>k.value["tencent-cos"]["base-url"]=e),modelModifiers:{trim:!0},placeholder:"请输入访问域名"},null,8,["modelValue"])])),_:1})],64)):c("",!0),"aliyun-oss"===k.value.system["oss-type"]?(s(),t(v,{key:3},[l[162]||(l[162]=i("h2",null,"阿里云OSS上传配置",-1)),n(o,{label:"区域"},{default:r((()=>[n(U,{modelValue:k.value["aliyun-oss"].endpoint,"onUpdate:modelValue":l[114]||(l[114]=e=>k.value["aliyun-oss"].endpoint=e),modelModifiers:{trim:!0},placeholder:"请输入区域"},null,8,["modelValue"])])),_:1}),n(o,{label:"accessKeyId"},{default:r((()=>[n(U,{modelValue:k.value["aliyun-oss"]["access-key-id"],"onUpdate:modelValue":l[115]||(l[115]=e=>k.value["aliyun-oss"]["access-key-id"]=e),modelModifiers:{trim:!0},placeholder:"请输入accessKeyId"},null,8,["modelValue"])])),_:1}),n(o,{label:"accessKeySecret"},{default:r((()=>[n(U,{modelValue:k.value["aliyun-oss"]["access-key-secret"],"onUpdate:modelValue":l[116]||(l[116]=e=>k.value["aliyun-oss"]["access-key-secret"]=e),modelModifiers:{trim:!0},placeholder:"请输入accessKeySecret"},null,8,["modelValue"])])),_:1}),n(o,{label:"存储桶名称"},{default:r((()=>[n(U,{modelValue:k.value["aliyun-oss"]["bucket-name"],"onUpdate:modelValue":l[117]||(l[117]=e=>k.value["aliyun-oss"]["bucket-name"]=e),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"访问域名"},{default:r((()=>[n(U,{modelValue:k.value["aliyun-oss"]["bucket-url"],"onUpdate:modelValue":l[118]||(l[118]=e=>k.value["aliyun-oss"]["bucket-url"]=e),modelModifiers:{trim:!0},placeholder:"请输入访问域名"},null,8,["modelValue"])])),_:1})],64)):c("",!0),"huawei-obs"===k.value.system["oss-type"]?(s(),t(v,{key:4},[l[163]||(l[163]=i("h2",null,"华为云OBS上传配置",-1)),n(o,{label:"路径"},{default:r((()=>[n(U,{modelValue:k.value["hua-wei-obs"].path,"onUpdate:modelValue":l[119]||(l[119]=e=>k.value["hua-wei-obs"].path=e),modelModifiers:{trim:!0},placeholder:"请输入路径"},null,8,["modelValue"])])),_:1}),n(o,{label:"存储桶名称"},{default:r((()=>[n(U,{modelValue:k.value["hua-wei-obs"].bucket,"onUpdate:modelValue":l[120]||(l[120]=e=>k.value["hua-wei-obs"].bucket=e),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"区域"},{default:r((()=>[n(U,{modelValue:k.value["hua-wei-obs"].endpoint,"onUpdate:modelValue":l[121]||(l[121]=e=>k.value["hua-wei-obs"].endpoint=e),modelModifiers:{trim:!0},placeholder:"请输入区域"},null,8,["modelValue"])])),_:1}),n(o,{label:"accessKey"},{default:r((()=>[n(U,{modelValue:k.value["hua-wei-obs"]["access-key"],"onUpdate:modelValue":l[122]||(l[122]=e=>k.value["hua-wei-obs"]["access-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入accessKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"secretKey"},{default:r((()=>[n(U,{modelValue:k.value["hua-wei-obs"]["secret-key"],"onUpdate:modelValue":l[123]||(l[123]=e=>k.value["hua-wei-obs"]["secret-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1})],64)):c("",!0),"cloudflare-r2"===k.value.system["oss-type"]?(s(),t(v,{key:5},[l[164]||(l[164]=i("h2",null,"Cloudflare R2上传配置",-1)),n(o,{label:"路径"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"].path,"onUpdate:modelValue":l[124]||(l[124]=e=>k.value["cloudflare-r2"].path=e),modelModifiers:{trim:!0},placeholder:"请输入路径"},null,8,["modelValue"])])),_:1}),n(o,{label:"存储桶名称"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"].bucket,"onUpdate:modelValue":l[125]||(l[125]=e=>k.value["cloudflare-r2"].bucket=e),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])])),_:1}),n(o,{label:"Base URL"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"]["base-url"],"onUpdate:modelValue":l[126]||(l[126]=e=>k.value["cloudflare-r2"]["base-url"]=e),modelModifiers:{trim:!0},placeholder:"请输入Base URL"},null,8,["modelValue"])])),_:1}),n(o,{label:"Account ID"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"]["account-id"],"onUpdate:modelValue":l[127]||(l[127]=e=>k.value["cloudflare-r2"]["account-id"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"Access Key ID"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"]["access-key-id"],"onUpdate:modelValue":l[128]||(l[128]=e=>k.value["cloudflare-r2"]["access-key-id"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1}),n(o,{label:"Secret Access Key"},{default:r((()=>[n(U,{modelValue:k.value["cloudflare-r2"]["secret-access-key"],"onUpdate:modelValue":l[129]||(l[129]=e=>k.value["cloudflare-r2"]["secret-access-key"]=e),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])])),_:1})],64)):c("",!0)])),_:1}),n(x,{label:"Excel上传配置",name:"11",class:"mt-3.5"},{default:r((()=>[n(o,{label:"合成目标地址"},{default:r((()=>[n(U,{modelValue:k.value.excel.dir,"onUpdate:modelValue":l[130]||(l[130]=e=>k.value.excel.dir=e),modelModifiers:{trim:!0},placeholder:"请输入合成目标地址"},null,8,["modelValue"])])),_:1})])),_:1}),n(x,{label:"自动化代码配置",name:"12",class:"mt-3.5"},{default:r((()=>[n(o,{label:"是否自动重启(linux)"},{default:r((()=>[n(y,{modelValue:k.value.autocode["transfer-restart"],"onUpdate:modelValue":l[131]||(l[131]=e=>k.value.autocode["transfer-restart"]=e)},null,8,["modelValue"])])),_:1}),n(o,{label:"root(项目根路径)"},{default:r((()=>[n(U,{modelValue:k.value.autocode.root,"onUpdate:modelValue":l[132]||(l[132]=e=>k.value.autocode.root=e),disabled:""},null,8,["modelValue"])])),_:1}),n(o,{label:"Server(后端代码地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode.server,"onUpdate:modelValue":l[133]||(l[133]=e=>k.value.autocode.server=e),modelModifiers:{trim:!0},placeholder:"请输入后端代码地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"SApi(后端api文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-api"],"onUpdate:modelValue":l[134]||(l[134]=e=>k.value.autocode["server-api"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端api文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"SInitialize(后端Initialize文件夹)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-initialize"],"onUpdate:modelValue":l[135]||(l[135]=e=>k.value.autocode["server-initialize"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端Initialize文件夹"},null,8,["modelValue"])])),_:1}),n(o,{label:"SModel(后端Model文件地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-model"],"onUpdate:modelValue":l[136]||(l[136]=e=>k.value.autocode["server-model"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端Model文件地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"SRequest(后端Request文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-request"],"onUpdate:modelValue":l[137]||(l[137]=e=>k.value.autocode["server-request"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端Request文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"SRouter(后端Router文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-router"],"onUpdate:modelValue":l[138]||(l[138]=e=>k.value.autocode["server-router"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端Router文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"SService(后端Service文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["server-service"],"onUpdate:modelValue":l[139]||(l[139]=e=>k.value.autocode["server-service"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端Service文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"Web(前端文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode.web,"onUpdate:modelValue":l[140]||(l[140]=e=>k.value.autocode.web=e),modelModifiers:{trim:!0},placeholder:"请输入前端文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"WApi(后端WApi文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["web-api"],"onUpdate:modelValue":l[141]||(l[141]=e=>k.value.autocode["web-api"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端WApi文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"WForm(后端WForm文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["web-form"],"onUpdate:modelValue":l[142]||(l[142]=e=>k.value.autocode["web-form"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端WForm文件夹地址"},null,8,["modelValue"])])),_:1}),n(o,{label:"WTable(后端WTable文件夹地址)"},{default:r((()=>[n(U,{modelValue:k.value.autocode["web-table"],"onUpdate:modelValue":l[143]||(l[143]=e=>k.value.autocode["web-table"]=e),modelModifiers:{trim:!0},placeholder:"请输入后端WTable文件夹地址"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["modelValue"])])),_:1},8,["model"]),i("div",M,[n(j,{type:"primary",onClick:C},{default:r((()=>l[165]||(l[165]=[V("立即更新 ")]))),_:1}),n(j,{type:"primary",onClick:z},{default:r((()=>l[166]||(l[166]=[V("重启服务 ")]))),_:1})])])}}}),[["__scopeId","data-v-4261dc95"]]);export{w as default};
