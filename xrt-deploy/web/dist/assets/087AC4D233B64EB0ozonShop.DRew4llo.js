/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{a as e,I as a,r as l,g as t,c as u,o,b as d,f as n,w as r,d as i,h as s,F as c,i as p,l as v,t as m,v as f,aa as y,az as g,D as b,aA as h,ab as w,E as C}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as _,f as A,c as k,u as I,d as V,a as D}from"./087AC4D233B64EB0ozonShop.DxUsuQjM.js";const T={class:"gva-search-box"},z={class:"gva-table-box"},x={class:"gva-btn-list"},S={class:"gva-pagination"},U={class:"flex justify-between items-center"},B={class:"text-lg"},E=Object.assign({name:"OzonShop"},{__name:"ozonShop",setup(E){const K=e(!1),N=a(),P=e(!1),Y=e([]),j=e({name:"",clientID:"",APIKey:"",shopType:"",currency:"CNY"}),F=l({}),R=l({createdAt:[{validator:(e,a,l)=>{H.value.startCreatedAt&&!H.value.endCreatedAt?l(new Error("请填写结束日期")):!H.value.startCreatedAt&&H.value.endCreatedAt?l(new Error("请填写开始日期")):H.value.startCreatedAt&&H.value.endCreatedAt&&(H.value.startCreatedAt.getTime()===H.value.endCreatedAt.getTime()||H.value.startCreatedAt.getTime()>H.value.endCreatedAt.getTime())?l(new Error("开始日期应当早于结束日期")):l()},trigger:"change"}]}),O=e(),M=e(),Q=e(1),W=e(0),q=e(10),G=e([]),H=e({}),J=()=>{H.value={},$()},L=()=>{var e;null==(e=M.value)||e.validate((async e=>{e&&(Q.value=1,$())}))},X=e=>{q.value=e,$()},Z=e=>{Q.value=e,$()},$=async()=>{const e=await _({page:Q.value,pageSize:q.value,...H.value});0===e.code&&(G.value=e.data.list,W.value=e.data.total,Q.value=e.data.page,q.value=e.data.pageSize)};$();(async()=>{Y.value=await h("Store_Type")})();const ee=e([]),ae=e=>{ee.value=e},le=async()=>{w.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===ee.value.length)return void C({type:"warning",message:"请选择要删除的数据"});ee.value&&ee.value.map((a=>{e.push(a.ID)}));0===(await D({IDs:e})).code&&(C({type:"success",message:"删除成功"}),G.value.length===e.length&&Q.value>1&&Q.value--,$())}))},te=e(""),ue=async e=>{0===(await V({ID:e.ID})).code&&(C({type:"success",message:"删除成功"}),1===G.value.length&&Q.value>1&&Q.value--,$())},oe=e(!1),de=()=>{oe.value=!1,j.value={name:"",clientID:"",APIKey:"",shopType:"",currency:"CNY"}},ne=async()=>{var e;K.value=!0,null==(e=O.value)||e.validate((async e=>{if(!e)return K.value=!1;let a;switch(te.value){case"create":default:a=await k(j.value);break;case"update":a=await I(j.value)}K.value=!1,0===a.code&&(C({type:"success",message:"创建/更改成功"}),de(),$())}))},re=e({}),ie=e(!1),se=async e=>{const a=await A({ID:e.ID});0===a.code&&(re.value=a.data,ie.value=!0)},ce=()=>{ie.value=!1,re.value={}};return(e,a)=>{const l=t("QuestionFilled"),h=t("el-icon"),C=t("el-tooltip"),_=t("el-date-picker"),k=t("el-form-item"),I=t("el-button"),V=t("el-form"),D=t("el-table-column"),E=t("InfoFilled"),$=t("el-table"),pe=t("el-pagination"),ve=t("el-input"),me=t("el-option"),fe=t("el-select"),ye=t("el-drawer"),ge=t("el-descriptions-item"),be=t("el-descriptions");return o(),u("div",null,[d("div",T,[n(V,{ref_key:"elSearchFormRef",ref:M,inline:!0,model:H.value,class:"demo-form-inline",rules:R,onKeyup:v(L,["enter"])},{default:r((()=>[n(k,{label:"创建日期",prop:"createdAt"},{label:r((()=>[d("span",null,[a[12]||(a[12]=s(" 创建日期 ")),n(C,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:r((()=>[n(h,null,{default:r((()=>[n(l)])),_:1})])),_:1})])])),default:r((()=>[n(_,{modelValue:H.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>H.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!H.value.endCreatedAt&&e.getTime()>H.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[13]||(a[13]=s(" — ")),n(_,{modelValue:H.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>H.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!H.value.startCreatedAt&&e.getTime()<H.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),P.value?(o(),u(c,{key:0},[],64)):i("",!0),n(k,null,{default:r((()=>[n(I,{type:"primary",icon:"search",onClick:L},{default:r((()=>a[14]||(a[14]=[s("查询")]))),_:1}),n(I,{icon:"refresh",onClick:J},{default:r((()=>a[15]||(a[15]=[s("重置")]))),_:1}),P.value?(o(),p(I,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[3]||(a[3]=e=>P.value=!1)},{default:r((()=>a[17]||(a[17]=[s("收起")]))),_:1})):(o(),p(I,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[2]||(a[2]=e=>P.value=!0)},{default:r((()=>a[16]||(a[16]=[s("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),d("div",z,[d("div",x,[n(I,{type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>(te.value="create",void(oe.value=!0)))},{default:r((()=>a[18]||(a[18]=[s("新增")]))),_:1}),n(I,{icon:"delete",style:{"margin-left":"10px"},disabled:!ee.value.length,onClick:le},{default:r((()=>a[19]||(a[19]=[s("删除")]))),_:1},8,["disabled"])]),n($,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:G.value,"row-key":"ID",onSelectionChange:ae},{default:r((()=>[n(D,{type:"selection",width:"55"}),n(D,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:r((e=>[s(m(f(y)(e.row.CreatedAt)),1)])),_:1}),n(D,{align:"left",label:"店铺名字",prop:"name",width:"120"}),n(D,{align:"left",label:"店铺ID",prop:"clientID",width:"120"}),n(D,{align:"left",label:"用户名",prop:"accountName",width:"120"}),n(D,{align:"left",label:"店铺类型",prop:"shopType",width:"120"},{default:r((e=>[s(m(f(g)(e.row.shopType,Y.value)),1)])),_:1}),n(D,{align:"left",label:"结算货币",prop:"currency",width:"100"},{default:r((e=>[s(m(e.row.currency||"CNY"),1)])),_:1}),n(D,{align:"left",label:"操作",fixed:"right","min-width":f(N).operateMinWith},{default:r((e=>[n(I,{type:"primary",link:"",class:"table-button",onClick:a=>se(e.row)},{default:r((()=>[n(h,{style:{"margin-right":"5px"}},{default:r((()=>[n(E)])),_:1}),a[20]||(a[20]=s("查看"))])),_:2},1032,["onClick"]),n(I,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await A({ID:e.ID});te.value="update",0===a.code&&(j.value=a.data,oe.value=!0)})(e.row)},{default:r((()=>a[21]||(a[21]=[s("编辑")]))),_:2},1032,["onClick"]),n(I,{type:"primary",link:"",icon:"delete",onClick:a=>{return l=e.row,void w.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ue(l)}));var l}},{default:r((()=>a[22]||(a[22]=[s("删除")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),d("div",S,[n(pe,{layout:"total, sizes, prev, pager, next, jumper","current-page":Q.value,"page-size":q.value,"page-sizes":[10,30,50,100],total:W.value,onCurrentChange:Z,onSizeChange:X},null,8,["current-page","page-size","total"])])]),n(ye,{"destroy-on-close":"",size:f(N).drawerSize,modelValue:oe.value,"onUpdate:modelValue":a[10]||(a[10]=e=>oe.value=e),"show-close":!1,"before-close":de},{header:r((()=>[d("div",U,[d("span",B,m("create"===te.value?"新增":"编辑"),1),d("div",null,[n(I,{loading:K.value,type:"primary",onClick:ne},{default:r((()=>a[23]||(a[23]=[s("确 定")]))),_:1},8,["loading"]),n(I,{onClick:de},{default:r((()=>a[24]||(a[24]=[s("取 消")]))),_:1})])])])),default:r((()=>[n(V,{model:j.value,"label-position":"top",ref_key:"elFormRef",ref:O,rules:F,"label-width":"80px"},{default:r((()=>[n(k,{label:"店铺名字:",prop:"name"},{default:r((()=>[n(ve,{modelValue:j.value.name,"onUpdate:modelValue":a[5]||(a[5]=e=>j.value.name=e),clearable:!0,placeholder:"请输入店铺名字"},null,8,["modelValue"])])),_:1}),n(k,{label:"店铺ID:",prop:"clientID"},{default:r((()=>[n(ve,{modelValue:j.value.clientID,"onUpdate:modelValue":a[6]||(a[6]=e=>j.value.clientID=e),clearable:!0,placeholder:"请输入店铺ID"},null,8,["modelValue"])])),_:1}),n(k,{label:"APIKey字段:",prop:"APIKey"},{default:r((()=>[n(ve,{modelValue:j.value.APIKey,"onUpdate:modelValue":a[7]||(a[7]=e=>j.value.APIKey=e),clearable:!0,placeholder:"请输入APIKey字段"},null,8,["modelValue"])])),_:1}),n(k,{label:"店铺类型:",prop:"shopType"},{default:r((()=>[n(fe,{modelValue:j.value.shopType,"onUpdate:modelValue":a[8]||(a[8]=e=>j.value.shopType=e),placeholder:"请选择店铺类型",style:{width:"100%"},clearable:!0},{default:r((()=>[(o(!0),u(c,null,b(Y.value,((e,a)=>(o(),p(me,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(k,{label:"结算货币:",prop:"currency"},{default:r((()=>[n(fe,{modelValue:j.value.currency,"onUpdate:modelValue":a[9]||(a[9]=e=>j.value.currency=e),placeholder:"请选择结算货币",style:{width:"100%"},clearable:!0},{default:r((()=>[n(me,{label:"人民币 (CNY)",value:"CNY"}),n(me,{label:"美元 (USD)",value:"USD"}),n(me,{label:"欧元 (EUR)",value:"EUR"})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["size","modelValue"]),n(ye,{"destroy-on-close":"",size:f(N).drawerSize,modelValue:ie.value,"onUpdate:modelValue":a[11]||(a[11]=e=>ie.value=e),"show-close":!0,"before-close":ce,title:"查看"},{default:r((()=>[n(be,{column:1,border:""},{default:r((()=>[n(ge,{label:"店铺名字"},{default:r((()=>[s(m(re.value.name),1)])),_:1}),n(ge,{label:"店铺ID"},{default:r((()=>[s(m(re.value.clientID),1)])),_:1}),n(ge,{label:"用户名"},{default:r((()=>[s(m(re.value.accountName),1)])),_:1}),n(ge,{label:"店铺类型"},{default:r((()=>[s(m(f(g)(re.value.shopType,Y.value)),1)])),_:1}),n(ge,{label:"结算货币"},{default:r((()=>[s(m(re.value.currency||"CNY"),1)])),_:1})])),_:1})])),_:1},8,["size","modelValue"])])}}});export{E as default};
