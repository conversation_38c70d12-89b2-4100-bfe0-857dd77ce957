/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{ac as e,g as a,c as s,o as n,f as t,w as i,a9 as o,i as u,ad as l,v as d,Y as r}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const c=Object.assign({name:"System"},{__name:"index",setup(c){const m=e();return(e,c)=>{const f=a("router-view");return n(),s("div",null,[t(f,null,{default:i((({Component:e})=>[t(o,{mode:"out-in",name:"el-fade-in-linear"},{default:i((()=>[(n(),u(l,{include:d(m).keepAliveRouters},[(n(),u(r(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{c as default};
