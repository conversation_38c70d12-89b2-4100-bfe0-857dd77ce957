/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{ac as e,g as a,c as n,o as s,f as t,w as i,a9 as u,i as o,ad as d,v as l,Y as r}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const c=Object.assign({name:"SuperAdmin"},{__name:"index",setup(c){const m=e();return(e,c)=>{const f=a("router-view");return s(),n("div",null,[t(f,null,{default:i((({Component:e})=>[t(u,{mode:"out-in",name:"el-fade-in-linear"},{default:i((()=>[(s(),o(d,{include:l(m).keepAliveRouters},[(s(),o(r(e)))],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{c as default};
