/*! 
 Build based on gin-vue-admin 
 Time : ************* */
import{f as e,c as a,u as l}from"./087AC4D233B64EB0ozonShop.CCOgv6DF.js";import{aj as u,u as o,a as t,r,g as n,c as s,o as d,b as c,f as i,w as m,h as p,E as v}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const f={class:"gva-form-box"},g=Object.assign({name:"OzonShopForm"},{__name:"ozonShopForm",setup(g){const I=u(),b=o(),y=t(!1),D=t(""),V=t({name:"",clientID:"",APIKey:"",accountID:""}),h=r({name:[{required:!0,message:"",trigger:["input","blur"]}],clientID:[{required:!0,message:"",trigger:["input","blur"]}],APIKey:[{required:!0,message:"",trigger:["input","blur"]}],accountID:[{required:!0,message:"",trigger:["input","blur"]}]}),_=t();(async()=>{if(I.query.id){const a=await e({ID:I.query.id});0===a.code&&(V.value=a.data,D.value="update")}else D.value="create"})();const A=async()=>{var e;y.value=!0,null==(e=_.value)||e.validate((async e=>{if(!e)return y.value=!1;let u;switch(D.value){case"create":default:u=await a(V.value);break;case"update":u=await l(V.value)}y.value=!1,0===u.code&&v({type:"success",message:"创建/更改成功"})}))},K=()=>{b.go(-1)};return(e,a)=>{const l=n("el-input"),u=n("el-form-item"),o=n("el-button"),t=n("el-form");return d(),s("div",null,[c("div",f,[i(t,{model:V.value,ref_key:"elFormRef",ref:_,"label-position":"right",rules:h,"label-width":"80px"},{default:m((()=>[i(u,{label:"店铺名称:",prop:"name"},{default:m((()=>[i(l,{modelValue:V.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>V.value.name=e),clearable:!0,placeholder:"请输入店铺名称"},null,8,["modelValue"])])),_:1}),i(u,{label:"店铺ID:",prop:"clientID"},{default:m((()=>[i(l,{modelValue:V.value.clientID,"onUpdate:modelValue":a[1]||(a[1]=e=>V.value.clientID=e),clearable:!0,placeholder:"请输入店铺ID"},null,8,["modelValue"])])),_:1}),i(u,{label:"APIKey:",prop:"APIKey"},{default:m((()=>[i(l,{modelValue:V.value.APIKey,"onUpdate:modelValue":a[2]||(a[2]=e=>V.value.APIKey=e),clearable:!0,placeholder:"请输入APIKey"},null,8,["modelValue"])])),_:1}),i(u,{label:"关联的账号:",prop:"accountID"},{default:m((()=>[i(l,{modelValue:V.value.accountID,"onUpdate:modelValue":a[3]||(a[3]=e=>V.value.accountID=e),clearable:!0,placeholder:"请输入关联的账号"},null,8,["modelValue"])])),_:1}),i(u,null,{default:m((()=>[i(o,{loading:y.value,type:"primary",onClick:A},{default:m((()=>a[4]||(a[4]=[p("保存")]))),_:1},8,["loading"]),i(o,{type:"primary",onClick:K},{default:m((()=>a[5]||(a[5]=[p("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])}}});export{g as default};
