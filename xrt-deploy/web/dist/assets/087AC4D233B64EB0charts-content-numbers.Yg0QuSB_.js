/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{u as t,L as o,_ as e}from"./087AC4D233B64EB0index.Dn-Bz6sd.js";import{I as a,J as l,K as i,a as s,i as r,o as n,v as c}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const p={__name:"charts-content-numbers",props:{height:{type:String,default:"128px"}},setup(p){const h=a(),{config:u}=l(h),y=i((()=>h.isDark?"#333":"#E5E8EF")),f=t=>({type:"text",bottom:"8",...t,style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}}),m=s(["2024-1","2024-2","2024-3","2024-4","2024-5","2024-6","2024-7","2024-8"]),v=s([12,22,32,45,32,78,89,92]),d=s([f({left:"5%"}),f({right:0})]),{chartOption:x}=t((()=>({grid:{left:"40",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,data:m.value,boundaryGap:!1,axisLabel:{color:"#4E5969",formatter:(t,o)=>0===o||o===m.value.length-1?"":"".concat(t)},axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!0,interval:t=>0!==t&&t!==m.value.length-1,lineStyle:{color:y.value}},axisPointer:{show:!0,lineStyle:{color:"".concat(u.value.primaryColor,"FF"),width:2}}},yAxis:{type:"value",axisLine:{show:!1},axisLabel:{formatter:(t,o)=>0===o?t:"".concat(t,"k")},splitLine:{show:!0,lineStyle:{type:"dashed",color:y.value}}},tooltip:{trigger:"axis",formatter(t){const[o]=t;return'<div>\n            <p class="tooltip-title">'.concat(o.axisValueLabel,'</p>\n            <div class="content-panel"><span>总内容量</span><span class="tooltip-value">').concat((1e4*Number(o.value)).toLocaleString(),"</span></div>\n          </div>")},className:"echarts-tooltip-diy"},graphic:{elements:d.value},series:[{data:v.value,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new o(0,0,1,0,[{offset:0,color:"".concat(u.value.primaryColor,"80")},{offset:.5,color:"".concat(u.value.primaryColor,"92")},{offset:1,color:"".concat(u.value.primaryColor,"FF")}])},showSymbol:!1,areaStyle:{opacity:.8,color:new o(0,0,0,1,[{offset:0,color:"".concat(u.value.primaryColor,"20")},{offset:1,color:"".concat(u.value.primaryColor,"08")}])}}]})));return(t,o)=>(n(),r(e,{height:p.height,option:c(x)},null,8,["height","option"]))}};export{p as default};
