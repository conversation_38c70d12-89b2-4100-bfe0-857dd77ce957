/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{j as e}from"./087AC4D233B64EB0api.DOS2t6hl.js";import{s as a,a as t,Q as l,g as s,c as o,o as n,b as r,f as u,w as d,h as p,t as c,E as i}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const h={class:"sticky top-0.5 z-10 flex space-x-2"},f={class:"tree-content"},v={class:"flex items-center justify-between w-full pr-1"},m={class:"max-w-[240px] break-all overflow-ellipsis overflow-hidden"},y=Object.assign({name:"Apis"},{__name:"apis",props:{row:{default:function(){return{}},type:Object}},setup(y,{expose:b}){const w=y,x=t({children:"children",label:"description"}),k=t(""),g=t(""),I=t([]),j=t([]),C=t("");(async()=>{const t=(await e()).data.apis;I.value=V(t);const l=await(s={authorityId:w.row.authorityId},a({url:"/casbin/getPolicyPathByAuthorityId",method:"post",data:s}));var s;C.value=w.row.authorityId,j.value=[],l.data.paths&&l.data.paths.forEach((e=>{j.value.push("p:"+e.path+"m:"+e.method)}))})();const _=t(!1),E=()=>{_.value=!0},V=e=>{const a={};e&&e.forEach((e=>{e.onlyId="p:"+e.path+"m:"+e.method,Object.prototype.hasOwnProperty.call(a,e.apiGroup)?a[e.apiGroup].push(e):Object.assign(a,{[e.apiGroup]:[e]})}));const t=[];for(const l in a){const e={ID:l,description:l+"组",children:a[l]};t.push(e)}return t},A=t(null),B=async()=>{const e=A.value.getCheckedNodes(!0);var t=[];e&&e.forEach((e=>{var a={path:e.path,method:e.method};t.push(a)}));var l;0===(await(l={authorityId:C.value,casbinInfos:t},a({url:"/casbin/updateCasbin",method:"post",data:l}))).code&&i({type:"success",message:"api设置成功"})};b({needConfirm:_,enterAndNext:()=>{B()}});const O=(e,a)=>{if(!k.value&&!g.value)return!0;let t,l;return t=!k.value||a.description&&a.description.includes(k.value),l=!g.value||a.path&&a.path.includes(g.value),t&&l};return l([k,g],(()=>{A.value.filter("")})),(e,a)=>{const t=s("el-input"),l=s("el-button"),i=s("el-tooltip"),y=s("el-tree"),b=s("el-scrollbar");return n(),o("div",null,[r("div",h,[u(t,{modelValue:k.value,"onUpdate:modelValue":a[0]||(a[0]=e=>k.value=e),class:"flex-1",placeholder:"筛选名字"},null,8,["modelValue"]),u(t,{modelValue:g.value,"onUpdate:modelValue":a[1]||(a[1]=e=>g.value=e),class:"flex-1",placeholder:"筛选路径"},null,8,["modelValue"]),u(l,{class:"float-right",type:"primary",onClick:B},{default:d((()=>a[2]||(a[2]=[p("确 定")]))),_:1})]),r("div",f,[u(b,null,{default:d((()=>[u(y,{ref_key:"apiTree",ref:A,data:I.value,"default-checked-keys":j.value,props:x.value,"default-expand-all":"","highlight-current":"","node-key":"onlyId","show-checkbox":"","filter-node-method":O,onCheck:E},{default:d((({_:e,data:a})=>[r("div",v,[r("span",null,c(a.description),1),u(i,{content:a.path},{default:d((()=>[r("span",m,c(a.path),1)])),_:2},1032,["content"])])])),_:1},8,["data","default-checked-keys","props"])])),_:1})])])}}});export{y as default};
