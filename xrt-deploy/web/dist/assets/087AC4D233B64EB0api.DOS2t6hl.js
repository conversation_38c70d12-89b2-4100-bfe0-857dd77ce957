/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as a}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const t=t=>a({url:"/api/getApiList",method:"post",data:t}),e=t=>a({url:"/api/createApi",method:"post",data:t}),p=t=>a({url:"/api/getApiById",method:"post",data:t}),s=t=>a({url:"/api/updateApi",method:"post",data:t}),d=t=>a({url:"/api/getAllApis",method:"post",data:t}),i=t=>a({url:"/api/deleteApi",method:"post",data:t}),o=t=>a({url:"/api/deleteApisByIds",method:"delete",data:t}),r=()=>a({url:"/api/freshCasbin",method:"get"}),l=()=>a({url:"/api/syncApi",method:"get"}),u=()=>a({url:"/api/getApiGroups",method:"get"}),h=t=>a({url:"/api/ignoreApi",method:"post",data:t}),m=t=>a({url:"/api/enterSyncApi",method:"post",data:t});export{u as a,p as b,e as c,i as d,m as e,o as f,t as g,r as h,h as i,d as j,l as s,s as u};
