/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{I as e,J as t,K as a,g as n,i as s,o,w as r,d as u,Y as i,h as l,t as m,X as f}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const d=Object.assign({name:"MenuItem"},{__name:"menuItem",props:{routerInfo:{default:function(){return null},type:Object}},setup(d){const c=e(),{config:I}=t(c),p=a((()=>I.value.layout_side_item_height+"px"));return(e,t)=>{const a=n("el-icon"),c=n("el-menu-item");return o(),s(c,{index:d.routerInfo.name,class:"dark:text-slate-300 overflow-hidden",style:f({height:p.value})},{title:r((()=>[l(m(d.routerInfo.meta.title),1)])),default:r((()=>[d.routerInfo.meta.icon?(o(),s(a,{key:0},{default:r((()=>[(o(),s(i(d.routerInfo.meta.icon)))])),_:1})):u("",!0)])),_:1},8,["index","style"])}}});export{d as default};
