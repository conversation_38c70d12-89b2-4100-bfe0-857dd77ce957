/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return n};var t,n={},r=Object.prototype,o=r.hasOwnProperty,u=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(t){p=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),i=new S(r||[]);return u(o,"_invoke",{value:j(e,n,i)}),o}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var v="suspendedStart",h="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function x(){}var _={};p(_,l,(function(){return this}));var k=Object.getPrototypeOf,A=k&&k(k(G([])));A&&A!==r&&o.call(A,l)&&(_=A);var C=x.prototype=b.prototype=Object.create(_);function I(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function E(t,n){function r(a,u,i,l){var c=d(t[a],t,u);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==e(p)&&o.call(p,"__await")?n.resolve(p.__await).then((function(e){r("next",e,i,l)}),(function(e){r("throw",e,i,l)})):n.resolve(p).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,l)}))}l(c.arg)}var a;u(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(o,o):o()}})}function j(e,n,r){var a=v;return function(o,u){if(a===m)throw Error("Generator is already running");if(a===y){if("throw"===o)throw u;return{value:t,done:!0}}for(r.method=o,r.arg=u;;){var i=r.delegate;if(i){var l=O(i,r);if(l){if(l===g)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===v)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var c=d(e,n,r);if("normal"===c.type){if(a=r.done?y:h,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=y,r.method="throw",r.arg=c.arg)}}}function O(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,O(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=d(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,g;var u=o.arg;return u?u.done?(n[e.resultName]=u.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,g):u:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function P(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function V(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function S(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(P,this),this.reset(!0)}function G(n){if(n||""===n){var r=n[l];if(r)return r.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var a=-1,u=function e(){for(;++a<n.length;)if(o.call(n,a))return e.value=n[a],e.done=!1,e;return e.value=t,e.done=!0,e};return u.next=u}}throw new TypeError(e(n)+" is not iterable")}return w.prototype=x,u(C,"constructor",{value:x,configurable:!0}),u(x,"constructor",{value:w,configurable:!0}),w.displayName=p(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,p(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},n.awrap=function(e){return{__await:e}},I(E.prototype),p(E.prototype,c,(function(){return this})),n.AsyncIterator=E,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var u=new E(f(e,t,r,a),o);return n.isGeneratorFunction(t)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},I(C),p(C,s,"Generator"),p(C,l,(function(){return this})),p(C,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=G,S.prototype={constructor:S,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(V),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,a){return i.type="throw",i.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],i=u.completion;if("root"===u.tryLoc)return r("end");if(u.tryLoc<=this.prev){var l=o.call(u,"catchLoc"),c=o.call(u,"finallyLoc");if(l&&c){if(this.prev<u.catchLoc)return r(u.catchLoc,!0);if(this.prev<u.finallyLoc)return r(u.finallyLoc)}else if(l){if(this.prev<u.catchLoc)return r(u.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return r(u.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),V(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;V(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:G(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),g}},n}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,a,o,u,i=[],l=!0,c=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=o.call(n)).done)&&(i.push(r.value),i.length!==t);l=!0);}catch(e){c=!0,a=e}finally{try{if(!l&&null!=n.return&&(u=n.return(),Object(u)!==u))return}finally{if(c)throw a}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return u(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?u(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t,n,r,a,o,u){try{var i=e[o](u),l=i.value}catch(e){return void n(e)}i.done?t(l):Promise.resolve(l).then(r,a)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var o=e.apply(t,n);function u(e){i(o,r,a,u,l,"next",e)}function l(e){i(o,r,a,u,l,"throw",e)}u(void 0)}))}}System.register(["./087AC4D233B64EB0api-legacy.E3o43UgG.js","./087AC4D233B64EB0stringFun-legacy.2vIcgB7Q.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0exportTemplate-legacy.CgI4m_1H.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js"],(function(e,t){"use strict";var r,u,i,c,s,p,f,d,v,h,m,y,g,b,w,x,_,k,A,C,I,E,j,O,P,V,S,G,L,D,T,B,U,z,N,F;return{setters:[function(e){r=e.g,u=e.a,i=e.b,c=e.d,s=e.c,p=e.i,f=e.e,d=e.u,v=e.f,h=e.h,m=e.s},function(e){y=e.t},function(e){g=e._},function(e){b=e.e,w=e.a},function(e){x=e.g,_=e.i,k=e.o,A=e.w,C=e.h,I=e.E,E=e.k,j=e.f,O=e.v,P=e._,V=e.I,S=e.a,G=e.ae,L=e.c,D=e.b,T=e.F,B=e.D,U=e.t,z=e.a7,N=e.ab},function(e){F=e.b}],execute:function(){var t=document.createElement("style");t.textContent=".warning[data-v-3b9f1fec]{color:#dc143c}\n/*$vite$:1*/",document.head.appendChild(t);var q={__name:"exportExcel",props:{templateId:{type:String,required:!0},condition:{type:Object,default:function(){return{}}},limit:{type:Number,default:0},offset:{type:Number,default:0},order:{type:String,default:""}},setup:function(e){var t=e,n=function(){var e=l(a().mark((function e(){var n,r,u,i,l;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==t.templateId){e.next=3;break}return I.error("组件未设置模板ID"),e.abrupt("return");case 3:return n="/api",r=JSON.parse(JSON.stringify(t.condition)),t.limit&&(r.limit=t.limit),t.offset&&(r.offset=t.offset),t.order&&(r.order=t.order),u=Object.entries(r).map((function(e){var t=o(e,2),n=t[0],r=t[1];return"".concat(encodeURIComponent(n),"=").concat(encodeURIComponent(r))})).join("&"),e.next=11,b({templateID:t.templateId,params:u});case 11:0===(i=e.sent).code&&(I.success("创建导出任务成功，开始下载"),l="".concat(n).concat(i.data),window.open(l,"_blank"));case 13:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,t){var r=x("el-button");return k(),_(r,{type:"primary",icon:"download",onClick:n},{default:A((function(){return t[0]||(t[0]=[C("导出")])})),_:1})}}},J={__name:"exportTemplate",props:{templateId:{type:String,required:!0}},setup:function(e){var t=e,n=function(){var e=l(a().mark((function e(){var n,r,o;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(""!==t.templateId){e.next=3;break}return I.error("组件未设置模板ID"),e.abrupt("return");case 3:return n="/api",e.next=6,w({templateID:t.templateId});case 6:0===(r=e.sent).code&&(I.success("创建导出任务成功，开始下载"),o="".concat(n).concat(r.data),window.open(o,"_blank"));case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,t){var r=x("el-button");return k(),_(r,{type:"primary",icon:"download",onClick:n},{default:A((function(){return t[0]||(t[0]=[C("下载模板")])})),_:1})}}},M={__name:"importExcel",props:{templateId:{type:String,required:!0}},emits:["on-success"],setup:function(e,t){var n=t.emit,r=e,a=E().token,o=n,u="".concat("/api","/sysExportTemplate/importExcel?templateID=").concat(r.templateId),i=function(e){0===e.code?(I.success("导入成功"),o("on-success")):I.error(e.msg)};return function(e,t){var n=x("el-button"),r=x("el-upload");return k(),_(r,{action:u,"show-file-list":!1,"on-success":i,multiple:!1,headers:{"x-token":O(a)}},{default:A((function(){return[j(n,{type:"primary",icon:"upload",class:"ml-3"},{default:A((function(){return t[0]||(t[0]=[C(" 导入 ")])})),_:1})]})),_:1},8,["headers"])}}},R={class:"gva-search-box"},$={class:"gva-table-box"},Y={class:"gva-btn-list"},K={class:"gva-pagination"},W={class:"flex justify-between items-center"},H={class:"flex justify-between items-center"},Q={class:"text-lg"},X=Object.assign({name:"Api"},{__name:"api",setup:function(e){var t=V(),o=function(e){var t=E.value.filter((function(t){return t.value===e}))[0];return t&&"".concat(t.label)},b=S([]),w=S({path:"",apiGroup:"",method:"",description:""}),E=S([{value:"POST",label:"创建",type:"success"},{value:"GET",label:"查看",type:""},{value:"PUT",label:"更新",type:"warning"},{value:"DELETE",label:"删除",type:"danger"}]),P=S(""),X=S({path:[{required:!0,message:"请输入api路径",trigger:"blur"}],apiGroup:[{required:!0,message:"请输入组名称",trigger:"blur"}],method:[{required:!0,message:"请选择请求方式",trigger:"blur"}],description:[{required:!0,message:"请输入api介绍",trigger:"blur"}]}),Z=S(1),ee=S(0),te=S(10),ne=S([]),re=S({}),ae=S([]),oe=S({}),ue=function(){var e=l(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u();case 2:0===(t=e.sent).code&&(n=t.data.groups,ae.value=n.map((function(e){return{label:e,value:e}})),oe.value=t.data.apiGroupMap);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(){var e=l(a().mark((function e(t,n){var r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,p({path:t.path,method:t.method,flag:n});case 2:if(0!==(r=e.sent).code){e.next=11;break}if(I({type:"success",message:r.msg}),!n){e.next=9;break}return xe.value.newApis=xe.value.newApis.filter((function(e){return!(e.path===t.path&&e.method===t.method)})),xe.value.ignoreApis.push(t),e.abrupt("return");case 9:xe.value.ignoreApis=xe.value.ignoreApis.filter((function(e){return!(e.path===t.path&&e.method===t.method)})),xe.value.newApis.push(t);case 11:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),le=function(){var e=l(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.apiGroup){e.next=3;break}return I({type:"error",message:"请先选择API分组"}),e.abrupt("return");case 3:if(t.description){e.next=6;break}return I({type:"error",message:"请先填写API描述"}),e.abrupt("return");case 6:return e.next=8,s(t);case 8:0===e.sent.code&&(I({type:"success",message:"添加成功",showClose:!0}),xe.value.newApis=xe.value.newApis.filter((function(e){return!(e.path===t.path&&e.method===t.method)}))),ye(),ue();case 12:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ce=function(){_e.value=!1},se=S(!1),pe=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!xe.value.newApis.some((function(e){return!e.apiGroup||!e.description}))){e.next=3;break}return I({type:"error",message:"存在API未分组或未填写描述"}),e.abrupt("return");case 3:return se.value=!0,e.next=6,f(xe.value);case 6:t=e.sent,se.value=!1,0===t.code&&(I({type:"success",message:t.msg}),_e.value=!1,ye());case 9:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),fe=function(){re.value={},ye()},de=function(){Z.value=1,ye()},ve=function(e){te.value=e,ye()},he=function(e){Z.value=e,ye()},me=function(e){var t=e.prop,n=e.order;t&&("ID"===t&&(t="id"),re.value.orderKey=y(t),re.value.desc="descending"===n),ye()},ye=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r(n({page:Z.value,pageSize:te.value},re.value));case 2:0===(t=e.sent).code&&(ne.value=t.data.list,ee.value=t.data.total,Z.value=t.data.page,te.value=t.data.pageSize);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();ye(),ue();var ge=function(e){b.value=e},be=function(){var e=l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:N.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=b.value.map((function(e){return e.ID})),e.next=3,v({ids:t});case 3:0===(n=e.sent).code&&(I({type:"success",message:n.msg}),ne.value.length===t.length&&Z.value>1&&Z.value--,ye());case 5:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),we=function(){var e=l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:N.confirm("确定要刷新缓存吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:0===(t=e.sent).code&&I({type:"success",message:t.msg});case 4:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),xe=S({newApis:[],deleteApis:[],ignoreApis:[]}),_e=S(!1),ke=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m();case 2:0===(t=e.sent).code&&(t.data.newApis.forEach((function(e){e.apiGroup=oe.value[e.path.split("/")[1]]})),xe.value=t.data,_e.value=!0);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ae=S(null),Ce=S("新增Api"),Ie=S(!1),Ee=function(e){switch(e){case"addApi":Ce.value="新增Api";break;case"edit":Ce.value="编辑Api"}P.value=e,Ie.value=!0},je=function(){Ae.value.resetFields(),w.value={path:"",apiGroup:"",method:"",description:""},Ie.value=!1},Oe=function(){var e=l(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,i({id:t.ID});case 2:n=e.sent,w.value=n.data.api,Ee("edit");case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Pe=function(){var e=l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Ae.value.validate(function(){var e=l(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=21;break}e.t0=P.value,e.next="addApi"===e.t0?4:"edit"===e.t0?12:19;break;case 4:return e.next=6,s(w.value);case 6:return 0===e.sent.code&&I({type:"success",message:"添加成功",showClose:!0}),ye(),ue(),je(),e.abrupt("break",21);case 12:return e.next=14,d(w.value);case 14:return 0===e.sent.code&&I({type:"success",message:"编辑成功",showClose:!0}),ye(),je(),e.abrupt("break",21);case 19:return I({type:"error",message:"未知操作",showClose:!0}),e.abrupt("break",21);case 21:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ve=function(){var e=l(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:N.confirm("此操作将永久删除所有角色下该api, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,c(t);case 2:0===e.sent.code&&(I({type:"success",message:"删除成功!"}),1===ne.value.length&&Z.value>1&&Z.value--,ye(),ue());case 4:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Se=S(!1),Ge=function(){var e=l(a().mark((function e(){var t,n,r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return Se.value=!0,t=xe.value.newApis.filter((function(e){return!e.apiGroup||!e.description})).map((function(e){return e.path})),e.next=4,F({data:t,command:"apiCompletion"});case 4:if(n=e.sent,Se.value=!1,0===n.code)try{r=JSON.parse(n.data),xe.value.newApis.forEach((function(e){var t=r.find((function(t){return t.path===e.path}));t&&(e.apiGroup||(e.apiGroup=t.apiGroup),e.description||(e.description=t.description))}))}catch(a){I({type:"error",message:"AI自动填充失败,请重新生成"})}case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,n){var r=x("el-input"),a=x("el-form-item"),u=x("el-option"),i=x("el-select"),l=x("el-button"),c=x("el-form"),s=x("el-table-column"),p=x("el-table"),f=x("el-pagination"),d=x("ai-gva"),v=x("el-icon"),h=x("el-drawer"),m=G("loading");return k(),L("div",null,[D("div",R,[j(c,{ref:"searchForm",inline:!0,model:re.value},{default:A((function(){return[j(a,{label:"路径"},{default:A((function(){return[j(r,{modelValue:re.value.path,"onUpdate:modelValue":n[0]||(n[0]=function(e){return re.value.path=e}),placeholder:"路径"},null,8,["modelValue"])]})),_:1}),j(a,{label:"描述"},{default:A((function(){return[j(r,{modelValue:re.value.description,"onUpdate:modelValue":n[1]||(n[1]=function(e){return re.value.description=e}),placeholder:"描述"},null,8,["modelValue"])]})),_:1}),j(a,{label:"API分组"},{default:A((function(){return[j(i,{modelValue:re.value.apiGroup,"onUpdate:modelValue":n[2]||(n[2]=function(e){return re.value.apiGroup=e}),clearable:"",placeholder:"请选择"},{default:A((function(){return[(k(!0),L(T,null,B(ae.value,(function(e){return k(),_(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),j(a,{label:"请求"},{default:A((function(){return[j(i,{modelValue:re.value.method,"onUpdate:modelValue":n[3]||(n[3]=function(e){return re.value.method=e}),clearable:"",placeholder:"请选择"},{default:A((function(){return[(k(!0),L(T,null,B(E.value,(function(e){return k(),_(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),j(a,null,{default:A((function(){return[j(l,{type:"primary",icon:"search",onClick:de},{default:A((function(){return n[11]||(n[11]=[C(" 查询 ")])})),_:1}),j(l,{icon:"refresh",onClick:fe},{default:A((function(){return n[12]||(n[12]=[C(" 重置 ")])})),_:1})]})),_:1})]})),_:1},8,["model"])]),D("div",$,[D("div",Y,[j(l,{type:"primary",icon:"plus",onClick:n[4]||(n[4]=function(e){return Ee("addApi")})},{default:A((function(){return n[13]||(n[13]=[C(" 新增 ")])})),_:1}),j(l,{icon:"delete",disabled:!b.value.length,onClick:be},{default:A((function(){return n[14]||(n[14]=[C(" 删除 ")])})),_:1},8,["disabled"]),j(l,{icon:"Refresh",onClick:we},{default:A((function(){return n[15]||(n[15]=[C(" 刷新缓存 ")])})),_:1}),j(l,{icon:"Compass",onClick:ke},{default:A((function(){return n[16]||(n[16]=[C(" 同步API ")])})),_:1}),j(J,{"template-id":"api"}),j(q,{"template-id":"api",limit:9999}),j(M,{"template-id":"api",onOnSuccess:ye})]),j(p,{data:ne.value,onSortChange:me,onSelectionChange:ge},{default:A((function(){return[j(s,{type:"selection",width:"55"}),j(s,{align:"left",label:"id","min-width":"60",prop:"ID",sortable:"custom"}),j(s,{align:"left",label:"API路径","min-width":"150",prop:"path",sortable:"custom"}),j(s,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup",sortable:"custom"}),j(s,{align:"left",label:"API简介","min-width":"150",prop:"description",sortable:"custom"}),j(s,{align:"left",label:"请求","min-width":"150",prop:"method",sortable:"custom"},{default:A((function(e){return[D("div",null,U(e.row.method)+" / "+U(o(e.row.method)),1)]})),_:1}),j(s,{align:"left",fixed:"right",label:"操作","min-width":O(t).operateMinWith},{default:A((function(e){return[j(l,{icon:"edit",type:"primary",link:"",onClick:function(t){return Oe(e.row)}},{default:A((function(){return n[17]||(n[17]=[C(" 编辑 ")])})),_:2},1032,["onClick"]),j(l,{icon:"delete",type:"primary",link:"",onClick:function(t){return Ve(e.row)}},{default:A((function(){return n[18]||(n[18]=[C(" 删除 ")])})),_:2},1032,["onClick"])]})),_:1},8,["min-width"])]})),_:1},8,["data"]),D("div",K,[j(f,{"current-page":Z.value,"page-size":te.value,"page-sizes":[10,30,50,100],total:ee.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:he,onSizeChange:ve},null,8,["current-page","page-size","total"])])]),j(h,{modelValue:_e.value,"onUpdate:modelValue":n[5]||(n[5]=function(e){return _e.value=e}),size:O(t).drawerSize,"before-close":ce,"show-close":!1},{header:A((function(){return[D("div",W,[n[21]||(n[21]=D("span",{class:"text-lg"},"同步路由",-1)),D("div",null,[j(l,{loading:Se.value,onClick:ce},{default:A((function(){return n[19]||(n[19]=[C(" 取 消 ")])})),_:1},8,["loading"]),j(l,{type:"primary",loading:se.value||Se.value,onClick:pe},{default:A((function(){return n[20]||(n[20]=[C(" 确 定 ")])})),_:1},8,["loading"])])])]})),default:A((function(){return[j(g,{title:"同步API，不输入路由分组将不会被自动同步，如果api不需要参与鉴权，可以按忽略按钮进行忽略。"}),D("h4",null,[n[23]||(n[23]=C(" 新增路由 ")),n[24]||(n[24]=D("span",{class:"text-xs text-gray-500 mx-2 font-normal"},"存在于当前路由中，但是不存在于api表",-1)),j(l,{type:"primary",size:"small",onClick:Ge},{default:A((function(){return[j(v,{size:"18"},{default:A((function(){return[j(d)]})),_:1}),n[22]||(n[22]=C(" 自动填充 "))]})),_:1})]),z((k(),_(p,{"element-loading-text":"小淼正在思考...",data:xe.value.newApis},{default:A((function(){return[j(s,{align:"left",label:"API路径","min-width":"150",prop:"path"}),j(s,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"},{default:A((function(e){var t=e.row;return[j(i,{modelValue:t.apiGroup,"onUpdate:modelValue":function(e){return t.apiGroup=e},placeholder:"请选择或新增","allow-create":"",filterable:"","default-first-option":""},{default:A((function(){return[(k(!0),L(T,null,B(ae.value,(function(e){return k(),_(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue"])]})),_:1}),j(s,{align:"left",label:"API简介","min-width":"150",prop:"description"},{default:A((function(e){var t=e.row;return[j(r,{modelValue:t.description,"onUpdate:modelValue":function(e){return t.description=e},autocomplete:"off"},null,8,["modelValue","onUpdate:modelValue"])]})),_:1}),j(s,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:A((function(e){return[D("div",null,U(e.row.method)+" / "+U(o(e.row.method)),1)]})),_:1}),j(s,{label:"操作","min-width":"150",fixed:"right"},{default:A((function(e){var t=e.row;return[j(l,{icon:"plus",type:"primary",link:"",onClick:function(e){return le(t)}},{default:A((function(){return n[25]||(n[25]=[C(" 单条新增 ")])})),_:2},1032,["onClick"]),j(l,{icon:"sunrise",type:"primary",link:"",onClick:function(e){return ie(t,!0)}},{default:A((function(){return n[26]||(n[26]=[C(" 忽略 ")])})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])),[[m,se.value||Se.value]]),n[28]||(n[28]=D("h4",null,[C(" 已删除路由 "),D("span",{class:"text-xs text-gray-500 ml-2 font-normal"},"已经不存在于当前项目的路由中，确定同步后会自动从apis表删除")],-1)),j(p,{data:xe.value.deleteApis},{default:A((function(){return[j(s,{align:"left",label:"API路径","min-width":"150",prop:"path"}),j(s,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"}),j(s,{align:"left",label:"API简介","min-width":"150",prop:"description"}),j(s,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:A((function(e){return[D("div",null,U(e.row.method)+" / "+U(o(e.row.method)),1)]})),_:1})]})),_:1},8,["data"]),n[29]||(n[29]=D("h4",null,[C(" 忽略路由 "),D("span",{class:"text-xs text-gray-500 ml-2 font-normal"},"忽略路由不参与api同步，常见为不需要进行鉴权行为的路由")],-1)),j(p,{data:xe.value.ignoreApis},{default:A((function(){return[j(s,{align:"left",label:"API路径","min-width":"150",prop:"path"}),j(s,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"}),j(s,{align:"left",label:"API简介","min-width":"150",prop:"description"}),j(s,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:A((function(e){return[D("div",null,U(e.row.method)+" / "+U(o(e.row.method)),1)]})),_:1}),j(s,{label:"操作","min-width":"150",fixed:"right"},{default:A((function(e){var t=e.row;return[j(l,{icon:"sunny",type:"primary",link:"",onClick:function(e){return ie(t,!1)}},{default:A((function(){return n[27]||(n[27]=[C(" 取消忽略 ")])})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]})),_:1},8,["modelValue","size"]),j(h,{modelValue:Ie.value,"onUpdate:modelValue":n[10]||(n[10]=function(e){return Ie.value=e}),size:O(t).drawerSize,"before-close":je,"show-close":!1},{header:A((function(){return[D("div",H,[D("span",Q,U(Ce.value),1),D("div",null,[j(l,{onClick:je},{default:A((function(){return n[30]||(n[30]=[C(" 取 消 ")])})),_:1}),j(l,{type:"primary",onClick:Pe},{default:A((function(){return n[31]||(n[31]=[C(" 确 定 ")])})),_:1})])])]})),default:A((function(){return[j(g,{title:"新增API，需要在角色管理内配置权限才可使用"}),j(c,{ref_key:"apiForm",ref:Ae,model:w.value,rules:X.value,"label-width":"80px"},{default:A((function(){return[j(a,{label:"路径",prop:"path"},{default:A((function(){return[j(r,{modelValue:w.value.path,"onUpdate:modelValue":n[6]||(n[6]=function(e){return w.value.path=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),j(a,{label:"请求",prop:"method"},{default:A((function(){return[j(i,{modelValue:w.value.method,"onUpdate:modelValue":n[7]||(n[7]=function(e){return w.value.method=e}),placeholder:"请选择",style:{width:"100%"}},{default:A((function(){return[(k(!0),L(T,null,B(E.value,(function(e){return k(),_(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),j(a,{label:"api分组",prop:"apiGroup"},{default:A((function(){return[j(i,{modelValue:w.value.apiGroup,"onUpdate:modelValue":n[8]||(n[8]=function(e){return w.value.apiGroup=e}),placeholder:"请选择或新增","allow-create":"",filterable:"","default-first-option":""},{default:A((function(){return[(k(!0),L(T,null,B(ae.value,(function(e){return k(),_(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),j(a,{label:"api简介",prop:"description"},{default:A((function(){return[j(r,{modelValue:w.value.description,"onUpdate:modelValue":n[9]||(n[9]=function(e){return w.value.description=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue","size"])])}}});e("default",P(X,[["__scopeId","data-v-3b9f1fec"]]))}}}))}();
