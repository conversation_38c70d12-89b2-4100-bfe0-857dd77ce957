/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{u as t,g as e,c as a,o as s,b as i,F as l,D as o,f as r,w as n,i as c,Y as u,t as d,Z as g,$ as p,a0 as h,a1 as m,a2 as x,a3 as v,a4 as f,a5 as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const b={class:"mt-8 w-full"},y={class:"grid grid-cols-2 md:grid-cols-3 3xl:grid-cols-4"},w=["onClick"],C={class:"w-8 h-8 rounded bg-gray-200 dark:bg-slate-500 flex items-center justify-center group-hover:bg-blue-400 group-hover:text-white"},_={class:"text-xs mt-2 text-gray-700 dark:text-gray-300"},j={class:"grid grid-cols-2 md:grid-cols-3 3xl:grid-cols-4 mt-8"},A=["onClick"],B={class:"w-8 h-8 rounded bg-gray-200 dark:bg-slate-500 flex items-center justify-center group-hover:bg-blue-400 group-hover:text-white"},D={class:"text-xs mt-2 text-gray-700 dark:text-gray-300"},P={__name:"quickLinks",setup(P){const q=t(),E=[{icon:g,title:"菜单管理",path:"menu"},{icon:p,title:"API管理",path:"api"},{icon:h,title:"角色管理",path:"authority"},{icon:m,title:"用户管理",path:"user"},{icon:x,title:"自动化包",path:"autoPkg"},{icon:v,title:"自动代码",path:"autoCode"}],F=[{icon:f,title:"授权购买",path:"https://gin-vue-admin.com/empower/index.html"},{icon:k,title:"插件市场",path:"https://plugin.gin-vue-admin.com/#/layout/home"}];return(t,g)=>{const p=e("el-icon");return s(),a("div",b,[i("div",y,[(s(),a(l,null,o(E,((t,e)=>i("div",{key:e,class:"flex flex-col items-center mb-3 group cursor-pointer",onClick:e=>(t=>{q.push({name:t.path})})(t)},[i("div",C,[r(p,null,{default:n((()=>[(s(),c(u(t.icon)))])),_:2},1024)]),i("div",_,d(t.title),1)],8,w))),64))]),i("div",j,[(s(),a(l,null,o(F,((t,e)=>i("div",{key:e,class:"flex flex-col items-center mb-3 group cursor-pointer",onClick:e=>(t=>{window.open(t.path,"_blank")})(t)},[i("div",B,[r(p,null,{default:n((()=>[(s(),c(u(t.icon)))])),_:2},1024)]),i("div",D,d(t.title),1)],8,A))),64))])])}}};export{P as default};
