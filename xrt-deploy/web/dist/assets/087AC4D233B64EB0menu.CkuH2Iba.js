/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,I as l,r as a,a as t,g as u,c as o,o as n,b as d,f as i,w as p,h as m,t as s,d as r,i as c,Y as f,v,aW as h,aX as _,aY as b,ab as w,aZ as y,E as V,a_ as g,a$ as k,aU as D}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import I from"./087AC4D233B64EB0icon.Hbg_Aj5D.js";import{_ as C}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{c as U}from"./087AC4D233B64EB0authorityBtn.COa_YS-K.js";import{t as B}from"./087AC4D233B64EB0doc.DwE8vRuh.js";import{a as x}from"./087AC4D233B64EB0stringFun.BxqK0MAg.js";import A from"./087AC4D233B64EB0components-cascader.VT_TD9Ql.js";const T={class:"gva-table-box"},j={class:"gva-btn-list"},E={key:0,class:"icon-column"},N={class:"flex justify-between items-center"},q={class:"text-lg"},M={style:{display:"inline-flex","align-items":"center"}},z={class:"flex items-center gap-2"},$={class:"flex items-center gap-2 mt-3"},F=e(Object.assign({name:"Menus"},{__name:"menu",setup(e){const F=l(),P=a({path:[{required:!0,message:"请输入菜单name",trigger:"blur"}],component:[{required:!0,message:"请输入文件路径",trigger:"blur"}],"meta.title":[{required:!0,message:"请输入菜单展示名称",trigger:"blur"}]}),H=t([]),S=async()=>{const e=await _();0===e.code&&(H.value=e.data)};S();const W=e=>{Y.value.component=e.replace(/\\/g,"/"),Y.value.name=x(D["/src/"+e]),Y.value.path=Y.value.name},Y=t({ID:0,path:"",name:"",hidden:!1,parentId:0,component:"",meta:{activeName:"",title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1},parameters:[],menuBtn:[]}),K=()=>{Y.value.path=Y.value.name},O=e=>{G(),e()},X=t(null),Z=t(!1),G=()=>{Z.value=!1,X.value.resetFields(),Y.value={ID:0,path:"",name:"",hidden:!1,parentId:0,component:"",meta:{title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1}}},J=t(!1),L=()=>{G(),J.value=!1},Q=async()=>{X.value.validate((async e=>{if(e){let e;e=ae.value?await g(Y.value):await k(Y.value),0===e.code&&(V({type:"success",message:ae.value?"编辑成功":"添加成功!"}),S()),G(),J.value=!1}}))},R=t([{ID:"0",title:"根菜单"}]),ee=()=>{R.value=[{ID:0,title:"根目录"}],le(H.value,R.value,!1)},le=(e,l,a)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const t={title:e.meta.title,ID:e.ID,disabled:a||e.ID===Y.value.ID,children:[]};le(e.children,t.children,a||e.ID===Y.value.ID),l.push(t)}else{const t={title:e.meta.title,ID:e.ID,disabled:a||e.ID===Y.value.ID};l.push(t)}}))},ae=t(!1),te=t("新增菜单"),ue=e=>{te.value="新增菜单",Y.value.parentId=e,ae.value=!1,ee(),J.value=!0};return(e,l)=>{const a=u("el-button"),t=u("el-table-column"),_=u("el-icon"),g=u("el-table"),k=u("el-form-item"),D=u("el-col"),x=u("el-input"),G=u("el-row"),le=u("el-checkbox"),oe=u("el-option"),ne=u("el-select"),de=u("el-cascader"),ie=u("el-tooltip"),pe=u("el-form"),me=u("el-drawer");return n(),o("div",null,[d("div",T,[d("div",j,[i(a,{type:"primary",icon:"plus",onClick:l[0]||(l[0]=e=>ue(0))},{default:p((()=>l[19]||(l[19]=[m(" 新增根菜单 ")]))),_:1})]),i(g,{data:H.value,"row-key":"ID"},{default:p((()=>[i(t,{align:"left",label:"ID","min-width":"100",prop:"ID"}),i(t,{align:"left",label:"展示名称","min-width":"120",prop:"authorityName"},{default:p((e=>[d("span",null,s(e.row.meta.title),1)])),_:1}),i(t,{align:"left",label:"图标","min-width":"140",prop:"authorityName"},{default:p((e=>[e.row.meta.icon?(n(),o("div",E,[i(_,null,{default:p((()=>[(n(),c(f(e.row.meta.icon)))])),_:2},1024),d("span",null,s(e.row.meta.icon),1)])):r("",!0)])),_:1}),i(t,{align:"left",label:"路由Name","show-overflow-tooltip":"","min-width":"160",prop:"name"}),i(t,{align:"left",label:"路由Path","show-overflow-tooltip":"","min-width":"160",prop:"path"}),i(t,{align:"left",label:"是否隐藏","min-width":"100",prop:"hidden"},{default:p((e=>[d("span",null,s(e.row.hidden?"隐藏":"显示"),1)])),_:1}),i(t,{align:"left",label:"父节点","min-width":"90",prop:"parentId"}),i(t,{align:"left",label:"排序","min-width":"70",prop:"sort"}),i(t,{align:"left",label:"文件路径","min-width":"360",prop:"component"}),i(t,{align:"left",fixed:"right",label:"操作","min-width":v(F).operateMinWith},{default:p((e=>[i(a,{type:"primary",link:"",icon:"plus",onClick:l=>ue(e.row.ID)},{default:p((()=>l[20]||(l[20]=[m(" 添加子菜单 ")]))),_:2},1032,["onClick"]),i(a,{type:"primary",link:"",icon:"edit",onClick:l=>(async e=>{te.value="编辑菜单";const l=await b({id:e});Y.value=l.data.menu,ae.value=!0,ee(),J.value=!0})(e.row.ID)},{default:p((()=>l[21]||(l[21]=[m(" 编辑 ")]))),_:2},1032,["onClick"]),i(a,{type:"primary",link:"",icon:"delete",onClick:l=>{return a=e.row.ID,void w.confirm("此操作将永久删除所有角色下该菜单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await y({ID:a})).code&&(V({type:"success",message:"删除成功!"}),S())})).catch((()=>{V({type:"info",message:"已取消删除"})}));var a}},{default:p((()=>l[22]||(l[22]=[m(" 删除 ")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"])]),i(me,{modelValue:J.value,"onUpdate:modelValue":l[18]||(l[18]=e=>J.value=e),size:v(F).drawerSize,"before-close":O,"show-close":!1},{header:p((()=>[d("div",N,[d("span",q,s(te.value),1),d("div",null,[i(a,{onClick:L},{default:p((()=>l[23]||(l[23]=[m(" 取 消 ")]))),_:1}),i(a,{type:"primary",onClick:Q},{default:p((()=>l[24]||(l[24]=[m(" 确 定 ")]))),_:1})])])])),default:p((()=>[i(C,{title:"新增菜单，需要在角色管理内配置权限才可使用"}),J.value?(n(),c(pe,{key:0,ref_key:"menuForm",ref:X,inline:!0,model:Y.value,rules:P,"label-position":"top"},{default:p((()=>[i(G,{class:"w-full"},{default:p((()=>[i(D,{span:16},{default:p((()=>[i(k,{label:"文件路径",prop:"component"},{default:p((()=>[i(A,{component:Y.value.component,onChange:W},null,8,["component"]),l[26]||(l[26]=d("span",{style:{"font-size":"12px","margin-right":"12px"}},"如果菜单包含子菜单，请创建router-view二级路由页面或者",-1)),i(a,{style:{"margin-top":"4px"},onClick:l[1]||(l[1]=e=>Y.value.component="view/routerHolder.vue")},{default:p((()=>l[25]||(l[25]=[m(" 点我设置 ")]))),_:1})])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"展示名称",prop:"meta.title"},{default:p((()=>[i(x,{modelValue:Y.value.meta.title,"onUpdate:modelValue":l[2]||(l[2]=e=>Y.value.meta.title=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(G,{class:"w-full"},{default:p((()=>[i(D,{span:8},{default:p((()=>[i(k,{label:"路由Name",prop:"path"},{default:p((()=>[i(x,{modelValue:Y.value.name,"onUpdate:modelValue":l[3]||(l[3]=e=>Y.value.name=e),autocomplete:"off",placeholder:"唯一英文字符串",onChange:K},null,8,["modelValue"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{prop:"path"},{label:p((()=>[d("span",M,[l[28]||(l[28]=d("span",null,"路由Path",-1)),i(le,{modelValue:Z.value,"onUpdate:modelValue":l[4]||(l[4]=e=>Z.value=e),style:{"margin-left":"12px",height:"auto"}},{default:p((()=>l[27]||(l[27]=[m("添加参数")]))),_:1},8,["modelValue"])])])),default:p((()=>[i(x,{modelValue:Y.value.path,"onUpdate:modelValue":l[5]||(l[5]=e=>Y.value.path=e),disabled:!Z.value,autocomplete:"off",placeholder:"建议只在后方拼接参数"},null,8,["modelValue","disabled"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"是否隐藏"},{default:p((()=>[i(ne,{modelValue:Y.value.hidden,"onUpdate:modelValue":l[6]||(l[6]=e=>Y.value.hidden=e),style:{width:"100%"},placeholder:"是否在列表隐藏"},{default:p((()=>[i(oe,{value:!1,label:"否"}),i(oe,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(G,{class:"w-full"},{default:p((()=>[i(D,{span:8},{default:p((()=>[i(k,{label:"父节点ID"},{default:p((()=>[i(de,{modelValue:Y.value.parentId,"onUpdate:modelValue":l[7]||(l[7]=e=>Y.value.parentId=e),style:{width:"100%"},disabled:!ae.value,options:R.value,props:{checkStrictly:!0,label:"title",value:"ID",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"图标",prop:"meta.icon"},{default:p((()=>[i(I,{modelValue:Y.value.meta.icon,"onUpdate:modelValue":l[8]||(l[8]=e=>Y.value.meta.icon=e)},null,8,["modelValue"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"排序标记",prop:"sort"},{default:p((()=>[i(x,{modelValue:Y.value.sort,"onUpdate:modelValue":l[9]||(l[9]=e=>Y.value.sort=e),modelModifiers:{number:!0},autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(G,{class:"w-full"},{default:p((()=>[i(D,{span:8},{default:p((()=>[i(k,{prop:"meta.activeName"},{label:p((()=>[d("div",null,[l[29]||(l[29]=d("span",null," 高亮菜单 ",-1)),i(ie,{content:"注：当到达此路由时候，指定左侧菜单指定name会处于活跃状态（亮起），可为空，为空则为本路由Name。",placement:"top",effect:"light"},{default:p((()=>[i(_,null,{default:p((()=>[i(v(h))])),_:1})])),_:1})])])),default:p((()=>[i(x,{modelValue:Y.value.meta.activeName,"onUpdate:modelValue":l[10]||(l[10]=e=>Y.value.meta.activeName=e),placeholder:Y.value.name,autocomplete:"off"},null,8,["modelValue","placeholder"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"KeepAlive",prop:"meta.keepAlive"},{default:p((()=>[i(ne,{modelValue:Y.value.meta.keepAlive,"onUpdate:modelValue":l[11]||(l[11]=e=>Y.value.meta.keepAlive=e),style:{width:"100%"},placeholder:"是否keepAlive缓存页面"},{default:p((()=>[i(oe,{value:!1,label:"否"}),i(oe,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,{label:"CloseTab",prop:"meta.closeTab"},{default:p((()=>[i(ne,{modelValue:Y.value.meta.closeTab,"onUpdate:modelValue":l[12]||(l[12]=e=>Y.value.meta.closeTab=e),style:{width:"100%"},placeholder:"是否自动关闭tab"},{default:p((()=>[i(oe,{value:!1,label:"否"}),i(oe,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1}),i(G,{class:"w-full"},{default:p((()=>[i(D,{span:8},{default:p((()=>[i(k,null,{label:p((()=>[d("div",null,[l[30]||(l[30]=d("span",null," 是否为基础页面 ",-1)),i(ie,{content:"此项选择为是，则不会展示左侧菜单以及顶部信息。",placement:"top",effect:"light"},{default:p((()=>[i(_,null,{default:p((()=>[i(v(h))])),_:1})])),_:1})])])),default:p((()=>[i(ne,{modelValue:Y.value.meta.defaultMenu,"onUpdate:modelValue":l[13]||(l[13]=e=>Y.value.meta.defaultMenu=e),style:{width:"100%"},placeholder:"是否为基础页面"},{default:p((()=>[i(oe,{value:!1,label:"否"}),i(oe,{value:!0,label:"是"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),i(D,{span:8},{default:p((()=>[i(k,null,{label:p((()=>[d("div",null,[l[31]||(l[31]=d("span",null," 路由切换动画 ",-1)),i(ie,{content:"如果设置了路由切换动画，在本路由下的动画优先级高于全局动画切换优先级。",placement:"top",effect:"light"},{default:p((()=>[i(_,null,{default:p((()=>[i(v(h))])),_:1})])),_:1})])])),default:p((()=>[i(ne,{modelValue:Y.value.meta.transitionType,"onUpdate:modelValue":l[14]||(l[14]=e=>Y.value.meta.transitionType=e),style:{width:"100%"},placeholder:"跟随全局",clearable:""},{default:p((()=>[i(oe,{value:"fade",label:"淡入淡出"}),i(oe,{value:"slide",label:"滑动"}),i(oe,{value:"zoom",label:"缩放"}),i(oe,{value:"none",label:"无动画"})])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["model","rules"])):r("",!0),d("div",null,[d("div",z,[i(a,{type:"primary",icon:"edit",onClick:l[15]||(l[15]=e=>{return(l=Y.value).parameters||(l.parameters=[]),void l.parameters.push({type:"query",key:"",value:""});var l})},{default:p((()=>l[32]||(l[32]=[m(" 新增菜单参数 ")]))),_:1})]),i(g,{data:Y.value.parameters,style:{width:"100%","margin-top":"12px"}},{default:p((()=>[i(t,{align:"left",prop:"type",label:"参数类型",width:"180"},{default:p((e=>[i(ne,{modelValue:e.row.type,"onUpdate:modelValue":l=>e.row.type=l,placeholder:"请选择"},{default:p((()=>[i(oe,{key:"query",value:"query",label:"query"}),i(oe,{key:"params",value:"params",label:"params"})])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),i(t,{align:"left",prop:"key",label:"参数key",width:"180"},{default:p((e=>[d("div",null,[i(x,{modelValue:e.row.key,"onUpdate:modelValue":l=>e.row.key=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(t,{align:"left",prop:"value",label:"参数值"},{default:p((e=>[d("div",null,[i(x,{modelValue:e.row.value,"onUpdate:modelValue":l=>e.row.value=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(t,{align:"left"},{default:p((e=>[d("div",null,[i(a,{type:"danger",icon:"delete",onClick:l=>{return a=Y.value.parameters,t=e.$index,void a.splice(t,1);var a,t}},{default:p((()=>l[33]||(l[33]=[m(" 删除 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"]),d("div",$,[i(a,{type:"primary",icon:"edit",onClick:l[16]||(l[16]=e=>{return(l=Y.value).menuBtn||(l.menuBtn=[]),void l.menuBtn.push({name:"",desc:""});var l})},{default:p((()=>l[34]||(l[34]=[m(" 新增可控按钮 ")]))),_:1}),i(_,{class:"cursor-pointer",onClick:l[17]||(l[17]=e=>v(B)("https://www.gin-vue-admin.com/guide/web/button-auth.html"))},{default:p((()=>[i(v(h))])),_:1})]),i(g,{data:Y.value.menuBtn,style:{width:"100%","margin-top":"12px"}},{default:p((()=>[i(t,{align:"left",prop:"name",label:"按钮名称",width:"180"},{default:p((e=>[d("div",null,[i(x,{modelValue:e.row.name,"onUpdate:modelValue":l=>e.row.name=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(t,{align:"left",prop:"name",label:"备注",width:"180"},{default:p((e=>[d("div",null,[i(x,{modelValue:e.row.desc,"onUpdate:modelValue":l=>e.row.desc=l},null,8,["modelValue","onUpdate:modelValue"])])])),_:1}),i(t,{align:"left"},{default:p((e=>[d("div",null,[i(a,{type:"danger",icon:"delete",onClick:l=>(async(e,l)=>{const a=e[l];if(0===a.ID)return void e.splice(l,1);0===(await U({id:a.ID})).code&&e.splice(l,1)})(Y.value.menuBtn,e.$index)},{default:p((()=>l[35]||(l[35]=[m(" 删除 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"])])])),_:1},8,["modelValue","size"])])}}}),[["__scopeId","data-v-63e1540b"]]);export{F as default};
