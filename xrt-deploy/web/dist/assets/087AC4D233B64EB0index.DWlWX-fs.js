/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,p as n,at as l,a as r,c,o as t}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const s=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],u=(()=>{if("undefined"==typeof document)return!1;const e=s[0],n={};for(const l of s){if((null==l?void 0:l[1])in document){for(const[r,c]of l.entries())n[e[r]]=c;return n}}return!1})(),o={change:u.fullscreenchange,error:u.fullscreenerror};let a={request:(e=document.documentElement,n)=>new Promise(((l,r)=>{const c=()=>{a.off("change",c),l()};a.on("change",c);const t=e[u.requestFullscreen](n);t instanceof Promise&&t.then(c).catch(r)})),exit:()=>new Promise(((e,n)=>{if(!a.isFullscreen)return void e();const l=()=>{a.off("change",l),e()};a.on("change",l);const r=document[u.exitFullscreen]();r instanceof Promise&&r.then(l).catch(n)})),toggle:(e,n)=>a.isFullscreen?a.exit():a.request(e,n),onchange(e){a.on("change",e)},onerror(e){a.on("error",e)},on(e,n){const l=o[e];l&&document.addEventListener(l,n,!1)},off(e,n){const l=o[e];l&&document.removeEventListener(l,n,!1)},raw:u};Object.defineProperties(a,{isFullscreen:{get:()=>Boolean(document[u.fullscreenElement])},element:{enumerable:!0,get:()=>{var e;return null!=(e=document[u.fullscreenElement])?e:void 0}},isEnabled:{enumerable:!0,get:()=>Boolean(document[u.fullscreenEnabled])}}),u||(a={isEnabled:!1});const i={key:0,class:"gvaIcon gvaIcon-fullscreen-expand"},f={key:1,class:"gvaIcon gvaIcon-fullscreen-shrink"},m=e(Object.assign({name:"Screenfull"},{__name:"index",props:{width:{type:Number,default:22},height:{type:Number,default:22},fill:{type:String,default:"#48576a"}},setup(e){n((()=>{a.isEnabled&&a.on("change",o)})),l((()=>{a.off("change")}));const s=()=>{a.isEnabled&&a.toggle()},u=r(!0),o=()=>{u.value=!a.isFullscreen};return(e,n)=>(t(),c("div",{onClick:s},[u.value?(t(),c("div",i)):(t(),c("div",f))]))}}),[["__scopeId","data-v-c1eb3148"]]);export{m as default};
