/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as t}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const o=o=>t({url:"/autoCode/preview",method:"post",data:o}),a=o=>t({url:"/autoCode/createTemp",method:"post",data:o}),e=o=>t({url:"/autoCode/getDB",method:"get",params:o}),d=o=>t({url:"/autoCode/getTables",method:"get",params:o}),u=o=>t({url:"/autoCode/getColumn",method:"get",params:o}),s=o=>t({url:"/autoCode/getSysHistory",method:"post",data:o}),l=o=>t({url:"/autoCode/rollback",method:"post",data:o}),m=o=>t({url:"/autoCode/getMeta",method:"post",data:o}),r=o=>t({url:"/autoCode/delSysHistory",method:"post",data:o}),p=o=>t({url:"/autoCode/createPackage",method:"post",data:o}),C=()=>t({url:"/autoCode/getPackage",method:"post"}),h=o=>t({url:"/autoCode/delPackage",method:"post",data:o}),g=()=>t({url:"/autoCode/getTemplates",method:"get"}),i=o=>t({url:"/autoCode/pubPlug",method:"post",params:o}),c=o=>t({url:"/autoCode/llmAuto",method:"post",data:{...o,mode:"ai"},timeout:6e5,loadingOption:{lock:!0,fullscreen:!0,text:"小淼正在思考，请稍候..."}}),n=o=>t({url:"/autoCode/llmAuto",method:"post",data:{...o,mode:"butler"},timeout:6e5}),k=o=>t({url:"/autoCode/llmAuto",method:"post",data:{...o,mode:"eye"},timeout:6e5}),b=o=>t({url:"/autoCode/addFunc",method:"post",data:o}),y=o=>t({url:"/autoCode/initMenu",method:"post",data:o}),A=o=>t({url:"/autoCode/initAPI",method:"post",data:o});export{u as a,n as b,d as c,C as d,k as e,a as f,e as g,m as h,s as i,r as j,b as k,c as l,g as m,h as n,p as o,o as p,y as q,l as r,A as s,i as t};
