/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as e,_ as l,a,r as t,g as o,c as u,o as d,b as i,f as r,w as s,h as n,l as c,t as p,i as m,v,aG as f,X as h,E as g,ab as y}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const b=l=>e({url:"/product/createProduct",method:"post",data:l}),w={class:"gva-search-box"},_={class:"gva-card-box"},V={class:"product-item"},k={class:"product-value"},x={class:"product-item"},U={class:"product-value"},z={class:"product-item"},W={class:"product-value"},C={class:"gva-table-box"},P={class:"gva-btn-list"},N={style:{display:"flex","align-items":"center",gap:"12px"}},A={style:{"flex-shrink":"0"}},R={style:{width:"60px",height:"60px",background:"#f5f5f5","border-radius":"4px",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column","font-size":"12px",color:"#999"}},D={key:1,style:{width:"60px",height:"60px",background:"#f5f5f5","border-radius":"4px",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column","font-size":"12px",color:"#999"}},O={style:{flex:"1","min-width":"0"}},S={style:{"margin-bottom":"4px"}},I=["title"],B={key:0,class:"weight-highlight"},F={key:0,class:"weight-highlight"},T={key:0,style:{color:"#0369a1","font-weight":"500"}},K={key:0,style:{color:"#dc2626","font-weight":"500"}},j={class:"gva-pagination"},L={class:"dialog-footer"},E={class:"dialog-footer"},q={class:"dialog-footer"},G=l(Object.assign({name:"Product"},{__name:"product",setup(l){const G=a(),H=a(),X=a(),J=a(),M=a(1),Q=a(0),Y=a(10),Z=a([]),$=a({}),ee=t({}),le=a({totalProducts:0,activeProducts:0,inactiveProducts:0,avgWeight:0,totalOrders:0}),ae=a([]),te=e=>{ae.value=e},oe=()=>{$.value={},re()},ue=()=>{var e;null==(e=H.value)||e.validate((async e=>{e&&(M.value=1,re())}))},de=e=>{Y.value=e,re()},ie=e=>{M.value=e,re()},re=async()=>{const l=await(a={page:M.value,pageSize:Y.value,...$.value},e({url:"/product/getProductList",method:"get",params:a}));var a;0===l.code&&(Z.value=l.data.list,Q.value=l.data.total)},se=async()=>{const l=await e({url:"/product/getProductSummary",method:"get",params:a});var a;0===l.code&&(le.value=l.data)},ne=a({sku:"",actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",imageUrl:"",notes:"",isActive:!0}),ce=t({sku:[{required:!0,message:"请输入SKU",trigger:"blur"}]}),pe=a(!1),me=a(""),ve=()=>{me.value="create",pe.value=!0},fe=()=>{pe.value=!1,ne.value={sku:"",actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",imageUrl:"",notes:"",isActive:!0}},he=async()=>{var l;null==(l=G.value)||l.validate((async l=>{if(!l)return;let a;switch(me.value){case"create":default:a=await b(ne.value);break;case"update":a=await(t=ne.value,e({url:"/product/updateProduct",method:"put",data:t}))}var t;0===a.code?(g({type:"success",message:"创建/更改成功"}),fe(),re(),se()):g({type:"error",message:a.message||"操作失败"})}))},ge=async l=>{const a=await(t={ID:l.ID},e({url:"/product/findProduct",method:"get",params:t}));var t;me.value="update",0===a.code?(ne.value=a.data,pe.value=!0):g({type:"error",message:"获取产品信息失败"})},ye=async l=>{var a;0===(await(a={ID:l.ID},e({url:"/product/deleteProduct",method:"delete",params:a}))).code&&(g({type:"success",message:"删除成功"}),1===Z.value.length&&M.value>1&&M.value--,re(),se())},be=async()=>{y.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const l=[];if(0===ae.value.length)return void g({type:"warning",message:"请选择要删除的数据"});ae.value&&ae.value.map((e=>{l.push(e.ID)}));var a;0===(await(a={IDs:l},e({url:"/product/deleteProductByIds",method:"delete",params:a}))).code&&(g({type:"success",message:"删除成功"}),Z.value.length===l.length&&M.value>1&&M.value--,re(),se())}))},we=a(!1),_e=a({actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",isActive:null}),Ve=()=>{_e.value={actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",isActive:null},we.value=!0},ke=async()=>{const l={ids:ae.value.map((e=>e.ID))};null!==_e.value.actualWeight&&(l.actualWeight=_e.value.actualWeight),null!==_e.value.ozonWeight&&(l.ozonWeight=_e.value.ozonWeight),_e.value.shopName&&(l.shopName=_e.value.shopName),_e.value.brand&&(l.brand=_e.value.brand),_e.value.description&&(l.description=_e.value.description),null!==_e.value.isActive&&(l.isActive=_e.value.isActive);var a;0===(await(a=l,e({url:"/product/batchUpdateProduct",method:"put",data:a}))).code&&(g({type:"success",message:"批量更新成功"}),we.value=!1,re(),se())},xe=a(!1),Ue=a(!1),ze=a({forceUpdate:!1}),We=()=>{ze.value={forceUpdate:!1},xe.value=!0},Ce=async()=>{Ue.value=!0;try{const a=await(l=ze.value,e({url:"/product/syncProductsFromOrders",method:"post",data:l}));0===a.code&&(g({type:"success",message:a.msg}),xe.value=!1,re(),se())}finally{Ue.value=!1}var l},Pe=async()=>{y.confirm("确定要从订单同步产品图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((async()=>{const l=g({message:"正在同步图片...",type:"info",duration:0});try{const a=await e({url:"/product/syncProductImagesFromOrders",method:"post"});l.close(),0===a.code&&(g({type:"success",message:a.msg}),re(),se())}catch(a){l.close(),g({type:"error",message:"同步图片失败"})}}))},Ne=e=>e?new Date(e).toLocaleDateString("zh-CN"):"",Ae=e=>{if(!e)return{color:"#999",fontSize:"12px",fontStyle:"italic"};const l=e.length;let a="13px";return l>50?a="11px":l>30&&(a="12px"),{fontSize:a,lineHeight:"1.3",color:"#333",wordBreak:"break-word",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden"}};return re(),se(),(e,l)=>{const a=o("el-input"),t=o("el-form-item"),b=o("el-option"),re=o("el-select"),Re=o("el-button"),De=o("el-form"),Oe=o("el-card"),Se=o("el-col"),Ie=o("el-row"),Be=o("el-table-column"),Fe=o("el-icon"),Te=o("el-image"),Ke=o("el-link"),je=o("el-tag"),Le=o("el-table"),Ee=o("el-pagination"),qe=o("el-input-number"),Ge=o("el-switch"),He=o("el-dialog");return d(),u("div",null,[i("div",w,[r(De,{ref_key:"elSearchFormRef",ref:H,inline:!0,model:$.value,class:"demo-form-inline",rules:ee,onKeyup:c(ue,["enter"])},{default:s((()=>[r(t,{label:"SKU",prop:"sku"},{default:s((()=>[r(a,{modelValue:$.value.sku,"onUpdate:modelValue":l[0]||(l[0]=e=>$.value.sku=e),placeholder:"搜索SKU"},null,8,["modelValue"])])),_:1}),r(t,{label:"店铺",prop:"shopName"},{default:s((()=>[r(a,{modelValue:$.value.shopName,"onUpdate:modelValue":l[1]||(l[1]=e=>$.value.shopName=e),placeholder:"搜索店铺"},null,8,["modelValue"])])),_:1}),r(t,{label:"品牌",prop:"brand"},{default:s((()=>[r(a,{modelValue:$.value.brand,"onUpdate:modelValue":l[2]||(l[2]=e=>$.value.brand=e),placeholder:"搜索品牌"},null,8,["modelValue"])])),_:1}),r(t,{label:"状态",prop:"isActive"},{default:s((()=>[r(re,{modelValue:$.value.isActive,"onUpdate:modelValue":l[3]||(l[3]=e=>$.value.isActive=e),placeholder:"选择状态",clearable:""},{default:s((()=>[r(b,{label:"启用",value:!0}),r(b,{label:"禁用",value:!1})])),_:1},8,["modelValue"])])),_:1}),r(t,null,{default:s((()=>[r(Re,{type:"primary",icon:"search",onClick:ue},{default:s((()=>l[29]||(l[29]=[n("查询")]))),_:1}),r(Re,{icon:"refresh",onClick:oe},{default:s((()=>l[30]||(l[30]=[n("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])]),i("div",_,[r(Ie,{gutter:20},{default:s((()=>[r(Se,{span:8},{default:s((()=>[r(Oe,{class:"product-card"},{default:s((()=>[i("div",V,[l[31]||(l[31]=i("div",{class:"product-label"},"总产品数",-1)),i("div",k,p(le.value.totalProducts),1)])])),_:1})])),_:1}),r(Se,{span:8},{default:s((()=>[r(Oe,{class:"product-card"},{default:s((()=>[i("div",x,[l[32]||(l[32]=i("div",{class:"product-label"},"启用产品",-1)),i("div",U,p(le.value.activeProducts),1)])])),_:1})])),_:1}),r(Se,{span:8},{default:s((()=>[r(Oe,{class:"product-card"},{default:s((()=>[i("div",z,[l[33]||(l[33]=i("div",{class:"product-label"},"禁用产品",-1)),i("div",W,p(le.value.inactiveProducts),1)])])),_:1})])),_:1})])),_:1})]),i("div",C,[i("div",P,[r(Re,{type:"primary",icon:"plus",onClick:ve},{default:s((()=>l[34]||(l[34]=[n("新增产品")]))),_:1}),r(Re,{type:"success",icon:"refresh",onClick:se},{default:s((()=>l[35]||(l[35]=[n("刷新汇总")]))),_:1}),r(Re,{type:"info",icon:"download",onClick:We},{default:s((()=>l[36]||(l[36]=[n("从订单同步")]))),_:1}),r(Re,{type:"primary",icon:"picture",onClick:Pe},{default:s((()=>l[37]||(l[37]=[n("同步图片")]))),_:1}),r(Re,{type:"warning",icon:"edit",disabled:!ae.value.length,onClick:Ve},{default:s((()=>l[38]||(l[38]=[n("批量编辑")]))),_:1},8,["disabled"]),r(Re,{type:"danger",icon:"delete",disabled:!ae.value.length,onClick:be},{default:s((()=>l[39]||(l[39]=[n("批量删除")]))),_:1},8,["disabled"])]),r(Le,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:Z.value,"row-key":"ID",onSelectionChange:te},{default:s((()=>[r(Be,{type:"selection",width:"55"}),r(Be,{align:"left",label:"店铺",prop:"shopName",width:"120"}),r(Be,{align:"left",label:"产品信息",prop:"productInfo","min-width":"280"},{default:s((e=>[i("div",N,[i("div",A,[e.row.imageUrl?(d(),m(Te,{key:0,src:e.row.imageUrl,"preview-src-list":[e.row.imageUrl],fit:"cover",style:{width:"60px",height:"60px","border-radius":"4px",cursor:"pointer"},"preview-teleported":!0},{error:s((()=>[i("div",R,[r(Fe,null,{default:s((()=>[r(v(f))])),_:1})])])),_:2},1032,["src","preview-src-list"])):(d(),u("div",D,[r(Fe,null,{default:s((()=>[r(v(f))])),_:1}),l[40]||(l[40]=i("span",{style:{"font-size":"10px","margin-top":"2px"}},"无图片",-1))]))]),i("div",O,[i("div",S,[r(Ke,{type:"primary",onClick:l=>{return a=e.row.sku,void g({type:"info",message:"查看产品详情: ".concat(a)});var a},style:{"font-size":"13px","font-weight":"500"}},{default:s((()=>[n(p(e.row.sku),1)])),_:2},1032,["onClick"])]),i("div",{style:h(Ae(e.row.productName)),title:e.row.productName},p(e.row.productName||"未设置产品名称"),13,I)])])])),_:1}),r(Be,{align:"left",label:"实际重量(kg)",prop:"actualWeight",width:"120"},{default:s((e=>[e.row.actualWeight?(d(),u("span",B,p(e.row.actualWeight)+"kg",1)):(d(),m(je,{key:1,type:"warning",size:"small"},{default:s((()=>l[41]||(l[41]=[n("未设置")]))),_:1}))])),_:1}),r(Be,{align:"left",label:"Ozon重量(kg)",prop:"ozonWeight",width:"120"},{default:s((e=>[e.row.ozonWeight?(d(),u("span",F,p(e.row.ozonWeight)+"kg",1)):(d(),m(je,{key:1,type:"info",size:"small"},{default:s((()=>l[42]||(l[42]=[n("未设置")]))),_:1}))])),_:1}),r(Be,{align:"center",label:"成本价格",prop:"costPrice",width:"100"},{default:s((e=>[e.row.costPrice?(d(),u("span",T," ¥"+p(parseFloat(e.row.costPrice).toFixed(2)),1)):(d(),m(je,{key:1,type:"warning",size:"small"},{default:s((()=>l[43]||(l[43]=[n("未设置")]))),_:1}))])),_:1}),r(Be,{align:"center",label:"佣金比率",prop:"commissionRate",width:"100"},{default:s((e=>[e.row.commissionRate?(d(),u("span",K,p((100*parseFloat(e.row.commissionRate)).toFixed(1))+"% ",1)):(d(),m(je,{key:1,type:"warning",size:"small"},{default:s((()=>l[44]||(l[44]=[n("未设置")]))),_:1}))])),_:1}),r(Be,{align:"left",label:"品牌",prop:"brand",width:"120"}),r(Be,{align:"left",label:"状态",prop:"isActive",width:"80"},{default:s((e=>[r(je,{type:e.row.isActive?"success":"danger"},{default:s((()=>[n(p(e.row.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),r(Be,{align:"left",label:"首次订单",prop:"firstOrderDate",width:"120"},{default:s((e=>[n(p(Ne(e.row.firstOrderDate)),1)])),_:1}),r(Be,{align:"left",label:"最后订单",prop:"lastOrderDate",width:"120"},{default:s((e=>[n(p(Ne(e.row.lastOrderDate)),1)])),_:1}),r(Be,{align:"center",label:"订单数",prop:"totalOrderCount",width:"80"},{default:s((e=>[r(je,{type:"info",size:"small"},{default:s((()=>[n(p(e.row.totalOrderCount||0),1)])),_:2},1024)])),_:1}),r(Be,{align:"left",label:"操作",fixed:"right",width:"160"},{default:s((e=>[r(Re,{type:"primary",link:"",icon:"edit",onClick:l=>ge(e.row)},{default:s((()=>l[45]||(l[45]=[n("编辑")]))),_:2},1032,["onClick"]),r(Re,{type:"danger",link:"",icon:"delete",onClick:l=>{return a=e.row,void y.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ye(a)}));var a}},{default:s((()=>l[46]||(l[46]=[n("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),i("div",j,[r(Ee,{layout:"total, sizes, prev, pager, next, jumper","current-page":M.value,"page-size":Y.value,"page-sizes":[10,30,50,100],total:Q.value,onCurrentChange:ie,onSizeChange:de},null,8,["current-page","page-size","total"])])]),r(He,{modelValue:pe.value,"onUpdate:modelValue":l[15]||(l[15]=e=>pe.value=e),title:"create"===me.value?"新增产品":"编辑产品",width:"600px"},{footer:s((()=>[i("div",L,[r(Re,{onClick:fe},{default:s((()=>l[48]||(l[48]=[n("取消")]))),_:1}),r(Re,{type:"primary",onClick:he},{default:s((()=>l[49]||(l[49]=[n("确定")]))),_:1})])])),default:s((()=>[r(De,{ref_key:"elFormRef",ref:G,model:ne.value,rules:ce,"label-width":"100px"},{default:s((()=>[r(t,{label:"SKU",prop:"sku"},{default:s((()=>[r(a,{modelValue:ne.value.sku,"onUpdate:modelValue":l[4]||(l[4]=e=>ne.value.sku=e),placeholder:"请输入SKU",disabled:"update"===me.value},null,8,["modelValue","disabled"])])),_:1}),r(t,{label:"实际重量(kg)",prop:"actualWeight"},{default:s((()=>[r(qe,{modelValue:ne.value.actualWeight,"onUpdate:modelValue":l[5]||(l[5]=e=>ne.value.actualWeight=e),precision:3,min:0,placeholder:"请输入实际重量",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"Ozon重量(kg)",prop:"ozonWeight"},{default:s((()=>[r(qe,{modelValue:ne.value.ozonWeight,"onUpdate:modelValue":l[6]||(l[6]=e=>ne.value.ozonWeight=e),precision:3,min:0,placeholder:"请输入Ozon重量",style:{width:"100%"},disabled:"update"===me.value},null,8,["modelValue","disabled"])])),_:1}),r(t,{label:"成本价格",prop:"costPrice"},{default:s((()=>[r(qe,{modelValue:ne.value.costPrice,"onUpdate:modelValue":l[7]||(l[7]=e=>ne.value.costPrice=e),precision:2,min:0,placeholder:"请输入成本价格",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"佣金比率",prop:"commissionRate"},{default:s((()=>[r(qe,{modelValue:ne.value.commissionRate,"onUpdate:modelValue":l[8]||(l[8]=e=>ne.value.commissionRate=e),precision:4,min:0,max:1,placeholder:"请输入佣金比率(0-1)",style:{width:"100%"}},null,8,["modelValue"]),l[47]||(l[47]=i("div",{style:{"font-size":"12px",color:"#909399","margin-top":"4px"}}," 例如：0.12 表示 12% ",-1))])),_:1}),r(t,{label:"店铺",prop:"shopName"},{default:s((()=>[r(a,{modelValue:ne.value.shopName,"onUpdate:modelValue":l[9]||(l[9]=e=>ne.value.shopName=e),placeholder:"请输入店铺名称",disabled:"update"===me.value},null,8,["modelValue","disabled"])])),_:1}),r(t,{label:"品牌",prop:"brand"},{default:s((()=>[r(a,{modelValue:ne.value.brand,"onUpdate:modelValue":l[10]||(l[10]=e=>ne.value.brand=e),placeholder:"请输入品牌"},null,8,["modelValue"])])),_:1}),r(t,{label:"产品描述",prop:"description"},{default:s((()=>[r(a,{modelValue:ne.value.description,"onUpdate:modelValue":l[11]||(l[11]=e=>ne.value.description=e),type:"textarea",placeholder:"请输入产品描述"},null,8,["modelValue"])])),_:1}),r(t,{label:"图片URL",prop:"imageUrl"},{default:s((()=>[r(a,{modelValue:ne.value.imageUrl,"onUpdate:modelValue":l[12]||(l[12]=e=>ne.value.imageUrl=e),placeholder:"请输入图片URL"},null,8,["modelValue"])])),_:1}),r(t,{label:"备注",prop:"notes"},{default:s((()=>[r(a,{modelValue:ne.value.notes,"onUpdate:modelValue":l[13]||(l[13]=e=>ne.value.notes=e),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])])),_:1}),r(t,{label:"状态",prop:"isActive"},{default:s((()=>[r(Ge,{modelValue:ne.value.isActive,"onUpdate:modelValue":l[14]||(l[14]=e=>ne.value.isActive=e),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),r(He,{modelValue:we.value,"onUpdate:modelValue":l[25]||(l[25]=e=>we.value=e),title:"批量编辑产品",width:"500px"},{footer:s((()=>[i("div",E,[r(Re,{onClick:l[24]||(l[24]=e=>we.value=!1)},{default:s((()=>l[51]||(l[51]=[n("取消")]))),_:1}),r(Re,{type:"primary",onClick:ke},{default:s((()=>l[52]||(l[52]=[n("确定")]))),_:1})])])),default:s((()=>[r(De,{ref_key:"batchFormRef",ref:X,model:_e.value,"label-width":"120px"},{default:s((()=>[r(t,{label:"实际重量(kg)"},{default:s((()=>[r(qe,{modelValue:_e.value.actualWeight,"onUpdate:modelValue":l[16]||(l[16]=e=>_e.value.actualWeight=e),precision:3,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"Ozon重量(kg)"},{default:s((()=>[r(qe,{modelValue:_e.value.ozonWeight,"onUpdate:modelValue":l[17]||(l[17]=e=>_e.value.ozonWeight=e),precision:3,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"成本价格"},{default:s((()=>[r(qe,{modelValue:_e.value.costPrice,"onUpdate:modelValue":l[18]||(l[18]=e=>_e.value.costPrice=e),precision:2,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),r(t,{label:"佣金比率"},{default:s((()=>[r(qe,{modelValue:_e.value.commissionRate,"onUpdate:modelValue":l[19]||(l[19]=e=>_e.value.commissionRate=e),precision:4,min:0,max:1,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"]),l[50]||(l[50]=i("div",{style:{"font-size":"12px",color:"#909399","margin-top":"4px"}}," 例如：0.12 表示 12% ",-1))])),_:1}),r(t,{label:"店铺"},{default:s((()=>[r(a,{modelValue:_e.value.shopName,"onUpdate:modelValue":l[20]||(l[20]=e=>_e.value.shopName=e),placeholder:"留空表示不修改"},null,8,["modelValue"])])),_:1}),r(t,{label:"品牌"},{default:s((()=>[r(a,{modelValue:_e.value.brand,"onUpdate:modelValue":l[21]||(l[21]=e=>_e.value.brand=e),placeholder:"留空表示不修改"},null,8,["modelValue"])])),_:1}),r(t,{label:"产品描述"},{default:s((()=>[r(a,{modelValue:_e.value.description,"onUpdate:modelValue":l[22]||(l[22]=e=>_e.value.description=e),type:"textarea",placeholder:"留空表示不修改"},null,8,["modelValue"])])),_:1}),r(t,{label:"状态"},{default:s((()=>[r(re,{modelValue:_e.value.isActive,"onUpdate:modelValue":l[23]||(l[23]=e=>_e.value.isActive=e),placeholder:"留空表示不修改",clearable:""},{default:s((()=>[r(b,{label:"启用",value:!0}),r(b,{label:"禁用",value:!1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),r(He,{modelValue:xe.value,"onUpdate:modelValue":l[28]||(l[28]=e=>xe.value=e),title:"从订单同步产品",width:"400px"},{footer:s((()=>[i("div",q,[r(Re,{onClick:l[27]||(l[27]=e=>xe.value=!1)},{default:s((()=>l[54]||(l[54]=[n("取消")]))),_:1}),r(Re,{type:"primary",onClick:Ce,loading:Ue.value},{default:s((()=>l[55]||(l[55]=[n("开始同步")]))),_:1},8,["loading"])])])),default:s((()=>[r(De,{ref_key:"syncFormRef",ref:J,model:ze.value,"label-width":"120px"},{default:s((()=>[r(t,{label:"强制更新"},{default:s((()=>[r(Ge,{modelValue:ze.value.forceUpdate,"onUpdate:modelValue":l[26]||(l[26]=e=>ze.value.forceUpdate=e),"active-text":"是","inactive-text":"否"},null,8,["modelValue"]),l[53]||(l[53]=i("div",{class:"form-tip"},"开启后会更新已存在产品的名称等信息",-1))])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-77a04b95"]]);export{G as default};
