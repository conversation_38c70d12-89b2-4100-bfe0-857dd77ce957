/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as e}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const d=d=>e({url:"/od/createOrder",method:"post",data:d}),r=d=>e({url:"/od/deleteOrder",method:"delete",params:d}),t=d=>e({url:"/od/deleteOrderByIds",method:"delete",params:d}),a=d=>e({url:"/od/updateOrder",method:"put",data:d}),o=d=>e({url:"/od/findOrder",method:"get",params:d}),s=d=>e({url:"/od/getOrderList",method:"get",params:d}),l=()=>e({url:"/od/pullOrders",method:"post"}),m=()=>e({url:"/od/pullYearOrders",method:"post"});export{m as a,t as b,d as c,r as d,o as f,s as g,l as p,a as u};
