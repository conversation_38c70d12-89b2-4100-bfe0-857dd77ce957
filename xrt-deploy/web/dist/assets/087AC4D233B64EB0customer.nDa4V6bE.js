/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as e,a,g as t,c as l,o,f as u,b as s,w as r,h as n,t as c,v as i,aa as d,ab as m,E as p}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as v}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";const f=a=>e({url:"/customer/customer",method:"post",data:a}),g={class:"gva-table-box"},h={class:"gva-btn-list"},w={class:"gva-pagination"},b={class:"flex justify-between items-center"},y=Object.assign({name:"Customer"},{__name:"customer",setup(y){const _=a({customerName:"",customerPhoneData:""}),C=a(1),D=a(0),k=a(10),x=a([]),V=e=>{k.value=e,B()},z=e=>{C.value=e,B()},B=async()=>{const a=await(t={page:C.value,pageSize:k.value},e({url:"/customer/customerList",method:"get",params:t}));var t;0===a.code&&(x.value=a.data.list,D.value=a.data.total,C.value=a.data.page,k.value=a.data.pageSize)};B();const I=a(!1),j=a(""),N=async a=>{const t=await(l={ID:a.ID},e({url:"/customer/customer",method:"get",params:l}));var l;j.value="update",0===t.code&&(_.value=t.data.customer,I.value=!0)},P=()=>{I.value=!1,_.value={customerName:"",customerPhoneData:""}},U=async a=>{m.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{var t;0===(await(t={ID:a.ID},e({url:"/customer/customer",method:"delete",data:t}))).code&&(p({type:"success",message:"删除成功"}),1===x.value.length&&C.value>1&&C.value--,B())}))},A=async()=>{let a;switch(j.value){case"create":default:a=await f(_.value);break;case"update":a=await(t=_.value,e({url:"/customer/customer",method:"put",data:t}))}var t;0===a.code&&(P(),B())},E=()=>{j.value="create",I.value=!0};return(e,a)=>{const m=t("el-button"),p=t("el-table-column"),f=t("el-table"),y=t("el-pagination"),B=t("el-input"),j=t("el-form-item"),S=t("el-form"),T=t("el-drawer");return o(),l("div",null,[u(v,{title:"在资源权限中将此角色的资源权限清空 或者不包含创建者的角色 即可屏蔽此客户资源的显示"}),s("div",g,[s("div",h,[u(m,{type:"primary",icon:"plus",onClick:E},{default:r((()=>a[3]||(a[3]=[n("新增")]))),_:1})]),u(f,{ref:"multipleTable",data:x.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID"},{default:r((()=>[u(p,{type:"selection",width:"55"}),u(p,{align:"left",label:"接入日期",width:"180"},{default:r((e=>[s("span",null,c(i(d)(e.row.CreatedAt)),1)])),_:1}),u(p,{align:"left",label:"姓名",prop:"customerName",width:"120"}),u(p,{align:"left",label:"电话",prop:"customerPhoneData",width:"120"}),u(p,{align:"left",label:"接入人ID",prop:"sysUserId",width:"120"}),u(p,{align:"left",label:"操作","min-width":"160"},{default:r((e=>[u(m,{type:"primary",link:"",icon:"edit",onClick:a=>N(e.row)},{default:r((()=>a[4]||(a[4]=[n("变更")]))),_:2},1032,["onClick"]),u(m,{type:"primary",link:"",icon:"delete",onClick:a=>U(e.row)},{default:r((()=>a[5]||(a[5]=[n("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),s("div",w,[u(y,{"current-page":C.value,"page-size":k.value,"page-sizes":[10,30,50,100],total:D.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:z,onSizeChange:V},null,8,["current-page","page-size","total"])])]),u(T,{modelValue:I.value,"onUpdate:modelValue":a[2]||(a[2]=e=>I.value=e),"before-close":P,"show-close":!1},{header:r((()=>[s("div",b,[a[8]||(a[8]=s("span",{class:"text-lg"},"客户",-1)),s("div",null,[u(m,{onClick:P},{default:r((()=>a[6]||(a[6]=[n("取 消")]))),_:1}),u(m,{type:"primary",onClick:A},{default:r((()=>a[7]||(a[7]=[n("确 定")]))),_:1})])])])),default:r((()=>[u(S,{inline:!0,model:_.value,"label-width":"80px"},{default:r((()=>[u(j,{label:"客户名"},{default:r((()=>[u(B,{modelValue:_.value.customerName,"onUpdate:modelValue":a[0]||(a[0]=e=>_.value.customerName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),u(j,{label:"客户电话"},{default:r((()=>[u(B,{modelValue:_.value.customerPhoneData,"onUpdate:modelValue":a[1]||(a[1]=e=>_.value.customerPhoneData=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}});export{y as default};
