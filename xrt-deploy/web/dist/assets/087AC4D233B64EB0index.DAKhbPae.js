/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import e from"./087AC4D233B64EB0menuItem.h4t5Q0OS.js";import r from"./087AC4D233B64EB0asyncSubmenu.BQ2WRSj6.js";import{K as n,g as o,i as t,d as u,o as s,w as i,c as a,F as f,D as d,Y as l}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const m=Object.assign({name:"AsideComponent"},{__name:"index",props:{routerInfo:{type:Object,default:()=>null},mode:{type:String,default:"vertical"}},setup(m){const c=m,p=n((()=>c.routerInfo.children&&c.routerInfo.children.filter((e=>!e.hidden)).length?r:e));return(e,r)=>{const n=o("AsideComponent");return m.routerInfo.hidden?u("",!0):(s(),t(l(p.value),{key:0,"router-info":m.routerInfo},{default:i((()=>[m.routerInfo.children&&m.routerInfo.children.length?(s(!0),a(f,{key:0},d(m.routerInfo.children,(e=>(s(),t(n,{key:e.name,"router-info":e},null,8,["router-info"])))),128)):u("",!0)])),_:1},8,["router-info"]))}}});export{m as default};
