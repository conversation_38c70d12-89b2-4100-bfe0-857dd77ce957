/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{g as e,b as l}from"./087AC4D233B64EB0image.DZXThNBc.js";import{_ as a,C as t,a as s,U as o,d as u,c as d,e as r,f as n,g as i,b as c}from"./087AC4D233B64EB0QR-code.DGKmWLV2.js";import{K as m,g as v,c as p,o as f,b as g,d as y,i as h,v as w,w as k,f as I,h as b,y as x,n as C,aB as _,_ as D,aC as V,a as B,ap as j,F as z,D as S,t as U,aD as E,aE as T,aF as A,aG as M,aH as O,E as F,ab as N}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const P={key:1,class:"w-full h-full object-cover",muted:"",preload:"metadata"},q=["src"],G={__name:"selectComponent",props:{model:{default:"",type:String},rounded:{default:!1,type:Boolean}},emits:["chooseItem","deleteItem"],setup(a,{emit:t}){const s=a,o=t,u=()=>{o("chooseItem")},d=()=>{o("deleteItem")},r=m((()=>e(s.model))),n=m((()=>r.value?[r.value]:[]));return(t,s)=>{const o=v("VideoPlay"),i=v("el-icon"),c=v("el-image");return f(),p("div",{class:C(["w-40 h-40 relative rounded border border-dashed border-gray-300 cursor-pointer group",a.rounded?"rounded-full":""])},[g("div",{class:C(["w-full h-full overflow-hidden",a.rounded?"rounded-full":""])},[w(l)(a.model||"")?(f(),h(i,{key:0,size:32,class:"absolute top-[calc(50%-16px)] left-[calc(50%-16px)]"},{default:k((()=>[I(o)])),_:1})):y("",!0),w(l)(a.model||"")?(f(),p("video",P,[g("source",{src:w(e)(a.model)+"#t=1"},null,8,q)])):y("",!0),a.model&&!w(l)(a.model)?(f(),h(c,{key:2,class:"w-full h-full",src:r.value,"preview-src-list":n.value,fit:"cover"},null,8,["src","preview-src-list"])):(f(),p("div",{key:3,class:"text-gray-600 group-hover:bg-gray-200 group-hover:opacity-60 w-full h-full flex justify-center items-center",onClick:u},[I(i,null,{default:k((()=>[I(w(x))])),_:1}),s[0]||(s[0]=b(" 上传 "))]))],2),a.model?(f(),p("div",{key:0,class:"right-0 top-0 hidden text-gray-400 group-hover:flex justify-center items-center absolute z-10",onClick:d},[I(i,{size:24},{default:k((()=>[I(w(_))])),_:1})])):y("",!0)],2)}}},H={key:1,class:"w-full gap-4 flex flex-wrap"},K={class:"flex"},L={class:"w-64",style:{"border-right":"solid 1px var(--el-border-color)"}},Q={class:"ml-4 w-[605px]"},R={class:"gva-btn-list gap-2"},J={class:"gva-btn-list gap-2"},W={class:"flex flex-wrap gap-4"},X={class:"w-40 h-40 border rounded overflow-hidden border-dashed border-gray-300 cursor-pointer relative group"},Y=["onClick"],Z=["src"],$={key:2,class:"w-full h-full object-cover flex items-center justify-center"},ee=["onClick"],le=["onClick"],ae=D({__name:"selectImage",props:V({multiple:{type:Boolean,default:!1},fileType:{type:String,default:""},maxUpdateCount:{type:Number,default:0},rounded:{type:Boolean,default:!1}},{modelValue:{type:[String,Array]},modelModifiers:{}}),emits:["update:modelValue"],setup(m){const _=B(""),D=B(""),V=B({keyword:null,classId:0}),P=B(1),q=B(0),ae=B(20),te=j(m,"modelValue"),se=m,oe=e=>{ae.value=e,me()},ue=e=>{P.value=e,me()},de=()=>{V.value.classId=0,P.value=1,me()},re=B(!1),ne=B([]),ie={image:["png","jpg","jpeg","gif","bmp","webp","svg"],video:["mp4","avi","rmvb","rm","asf","divx","mpg","mpeg","mpe","wmv","mkv","vob"]},ce=async()=>{!te.value||se.multiple?(await me(),await fe(),re.value=!0):te.value=""},me=async()=>{const e=await i({page:P.value,pageSize:ae.value,...V.value});0===e.code&&(ne.value=e.data.list,q.value=e.data.total,P.value=e.data.page,ae.value=e.data.pageSize)},ve={children:"children",label:"name",value:"ID"},pe=B([]),fe=async()=>{const e=await c();let l={name:"全部分类",ID:0,pid:0,children:[]};0===e.code&&(pe.value=e.data||[],pe.value.unshift(l))},ge=e=>{V.value.keyword=null,V.value.classId=e.ID,P.value=1,me()},ye=()=>{V.value.keyword=null,P.value=1,me()},he=B(!1),we=B({ID:0,pid:0,name:""}),ke=B(null),Ie=B({name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{max:20,message:"最多20位字符",trigger:"blur"}]}),be=async()=>{ke.value.validate((async e=>{if(e){0===(await n(we.value)).code&&(F({type:"success",message:"操作成功"}),await fe(),xe())}}))},xe=()=>{he.value=!1,we.value={ID:0,pid:0,name:""}},Ce=B([]),_e=e=>{if(!1===se.multiple)return void(e=>{if(se.fileType&&!ie[se.fileType].some((l=>{if(null==e?void 0:e.toLowerCase().includes(l))return!0})))return void F({type:"error",message:"当前类型不支持使用"});te.value=e,re.value=!1})(e.url);const l=Ce.value.findIndex((l=>l.ID===e.ID));l>-1?Ce.value.splice(l,1):Ce.value.push(e)},De=e=>Ce.value.some((l=>l.ID===e.ID)),Ve=()=>{Ce.value.forEach((e=>{te.value.push(e.url)})),re.value=!1,Ce.value=[]};return(n,i)=>{var c;const B=v("el-icon"),j=v("el-dropdown-item"),ie=v("el-dropdown-menu"),Be=v("el-dropdown"),je=v("el-tree"),ze=v("el-scrollbar"),Se=v("el-input"),Ue=v("el-button"),Ee=v("el-image"),Te=v("el-pagination"),Ae=v("el-drawer"),Me=v("el-tree-select"),Oe=v("el-form-item"),Fe=v("el-form"),Ne=v("el-dialog");return f(),p("div",null,[se.multiple?(f(),p("div",H,[(f(!0),p(z,null,S(te.value,((e,l)=>(f(),h(G,{rounded:m.rounded,key:l,model:e,onChooseItem:ce,onDeleteItem:e=>(e=>{te.value.splice(e,1)})(l)},null,8,["rounded","model","onDeleteItem"])))),128)),(null==(c=te.value)?void 0:c.length)<se.maxUpdateCount||0===se.maxUpdateCount?(f(),h(G,{key:0,rounded:m.rounded,onChooseItem:ce,onDeleteItem:ce},null,8,["rounded"])):y("",!0)])):(f(),h(G,{key:0,rounded:m.rounded,model:te.value,onChooseItem:ce,onDeleteItem:ce},null,8,["rounded","model"])),I(Ae,{modelValue:re.value,"onUpdate:modelValue":i[1]||(i[1]=e=>re.value=e),title:"媒体库 | 点击“文件名”可以编辑，选择的类别即是上传的类别",size:880},{default:k((()=>[g("div",K,[g("div",L,[I(ze,{style:{height:"calc(100vh - 110px)"}},{default:k((()=>[I(je,{data:pe.value,"node-key":"id",props:ve,onNodeClick:ge,"default-expand-all":""},{default:k((({node:e,data:l})=>[g("div",{class:C(["w-36",V.value.classId===l.ID?"text-blue-500 font-bold":""])},U(l.name),3),I(Be,null,{dropdown:k((()=>[I(ie,null,{default:k((()=>[I(j,{onClick:e=>{return a=l,he.value=!0,we.value.ID=0,void(we.value.pid=a.ID);var a}},{default:k((()=>i[5]||(i[5]=[b("添加分类")]))),_:2},1032,["onClick"]),l.ID>0?(f(),h(j,{key:0,onClick:e=>{return a=l,we.value={ID:a.ID,pid:a.pid,name:a.name},void(he.value=!0);var a}},{default:k((()=>i[6]||(i[6]=[b("编辑分类")]))),_:2},1032,["onClick"])):y("",!0),l.ID>0?(f(),h(j,{key:1,onClick:e=>(async e=>{0===(await u({id:e})).code&&(F.success({type:"success",message:"删除成功"}),await fe())})(l.ID)},{default:k((()=>i[7]||(i[7]=[b("删除分类")]))),_:2},1032,["onClick"])):y("",!0)])),_:2},1024)])),default:k((()=>[l.ID>0?(f(),h(B,{key:0,class:"ml-3 text-right"},{default:k((()=>[I(w(E))])),_:1})):(f(),h(B,{key:1,class:"ml-3 text-right mt-1"},{default:k((()=>[I(w(x))])),_:1}))])),_:2},1024)])),_:1},8,["data"])])),_:1})]),g("div",Q,[g("div",R,[I(Se,{modelValue:V.value.keyword,"onUpdate:modelValue":i[0]||(i[0]=e=>V.value.keyword=e),modelModifiers:{trim:!0},class:"w-96",placeholder:"请输入文件名或备注",clearable:""},null,8,["modelValue"]),I(Ue,{type:"primary",icon:"search",onClick:de})]),g("div",J,[I(Ue,{onClick:Ve,type:"danger",disabled:0===Ce.value.length,icon:w(T)},{default:k((()=>i[8]||(i[8]=[b("选定")]))),_:1},8,["disabled","icon"]),I(a,{"image-common":D.value,classId:V.value.classId,onOnSuccess:ye},null,8,["image-common","classId"]),I(t,{classId:V.value.classId,onOnSuccess:ye},null,8,["classId"]),I(s,{classId:V.value.classId,onOnSuccess:ye},null,8,["classId"]),I(o,{"image-url":_.value,"file-size":2048,"max-w-h":1080,classId:V.value.classId,onOnSuccess:ye},null,8,["image-url","classId"])]),g("div",W,[(f(!0),p(z,null,S(ne.value,((a,t)=>(f(),p("div",{key:t,class:"w-40"},[g("div",X,[(f(),h(Ee,{key:t,src:w(e)(a.url),fit:"cover",class:C(["w-full h-full relative",{selected:De(a)}]),onClick:e=>_e(a)},{error:k((()=>[w(l)(a.url||"")?(f(),h(B,{key:0,size:32,class:"absolute top-[calc(50%-16px)] left-[calc(50%-16px)]"},{default:k((()=>[I(w(A))])),_:1})):y("",!0),w(l)(a.url||"")?(f(),p("video",{key:1,class:C(["w-full h-full object-cover",{selected:De(a)}]),muted:"",preload:"metadata",onClick:e=>_e(a)},[g("source",{src:w(e)(a.url)+"#t=1"},null,8,Z),i[9]||(i[9]=b(" 您的浏览器不支持视频播放 "))],10,Y)):(f(),p("div",$,[I(B,{size:32},{default:k((()=>[I(w(M))])),_:1})]))])),_:2},1032,["src","onClick","class"])),g("div",{class:"absolute -right-1 top-1 w-8 h-8 group-hover:inline-block hidden",onClick:e=>(e=>{N.confirm("是否删除该文件","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await d(e)).code&&(F({type:"success",message:"删除成功!"}),await me())})).catch((()=>{F({type:"info",message:"已取消删除"})}))})(a)},[I(B,{size:18},{default:k((()=>[I(w(O))])),_:1})],8,ee)]),g("div",{class:"overflow-hidden text-nowrap overflow-ellipsis text-center w-full cursor-pointer",onClick:e=>(async e=>{N.prompt("请输入文件名或者备注","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S/,inputErrorMessage:"不能为空",inputValue:e.name}).then((async({value:l})=>{e.name=l,0===(await r(e)).code&&(F({type:"success",message:"编辑成功!"}),await me())})).catch((()=>{F({type:"info",message:"取消修改"})}))})(a)},U(a.name),9,le)])))),128))]),I(Te,{"current-page":P.value,"page-size":ae.value,total:q.value,class:"justify-center",layout:"total, prev, pager, next, jumper",onCurrentChange:ue,onSizeChange:oe},null,8,["current-page","page-size","total"])])])])),_:1},8,["modelValue"]),I(Ne,{modelValue:he.value,"onUpdate:modelValue":i[4]||(i[4]=e=>he.value=e),onClose:xe,width:"520",title:(0===we.value.ID?"添加":"编辑")+"分类",draggable:""},{footer:k((()=>[I(Ue,{onClick:xe},{default:k((()=>i[10]||(i[10]=[b("取消")]))),_:1}),I(Ue,{type:"primary",onClick:be},{default:k((()=>i[11]||(i[11]=[b("确定")]))),_:1})])),default:k((()=>[I(Fe,{ref_key:"categoryForm",ref:ke,rules:Ie.value,model:we.value,"label-width":"80px"},{default:k((()=>[I(Oe,{label:"上级分类"},{default:k((()=>[I(Me,{modelValue:we.value.pid,"onUpdate:modelValue":i[2]||(i[2]=e=>we.value.pid=e),data:pe.value,"check-strictly":"",props:ve,"render-after-expand":!1,style:{width:"240px"}},null,8,["modelValue","data"])])),_:1}),I(Oe,{label:"分类名称",prop:"name"},{default:k((()=>[I(Se,{modelValue:we.value.name,"onUpdate:modelValue":i[3]||(i[3]=e=>we.value.name=e),modelModifiers:{trim:!0},placeholder:"分类名称"},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","model"])])),_:1},8,["modelValue","title"])])}}},[["__scopeId","data-v-1c8790b8"]]);export{ae as S};
