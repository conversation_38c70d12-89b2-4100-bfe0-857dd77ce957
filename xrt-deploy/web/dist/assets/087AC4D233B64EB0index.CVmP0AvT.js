/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{i as e,j as t,r as n,k as a,b as o}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";import{a as r,u as i,g as l,ae as s,c as u,o as c,b as d,f as g,w as p,h as m,t as h,v as f,aa as x,i as v,a7 as k,d as b,F as y,D as _,ab as w,E as C}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{b as R}from"./087AC4D233B64EB0stringFun.BxqK0MAg.js";import{V as F}from"./087AC4D233B64EB0theme-github_dark.JrEfhomR.js";var T,S,A={exports:{}};T||(T=1,S=A,ace.define("ace/mode/jsdoc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(){this.$rules={start:[{token:["comment.doc.tag","comment.doc.text","lparen.doc"],regex:"(@(?:param|member|typedef|property|namespace|var|const|callback))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:["rparen.doc","text.doc","variable.parameter.doc","lparen.doc","variable.parameter.doc","rparen.doc"],regex:/(})(\s*)(?:([\w=:\/\.]+)|(?:(\[)([\w=:\/\.\-\'\" ]+)(\])))/,next:"pop"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","lparen.doc"],regex:"(@(?:returns?|yields|type|this|suppress|public|protected|private|package|modifies|implements|external|exception|throws|enum|define|extends))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:'(@(?:alias|memberof|instance|module|name|lends|namespace|external|this|template|requires|param|implements|function|extends|typedef|mixes|constructor|var|memberof\\!|event|listens|exports|class|constructs|interface|emits|fires|throws|const|callback|borrows|augments))(\\s+)(\\w[\\w#.:/~"\\-]*)?'},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:"(@method)(\\s+)(\\w[\\w.\\(\\)]*)"},{token:"comment.doc.tag",regex:"@access\\s+(?:private|public|protected)"},{token:"comment.doc.tag",regex:"@kind\\s+(?:class|constant|event|external|file|function|member|mixin|module|namespace|typedef)"},{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},r.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}],"doc-syntax":[{token:"operator.doc",regex:/[|:]/},{token:"paren.doc",regex:/[\[\]]/}]},this.normalizeRules()};a.inherits(r,o),r.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},r.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},r.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.JsDocCommentHighlightRules=r})),ace.define("ace/mode/javascript_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/jsdoc_comment_highlight_rules","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./jsdoc_comment_highlight_rules").JsDocCommentHighlightRules,r=e("./text_highlight_rules").TextHighlightRules,i="[a-zA-Z\\$_¡-￿][a-zA-Z\\d\\$_¡-￿]*",l=function(e){var t={"variable.language":"Array|Boolean|Date|Function|Iterator|Number|Object|RegExp|String|Proxy|Symbol|Namespace|QName|XML|XMLList|ArrayBuffer|Float32Array|Float64Array|Int16Array|Int32Array|Int8Array|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray|Error|EvalError|InternalError|RangeError|ReferenceError|StopIteration|SyntaxError|TypeError|URIError|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|eval|isFinite|isNaN|parseFloat|parseInt|JSON|Math|this|arguments|prototype|window|document",keyword:"const|yield|import|get|set|async|await|break|case|catch|continue|default|delete|do|else|finally|for|if|in|of|instanceof|new|return|switch|throw|try|typeof|let|var|while|with|debugger|__parent__|__count__|escape|unescape|with|__proto__|class|enum|extends|super|export|implements|private|public|interface|package|protected|static|constructor","storage.type":"const|let|var|function","constant.language":"null|Infinity|NaN|undefined","support.function":"alert","constant.language.boolean":"true|false"},n=this.createKeywordMapper(t,"identifier"),a="\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|u{[0-9a-fA-F]{1,6}}|[0-2][0-7]{0,2}|3[0-7][0-7]?|[4-7][0-7]?|.)",r="(function)(\\s*)(\\*?)",l={token:["identifier","text","paren.lparen"],regex:"(\\b(?!"+Object.values(t).join("|")+"\\b)"+i+")(\\s*)(\\()"};this.$rules={no_regex:[o.getStartRule("doc-start"),u("no_regex"),l,{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:["entity.name.function","text","keyword.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(=)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))("+i+")(\\s*)(\\()",next:"function_arguments"},{token:["entity.name.function","text","punctuation.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(:)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:["text","text","storage.type","text","storage.type","text","paren.lparen"],regex:"(:)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:"keyword",regex:"from(?=\\s*('|\"))"},{token:"keyword",regex:"(?:case|do|else|finally|in|instanceof|return|throw|try|typeof|yield|void)\\b",next:"start"},{token:"support.constant",regex:/that\b/},{token:["storage.type","punctuation.operator","support.function.firebug"],regex:/(console)(\.)(warn|info|log|error|debug|time|trace|timeEnd|assert)\b/},{token:n,regex:i},{token:"punctuation.operator",regex:/[.](?![.])/,next:"property"},{token:"storage.type",regex:/=>/,next:"start"},{token:"keyword.operator",regex:/--|\+\+|\.{3}|===|==|=|!=|!==|<+=?|>+=?|!|&&|\|\||\?:|[!$%&*+\-~\/^]=?/,next:"start"},{token:"punctuation.operator",regex:/[?:,;.]/,next:"start"},{token:"paren.lparen",regex:/[\[({]/,next:"start"},{token:"paren.rparen",regex:/[\])}]/},{token:"comment",regex:/^#!.*$/}],property:[{token:"text",regex:"\\s+"},{token:"keyword.operator",regex:/=/},{token:["storage.type","text","storage.type","text","paren.lparen"],regex:r+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))(\\w+)(\\s*)(\\()",next:"function_arguments"},{token:"punctuation.operator",regex:/[.](?![.])/},{token:"support.function",regex:"prototype"},{token:"support.function",regex:/(s(?:h(?:ift|ow(?:Mod(?:elessDialog|alDialog)|Help))|croll(?:X|By(?:Pages|Lines)?|Y|To)?|t(?:op|rike)|i(?:n|zeToContent|debar|gnText)|ort|u(?:p|b(?:str(?:ing)?)?)|pli(?:ce|t)|e(?:nd|t(?:Re(?:sizable|questHeader)|M(?:i(?:nutes|lliseconds)|onth)|Seconds|Ho(?:tKeys|urs)|Year|Cursor|Time(?:out)?|Interval|ZOptions|Date|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Date|FullYear)|FullYear|Active)|arch)|qrt|lice|avePreferences|mall)|h(?:ome|andleEvent)|navigate|c(?:har(?:CodeAt|At)|o(?:s|n(?:cat|textual|firm)|mpile)|eil|lear(?:Timeout|Interval)?|a(?:ptureEvents|ll)|reate(?:StyleSheet|Popup|EventObject))|t(?:o(?:GMTString|S(?:tring|ource)|U(?:TCString|pperCase)|Lo(?:caleString|werCase))|est|a(?:n|int(?:Enabled)?))|i(?:s(?:NaN|Finite)|ndexOf|talics)|d(?:isableExternalCapture|ump|etachEvent)|u(?:n(?:shift|taint|escape|watch)|pdateCommands)|j(?:oin|avaEnabled)|p(?:o(?:p|w)|ush|lugins.refresh|a(?:ddings|rse(?:Int|Float)?)|r(?:int|ompt|eference))|e(?:scape|nableExternalCapture|val|lementFromPoint|x(?:p|ec(?:Script|Command)?))|valueOf|UTC|queryCommand(?:State|Indeterm|Enabled|Value)|f(?:i(?:nd|lter|le(?:ModifiedDate|Size|CreatedDate|UpdatedDate)|xed)|o(?:nt(?:size|color)|rward|rEach)|loor|romCharCode)|watch|l(?:ink|o(?:ad|g)|astIndexOf)|a(?:sin|nchor|cos|t(?:tachEvent|ob|an(?:2)?)|pply|lert|b(?:s|ort))|r(?:ou(?:nd|teEvents)|e(?:size(?:By|To)|calc|turnValue|place|verse|l(?:oad|ease(?:Capture|Events)))|andom)|g(?:o|et(?:ResponseHeader|M(?:i(?:nutes|lliseconds)|onth)|Se(?:conds|lection)|Hours|Year|Time(?:zoneOffset)?|Da(?:y|te)|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Da(?:y|te)|FullYear)|FullYear|A(?:ttention|llResponseHeaders)))|m(?:in|ove(?:B(?:y|elow)|To(?:Absolute)?|Above)|ergeAttributes|a(?:tch|rgins|x))|b(?:toa|ig|o(?:ld|rderWidths)|link|ack))\b(?=\()/},{token:"support.function.dom",regex:/(s(?:ub(?:stringData|mit)|plitText|e(?:t(?:NamedItem|Attribute(?:Node)?)|lect))|has(?:ChildNodes|Feature)|namedItem|c(?:l(?:ick|o(?:se|neNode))|reate(?:C(?:omment|DATASection|aption)|T(?:Head|extNode|Foot)|DocumentFragment|ProcessingInstruction|E(?:ntityReference|lement)|Attribute))|tabIndex|i(?:nsert(?:Row|Before|Cell|Data)|tem)|open|delete(?:Row|C(?:ell|aption)|T(?:Head|Foot)|Data)|focus|write(?:ln)?|a(?:dd|ppend(?:Child|Data))|re(?:set|place(?:Child|Data)|move(?:NamedItem|Child|Attribute(?:Node)?)?)|get(?:NamedItem|Element(?:sBy(?:Name|TagName|ClassName)|ById)|Attribute(?:Node)?)|blur)\b(?=\()/},{token:"support.constant",regex:/(s(?:ystemLanguage|cr(?:ipts|ollbars|een(?:X|Y|Top|Left))|t(?:yle(?:Sheets)?|atus(?:Text|bar)?)|ibling(?:Below|Above)|ource|uffixes|e(?:curity(?:Policy)?|l(?:ection|f)))|h(?:istory|ost(?:name)?|as(?:h|Focus))|y|X(?:MLDocument|SLDocument)|n(?:ext|ame(?:space(?:s|URI)|Prop))|M(?:IN_VALUE|AX_VALUE)|c(?:haracterSet|o(?:n(?:structor|trollers)|okieEnabled|lorDepth|mp(?:onents|lete))|urrent|puClass|l(?:i(?:p(?:boardData)?|entInformation)|osed|asses)|alle(?:e|r)|rypto)|t(?:o(?:olbar|p)|ext(?:Transform|Indent|Decoration|Align)|ags)|SQRT(?:1_2|2)|i(?:n(?:ner(?:Height|Width)|put)|ds|gnoreCase)|zIndex|o(?:scpu|n(?:readystatechange|Line)|uter(?:Height|Width)|p(?:sProfile|ener)|ffscreenBuffering)|NEGATIVE_INFINITY|d(?:i(?:splay|alog(?:Height|Top|Width|Left|Arguments)|rectories)|e(?:scription|fault(?:Status|Ch(?:ecked|arset)|View)))|u(?:ser(?:Profile|Language|Agent)|n(?:iqueID|defined)|pdateInterval)|_content|p(?:ixelDepth|ort|ersonalbar|kcs11|l(?:ugins|atform)|a(?:thname|dding(?:Right|Bottom|Top|Left)|rent(?:Window|Layer)?|ge(?:X(?:Offset)?|Y(?:Offset)?))|r(?:o(?:to(?:col|type)|duct(?:Sub)?|mpter)|e(?:vious|fix)))|e(?:n(?:coding|abledPlugin)|x(?:ternal|pando)|mbeds)|v(?:isibility|endor(?:Sub)?|Linkcolor)|URLUnencoded|P(?:I|OSITIVE_INFINITY)|f(?:ilename|o(?:nt(?:Size|Family|Weight)|rmName)|rame(?:s|Element)|gColor)|E|whiteSpace|l(?:i(?:stStyleType|n(?:eHeight|kColor))|o(?:ca(?:tion(?:bar)?|lName)|wsrc)|e(?:ngth|ft(?:Context)?)|a(?:st(?:M(?:odified|atch)|Index|Paren)|yer(?:s|X)|nguage))|a(?:pp(?:MinorVersion|Name|Co(?:deName|re)|Version)|vail(?:Height|Top|Width|Left)|ll|r(?:ity|guments)|Linkcolor|bove)|r(?:ight(?:Context)?|e(?:sponse(?:XML|Text)|adyState))|global|x|m(?:imeTypes|ultiline|enubar|argin(?:Right|Bottom|Top|Left))|L(?:N(?:10|2)|OG(?:10E|2E))|b(?:o(?:ttom|rder(?:Width|RightWidth|BottomWidth|Style|Color|TopWidth|LeftWidth))|ufferDepth|elow|ackground(?:Color|Image)))\b/},{token:"identifier",regex:i},{regex:"",token:"empty",next:"no_regex"}],start:[o.getStartRule("doc-start"),u("start"),{token:"string.regexp",regex:"\\/",next:"regex"},{token:"text",regex:"\\s+|^$",next:"start"},{token:"empty",regex:"",next:"no_regex"}],regex:[{token:"regexp.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"string.regexp",regex:"/[sxngimy]*",next:"no_regex"},{token:"invalid",regex:/\{\d+\b,?\d*\}[+*]|[+*$^?][+*]|[$^][?]|\?{3,}/},{token:"constant.language.escape",regex:/\(\?[:=!]|\)|\{\d+\b,?\d*\}|[+*]\?|[()$^+*?.]/},{token:"constant.language.delimiter",regex:/\|/},{token:"constant.language.escape",regex:/\[\^?/,next:"regex_character_class"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp"}],regex_character_class:[{token:"regexp.charclass.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"constant.language.escape",regex:"]",next:"regex"},{token:"constant.language.escape",regex:"-"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp.charachterclass"}],default_parameter:[{token:"string",regex:"'(?=.)",push:[{token:"string",regex:"'|$",next:"pop"},{include:"qstring"}]},{token:"string",regex:'"(?=.)',push:[{token:"string",regex:'"|$',next:"pop"},{include:"qqstring"}]},{token:"constant.language",regex:"null|Infinity|NaN|undefined"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:"punctuation.operator",regex:",",next:"function_arguments"},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],function_arguments:[u("function_arguments"),{token:"variable.parameter",regex:i},{token:"punctuation.operator",regex:","},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],qqstring:[{token:"constant.language.escape",regex:a},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:'"|$',next:"no_regex"},{defaultToken:"string"}],qstring:[{token:"constant.language.escape",regex:a},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:"'|$",next:"no_regex"},{defaultToken:"string"}]},e&&e.noES6||(this.$rules.no_regex.unshift({regex:"[{}]",onMatch:function(e,t,n){if(this.next="{"==e?this.nextState:"","{"==e&&n.length)n.unshift("start",t);else if("}"==e&&n.length&&(n.shift(),this.next=n.shift(),-1!=this.next.indexOf("string")||-1!=this.next.indexOf("jsx")))return"paren.quasi.end";return"{"==e?"paren.lparen":"paren.rparen"},nextState:"start"},{token:"string.quasi.start",regex:/`/,push:[{token:"constant.language.escape",regex:a},{token:"paren.quasi.start",regex:/\${/,push:"start"},{token:"string.quasi.end",regex:/`/,next:"pop"},{defaultToken:"string.quasi"}]},{token:["variable.parameter","text"],regex:"("+i+")(\\s*)(?=\\=>)"},{token:"paren.lparen",regex:"(\\()(?=[^\\(]+\\s*=>)",next:"function_arguments"},{token:"variable.language",regex:"(?:(?:(?:Weak)?(?:Set|Map))|Promise)\\b"}),this.$rules.function_arguments.unshift({token:"keyword.operator",regex:"=",next:"default_parameter"},{token:"keyword.operator",regex:"\\.{3}"}),this.$rules.property.unshift({token:"support.function",regex:"(findIndex|repeat|startsWith|endsWith|includes|isSafeInteger|trunc|cbrt|log2|log10|sign|then|catch|finally|resolve|reject|race|any|all|allSettled|keys|entries|isInteger)\\b(?=\\()"},{token:"constant.language",regex:"(?:MAX_SAFE_INTEGER|MIN_SAFE_INTEGER|EPSILON)\\b"}),e&&0==e.jsx||s.call(this)),this.embedRules(o,"doc-",[o.getEndRule("no_regex")]),this.normalizeRules()};function s(){var e=i.replace("\\d","\\d\\-"),t={onMatch:function(e,t,n){var a="/"==e.charAt(1)?2:1;return 1==a?(t!=this.nextState?n.unshift(this.next,this.nextState,0):n.unshift(this.next),n[2]++):2==a&&t==this.nextState&&(n[1]--,(!n[1]||n[1]<0)&&(n.shift(),n.shift())),[{type:"meta.tag.punctuation."+(1==a?"":"end-")+"tag-open.xml",value:e.slice(0,a)},{type:"meta.tag.tag-name.xml",value:e.substr(a)}]},regex:"</?(?:"+e+"|(?=>))",next:"jsxAttributes",nextState:"jsx"};this.$rules.start.unshift(t);var n={regex:"{",token:"paren.quasi.start",push:"start"};this.$rules.jsx=[n,t,{include:"reference"},{defaultToken:"string.xml"}],this.$rules.jsxAttributes=[{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",onMatch:function(e,t,n){return t==n[0]&&n.shift(),2==e.length&&(n[0]==this.nextState&&n[1]--,(!n[1]||n[1]<0)&&n.splice(0,2)),this.next=n[0]||"start",[{type:this.token,value:e}]},nextState:"jsx"},n,u("jsxAttributes"),{token:"entity.other.attribute-name.xml",regex:e},{token:"keyword.operator.attribute-equals.xml",regex:"="},{token:"text.tag-whitespace.xml",regex:"\\s+"},{token:"string.attribute-value.xml",regex:"'",stateName:"jsx_attr_q",push:[{token:"string.attribute-value.xml",regex:"'",next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},{token:"string.attribute-value.xml",regex:'"',stateName:"jsx_attr_qq",push:[{token:"string.attribute-value.xml",regex:'"',next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},t],this.$rules.reference=[{token:"constant.language.escape.reference.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}]}function u(e){return[{token:"comment",regex:/\/\*/,next:[o.getTagRule(),{token:"comment",regex:"\\*\\/",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]},{token:"comment",regex:"\\/\\/",next:[o.getTagRule(),{token:"comment",regex:"$|^",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]}]}a.inherits(l,r),t.JavaScriptHighlightRules=l})),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],(function(e,t,n){var a=e("../range").Range,o=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var o=n[1].length,r=e.findMatchingBracket({row:t,column:o});if(!r||r.row==t)return 0;var i=this.$getIndent(e.getLine(r.row));e.replace(new a(t,0,t,o-1),i)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(o.prototype),t.MatchingBraceOutdent=o})),ace.define("ace/mode/behaviour/xml",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/token_iterator"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../behaviour").Behaviour,r=e("../../token_iterator").TokenIterator;function i(e,t){return e&&e.type.lastIndexOf(t+".xml")>-1}var l=function(){this.add("string_dquotes","insertion",(function(e,t,n,a,o){if('"'==o||"'"==o){var l=o,s=a.doc.getTextRange(n.getSelectionRange());if(""!==s&&"'"!==s&&'"'!=s&&n.getWrapBehavioursEnabled())return{text:l+s+l,selection:!1};var u=n.getCursorPosition(),c=a.doc.getLine(u.row).substring(u.column,u.column+1),d=new r(a,u.row,u.column),g=d.getCurrentToken();if(c==l&&(i(g,"attribute-value")||i(g,"string")))return{text:"",selection:[1,1]};if(g||(g=d.stepBackward()),!g)return;for(;i(g,"tag-whitespace")||i(g,"whitespace");)g=d.stepBackward();var p=!c||c.match(/\s/);if(i(g,"attribute-equals")&&(p||">"==c)||i(g,"decl-attribute-equals")&&(p||"?"==c))return{text:l+l,selection:[1,1]}}})),this.add("string_dquotes","deletion",(function(e,t,n,a,o){var r=a.doc.getTextRange(o);if(!o.isMultiLine()&&('"'==r||"'"==r)&&a.doc.getLine(o.start.row).substring(o.start.column+1,o.start.column+2)==r)return o.end.column++,o})),this.add("autoclosing","insertion",(function(e,t,n,a,o){if(">"==o){var l=n.getSelectionRange().start,s=new r(a,l.row,l.column),u=s.getCurrentToken()||s.stepBackward();if(!u||!(i(u,"tag-name")||i(u,"tag-whitespace")||i(u,"attribute-name")||i(u,"attribute-equals")||i(u,"attribute-value")))return;if(i(u,"reference.attribute-value"))return;if(i(u,"attribute-value")){var c=s.getCurrentTokenColumn()+u.value.length;if(l.column<c)return;if(l.column==c){var d=s.stepForward();if(d&&i(d,"attribute-value"))return;s.stepBackward()}}if(/^\s*>/.test(a.getLine(l.row).slice(l.column)))return;for(;!i(u,"tag-name");)if("<"==(u=s.stepBackward()).value){u=s.stepForward();break}var g=s.getCurrentTokenRow(),p=s.getCurrentTokenColumn();if(i(s.stepBackward(),"end-tag-open"))return;var m=u.value;if(g==l.row&&(m=m.substring(0,l.column-p)),this.voidElements&&this.voidElements.hasOwnProperty(m.toLowerCase()))return;return{text:"></"+m+">",selection:[1,1]}}})),this.add("autoindent","insertion",(function(e,t,n,a,o){if("\n"==o){var l=n.getCursorPosition(),s=a.getLine(l.row),u=new r(a,l.row,l.column),c=u.getCurrentToken();if(i(c,"")&&-1!==c.type.indexOf("tag-close")){if("/>"==c.value)return;for(;c&&-1===c.type.indexOf("tag-name");)c=u.stepBackward();if(!c)return;var d=c.value,g=u.getCurrentTokenRow();if(!(c=u.stepBackward())||-1!==c.type.indexOf("end-tag"))return;if(this.voidElements&&!this.voidElements[d]||!this.voidElements){var p=a.getTokenAt(l.row,l.column+1),m=(s=a.getLine(g),this.$getIndent(s)),h=m+a.getTabString();return p&&"</"===p.value?{text:"\n"+h+"\n"+m,selection:[1,h.length,1,h.length]}:{text:"\n"+h}}}}}))};a.inherits(l,o),t.XmlBehaviour=l})),ace.define("ace/mode/behaviour/javascript",["require","exports","module","ace/lib/oop","ace/token_iterator","ace/mode/behaviour/cstyle","ace/mode/behaviour/xml"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../token_iterator").TokenIterator,r=e("../behaviour/cstyle").CstyleBehaviour,i=e("../behaviour/xml").XmlBehaviour,l=function(){var e=new i({closeCurlyBraces:!0}).getBehaviours();this.addBehaviours(e),this.inherit(r),this.add("autoclosing-fragment","insertion",(function(e,t,n,a,r){if(">"==r){var i=n.getSelectionRange().start,l=new o(a,i.row,i.column),s=l.getCurrentToken()||l.stepBackward();if(!s)return;if("<"==s.value)return{text:"></>",selection:[1,1]}}}))};a.inherits(l,r),t.JavaScriptBehaviour=l})),ace.define("ace/mode/folding/xml",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,i=t.FoldMode=function(e,t){r.call(this),this.voidElements=e||{},this.optionalEndTags=a.mixin({},this.voidElements),t&&a.mixin(this.optionalEndTags,t)};a.inherits(i,r);var l=function(){this.tagName="",this.closing=!1,this.selfClosing=!1,this.start={row:0,column:0},this.end={row:0,column:0}};function s(e,t){return e&&e.type&&e.type.lastIndexOf(t+".xml")>-1}(function(){this.getFoldWidget=function(e,t,n){var a=this._getFirstTagInLine(e,n);return a?a.closing||!a.tagName&&a.selfClosing?"markbeginend"===t?"end":"":!a.tagName||a.selfClosing||this.voidElements.hasOwnProperty(a.tagName.toLowerCase())||this._findEndTagInLine(e,n,a.tagName,a.end.column)?"":"start":this.getCommentFoldWidget(e,n)},this.getCommentFoldWidget=function(e,t){return/comment/.test(e.getState(t))&&/<!-/.test(e.getLine(t))?"start":""},this._getFirstTagInLine=function(e,t){for(var n=e.getTokens(t),a=new l,o=0;o<n.length;o++){var r=n[o];if(s(r,"tag-open")){if(a.end.column=a.start.column+r.value.length,a.closing=s(r,"end-tag-open"),!(r=n[++o]))return null;if(a.tagName=r.value,""===r.value){if(!(r=n[++o]))return null;a.tagName=r.value}for(a.end.column+=r.value.length,o++;o<n.length;o++)if(r=n[o],a.end.column+=r.value.length,s(r,"tag-close")){a.selfClosing="/>"==r.value;break}return a}if(s(r,"tag-close"))return a.selfClosing="/>"==r.value,a;a.start.column+=r.value.length}return null},this._findEndTagInLine=function(e,t,n,a){for(var o=e.getTokens(t),r=0,i=0;i<o.length;i++){var l=o[i];if(!((r+=l.value.length)<a-1)&&s(l,"end-tag-open")&&(s(l=o[i+1],"tag-name")&&""===l.value&&(l=o[i+2]),l&&l.value==n))return!0}return!1},this.getFoldWidgetRange=function(e,t,n){if(!this._getFirstTagInLine(e,n))return this.getCommentFoldWidget(e,n)&&e.getCommentFoldRange(n,e.getLine(n).length);var a=e.getMatchingTags({row:n,column:0});return a?new o(a.openTag.end.row,a.openTag.end.column,a.closeTag.start.row,a.closeTag.start.column):void 0}}).call(i.prototype)})),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};a.inherits(i,r),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var a=e.getLine(n);if(this.singleLineBlockCommentRe.test(a)&&!this.startRegionRe.test(a)&&!this.tripleStarBlockCommentRe.test(a))return"";var o=this._getFoldWidgetBase(e,t,n);return!o&&this.startRegionRe.test(a)?"start":o},this.getFoldWidgetRange=function(e,t,n,a){var o,r=e.getLine(n);if(this.startRegionRe.test(r))return this.getCommentRegionBlock(e,r,n);if(o=r.match(this.foldingStartMarker)){var i=o.index;if(o[1])return this.openingBracketBlock(e,o[1],n,i);var l=e.getCommentFoldRange(n,i+o[0].length,1);return l&&!l.isMultiLine()&&(a?l=this.getSectionRange(e,n):"all"!=t&&(l=null)),l}return"markbegin"!==t&&(o=r.match(this.foldingStopMarker))?(i=o.index+o[0].length,o[1]?this.closingBracketBlock(e,o[1],n,i):e.getCommentFoldRange(n,i,-1)):void 0},this.getSectionRange=function(e,t){for(var n=e.getLine(t),a=n.search(/\S/),r=t,i=n.length,l=t+=1,s=e.getLength();++t<s;){var u=(n=e.getLine(t)).search(/\S/);if(-1!==u){if(a>u)break;var c=this.getFoldWidgetRange(e,"all",t);if(c){if(c.start.row<=r)break;if(c.isMultiLine())t=c.end.row;else if(a==u)break}l=t}}return new o(r,i,l,e.getLine(l).length)},this.getCommentRegionBlock=function(e,t,n){for(var a=t.search(/\s*$/),r=e.getLength(),i=n,l=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,s=1;++n<r;){t=e.getLine(n);var u=l.exec(t);if(u&&(u[1]?s--:s++,!s))break}if(n>i)return new o(i,a,n,t.length)}}.call(i.prototype)})),ace.define("ace/mode/folding/javascript",["require","exports","module","ace/lib/oop","ace/mode/folding/xml","ace/mode/folding/cstyle"],(function(e,t,n){var a=e("../../lib/oop"),o=e("./xml").FoldMode,r=e("./cstyle").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end))),this.xmlFoldMode=new o};a.inherits(i,r),function(){this.getFoldWidgetRangeBase=this.getFoldWidgetRange,this.getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var a=this.getFoldWidgetBase(e,t,n);return a||this.xmlFoldMode.getFoldWidget(e,t,n)},this.getFoldWidgetRange=function(e,t,n,a){var o=this.getFoldWidgetRangeBase(e,t,n,a);return o||this.xmlFoldMode.getFoldWidgetRange(e,t,n)}}.call(i.prototype)})),ace.define("ace/mode/javascript",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/javascript_highlight_rules","ace/mode/matching_brace_outdent","ace/worker/worker_client","ace/mode/behaviour/javascript","ace/mode/folding/javascript"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text").Mode,r=e("./javascript_highlight_rules").JavaScriptHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,l=e("../worker/worker_client").WorkerClient,s=e("./behaviour/javascript").JavaScriptBehaviour,u=e("./folding/javascript").FoldMode,c=function(){this.HighlightRules=r,this.$outdent=new i,this.$behaviour=new s,this.foldingRules=new u};a.inherits(c,o),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.$quotes={'"':'"',"'":"'","`":"`"},this.$pairQuotesAfter={"`":/\w/},this.getNextLineIndent=function(e,t,n){var a=this.$getIndent(t),o=this.getTokenizer().getLineTokens(t,e),r=o.tokens,i=o.state;if(r.length&&"comment"==r[r.length-1].type)return a;if("start"==e||"no_regex"==e)t.match(/^.*(?:\bcase\b.*:|[\{\(\[])\s*$/)&&(a+=n);else if("doc-start"==e&&("start"==i||"no_regex"==i))return"";return a},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.createWorker=function(e){var t=new l(["ace"],"ace/mode/javascript_worker","JavaScriptWorker");return t.attachToDocument(e.getDocument()),t.on("annotate",(function(t){e.setAnnotations(t.data)})),t.on("terminate",(function(){e.clearAnnotations()})),t},this.$id="ace/mode/javascript",this.snippetFileId="ace/snippets/javascript"}.call(c.prototype),t.Mode=c})),ace.require(["ace/mode/javascript"],(function(e){S&&(S.exports=e)})));var I,B={exports:{}};I||(I=1,function(e){ace.define("ace/mode/doc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(){this.$rules={start:[{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},r.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}]}};a.inherits(r,o),r.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},r.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},r.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.DocCommentHighlightRules=r})),ace.define("ace/mode/golang_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/doc_comment_highlight_rules","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./doc_comment_highlight_rules").DocCommentHighlightRules,r=e("./text_highlight_rules").TextHighlightRules,i=function(){var e=this.createKeywordMapper({keyword:"else|break|case|return|goto|if|const|select|continue|struct|default|switch|for|range|func|import|package|chan|defer|fallthrough|go|interface|map|range|select|type|var","constant.language":"nil|true|false|iota","support.function":"new|close|cap|copy|panic|panicln|print|println|len|make|delete|real|recover|imag|append","support.type":"string|uint8|uint16|uint32|uint64|int8|int16|int32|int64|float32|float64|complex64|complex128|byte|rune|uint|int|uintptr|bool|error"},""),t="\\\\(?:[0-7]{3}|x\\h{2}|u{4}|U\\h{6}|[abfnrtv'\"\\\\])".replace(/\\h/g,"[a-fA-F\\d]");this.$rules={start:[{token:"comment",regex:"\\/\\/.*$"},o.getStartRule("doc-start"),{token:"comment.start",regex:"\\/\\*",next:"comment"},{token:"string",regex:/"(?:[^"\\]|\\.)*?"/},{token:"string",regex:"`",next:"bqstring"},{token:"constant.numeric",regex:"'(?:[^\\'\ud800-\udbff]|[\ud800-\udbff][\udc00-\udfff]|"+t.replace('"',"")+")'"},{token:"constant.numeric",regex:"0[xX][0-9a-fA-F]+\\b"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?\\b"},{token:["keyword","text","entity.name.function"],regex:"(func)(\\s+)([a-zA-Z_$][a-zA-Z0-9_$]*)\\b"},{token:function(t){return"("==t[t.length-1]?[{type:e(t.slice(0,-1))||"support.function",value:t.slice(0,-1)},{type:"paren.lparen",value:t.slice(-1)}]:e(t)||"identifier"},regex:"[a-zA-Z_$][a-zA-Z0-9_$]*\\b\\(?"},{token:"keyword.operator",regex:"!|\\$|%|&|\\*|\\-\\-|\\-|\\+\\+|\\+|~|==|=|!=|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\|\\||\\?\\:|\\*=|%=|\\+=|\\-=|&=|\\^="},{token:"punctuation.operator",regex:"\\?|\\:|\\,|\\;|\\."},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:"\\s+"}],comment:[{token:"comment.end",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}],bqstring:[{token:"string",regex:"`",next:"start"},{defaultToken:"string"}]},this.embedRules(o,"doc-",[o.getEndRule("start")])};a.inherits(i,r),t.GolangHighlightRules=i})),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],(function(e,t,n){var a=e("../range").Range,o=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var o=n[1].length,r=e.findMatchingBracket({row:t,column:o});if(!r||r.row==t)return 0;var i=this.$getIndent(e.getLine(r.row));e.replace(new a(t,0,t,o-1),i)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(o.prototype),t.MatchingBraceOutdent=o})),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};a.inherits(i,r),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var a=e.getLine(n);if(this.singleLineBlockCommentRe.test(a)&&!this.startRegionRe.test(a)&&!this.tripleStarBlockCommentRe.test(a))return"";var o=this._getFoldWidgetBase(e,t,n);return!o&&this.startRegionRe.test(a)?"start":o},this.getFoldWidgetRange=function(e,t,n,a){var o,r=e.getLine(n);if(this.startRegionRe.test(r))return this.getCommentRegionBlock(e,r,n);if(o=r.match(this.foldingStartMarker)){var i=o.index;if(o[1])return this.openingBracketBlock(e,o[1],n,i);var l=e.getCommentFoldRange(n,i+o[0].length,1);return l&&!l.isMultiLine()&&(a?l=this.getSectionRange(e,n):"all"!=t&&(l=null)),l}return"markbegin"!==t&&(o=r.match(this.foldingStopMarker))?(i=o.index+o[0].length,o[1]?this.closingBracketBlock(e,o[1],n,i):e.getCommentFoldRange(n,i,-1)):void 0},this.getSectionRange=function(e,t){for(var n=e.getLine(t),a=n.search(/\S/),r=t,i=n.length,l=t+=1,s=e.getLength();++t<s;){var u=(n=e.getLine(t)).search(/\S/);if(-1!==u){if(a>u)break;var c=this.getFoldWidgetRange(e,"all",t);if(c){if(c.start.row<=r)break;if(c.isMultiLine())t=c.end.row;else if(a==u)break}l=t}}return new o(r,i,l,e.getLine(l).length)},this.getCommentRegionBlock=function(e,t,n){for(var a=t.search(/\s*$/),r=e.getLength(),i=n,l=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,s=1;++n<r;){t=e.getLine(n);var u=l.exec(t);if(u&&(u[1]?s--:s++,!s))break}if(n>i)return new o(i,a,n,t.length)}}.call(i.prototype)})),ace.define("ace/mode/golang",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/golang_highlight_rules","ace/mode/matching_brace_outdent","ace/mode/folding/cstyle"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text").Mode,r=e("./golang_highlight_rules").GolangHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,l=e("./folding/cstyle").FoldMode,s=function(){this.HighlightRules=r,this.$outdent=new i,this.foldingRules=new l,this.$behaviour=this.$defaultBehaviour};a.inherits(s,o),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.getNextLineIndent=function(e,t,n){var a=this.$getIndent(t),o=this.getTokenizer().getLineTokens(t,e),r=o.tokens;return o.state,r.length&&"comment"==r[r.length-1].type||"start"==e&&t.match(/^.*[\{\(\[]\s*$/)&&(a+=n),a},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.$id="ace/mode/golang"}.call(s.prototype),t.Mode=s})),ace.require(["ace/mode/golang"],(function(t){e&&(e.exports=t)}))}(B));const N={class:"gva-table-box"},E={class:"gva-btn-list"},M={class:"gva-pagination"},$={class:"dialog-footer"},L={class:"flex justify-between items-center"},V={class:""},D={class:"relative w-full"},O={class:"flex w-full gap-2"},q=Object.assign({name:"AutoCodeAdmin"},{__name:"index",setup(T){const S=r(!1),A=r({id:void 0,deleteApi:!0,deleteMenu:!0,deleteTable:!1}),I=i(),B=r(!1),q=r(""),j=r(1),W=r(0),U=r(10),H=r([]),P=r(""),z=r({package:"",funcName:"",structName:"",packageName:"",description:"",abbreviation:"",humpPackageName:"",businessDB:"",method:"",funcDesc:"",isAuth:!1,isAi:!1,apiFunc:"",serverFunc:"",jsFunc:""}),X=r(!1),J=()=>{X.value=!1},Y=async()=>{if(z.value.funcName=R(z.value.funcName),!z.value.funcName)return void C.error("请输入方法名");if(!z.value.method)return void C.error("请选择方法");if(!z.value.router)return void C.error("请输入路由");if(!z.value.funcDesc)return void C.error("请输入方法介绍");if(z.value.isAi&&(!z.value.apiFunc||!z.value.serverFunc||!z.value.jsFunc))return void C.error("请先使用AI帮写完成基础代码，如果生成失败请重新调用");0===(await a(z.value)).code&&(C.success("增加方法成功"),J())},G=e=>{U.value=e,K()},Z=e=>{j.value=e,K()},K=async()=>{const t=await e({page:j.value,pageSize:U.value});0===t.code&&(H.value=t.data.list,W.value=t.data.total,j.value=t.data.page,U.value=t.data.pageSize)};K();const Q=()=>{B.value=!1,A.value={id:void 0,deleteApi:!0,deleteMenu:!0,deleteTable:!1}},ee=e=>{e&&w.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 是否继续?","提示",{closeOnClickModal:!1,distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{w.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 请继续确认！！！","会删除表",{closeOnClickModal:!1,distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).catch((()=>{A.value.deleteTable=!1}))})).catch((()=>{A.value.deleteTable=!1}))},te=async()=>{0===(await n(A.value)).code&&(C.success("回滚成功"),K())},ne=(e,t)=>{e?I.push({name:"autoCodeEdit",params:{id:e.ID},query:{isAdd:t}}):I.push({name:"autoCode"})},ae=async()=>{if(S.value=!0,z.value.apiFunc="",z.value.serverFunc="",z.value.jsFunc="",!z.value.prompt)return void C.error("请输入提示信息");const e=await a({...z.value,isPreview:!0});if(0!==e.code)return S.value=!1,void C.error(e.msg);const t=await o({structInfo:P.value,template:JSON.stringify(e.data),prompt:z.value.prompt,command:"addFunc"});if(S.value=!1,0===t.code)try{const e=JSON.parse(t.data);z.value.apiFunc=e.api,z.value.serverFunc=e.server,z.value.jsFunc=e.js,z.value.method=e.method,z.value.funcName=e.funcName;const n=e.router.split("/");z.value.router=n[n.length-1],z.value.funcDesc=z.value.prompt}catch(n){C.error("小淼忙碌，请重新调用")}},oe=async()=>{S.value=!0;const e=await o({prompt:z.value.funcDesc,command:"autoCompleteFunc"});if(S.value=!1,0===e.code)try{const t=JSON.parse(e.data);z.value.method=t.method,z.value.funcName=t.funcName,z.value.router=t.router,z.value.prompt=z.value.funcDesc}catch(t){C.error("小淼开小差了，请重新调用")}};return(e,n)=>{const a=l("el-button"),o=l("el-table-column"),r=l("el-tag"),i=l("el-table"),T=l("el-pagination"),I=l("el-checkbox"),re=l("el-form-item"),ie=l("el-form"),le=l("el-popconfirm"),se=l("el-dialog"),ue=l("el-input"),ce=l("el-col"),de=l("el-row"),ge=l("el-switch"),pe=l("ai-gva"),me=l("el-option"),he=l("el-select"),fe=l("el-drawer"),xe=s("loading");return c(),u("div",null,[d("div",N,[d("div",E,[g(a,{type:"primary",icon:"plus",onClick:n[0]||(n[0]=e=>ne(null))},{default:p((()=>n[25]||(n[25]=[m(" 新增 ")]))),_:1})]),g(i,{data:H.value},{default:p((()=>[g(o,{type:"selection",width:"55"}),g(o,{align:"left",label:"id",width:"60",prop:"ID"}),g(o,{align:"left",label:"日期",width:"180"},{default:p((e=>[m(h(f(x)(e.row.CreatedAt)),1)])),_:1}),g(o,{align:"left",label:"结构体名","min-width":"150",prop:"structName"}),g(o,{align:"left",label:"结构体描述","min-width":"150",prop:"description"}),g(o,{align:"left",label:"表名称","min-width":"150",prop:"tableName"}),g(o,{align:"left",label:"回滚标记","min-width":"150",prop:"flag"},{default:p((e=>[e.row.flag?(c(),v(r,{key:0,type:"danger",effect:"dark"},{default:p((()=>n[26]||(n[26]=[m(" 已回滚 ")]))),_:1})):(c(),v(r,{key:1,type:"success",effect:"dark"},{default:p((()=>n[27]||(n[27]=[m(" 未回滚 ")]))),_:1}))])),_:1}),g(o,{align:"left",label:"操作","min-width":"240"},{default:p((e=>[d("div",null,[g(a,{type:"primary",link:"",disabled:1===e.row.flag,onClick:t=>(e=>{const t=JSON.parse(e.request);P.value=e.request,z.value.package=t.package,z.value.structName=t.structName,z.value.packageName=t.packageName,z.value.description=t.description,z.value.abbreviation=t.abbreviation,z.value.humpPackageName=t.humpPackageName,z.value.businessDB=t.businessDB,z.value.method="",z.value.funcName="",z.value.router="",z.value.funcDesc="",z.value.isAuth=!1,z.value.isAi=!1,z.value.apiFunc="",z.value.serverFunc="",z.value.jsFunc="",X.value=!0})(e.row)},{default:p((()=>n[28]||(n[28]=[m(" 增加方法 ")]))),_:2},1032,["disabled","onClick"]),g(a,{type:"primary",link:"",onClick:t=>ne(e.row,1)},{default:p((()=>n[29]||(n[29]=[m(" 增加字段 ")]))),_:2},1032,["onClick"]),g(a,{type:"primary",link:"",disabled:1===e.row.flag,onClick:t=>{return n=e.row,q.value="回滚："+n.structName,A.value.id=n.ID,void(B.value=!0);var n}},{default:p((()=>n[30]||(n[30]=[m(" 回滚 ")]))),_:2},1032,["disabled","onClick"]),g(a,{type:"primary",link:"",onClick:t=>ne(e.row)},{default:p((()=>n[31]||(n[31]=[m(" 复用 ")]))),_:2},1032,["onClick"]),g(a,{type:"primary",link:"",onClick:n=>(async e=>{w.confirm("此操作将删除本历史, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await t({id:Number(e.ID)})).code&&(C.success("删除成功"),K())}))})(e.row)},{default:p((()=>n[32]||(n[32]=[m(" 删除 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"]),d("div",M,[g(T,{"current-page":j.value,"page-size":U.value,"page-sizes":[10,30,50,100],total:W.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:Z,onSizeChange:G},null,8,["current-page","page-size","total"])])]),g(se,{modelValue:B.value,"onUpdate:modelValue":n[4]||(n[4]=e=>B.value=e),title:q.value,"before-close":Q,width:"600px"},{footer:p((()=>[d("div",$,[g(a,{onClick:Q},{default:p((()=>n[33]||(n[33]=[m(" 取 消 ")]))),_:1}),g(le,{title:"此操作将回滚生成文件和勾选项目, 是否继续?",onConfirm:te},{reference:p((()=>[g(a,{type:"primary"},{default:p((()=>n[34]||(n[34]=[m(" 确 定 ")]))),_:1})])),_:1})])])),default:p((()=>[g(ie,{inline:!0,model:A.value,"label-width":"80px"},{default:p((()=>[g(re,{label:"选项："},{default:p((()=>[g(I,{modelValue:A.value.deleteApi,"onUpdate:modelValue":n[1]||(n[1]=e=>A.value.deleteApi=e),label:"删除接口"},null,8,["modelValue"]),g(I,{modelValue:A.value.deleteMenu,"onUpdate:modelValue":n[2]||(n[2]=e=>A.value.deleteMenu=e),label:"删除菜单"},null,8,["modelValue"]),g(I,{modelValue:A.value.deleteTable,"onUpdate:modelValue":n[3]||(n[3]=e=>A.value.deleteTable=e),label:"删除表",onChange:ee},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"]),g(fe,{modelValue:X.value,"onUpdate:modelValue":n[24]||(n[24]=e=>X.value=e),size:"60%","show-close":!1,"close-on-click-modal":!1},{header:p((()=>[d("div",L,[n[37]||(n[37]=d("span",{class:"text-lg"},"操作栏",-1)),d("div",null,[g(a,{type:"primary",onClick:Y,loading:S.value},{default:p((()=>n[35]||(n[35]=[m(" 生成 ")]))),_:1},8,["loading"]),g(a,{type:"primary",onClick:J,loading:S.value},{default:p((()=>n[36]||(n[36]=[m(" 取消 ")]))),_:1},8,["loading"])])])])),default:p((()=>[d("div",V,[k((c(),v(ie,{"label-position":"top","element-loading-text":"小淼正在思考，请稍候...",model:z.value,"label-width":"80px"},{default:p((()=>[g(de,{gutter:12},{default:p((()=>[g(ce,{span:8},{default:p((()=>[g(re,{label:"包名："},{default:p((()=>[g(ue,{modelValue:z.value.package,"onUpdate:modelValue":n[5]||(n[5]=e=>z.value.package=e),placeholder:"请输入包名",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),g(ce,{span:8},{default:p((()=>[g(re,{label:"结构体名："},{default:p((()=>[g(ue,{modelValue:z.value.structName,"onUpdate:modelValue":n[6]||(n[6]=e=>z.value.structName=e),placeholder:"请输入结构体名",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),g(ce,{span:8},{default:p((()=>[g(re,{label:"前端文件名："},{default:p((()=>[g(ue,{modelValue:z.value.packageName,"onUpdate:modelValue":n[7]||(n[7]=e=>z.value.packageName=e),placeholder:"请输入文件名",disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),g(de,{gutter:12},{default:p((()=>[g(ce,{span:8},{default:p((()=>[g(re,{label:"后端文件名："},{default:p((()=>[g(ue,{modelValue:z.value.humpPackageName,"onUpdate:modelValue":n[8]||(n[8]=e=>z.value.humpPackageName=e),placeholder:"请输入文件名",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),g(ce,{span:8},{default:p((()=>[g(re,{label:"描述："},{default:p((()=>[g(ue,{modelValue:z.value.description,"onUpdate:modelValue":n[9]||(n[9]=e=>z.value.description=e),placeholder:"请输入描述",disabled:""},null,8,["modelValue"])])),_:1})])),_:1}),g(ce,{span:8},{default:p((()=>[g(re,{label:"缩写："},{default:p((()=>[g(ue,{modelValue:z.value.abbreviation,"onUpdate:modelValue":n[10]||(n[10]=e=>z.value.abbreviation=e),placeholder:"请输入缩写",disabled:""},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),g(re,{label:"是否AI填充："},{default:p((()=>[g(ge,{modelValue:z.value.isAi,"onUpdate:modelValue":n[11]||(n[11]=e=>z.value.isAi=e)},null,8,["modelValue"]),n[38]||(n[38]=d("span",{class:"text-sm text-red-600 p-2"},"当前ai帮写存在不稳定因素，生成代码后请注意手动调整部分内容",-1))])),_:1}),z.value.isAi?(c(),u(y,{key:0},[g(re,{label:"Ai帮写:"},{default:p((()=>[d("div",D,[g(ue,{type:"textarea",placeholder:"AI帮写功能，输入提示信息，自动生成代码",modelValue:z.value.prompt,"onUpdate:modelValue":n[12]||(n[12]=e=>z.value.prompt=e),rows:5,onInput:n[13]||(n[13]=e=>z.value.router=z.value.router.replace(/\//g,""))},null,8,["modelValue"]),g(a,{onClick:ae,type:"primary",class:"absolute right-2 bottom-2"},{default:p((()=>[g(pe),n[39]||(n[39]=m("帮写"))])),_:1})])])),_:1}),g(re,{label:"Api方法:"},{default:p((()=>[g(f(F),{value:z.value.apiFunc,"onUpdate:value":n[14]||(n[14]=e=>z.value.apiFunc=e),lang:"golang",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])])),_:1}),g(re,{label:"Server方法:"},{default:p((()=>[g(f(F),{value:z.value.serverFunc,"onUpdate:value":n[15]||(n[15]=e=>z.value.serverFunc=e),lang:"golang",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])])),_:1}),g(re,{label:"前端JSAPI方法:"},{default:p((()=>[g(f(F),{value:z.value.jsFunc,"onUpdate:value":n[16]||(n[16]=e=>z.value.jsFunc=e),lang:"javascript",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])])),_:1})],64)):b("",!0),g(re,{label:"方法介绍："},{default:p((()=>[d("div",O,[g(ue,{class:"flex-1",modelValue:z.value.funcDesc,"onUpdate:modelValue":n[17]||(n[17]=e=>z.value.funcDesc=e),placeholder:"请输入方法介绍"},null,8,["modelValue"]),g(a,{type:"primary",onClick:oe},{default:p((()=>[g(pe),n[40]||(n[40]=m("补全"))])),_:1})])])),_:1}),g(re,{label:"方法名："},{default:p((()=>[g(ue,{onBlur:n[18]||(n[18]=e=>z.value.funcName=f(R)(z.value.funcName)),modelValue:z.value.funcName,"onUpdate:modelValue":n[19]||(n[19]=e=>z.value.funcName=e),placeholder:"请输入方法名"},null,8,["modelValue"])])),_:1}),g(re,{label:"方法："},{default:p((()=>[g(he,{modelValue:z.value.method,"onUpdate:modelValue":n[20]||(n[20]=e=>z.value.method=e),placeholder:"请选择方法"},{default:p((()=>[(c(),u(y,null,_(["GET","POST","PUT","DELETE"],(e=>g(me,{key:e,label:e,value:e},null,8,["label","value"]))),64))])),_:1},8,["modelValue"])])),_:1}),g(re,{label:"是否鉴权："},{default:p((()=>[g(ge,{modelValue:z.value.isAuth,"onUpdate:modelValue":n[21]||(n[21]=e=>z.value.isAuth=e),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])])),_:1}),g(re,{label:"路由path:"},{default:p((()=>[g(ue,{modelValue:z.value.router,"onUpdate:modelValue":n[22]||(n[22]=e=>z.value.router=e),placeholder:"路由path",onInput:n[23]||(n[23]=e=>z.value.router=z.value.router.replace(/\//g,""))},null,8,["modelValue"]),d("div",null," API路径: ["+h(z.value.method)+"] /"+h(z.value.abbreviation)+"/"+h(z.value.router),1)])),_:1})])),_:1},8,["model"])),[[xe,S.value]])])])),_:1},8,["modelValue"])])}}});export{q as default};
