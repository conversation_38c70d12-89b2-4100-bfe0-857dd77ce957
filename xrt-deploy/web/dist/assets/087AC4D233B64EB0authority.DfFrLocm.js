/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{g as e,d as a,c as t,u as l,a as u}from"./087AC4D233B64EB0authority.YPU6vVUR.js";import r from"./087AC4D233B64EB0menus.CliLThoq.js";import o from"./087AC4D233B64EB0apis.Diywf0Ph.js";import i from"./087AC4D233B64EB0datas.D6cHOkJS.js";import{_ as d}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{a as s,I as n,g as y,c as h,o as c,f as m,b as v,i as p,d as f,w as I,h as b,t as w,v as g,ab as C,E as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0authorityBtn.COa_YS-K.js";import"./087AC4D233B64EB0api.DOS2t6hl.js";const _={class:"authority"},B={class:"gva-table-box"},N={class:"gva-btn-list"},V={class:"flex justify-between items-center"},A={class:"text-lg"},D=Object.assign({name:"Authority"},{__name:"authority",setup(D){const E=s([{authorityId:0,authorityName:"根角色/严格模式下为当前角色"}]),j=s(!1),x=s("add"),z=s({}),U=n(),q=s("新增角色"),R=s(!1),S=s(!1),F=s({}),T=s({authorityId:0,authorityName:"",parentId:0}),G=s({authorityId:[{required:!0,message:"请输入角色ID",trigger:"blur"},{validator:(e,a,t)=>/^[0-9]*[1-9][0-9]*$/.test(a)?t():t(new Error("请输入正整数")),trigger:"blur",message:"必须为正整数"}],authorityName:[{required:!0,message:"请输入角色名",trigger:"blur"}],parentId:[{required:!0,message:"请选择父角色",trigger:"blur"}]}),H=s([]),O=async()=>{const a=await e();0===a.code&&(H.value=a.data)};O();const P=(e,a)=>{z.value[e]=a},W=s(null),X=s(null),$=s(null),J=(e,a)=>{const t=[W,X,$];a&&t[a].value.needConfirm&&(t[a].value.enterAndNext(),t[a].value.needConfirm=!1)},K=s(null),L=()=>{K.value&&K.value.resetFields(),T.value={authorityId:0,authorityName:"",parentId:0}},M=()=>{L(),R.value=!1,S.value=!1},Q=()=>{K.value.validate((async e=>{if(e){switch(T.value.authorityId=Number(T.value.authorityId),x.value){case"add":0===(await u(T.value)).code&&(k({type:"success",message:"添加成功!"}),O(),M());break;case"edit":0===(await l(T.value)).code&&(k({type:"success",message:"添加成功!"}),O(),M());break;case"copy":{const e={authority:{authorityId:0,authorityName:"",datauthorityId:[],parentId:0},oldAuthorityId:0};e.authority.authorityId=T.value.authorityId,e.authority.authorityName=T.value.authorityName,e.authority.parentId=T.value.parentId,e.authority.dataAuthorityId=F.value.dataAuthorityId,e.oldAuthorityId=F.value.authorityId;0===(await t(e)).code&&(k({type:"success",message:"复制成功！"}),O())}}L(),R.value=!1}}))},Y=()=>{E.value=[{authorityId:0,authorityName:"根角色(严格模式下为当前用户角色)"}],Z(H.value,E.value,!1)},Z=(e,a,t)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const l={authorityId:e.authorityId,authorityName:e.authorityName,disabled:t||e.authorityId===T.value.authorityId,children:[]};Z(e.children,l.children,t||e.authorityId===T.value.authorityId),a.push(l)}else{const l={authorityId:e.authorityId,authorityName:e.authorityName,disabled:t||e.authorityId===T.value.authorityId};a.push(l)}}))},ee=e=>{L(),q.value="新增角色",x.value="add",T.value.parentId=e,Y(),R.value=!0};return(e,t)=>{const l=y("el-button"),u=y("el-table-column"),s=y("el-table"),n=y("el-cascader"),D=y("el-form-item"),S=y("el-input"),L=y("el-form"),Z=y("el-drawer"),ae=y("el-tab-pane"),te=y("el-tabs");return c(),h("div",_,[m(d,{title:"注：右上角头像下拉可切换角色"}),v("div",B,[v("div",N,[m(l,{type:"primary",icon:"plus",onClick:t[0]||(t[0]=e=>ee(0))},{default:I((()=>t[6]||(t[6]=[b("新增角色")]))),_:1})]),m(s,{data:H.value,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"authorityId",style:{width:"100%"}},{default:I((()=>[m(u,{label:"角色ID","min-width":"180",prop:"authorityId"}),m(u,{align:"left",label:"角色名称","min-width":"180",prop:"authorityName"}),m(u,{align:"left",label:"操作",width:"460"},{default:I((e=>[m(l,{icon:"setting",type:"primary",link:"",onClick:a=>{return t=e.row,j.value=!0,void(z.value=t);var t}},{default:I((()=>t[7]||(t[7]=[b("设置权限")]))),_:2},1032,["onClick"]),m(l,{icon:"plus",type:"primary",link:"",onClick:a=>ee(e.row.authorityId)},{default:I((()=>t[8]||(t[8]=[b("新增子角色")]))),_:2},1032,["onClick"]),m(l,{icon:"copy-document",type:"primary",link:"",onClick:a=>(e=>{Y(),q.value="拷贝角色",x.value="copy";for(const a in T.value)T.value[a]=e[a];F.value=e,R.value=!0})(e.row)},{default:I((()=>t[9]||(t[9]=[b("拷贝")]))),_:2},1032,["onClick"]),m(l,{icon:"edit",type:"primary",link:"",onClick:a=>(e=>{Y(),q.value="编辑角色",x.value="edit";for(const a in T.value)T.value[a]=e[a];Y(),K.value&&K.value.clearValidate(),R.value=!0})(e.row)},{default:I((()=>t[10]||(t[10]=[b("编辑")]))),_:2},1032,["onClick"]),m(l,{icon:"delete",type:"primary",link:"",onClick:t=>{return l=e.row,void C.confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await a({authorityId:l.authorityId})).code&&(k({type:"success",message:"删除成功!"}),O())})).catch((()=>{k({type:"info",message:"已取消删除"})}));var l}},{default:I((()=>t[11]||(t[11]=[b("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),m(Z,{modelValue:R.value,"onUpdate:modelValue":t[4]||(t[4]=e=>R.value=e),size:g(U).drawerSize,"show-close":!1},{header:I((()=>[v("div",V,[v("span",A,w(q.value),1),v("div",null,[m(l,{onClick:M},{default:I((()=>t[12]||(t[12]=[b("取 消")]))),_:1}),m(l,{type:"primary",onClick:Q},{default:I((()=>t[13]||(t[13]=[b("确 定")]))),_:1})])])])),default:I((()=>[m(L,{ref_key:"authorityForm",ref:K,model:T.value,rules:G.value,"label-width":"80px"},{default:I((()=>[m(D,{label:"父级角色",prop:"parentId"},{default:I((()=>[m(n,{modelValue:T.value.parentId,"onUpdate:modelValue":t[1]||(t[1]=e=>T.value.parentId=e),style:{width:"100%"},disabled:"add"===x.value,options:E.value,props:{checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])])),_:1}),m(D,{label:"角色ID",prop:"authorityId"},{default:I((()=>[m(S,{modelValue:T.value.authorityId,"onUpdate:modelValue":t[2]||(t[2]=e=>T.value.authorityId=e),disabled:"edit"===x.value,autocomplete:"off",maxlength:"15"},null,8,["modelValue","disabled"])])),_:1}),m(D,{label:"角色姓名",prop:"authorityName"},{default:I((()=>[m(S,{modelValue:T.value.authorityName,"onUpdate:modelValue":t[3]||(t[3]=e=>T.value.authorityName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","size"]),j.value?(c(),p(Z,{key:0,modelValue:j.value,"onUpdate:modelValue":t[5]||(t[5]=e=>j.value=e),size:g(U).drawerSize,title:"角色配置"},{default:I((()=>[m(te,{"before-leave":J,type:"border-card"},{default:I((()=>[m(ae,{label:"角色菜单"},{default:I((()=>[m(r,{ref_key:"menus",ref:W,row:z.value,onChangeRow:P},null,8,["row"])])),_:1}),m(ae,{label:"角色api"},{default:I((()=>[m(o,{ref_key:"apis",ref:X,row:z.value,onChangeRow:P},null,8,["row"])])),_:1}),m(ae,{label:"资源权限"},{default:I((()=>[m(i,{ref_key:"datas",ref:$,authority:H.value,row:z.value,onChangeRow:P},null,8,["authority","row"])])),_:1})])),_:1})])),_:1},8,["modelValue","size"])):f("",!0)])}}});export{D as default};
