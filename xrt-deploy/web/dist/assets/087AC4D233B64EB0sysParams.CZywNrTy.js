/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{s as e,a,r as l,g as t,c as r,o as u,f as s,b as d,w as o,d as n,h as i,F as m,i as c,l as p,t as v,v as g,aa as y,ab as f,E as b}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as h}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";const w=a=>e({url:"/sysParams/createSysParams",method:"post",data:a}),k=a=>e({url:"/sysParams/findSysParams",method:"get",params:a}),C={class:"gva-search-box"},_={class:"gva-table-box"},V={class:"gva-btn-list"},x={class:"gva-pagination"},A={class:"flex justify-between items-center"},P={class:"text-lg"},S={class:"usage-instructions bg-gray-100 border border-gray-300 rounded-lg p-4 mt-5"},T={class:"mb-2 text-sm text-gray-600"},D={class:"bg-blue-100 px-1 py-0.5 rounded"},I={class:"mb-2 text-sm text-gray-600"},B={class:"bg-blue-100 px-1 py-0.5 rounded"},U=Object.assign({name:"SysParams"},{__name:"sysParams",setup(U){const z=a(!1),E=a({name:"",key:"",value:"",desc:""}),j=l({name:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],key:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],value:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}]}),F=l({createdAt:[{validator:(e,a,l)=>{Q.value.startCreatedAt&&!Q.value.endCreatedAt?l(new Error("请填写结束日期")):!Q.value.startCreatedAt&&Q.value.endCreatedAt?l(new Error("请填写开始日期")):Q.value.startCreatedAt&&Q.value.endCreatedAt&&(Q.value.startCreatedAt.getTime()===Q.value.endCreatedAt.getTime()||Q.value.startCreatedAt.getTime()>Q.value.endCreatedAt.getTime())?l(new Error("开始日期应当早于结束日期")):l()},trigger:"change"}]}),q=a(),R=a(),G=a(1),K=a(0),L=a(10),O=a([]),Q=a({}),H=()=>{Q.value={},W()},J=()=>{var e;null==(e=R.value)||e.validate((async e=>{e&&(G.value=1,W())}))},M=e=>{L.value=e,W()},N=e=>{G.value=e,W()},W=async()=>{const a=await(l={page:G.value,pageSize:L.value,...Q.value},e({url:"/sysParams/getSysParamsList",method:"get",params:l}));var l;0===a.code&&(O.value=a.data.list,K.value=a.data.total,G.value=a.data.page,L.value=a.data.pageSize)};W();(async()=>{})();const X=a([]),Y=e=>{X.value=e},Z=async()=>{f.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const a=[];if(0===X.value.length)return void b({type:"warning",message:"请选择要删除的数据"});X.value&&X.value.map((e=>{a.push(e.ID)}));var l;0===(await(l={IDs:a},e({url:"/sysParams/deleteSysParamsByIds",method:"delete",params:l}))).code&&(b({type:"success",message:"删除成功"}),O.value.length===a.length&&G.value>1&&G.value--,W())}))},$=a(""),ee=async a=>{var l;0===(await(l={ID:a.ID},e({url:"/sysParams/deleteSysParams",method:"delete",params:l}))).code&&(b({type:"success",message:"删除成功"}),1===O.value.length&&G.value>1&&G.value--,W())},ae=a(!1),le=()=>{$.value="create",ae.value=!0},te=()=>{ae.value=!1,E.value={name:"",key:"",value:"",desc:""}},re=async()=>{var a;null==(a=q.value)||a.validate((async a=>{if(!a)return;let l;switch($.value){case"create":default:l=await w(E.value);break;case"update":l=await(t=E.value,e({url:"/sysParams/updateSysParams",method:"put",data:t}))}var t;0===l.code&&(b({type:"success",message:"创建/更改成功"}),te(),W())}))},ue=a({}),se=a(!1),de=async e=>{const a=await k({ID:e.ID});0===a.code&&(ue.value=a.data,se.value=!0)},oe=()=>{se.value=!1,ue.value={}};return(e,a)=>{const l=t("QuestionFilled"),b=t("el-icon"),w=t("el-tooltip"),U=t("el-date-picker"),W=t("el-form-item"),ne=t("el-input"),ie=t("el-button"),me=t("el-form"),ce=t("el-table-column"),pe=t("InfoFilled"),ve=t("el-table"),ge=t("el-pagination"),ye=t("el-drawer"),fe=t("el-descriptions-item"),be=t("el-descriptions");return u(),r("div",null,[s(h,{title:"获取参数且缓存方法已在前端utils/params 已经封装完成 不必自己书写 使用方法查看文件内注释"}),d("div",C,[s(me,{ref_key:"elSearchFormRef",ref:R,inline:!0,model:Q.value,class:"demo-form-inline",rules:F,onKeyup:p(J,["enter"])},{default:o((()=>[s(W,{label:"创建日期",prop:"createdAt"},{label:o((()=>[d("span",null,[a[12]||(a[12]=i(" 创建日期 ")),s(w,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:o((()=>[s(b,null,{default:o((()=>[s(l)])),_:1})])),_:1})])])),default:o((()=>[s(U,{modelValue:Q.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!Q.value.endCreatedAt&&e.getTime()>Q.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[13]||(a[13]=i(" — ")),s(U,{modelValue:Q.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!Q.value.startCreatedAt&&e.getTime()<Q.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),s(W,{label:"参数名称",prop:"name"},{default:o((()=>[s(ne,{modelValue:Q.value.name,"onUpdate:modelValue":a[2]||(a[2]=e=>Q.value.name=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(W,{label:"参数键",prop:"key"},{default:o((()=>[s(ne,{modelValue:Q.value.key,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.value.key=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),z.value?(u(),r(m,{key:0},[],64)):n("",!0),s(W,null,{default:o((()=>[s(ie,{type:"primary",icon:"search",onClick:J},{default:o((()=>a[14]||(a[14]=[i("查询")]))),_:1}),s(ie,{icon:"refresh",onClick:H},{default:o((()=>a[15]||(a[15]=[i("重置")]))),_:1}),z.value?(u(),c(ie,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[5]||(a[5]=e=>z.value=!1)},{default:o((()=>a[17]||(a[17]=[i("收起")]))),_:1})):(u(),c(ie,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[4]||(a[4]=e=>z.value=!0)},{default:o((()=>a[16]||(a[16]=[i("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),d("div",_,[d("div",V,[s(ie,{type:"primary",icon:"plus",onClick:le},{default:o((()=>a[18]||(a[18]=[i("新增")]))),_:1}),s(ie,{icon:"delete",style:{"margin-left":"10px"},disabled:!X.value.length,onClick:Z},{default:o((()=>a[19]||(a[19]=[i("删除")]))),_:1},8,["disabled"])]),s(ve,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:O.value,"row-key":"ID",onSelectionChange:Y},{default:o((()=>[s(ce,{type:"selection",width:"55"}),s(ce,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:o((e=>[i(v(g(y)(e.row.CreatedAt)),1)])),_:1}),s(ce,{align:"left",label:"参数名称",prop:"name",width:"120"}),s(ce,{align:"left",label:"参数键",prop:"key",width:"120"}),s(ce,{align:"left",label:"参数值",prop:"value",width:"120"}),s(ce,{align:"left",label:"参数说明",prop:"desc",width:"120"}),s(ce,{align:"left",label:"操作",fixed:"right","min-width":"240"},{default:o((e=>[s(ie,{type:"primary",link:"",class:"table-button",onClick:a=>de(e.row)},{default:o((()=>[s(b,{style:{"margin-right":"5px"}},{default:o((()=>[s(pe)])),_:1}),a[20]||(a[20]=i("查看详情"))])),_:2},1032,["onClick"]),s(ie,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await k({ID:e.ID});$.value="update",0===a.code&&(E.value=a.data,ae.value=!0)})(e.row)},{default:o((()=>a[21]||(a[21]=[i("变更")]))),_:2},1032,["onClick"]),s(ie,{type:"primary",link:"",icon:"delete",onClick:a=>{return l=e.row,void f.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ee(l)}));var l}},{default:o((()=>a[22]||(a[22]=[i("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),d("div",x,[s(ge,{layout:"total, sizes, prev, pager, next, jumper","current-page":G.value,"page-size":L.value,"page-sizes":[10,30,50,100],total:K.value,onCurrentChange:N,onSizeChange:M},null,8,["current-page","page-size","total"])])]),s(ye,{"destroy-on-close":"",size:"800",modelValue:ae.value,"onUpdate:modelValue":a[10]||(a[10]=e=>ae.value=e),"show-close":!1,"before-close":te},{header:o((()=>[d("div",A,[d("span",P,v("create"===$.value?"添加":"修改"),1),d("div",null,[s(ie,{type:"primary",onClick:re},{default:o((()=>a[23]||(a[23]=[i("确 定")]))),_:1}),s(ie,{onClick:te},{default:o((()=>a[24]||(a[24]=[i("取 消")]))),_:1})])])])),default:o((()=>[s(me,{model:E.value,"label-position":"top",ref_key:"elFormRef",ref:q,rules:j,"label-width":"80px"},{default:o((()=>[s(W,{label:"参数名称:",prop:"name"},{default:o((()=>[s(ne,{modelValue:E.value.name,"onUpdate:modelValue":a[6]||(a[6]=e=>E.value.name=e),clearable:!0,placeholder:"请输入参数名称"},null,8,["modelValue"])])),_:1}),s(W,{label:"参数键:",prop:"key"},{default:o((()=>[s(ne,{modelValue:E.value.key,"onUpdate:modelValue":a[7]||(a[7]=e=>E.value.key=e),clearable:!0,placeholder:"请输入参数键"},null,8,["modelValue"])])),_:1}),s(W,{label:"参数值:",prop:"value"},{default:o((()=>[s(ne,{type:"textarea",rows:5,modelValue:E.value.value,"onUpdate:modelValue":a[8]||(a[8]=e=>E.value.value=e),clearable:!0,placeholder:"请输入参数值"},null,8,["modelValue"])])),_:1}),s(W,{label:"参数说明:",prop:"desc"},{default:o((()=>[s(ne,{modelValue:E.value.desc,"onUpdate:modelValue":a[9]||(a[9]=e=>E.value.desc=e),clearable:!0,placeholder:"请输入参数说明"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),d("div",S,[a[31]||(a[31]=d("h3",{class:"mb-3 text-lg text-gray-800"},"使用说明",-1)),d("p",T,[a[25]||(a[25]=i(" 前端可以通过引入 ")),a[26]||(a[26]=d("code",{class:"bg-blue-100 px-1 py-0.5 rounded"},"import { getParams } from '@/utils/params'",-1)),a[27]||(a[27]=i(" 然后通过 ")),d("code",D,'await getParams("'+v(E.value.key)+'")',1),a[28]||(a[28]=i(" 来获取对应的参数。 "))]),a[32]||(a[32]=d("p",{class:"text-sm text-gray-600"},[i(" 后端需要提前 "),d("code",{class:"bg-blue-100 px-1 py-0.5 rounded"},'import "github.com/flipped-aurora/gin-vue-admin/server/service/system"')],-1)),d("p",I,[a[29]||(a[29]=i(" 然后调用 ")),d("code",B,'new(system.SysParamsService).GetSysParam("'+v(E.value.key)+'")',1),a[30]||(a[30]=i(" 来获取对应的 value 值。 "))])])])),_:1},8,["modelValue"]),s(ye,{"destroy-on-close":"",size:"800",modelValue:se.value,"onUpdate:modelValue":a[11]||(a[11]=e=>se.value=e),"show-close":!0,"before-close":oe},{default:o((()=>[s(be,{column:1,border:""},{default:o((()=>[s(fe,{label:"参数名称"},{default:o((()=>[i(v(ue.value.name),1)])),_:1}),s(fe,{label:"参数键"},{default:o((()=>[i(v(ue.value.key),1)])),_:1}),s(fe,{label:"参数值"},{default:o((()=>[i(v(ue.value.value),1)])),_:1}),s(fe,{label:"参数说明"},{default:o((()=>[i(v(ue.value.desc),1)])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}});export{U as default};
