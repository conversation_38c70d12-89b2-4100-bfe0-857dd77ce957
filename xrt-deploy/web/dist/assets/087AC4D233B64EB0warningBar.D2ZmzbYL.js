/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{g as e,c as t,o as a,f as r,b as s,w as n,v as o,ag as i,t as l,n as p}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const c={__name:"warningBar",props:{title:{type:String,default:""},href:{type:String,default:""}},setup(c){const d=c,f=()=>{d.href&&window.open(d.href)};return(d,m)=>{const g=e("el-icon");return a(),t("div",{class:p(["px-1.5 py-2 flex items-center rounded-sm mt-2 bg-amber-50 gap-2 mb-3 text-amber-500 dark:bg-amber-700 dark:text-gray-200",c.href&&"cursor-pointer"]),onClick:f},[r(g,{class:"text-xl"},{default:n((()=>[r(o(i))])),_:1}),s("span",null,l(c.title),1)],2)}}};export{c as _};
