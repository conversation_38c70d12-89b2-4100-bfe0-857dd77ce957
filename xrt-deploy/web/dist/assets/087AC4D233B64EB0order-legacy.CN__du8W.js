/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){o(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function o(t,r,n){return(r=function(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,r||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}function i(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */i=function(){return r};var t,r={},n=Object.prototype,a=n.hasOwnProperty,o=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},u=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",s=l.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof y?t:y,i=Object.create(a.prototype),l=new E(n||[]);return o(i,"_invoke",{value:j(e,r,l)}),i}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=f;var v="suspendedStart",b="suspendedYield",m="executing",g="completed",h={};function y(){}function x(){}function w(){}var k={};d(k,u,(function(){return this}));var _=Object.getPrototypeOf,S=_&&_(_(D([])));S&&S!==n&&a.call(S,u)&&(k=S);var N=w.prototype=y.prototype=Object.create(k);function C(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function I(t,r){function n(o,i,l,u){var c=p(t[o],t,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==e(d)&&a.call(d,"__await")?r.resolve(d.__await).then((function(e){n("next",e,l,u)}),(function(e){n("throw",e,l,u)})):r.resolve(d).then((function(e){s.value=e,l(s)}),(function(e){return n("throw",e,l,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(e,t){function a(){return new r((function(r,a){n(e,t,r,a)}))}return i=i?i.then(a,a):a()}})}function j(e,r,n){var a=v;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw i;return{value:t,done:!0}}for(n.method=o,n.arg=i;;){var l=n.delegate;if(l){var u=V(l,n);if(u){if(u===h)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===v)throw a=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var c=p(e,r,n);if("normal"===c.type){if(a=n.done?g:b,c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=g,n.method="throw",n.arg=c.arg)}}}function V(e,r){var n=r.method,a=e.iterator[n];if(a===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,V(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var o=p(a,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,h;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,h):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,h)}function z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function P(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function E(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(z,this),this.reset(!0)}function D(r){if(r||""===r){var n=r[u];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function e(){for(;++o<r.length;)if(a.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(e(r)+" is not iterable")}return x.prototype=w,o(N,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:x,configurable:!0}),x.displayName=d(w,s,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===x||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,s,"GeneratorFunction")),e.prototype=Object.create(N),e},r.awrap=function(e){return{__await:e}},C(I.prototype),d(I.prototype,c,(function(){return this})),r.AsyncIterator=I,r.async=function(e,t,n,a,o){void 0===o&&(o=Promise);var i=new I(f(e,t,n,a),o);return r.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},C(N),d(N,s,"Generator"),d(N,u,(function(){return this})),d(N,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=D,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(P),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,a){return l.type="throw",l.arg=e,r.next=n,a&&(r.method="next",r.arg=t),!!a}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var u=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),P(r),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;P(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:D(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),h}},r}function l(e,t,r,n,a,o,i){try{var l=e[o](i),u=l.value}catch(e){return void r(e)}l.done?t(u):Promise.resolve(u).then(n,a)}function u(e){return function(){var t=this,r=arguments;return new Promise((function(n,a){var o=e.apply(t,r);function i(e){l(o,n,a,i,u,"next",e)}function u(e){l(o,n,a,i,u,"throw",e)}i(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0order-legacy.BqaGmf2o.js","./087AC4D233B64EB0image-legacy.CRPxTu2y.js","./087AC4D233B64EB0selectImage-legacy.B9If-S4b.js","./087AC4D233B64EB0QR-code-legacy.DcvYsf4S.js","./087AC4D233B64EB0vue-cropper.es-legacy.CQhcf0py.js","./087AC4D233B64EB0logo-legacy.BFIBdCh0.js"],(function(e,r){"use strict";var n,o,l,c,s,d,f,p,v,b,m,g,h,y,x,w,k,_,S,N,C,I,j,V,z,P,E,D,O,L,A,B,T,U,q;return{setters:[function(e){n=e._,o=e.a,l=e.I,c=e.r,s=e.Q,d=e.g,f=e.c,p=e.o,v=e.b,b=e.f,m=e.w,g=e.d,h=e.F,y=e.D,x=e.i,w=e.h,k=e.l,_=e.t,S=e.v,N=e.ay,C=e.n,I=e.az,j=e.aA,V=e.E,z=e.ab},function(e){P=e.g,E=e.f,D=e.c,O=e.u,L=e.d,A=e.p,B=e.a,T=e.b},function(e){U=e.g},function(e){q=e.S},null,null,null],execute:function(){var r=document.createElement("style");r.textContent='.products-container[data-v-7cba740f]{display:flex;flex-direction:column;gap:0;padding:3px}.product-row[data-v-7cba740f]{display:flex;align-items:flex-start;gap:6px;min-height:45px;padding:3px 0;position:relative}.product-row[data-v-7cba740f]:not(:last-child):after{content:"";position:absolute;bottom:0;left:58px;right:0;height:1px;background-color:#f0f0f0}.product-image-wrapper[data-v-7cba740f]{flex-shrink:0;width:50px;display:flex;flex-direction:column;align-items:center;gap:2px}.product-image-wrapper .el-image[data-v-7cba740f]{border-radius:4px;border:1px solid #dcdfe6;transition:all .3s ease}.product-image-wrapper .el-image[data-v-7cba740f]:hover{border-color:#409eff;transform:scale(1.05);box-shadow:0 2px 8px rgba(64,158,255,.3)}.no-image[data-v-7cba740f]{width:50px;height:50px;border:1px dashed #dcdfe6;border-radius:4px;display:flex;align-items:center;justify-content:center;color:#999;font-size:10px;background-color:#f5f7fa}.product-quantity[data-v-7cba740f]{font-size:11px;color:#409eff;font-weight:700;text-align:center;line-height:1;margin-top:2px;background-color:rgba(64,158,255,.1);border-radius:3px;padding:1px 4px;min-width:20px}.product-info[data-v-7cba740f]{flex:1;display:flex;flex-direction:column;gap:1px;min-width:0}.product-info-row[data-v-7cba740f]{display:flex;align-items:center;gap:4px;min-height:16px}.product-badge[data-v-7cba740f]{display:inline-block;padding:1px 4px;border-radius:3px;font-size:9px;font-weight:600;color:#fff;min-width:24px;text-align:center;flex-shrink:0}.ozon-badge[data-v-7cba740f]{background-color:#409eff}.quantity-badge[data-v-7cba740f]{background-color:#e6a23c}.name-badge[data-v-7cba740f]{background-color:#909399}.product-text[data-v-7cba740f]{font-size:11px;color:#333;font-family:Courier New,monospace;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;flex:1}.product-name[data-v-7cba740f]{font-family:inherit;line-height:1.1;white-space:normal;overflow:visible;display:block;font-size:11px;word-break:break-word;word-wrap:break-word;-webkit-hyphens:auto;hyphens:auto;max-height:none;text-overflow:clip}.product-name.long-text[data-v-7cba740f]{font-size:9px;line-height:1}.product-name.very-long-text[data-v-7cba740f]{font-size:8px;line-height:1}.product-sku[data-v-7cba740f]{color:#409eff!important;cursor:pointer;text-decoration:underline;text-decoration-color:#409eff;transition:all .3s ease;font-weight:700}.product-sku[data-v-7cba740f]:hover{color:#337ecc!important;text-decoration-color:#337ecc;transform:scale(1.05)}.posting-number[data-v-7cba740f]{color:#409eff;font-weight:600;cursor:pointer;transition:color .3s ease;font-family:Courier New,monospace;font-size:13px;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap;text-decoration:underline;text-decoration-color:transparent;overflow:visible;text-overflow:clip;max-width:none;width:auto;display:inline-block}.posting-number[data-v-7cba740f]:hover{color:#337ecc;text-decoration-color:#337ecc}.time-info[data-v-7cba740f]{display:flex;flex-direction:column;gap:4px;font-size:12px;line-height:1.2}.order-time[data-v-7cba740f],.ship-time[data-v-7cba740f]{display:flex;align-items:center;gap:4px}.time-badge[data-v-7cba740f]{display:inline-block;padding:2px 6px;border-radius:4px;font-size:10px;font-weight:600;color:#fff;min-width:28px;text-align:center;flex-shrink:0}.order-time-badge[data-v-7cba740f]{background-color:#909399}.ship-time-badge[data-v-7cba740f]{background-color:#f56c6c}.time-value[data-v-7cba740f]{color:#333;font-family:Courier New,monospace;font-size:12px}.order-info[data-v-7cba740f]{display:flex;flex-direction:column;gap:2px;font-size:12px;line-height:1.2}.posting-info[data-v-7cba740f],.order-info-row[data-v-7cba740f],.tracking-info[data-v-7cba740f],.logistics-info[data-v-7cba740f]{display:flex;align-items:center;gap:6px;min-height:18px}.info-badge[data-v-7cba740f]{display:inline-block;padding:2px 6px;border-radius:4px;font-size:10px;font-weight:600;color:#fff;min-width:28px;text-align:center;flex-shrink:0}.order-badge[data-v-7cba740f]{background-color:#409eff}.posting-badge[data-v-7cba740f]{background-color:#67c23a}.tracking-badge[data-v-7cba740f]{background-color:#e6a23c}.logistics-badge[data-v-7cba740f]{background-color:#67c23a}.posting-number[data-v-7cba740f],.order-number[data-v-7cba740f],.tracking-number[data-v-7cba740f]{color:#409eff;font-weight:600;cursor:pointer;transition:color .3s ease;font-family:Courier New,monospace;font-size:12px;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap;text-decoration:underline;text-decoration-color:transparent;overflow:visible;text-overflow:clip;max-width:none;width:auto;display:inline-block;flex:1}.posting-number[data-v-7cba740f]:hover,.order-number[data-v-7cba740f]:hover,.tracking-number[data-v-7cba740f]:hover{color:#337ecc;text-decoration-color:#337ecc}.tracking-number[title*=国际单号为空][data-v-7cba740f]{color:#999;cursor:default;text-decoration:none}.tracking-number[title*=国际单号为空][data-v-7cba740f]:hover{color:#999;text-decoration:none}.logistics-text[data-v-7cba740f]{color:#606266;font-size:12px;line-height:1.2;max-width:140px;max-height:28.8px;word-wrap:break-word;word-break:break-all;overflow:hidden;display:block;flex:1;white-space:normal;text-overflow:clip}.logistics-text[data-long=true][data-v-7cba740f]{font-size:10px;line-height:1.15;max-height:23px}.logistics-text[data-very-long=true][data-v-7cba740f]{font-size:8px;line-height:1.1;max-height:17.6px}.logistics-text[data-v-7cba740f]:hover{color:#409eff}.shop-name[data-v-7cba740f]{color:#606266;font-size:12px;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:block;max-width:100%}.shop-name[data-v-7cba740f]:hover{color:#409eff}.status-text[data-v-7cba740f]{font-weight:500;padding:2px 8px;border-radius:4px;font-size:12px;display:inline-block;min-width:60px;text-align:center}.status-normal[data-v-7cba740f]{color:#409eff;background-color:#ecf5ff;border:1px solid #d9ecff}.status-error[data-v-7cba740f]{color:#f56c6c;background-color:#fef0f0;border:1px solid #fbc4c4}.operation-buttons[data-v-7cba740f]{display:flex;flex-direction:column;gap:4px;align-items:flex-start}.operation-buttons .el-button[data-v-7cba740f]{margin:0;padding:2px 8px;font-size:12px;min-height:auto;height:auto;line-height:1.2}.operation-buttons .el-button+.el-button[data-v-7cba740f]{margin-left:0}.gva-search-box[data-v-7cba740f],.gva-table-box[data-v-7cba740f]{width:100%;max-width:none;transition:all .3s ease}.el-table[data-v-7cba740f]{width:100%!important;transition:all .3s ease}.el-table .el-table__body-wrapper[data-v-7cba740f]{overflow-x:auto}.gva-card[data-v-7cba740f]{margin:0;width:100%;transition:all .3s ease}.order-container[data-v-7cba740f]{width:100%;height:100%;transition:all .3s ease}.el-table__body-wrapper[data-v-7cba740f],.el-table__header-wrapper[data-v-7cba740f]{transition:all .3s ease}.el-table--fit[data-v-7cba740f]{width:100%!important}.gva-table-box .el-table[data-v-7cba740f]{width:100%!important;min-width:auto!important}.el-table .el-table__header-wrapper[data-v-7cba740f],.el-table .el-table__body-wrapper[data-v-7cba740f]{width:100%!important}@media (min-width: 1200px){.el-table-column[data-v-7cba740f]{min-width:auto}}.el-table[data-v-7cba740f]{table-layout:auto!important;width:100%!important}.el-table .el-table__header-wrapper th[data-v-7cba740f],.el-table .el-table__body-wrapper td[data-v-7cba740f]{text-align:center;vertical-align:middle}.el-table .el-table__body-wrapper td .cell[data-v-7cba740f]{padding:8px 4px;word-break:break-word;overflow:hidden;text-overflow:ellipsis}.el-table-column[label=产品信息] .cell[data-v-7cba740f],.el-table-column[label=订单信息] .cell[data-v-7cba740f]{text-align:left!important}.el-table .el-table__body[data-v-7cba740f],.el-table .el-table__header[data-v-7cba740f]{width:100%!important}.el-table th[data-v-7cba740f],.el-table td[data-v-7cba740f]{overflow:hidden;text-overflow:ellipsis}\n/*$vite$:1*/',document.head.appendChild(r);var F={class:"order-container"},G={class:"gva-search-box"},M={class:"gva-table-box"},R={class:"gva-btn-list"},Y={class:"order-info"},$={class:"order-info-row"},K=["onClick","title"],Q={class:"posting-info"},J=["onClick","title"],H={key:0,class:"tracking-info"},W=["onClick","title"],X={class:"logistics-info"},Z=["title","data-long","data-very-long"],ee=["title"],te={class:"products-container"},re={class:"product-image-wrapper"},ne={key:1,class:"no-image"},ae={class:"product-quantity"},oe={class:"product-info"},ie={class:"product-info-row"},le=["onClick","title"],ue={class:"product-info-row"},ce=["title"],se={key:0,class:"product-row"},de={class:"product-info"},fe={class:"product-info-row"},pe={class:"product-info-row"},ve={class:"time-info"},be={class:"order-time"},me={class:"time-value"},ge={class:"ship-time"},he={class:"time-value"},ye={class:"operation-buttons"},xe={class:"gva-pagination"},we={class:"flex justify-between items-center"},ke={class:"text-lg"},_e=Object.assign({name:"Order"},{__name:"order",setup:function(e){var r=o(!1),n=o(!1),_e=o(!1),Se=l(),Ne=o(),Ce=o(!1),Ie=o([]),je=o([]),Ve=o({orderImg:[],postingNumber:"",inProcessAt:new Date,orderNumber:"",tplProvider:"",status:"",distributionStatus:"",trackingNumber:"",shipmentDate:new Date}),ze=c({}),Pe=c({}),Ee=o(),De=o(),Oe=localStorage.getItem("orderListPage"),Le=localStorage.getItem("orderListPageSize"),Ae=o(Oe?parseInt(Oe):1),Be=o(0),Te=o(Le?parseInt(Le):10),Ue=o([]);s(Ae,(function(e){localStorage.setItem("orderListPage",e.toString())})),s(Te,(function(e){localStorage.setItem("orderListPageSize",e.toString())}));var qe=o({distributionStatus:"unprocessed"}),Fe=function(){qe.value={distributionStatus:"unprocessed"},Ye()},Ge=function(){var e;null===(e=De.value)||void 0===e||e.validate(function(){var e=u(i().mark((function e(t){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:Ae.value=1,Ye();case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},Me=function(e){Te.value=e,Ye()},Re=function(e){Ae.value=e,Ye()},Ye=function(){var e=u(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,P(a({page:Ae.value,pageSize:Te.value},qe.value));case 2:0===(t=e.sent).code&&(Ue.value=t.data.list,Be.value=t.data.total);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();Ye();var $e=function(){var e=u(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j("order_status");case 2:return Ie.value=e.sent,e.next=5,j("distribution_status");case 5:je.value=e.sent;case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();$e();var Ke=o([]),Qe=function(e){Ke.value=e},Je=function(){var e=u(i().mark((function e(){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:z.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(u(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=[],0!==Ke.value.length){e.next=4;break}return V({type:"warning",message:"请选择要删除的数据"}),e.abrupt("return");case 4:return Ke.value&&Ke.value.map((function(e){t.push(e.ID)})),e.next=7,T({IDs:t});case 7:0===e.sent.code&&(V({type:"success",message:"删除成功"}),Ue.value.length===t.length&&Ae.value>1&&Ae.value--,Ye());case 9:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),He=o(""),We=function(){var e=u(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,E({ID:t.ID});case 2:r=e.sent,He.value="update",0===r.code&&(Ve.value=r.data,Ze.value=!0);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Xe=function(){var e=u(i().mark((function e(t){return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,L({ID:t.ID});case 2:0===e.sent.code&&(V({type:"success",message:"删除成功"}),1===Ue.value.length&&Ae.value>1&&Ae.value--,Ye());case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ze=o(!1),et=function(){Ze.value=!1,Ve.value={orderImg:[],postingNumber:"",inProcessAt:new Date,orderNumber:"",tplProvider:"",status:"",distributionStatus:"",trackingNumber:"",shipmentDate:new Date}},tt=function(){var e=u(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:r.value=!0,null===(t=Ee.value)||void 0===t||t.validate(function(){var e=u(i().mark((function e(t){var n;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return",r.value=!1);case 2:e.t0=He.value,e.next="create"===e.t0?5:"update"===e.t0?9:13;break;case 5:return e.next=7,D(Ve.value);case 7:return n=e.sent,e.abrupt("break",17);case 9:return e.next=11,O(Ve.value);case 11:return n=e.sent,e.abrupt("break",17);case 13:return e.next=15,D(Ve.value);case 15:return n=e.sent,e.abrupt("break",17);case 17:r.value=!1,0===n.code&&(V({type:"success",message:"创建/更改成功"}),et(),Ye());case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),rt=o({}),nt=o(!1),at=function(){var e=u(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,E({ID:t.ID});case 2:0===(r=e.sent).code&&(rt.value=r.data,nt.value=!0);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ot=function(){nt.value=!1,rt.value={}},it=function(){var e=u(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,n.value=!0,V({type:"info",message:"正在从Ozon同步订单..."}),e.next=5,A();case 5:0===(t=e.sent).code?(V({type:"success",message:"同步成功！共拉取 ".concat(t.data.count," 个订单")}),Ye()):V({type:"error",message:"同步失败: "+t.msg}),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),V({type:"error",message:"同步订单失败: "+e.t0.message});case 12:return e.prev=12,n.value=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),lt=function(){var e=u(i().mark((function e(){var t;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,_e.value=!0,V({type:"info",message:"正在拉取今年一整年的订单..."}),e.next=5,B();case 5:0===(t=e.sent).code?(V({type:"success",message:"拉取成功！共拉取今年 ".concat(t.data.count," 个订单")}),Ye()):V({type:"error",message:"拉取今年订单失败: "+t.msg}),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0),V({type:"error",message:"拉取今年订单失败: "+e.t0.message});case 12:return e.prev=12,_e.value=!1,e.finish(12);case 15:case"end":return e.stop()}}),e,null,[[0,9,12,15]])})));return function(){return e.apply(this,arguments)}}(),ut=function(){var e=u(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return V({type:"warning",message:"货件号为空，无法复制"}),e.abrupt("return");case 3:if(e.prev=3,!navigator.clipboard||!window.isSecureContext){e.next=9;break}return e.next=7,navigator.clipboard.writeText(t);case 7:e.next=19;break;case 9:(r=document.createElement("textarea")).value=t,r.style.position="fixed",r.style.left="-999999px",r.style.top="-999999px",document.body.appendChild(r),r.focus(),r.select(),document.execCommand("copy"),document.body.removeChild(r);case 19:V({type:"success",message:"货件号已复制: ".concat(t),duration:2e3}),e.next=26;break;case 22:e.prev=22,e.t0=e.catch(3),V({type:"error",message:"复制失败，请手动复制"});case 26:case"end":return e.stop()}}),e,null,[[3,22]])})));return function(t){return e.apply(this,arguments)}}(),ct=function(){var e=u(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return V({type:"warning",message:"订单号为空，无法复制"}),e.abrupt("return");case 3:if(e.prev=3,!navigator.clipboard||!window.isSecureContext){e.next=9;break}return e.next=7,navigator.clipboard.writeText(t);case 7:e.next=19;break;case 9:(r=document.createElement("textarea")).value=t,r.style.position="fixed",r.style.left="-999999px",r.style.top="-999999px",document.body.appendChild(r),r.focus(),r.select(),document.execCommand("copy"),document.body.removeChild(r);case 19:V({type:"success",message:"订单号已复制: ".concat(t),duration:2e3}),e.next=26;break;case 22:e.prev=22,e.t0=e.catch(3),V({type:"error",message:"复制失败，请手动复制"});case 26:case"end":return e.stop()}}),e,null,[[3,22]])})));return function(t){return e.apply(this,arguments)}}(),st=function(e){if(!e)return"";try{var t=new Date(e);if(isNaN(t.getTime()))return"";var r=t.getFullYear().toString().slice(-2),n=(t.getMonth()+1).toString().padStart(2,"0"),a=t.getDate().toString().padStart(2,"0");return"".concat(r,"/").concat(n,"/").concat(a)}catch(o){return""}},dt=function(e){try{if(e.jsonData){var r="string"==typeof e.jsonData?JSON.parse(e.jsonData):e.jsonData;if(r.products&&r.products.length>0){var n=new Map,a=0;return r.products.forEach((function(t,r){var o="".concat(t.sku||"unknown","_").concat(t.name||"unknown");if(n.has(o)){var i=n.get(o);i.quantity=(i.quantity||1)+(t.quantity||1)}else{var l=null;e.orderImg&&e.orderImg.length>a&&(l=e.orderImg[a],a++),n.set(o,{name:t.name||"未知产品",sku:t.sku||"",offerId:t.offer_id||"",quantity:t.quantity||1,image:l,imageIndex:a-1})}})),Array.from(n.values()).slice(0,3)}}return e.orderImg&&e.orderImg.length>0?t(new Set(e.orderImg)).slice(0,3).map((function(e,t){return{name:"未知产品",sku:"",offerId:"",image:e,imageIndex:t}})):[]}catch(o){return[]}},ft=function(e){if(!e)return"";var t=e.length;return t>80?"very-long-text":t>50?"long-text":""},pt=function(e){var t=e.trackingNumber,r=e.postingNumber;return!(!t||"-"===t||t===r)},vt=function(){var e=u(i().mark((function e(t){var r;return i().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t&&"-"!==t){e.next=3;break}return V({type:"warning",message:"国际单号为空，无法复制"}),e.abrupt("return");case 3:if(e.prev=3,!navigator.clipboard||!window.isSecureContext){e.next=9;break}return e.next=7,navigator.clipboard.writeText(t);case 7:e.next=19;break;case 9:(r=document.createElement("textarea")).value=t,r.style.position="fixed",r.style.left="-999999px",r.style.top="-999999px",document.body.appendChild(r),r.focus(),r.select(),document.execCommand("copy"),document.body.removeChild(r);case 19:V({type:"success",message:"国际单号已复制: ".concat(t),duration:2e3}),e.next=26;break;case 22:e.prev=22,e.t0=e.catch(3),V({type:"error",message:"复制失败，请手动复制"});case 26:case"end":return e.stop()}}),e,null,[[3,22]])})));return function(t){return e.apply(this,arguments)}}(),bt=function(e){return e?e.length:0};return function(e,t){var a=d("el-input"),o=d("el-form-item"),i=d("el-option"),l=d("el-select"),u=d("el-button"),c=d("el-form"),s=d("el-table-column"),j=d("el-image"),P=d("Goods"),E=d("el-icon"),D=d("Document"),O=d("InfoFilled"),L=d("el-table"),A=d("el-pagination"),B=d("el-date-picker"),T=d("el-drawer"),Oe=d("el-descriptions-item"),Le=d("el-descriptions");return p(),f("div",F,[v("div",G,[b(c,{ref_key:"elSearchFormRef",ref:De,inline:!0,model:qe.value,class:"demo-form-inline",rules:Pe,onKeyup:k(Ge,["enter"])},{default:m((function(){return[b(o,{label:"货件号",prop:"postingNumber"},{default:m((function(){return[b(a,{modelValue:qe.value.postingNumber,"onUpdate:modelValue":t[0]||(t[0]=function(e){return qe.value.postingNumber=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),b(o,{label:"订单号",prop:"orderNumber"},{default:m((function(){return[b(a,{modelValue:qe.value.orderNumber,"onUpdate:modelValue":t[1]||(t[1]=function(e){return qe.value.orderNumber=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),b(o,{label:"配货状态",prop:"distributionStatus"},{default:m((function(){return[b(l,{modelValue:qe.value.distributionStatus,"onUpdate:modelValue":t[2]||(t[2]=function(e){return qe.value.distributionStatus=e}),clearable:"",placeholder:"请选择",onClear:t[3]||(t[3]=function(){qe.value.distributionStatus=void 0})},{default:m((function(){return[(p(!0),f(h,null,y(je.value,(function(e,t){return p(),x(i,{key:t,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),b(o,{label:"国际单号",prop:"trackingNumber"},{default:m((function(){return[b(a,{modelValue:qe.value.trackingNumber,"onUpdate:modelValue":t[4]||(t[4]=function(e){return qe.value.trackingNumber=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),Ce.value?(p(),f(h,{key:0},[],64)):g("",!0),b(o,null,{default:m((function(){return[b(u,{type:"primary",icon:"search",onClick:Ge},{default:m((function(){return t[19]||(t[19]=[w("查询")])})),_:1}),b(u,{icon:"refresh",onClick:Fe},{default:m((function(){return t[20]||(t[20]=[w("重置")])})),_:1}),Ce.value?(p(),x(u,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:t[6]||(t[6]=function(e){return Ce.value=!1})},{default:m((function(){return t[22]||(t[22]=[w("收起")])})),_:1})):(p(),x(u,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:t[5]||(t[5]=function(e){return Ce.value=!0})},{default:m((function(){return t[21]||(t[21]=[w("展开")])})),_:1}))]})),_:1})]})),_:1},8,["model","rules"])]),v("div",M,[v("div",R,[b(u,{type:"success",icon:"download",loading:n.value,onClick:it},{default:m((function(){return t[23]||(t[23]=[w("同步订单")])})),_:1},8,["loading"]),b(u,{type:"warning",icon:"calendar",loading:_e.value,onClick:lt},{default:m((function(){return t[24]||(t[24]=[w("拉取今年订单")])})),_:1},8,["loading"]),b(u,{type:"primary",icon:"plus",onClick:t[7]||(t[7]=function(e){return He.value="create",void(Ze.value=!0)})},{default:m((function(){return t[25]||(t[25]=[w("新增")])})),_:1}),b(u,{icon:"delete",style:{"margin-left":"10px"},disabled:!Ke.value.length,onClick:Je},{default:m((function(){return t[26]||(t[26]=[w("删除")])})),_:1},8,["disabled"])]),b(L,{ref_key:"multipleTable",ref:Ne,style:{width:"100%"},"tooltip-effect":"dark",data:Ue.value,"row-key":"ID",onSelectionChange:Qe},{default:m((function(){return[b(s,{type:"selection",width:"55"}),b(s,{align:"left",label:"订单信息",prop:"postingNumber","min-width":"180","show-overflow-tooltip":!1},{default:m((function(e){return[v("div",Y,[v("div",$,[t[27]||(t[27]=v("span",{class:"info-badge order-badge"},"订单",-1)),v("span",{class:"order-number",onClick:function(t){return ct(e.row.orderNumber)},title:"点击复制订单号: "+e.row.orderNumber},_(e.row.orderNumber),9,K)]),v("div",Q,[t[28]||(t[28]=v("span",{class:"info-badge posting-badge"},"货件",-1)),v("span",{class:"posting-number",onClick:function(t){return ut(e.row.postingNumber)},title:"点击复制货件号: "+e.row.postingNumber},_(e.row.postingNumber),9,J)]),pt(e.row)?(p(),f("div",H,[t[29]||(t[29]=v("span",{class:"info-badge tracking-badge"},"国际",-1)),v("span",{class:"tracking-number",onClick:function(t){return vt(e.row.trackingNumber)},title:"点击复制国际单号: "+e.row.trackingNumber},_(e.row.trackingNumber),9,W)])):g("",!0),v("div",X,[t[30]||(t[30]=v("span",{class:"info-badge logistics-badge"},"物流",-1)),v("span",{class:"logistics-text",title:e.row.tplProvider||"未知物流","data-long":bt(e.row.tplProvider)>15,"data-very-long":bt(e.row.tplProvider)>25},_(e.row.tplProvider||"未知物流"),9,Z)])])]})),_:1}),b(s,{align:"left",label:"店铺",prop:"shopName","min-width":"180"},{default:m((function(e){return[v("span",{class:"shop-name",title:e.row.shopName||"未知店铺"},_(e.row.shopName||"未知店铺"),9,ee)]})),_:1}),b(s,{label:"产品信息",prop:"orderImg","min-width":"180"},{default:m((function(e){return[v("div",te,[(p(!0),f(h,null,y(dt(e.row),(function(r,n){return p(),f("div",{key:n,class:"product-row"},[v("div",re,[r.image?(p(),x(j,{key:0,"preview-teleported":"",style:{width:"50px",height:"50px",cursor:"pointer"},src:S(U)(r.image),"preview-src-list":S(N)(e.row.orderImg),"initial-index":r.imageIndex,fit:"cover",onClick:function(t){return e.row.orderImg,void r.imageIndex}},null,8,["src","preview-src-list","initial-index","onClick"])):(p(),f("div",ne,t[31]||(t[31]=[v("span",null,"无图",-1)]))),v("div",ae," x"+_(r.quantity||1),1)]),v("div",oe,[v("div",ie,[b(E,{class:"product-icon sku-icon",size:12},{default:m((function(){return[b(P)]})),_:1}),v("span",{class:"product-text product-sku",onClick:function(e){return function(e){if(e&&"-"!==e)try{var t="https://www.ozon.ru/product/".concat(e,"/");window.open(t,"_blank"),V({type:"success",message:"正在打开产品页面: ".concat(e),duration:2e3})}catch(r){V({type:"error",message:"打开产品页面失败"})}else V({type:"warning",message:"SKU为空，无法跳转"})}(r.sku)},title:"点击查看产品: "+(r.sku||"-")},_(r.sku||"-"),9,le)]),v("div",ue,[b(E,{class:"product-icon name-icon",size:12},{default:m((function(){return[b(D)]})),_:1}),v("span",{class:C(["product-text product-name",ft(r.name)]),title:r.name},_(r.name),11,ce)])])])})),128)),0===dt(e.row).length?(p(),f("div",se,[t[34]||(t[34]=v("div",{class:"product-image-wrapper"},[v("div",{class:"no-image"},[v("span",null,"无图")]),v("div",{class:"product-quantity"}," x- ")],-1)),v("div",de,[v("div",fe,[b(E,{class:"product-icon sku-icon",size:12},{default:m((function(){return[b(P)]})),_:1}),t[32]||(t[32]=v("span",{class:"product-text"},"-",-1))]),v("div",pe,[b(E,{class:"product-icon name-icon",size:12},{default:m((function(){return[b(D)]})),_:1}),t[33]||(t[33]=v("span",{class:"product-text product-name"},"未知产品",-1))])])])):g("",!0)])]})),_:1}),b(s,{align:"left",label:"配货状态",prop:"distributionStatus","min-width":"180"},{default:m((function(e){return[v("span",{class:C(["status-text",(t=e.row.distributionStatus,["unprocessed","cancelled","error","failed","rejected"].includes(t)?"status-error":"status-normal")])},_(S(I)(e.row.distributionStatus,je.value)),3)];var t})),_:1}),b(s,{align:"left",label:"订单状态",prop:"status","min-width":"180"},{default:m((function(e){return[v("span",{class:C(["status-text",(t=e.row.status,["cancelled","canceled","error","failed","rejected","not_accepted","client_arbitration","arbitration"].includes(t)?"status-error":"status-normal")])},_(S(I)(e.row.status,Ie.value)),3)];var t})),_:1}),b(s,{align:"left",label:"时间",prop:"inProcessAt","min-width":"180"},{default:m((function(e){return[v("div",ve,[v("div",be,[t[35]||(t[35]=v("span",{class:"time-badge order-time-badge"},"下单",-1)),v("span",me,_(st(e.row.inProcessAt)||"-"),1)]),v("div",ge,[t[36]||(t[36]=v("span",{class:"time-badge ship-time-badge"},"发货",-1)),v("span",he,_(st(e.row.shipmentDate)||"-"),1)])])]})),_:1}),b(s,{align:"left",label:"操作",fixed:"right",width:"80"},{default:m((function(e){return[v("div",ye,[b(u,{type:"primary",link:"",class:"table-button",onClick:function(t){return at(e.row)}},{default:m((function(){return[b(E,{style:{"margin-right":"3px"}},{default:m((function(){return[b(O)]})),_:1}),t[37]||(t[37]=w("查看 "))]})),_:2},1032,["onClick"]),b(u,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:function(t){return We(e.row)}},{default:m((function(){return t[38]||(t[38]=[w(" 编辑 ")])})),_:2},1032,["onClick"]),b(u,{type:"primary",link:"",icon:"delete",onClick:function(t){return r=e.row,void z.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){Xe(r)}));var r}},{default:m((function(){return t[39]||(t[39]=[w(" 删除 ")])})),_:2},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"]),v("div",xe,[b(A,{layout:"total, sizes, prev, pager, next, jumper","current-page":Ae.value,"page-size":Te.value,"page-sizes":[10,30,50,100],total:Be.value,onCurrentChange:Re,onSizeChange:Me},null,8,["current-page","page-size","total"])])]),b(T,{"destroy-on-close":"",size:S(Se).drawerSize,modelValue:Ze.value,"onUpdate:modelValue":t[17]||(t[17]=function(e){return Ze.value=e}),"show-close":!1,"before-close":et},{header:m((function(){return[v("div",we,[v("span",ke,_("create"===He.value?"新增":"编辑"),1),v("div",null,[b(u,{loading:r.value,type:"primary",onClick:tt},{default:m((function(){return t[40]||(t[40]=[w("确 定")])})),_:1},8,["loading"]),b(u,{onClick:et},{default:m((function(){return t[41]||(t[41]=[w("取 消")])})),_:1})])])]})),default:m((function(){return[b(c,{model:Ve.value,"label-position":"top",ref_key:"elFormRef",ref:Ee,rules:ze,"label-width":"80px"},{default:m((function(){return[b(o,{label:"产品图:",prop:"orderImg"},{default:m((function(){return[b(q,{multiple:"",modelValue:Ve.value.orderImg,"onUpdate:modelValue":t[8]||(t[8]=function(e){return Ve.value.orderImg=e}),"file-type":"image"},null,8,["modelValue"])]})),_:1}),b(o,{label:"货件号:",prop:"postingNumber"},{default:m((function(){return[b(a,{modelValue:Ve.value.postingNumber,"onUpdate:modelValue":t[9]||(t[9]=function(e){return Ve.value.postingNumber=e}),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])]})),_:1}),b(o,{label:"下单时间:",prop:"inProcessAt"},{default:m((function(){return[b(B,{modelValue:Ve.value.inProcessAt,"onUpdate:modelValue":t[10]||(t[10]=function(e){return Ve.value.inProcessAt=e}),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),b(o,{label:"订单号:",prop:"orderNumber"},{default:m((function(){return[b(a,{modelValue:Ve.value.orderNumber,"onUpdate:modelValue":t[11]||(t[11]=function(e){return Ve.value.orderNumber=e}),clearable:!0,placeholder:"请输入订单号"},null,8,["modelValue"])]})),_:1}),b(o,{label:"物流:",prop:"tplProvider"},{default:m((function(){return[b(a,{modelValue:Ve.value.tplProvider,"onUpdate:modelValue":t[12]||(t[12]=function(e){return Ve.value.tplProvider=e}),clearable:!0,placeholder:"请输入物流"},null,8,["modelValue"])]})),_:1}),b(o,{label:"订单状态:",prop:"status"},{default:m((function(){return[b(l,{modelValue:Ve.value.status,"onUpdate:modelValue":t[13]||(t[13]=function(e){return Ve.value.status=e}),placeholder:"请选择订单状态",style:{width:"100%"},clearable:!0},{default:m((function(){return[(p(!0),f(h,null,y(Ie.value,(function(e,t){return p(),x(i,{key:t,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),b(o,{label:"配货状态:",prop:"distributionStatus"},{default:m((function(){return[b(l,{modelValue:Ve.value.distributionStatus,"onUpdate:modelValue":t[14]||(t[14]=function(e){return Ve.value.distributionStatus=e}),placeholder:"请选择配货状态",style:{width:"100%"},clearable:!0},{default:m((function(){return[(p(!0),f(h,null,y(je.value,(function(e,t){return p(),x(i,{key:t,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),b(o,{label:"国际单号:",prop:"trackingNumber"},{default:m((function(){return[b(a,{modelValue:Ve.value.trackingNumber,"onUpdate:modelValue":t[15]||(t[15]=function(e){return Ve.value.trackingNumber=e}),clearable:!0,placeholder:"请输入国际单号"},null,8,["modelValue"])]})),_:1}),b(o,{label:"发货日期:",prop:"shipmentDate"},{default:m((function(){return[b(B,{modelValue:Ve.value.shipmentDate,"onUpdate:modelValue":t[16]||(t[16]=function(e){return Ve.value.shipmentDate=e}),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["size","modelValue"]),b(T,{"destroy-on-close":"",size:S(Se).drawerSize,modelValue:nt.value,"onUpdate:modelValue":t[18]||(t[18]=function(e){return nt.value=e}),"show-close":!0,"before-close":ot,title:"查看"},{default:m((function(){return[b(Le,{column:1,border:""},{default:m((function(){return[b(Oe,{label:"产品图"},{default:m((function(){return[(p(!0),f(h,null,y(rt.value.orderImg,(function(e,t){return p(),x(j,{style:{width:"50px",height:"50px","margin-right":"10px"},"preview-src-list":S(N)(rt.value.orderImg),"initial-index":t,key:t,src:S(U)(e),fit:"cover"},null,8,["preview-src-list","initial-index","src"])})),128))]})),_:1}),b(Oe,{label:"货件号"},{default:m((function(){return[w(_(rt.value.postingNumber),1)]})),_:1}),b(Oe,{label:"下单时间"},{default:m((function(){return[w(_(rt.value.inProcessAt),1)]})),_:1}),b(Oe,{label:"订单号"},{default:m((function(){return[w(_(rt.value.orderNumber),1)]})),_:1}),b(Oe,{label:"物流"},{default:m((function(){return[w(_(rt.value.tplProvider),1)]})),_:1}),b(Oe,{label:"订单状态"},{default:m((function(){return[w(_(S(I)(rt.value.status,Ie.value)),1)]})),_:1}),b(Oe,{label:"配货状态"},{default:m((function(){return[w(_(S(I)(rt.value.distributionStatus,je.value)),1)]})),_:1}),b(Oe,{label:"国际单号"},{default:m((function(){return[w(_(rt.value.trackingNumber),1)]})),_:1}),b(Oe,{label:"发货日期"},{default:m((function(){return[w(_(rt.value.shipmentDate),1)]})),_:1})]})),_:1})]})),_:1},8,["size","modelValue"])])}}});e("default",n(_e,[["__scopeId","data-v-7cba740f"]]))}}}))}();
