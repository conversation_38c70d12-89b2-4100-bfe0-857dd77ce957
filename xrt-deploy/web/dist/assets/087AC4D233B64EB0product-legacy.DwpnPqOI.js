/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return n};var t,n={},r=Object.prototype,o=r.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},i=u.iterator||"@@iterator",c=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),u=new C(r||[]);return l(o,"_invoke",{value:z(e,n,u)}),o}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var v="suspendedStart",h="suspendedYield",m="executing",g="completed",y={};function b(){}function w(){}function x(){}var _={};d(_,i,(function(){return this}));var k=Object.getPrototypeOf,V=k&&k(k(S([])));V&&V!==r&&o.call(V,i)&&(_=V);var P=x.prototype=b.prototype=Object.create(_);function O(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function U(t,n){function r(a,l,u,i){var c=p(t[a],t,l);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==e(d)&&o.call(d,"__await")?n.resolve(d.__await).then((function(e){r("next",e,u,i)}),(function(e){r("throw",e,u,i)})):n.resolve(d).then((function(e){s.value=e,u(s)}),(function(e){return r("throw",e,u,i)}))}i(c.arg)}var a;l(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(o,o):o()}})}function z(e,n,r){var a=v;return function(o,l){if(a===m)throw Error("Generator is already running");if(a===g){if("throw"===o)throw l;return{value:t,done:!0}}for(r.method=o,r.arg=l;;){var u=r.delegate;if(u){var i=j(u,r);if(i){if(i===y)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===v)throw a=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var c=p(e,n,r);if("normal"===c.type){if(a=r.done?g:h,c.arg===y)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=g,r.method="throw",r.arg=c.arg)}}}function j(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,j(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var o=p(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,y;var l=o.arg;return l?l.done?(n[e.resultName]=l.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,y):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function W(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function S(n){if(n||""===n){var r=n[i];if(r)return r.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var a=-1,l=function e(){for(;++a<n.length;)if(o.call(n,a))return e.value=n[a],e.done=!1,e;return e.value=t,e.done=!0,e};return l.next=l}}throw new TypeError(e(n)+" is not iterable")}return w.prototype=x,l(P,"constructor",{value:x,configurable:!0}),l(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,d(e,s,"GeneratorFunction")),e.prototype=Object.create(P),e},n.awrap=function(e){return{__await:e}},O(U.prototype),d(U.prototype,c,(function(){return this})),n.AsyncIterator=U,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var l=new U(f(e,t,r,a),o);return n.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},O(P),d(P,s,"Generator"),d(P,i,(function(){return this})),d(P,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=S,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(W),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,a){return u.type="throw",u.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var l=this.tryEntries[a],u=l.completion;if("root"===l.tryLoc)return r("end");if(l.tryLoc<=this.prev){var i=o.call(l,"catchLoc"),c=o.call(l,"finallyLoc");if(i&&c){if(this.prev<l.catchLoc)return r(l.catchLoc,!0);if(this.prev<l.finallyLoc)return r(l.finallyLoc)}else if(i){if(this.prev<l.catchLoc)return r(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return r(l.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),W(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;W(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:S(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),y}},n}function o(e,t,n,r,a,o,l){try{var u=e[o](l),i=u.value}catch(e){return void n(e)}u.done?t(i):Promise.resolve(i).then(r,a)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var l=e.apply(t,n);function u(e){o(l,r,a,u,i,"next",e)}function i(e){o(l,r,a,u,i,"throw",e)}u(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,t){"use strict";var r,o,u,i,c,s,d,f,p,v,h,m,g,y,b,w,x,_,k;return{setters:[function(e){r=e.s,o=e._,u=e.a,i=e.r,c=e.g,s=e.c,d=e.o,f=e.b,p=e.f,v=e.w,h=e.h,m=e.l,g=e.t,y=e.i,b=e.v,w=e.aG,x=e.X,_=e.E,k=e.ab}],execute:function(){var t=document.createElement("style");t.textContent=".gva-card-box[data-v-77a04b95]{margin-bottom:20px}.product-card[data-v-77a04b95]{text-align:center}.product-item[data-v-77a04b95]{padding:10px}.product-label[data-v-77a04b95]{font-size:14px;color:#666;margin-bottom:8px}.product-value[data-v-77a04b95]{font-size:24px;font-weight:700;color:#333}.weight-highlight[data-v-77a04b95]{font-weight:700;color:#409eff}.form-tip[data-v-77a04b95]{font-size:12px;color:#999;margin-top:5px}.product-image-container[data-v-77a04b95]{display:flex;justify-content:center;align-items:center;height:60px}.product-image[data-v-77a04b95]{width:50px;height:50px;border-radius:4px;cursor:pointer}.image-slot[data-v-77a04b95]{display:flex;justify-content:center;align-items:center;width:50px;height:50px;background:#f5f7fa;color:#909399;font-size:20px;border-radius:4px}.no-image[data-v-77a04b95]{display:flex;flex-direction:column;justify-content:center;align-items:center;width:50px;height:50px;background:#f5f7fa;color:#909399;font-size:12px;border-radius:4px}.no-image .el-icon[data-v-77a04b95]{font-size:16px;margin-bottom:2px}\n/*$vite$:1*/",document.head.appendChild(t);var V=function(e){return r({url:"/product/createProduct",method:"post",data:e})},P={class:"gva-search-box"},O={class:"gva-card-box"},U={class:"product-item"},z={class:"product-value"},j={class:"product-item"},L={class:"product-value"},W={class:"product-item"},C={class:"product-value"},S={class:"gva-table-box"},E={class:"gva-btn-list"},N={style:{display:"flex","align-items":"center",gap:"12px"}},A={style:{"flex-shrink":"0"}},D={style:{width:"60px",height:"60px",background:"#f5f5f5","border-radius":"4px",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column","font-size":"12px",color:"#999"}},R={key:1,style:{width:"60px",height:"60px",background:"#f5f5f5","border-radius":"4px",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column","font-size":"12px",color:"#999"}},F={style:{flex:"1","min-width":"0"}},I={style:{"margin-bottom":"4px"}},T=["title"],B={key:0,class:"weight-highlight"},G={key:0,class:"weight-highlight"},K={key:0,style:{color:"#0369a1","font-weight":"500"}},Y={key:0,style:{color:"#dc2626","font-weight":"500"}},$={class:"gva-pagination"},q={class:"dialog-footer"},H={class:"dialog-footer"},X={class:"dialog-footer"},J=Object.assign({name:"Product"},{__name:"product",setup:function(e){var t=u(),o=u(),J=u(),M=u(),Q=u(1),Z=u(0),ee=u(10),te=u([]),ne=u({}),re=i({}),ae=u({totalProducts:0,activeProducts:0,inactiveProducts:0,avgWeight:0,totalOrders:0}),oe=u([]),le=function(e){oe.value=e},ue=function(){ne.value={},de()},ie=function(){var e;null===(e=o.value)||void 0===e||e.validate(function(){var e=l(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:Q.value=1,de();case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},ce=function(e){ee.value=e,de()},se=function(e){Q.value=e,de()},de=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a=n({page:Q.value,pageSize:ee.value},ne.value),r({url:"/product/getProductList",method:"get",params:a});case 2:0===(t=e.sent).code&&(te.value=t.data.list,Z.value=t.data.total);case 4:case"end":return e.stop()}var a}),e)})));return function(){return e.apply(this,arguments)}}(),fe=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r({url:"/product/getProductSummary",method:"get",params:void 0});case 2:0===(t=e.sent).code&&(ae.value=t.data);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),pe=u({sku:"",actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",imageUrl:"",notes:"",isActive:!0}),ve=i({sku:[{required:!0,message:"请输入SKU",trigger:"blur"}]}),he=u(!1),me=u(""),ge=function(){me.value="create",he.value=!0},ye=function(){he.value=!1,pe.value={sku:"",actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",imageUrl:"",notes:"",isActive:!0}},be=function(){var e=l(a().mark((function e(){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:null===(n=t.value)||void 0===n||n.validate(function(){var e=l(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return e.abrupt("return");case 3:e.t0=me.value,e.next="create"===e.t0?7:"update"===e.t0?12:17;break;case 7:return e.next=10,V(pe.value);case 10:return n=e.sent,e.abrupt("break",22);case 12:return e.next=15,a=pe.value,r({url:"/product/updateProduct",method:"put",data:a});case 15:return n=e.sent,e.abrupt("break",22);case 17:return e.next=20,V(pe.value);case 20:return n=e.sent,e.abrupt("break",22);case 22:0===n.code?(_({type:"success",message:"创建/更改成功"}),ye(),de(),fe()):_({type:"error",message:n.message||"操作失败"});case 24:case"end":return e.stop()}var a}),e)})));return function(t){return e.apply(this,arguments)}}());case 3:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),we=function(){var e=l(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,a={ID:t.ID},r({url:"/product/findProduct",method:"get",params:a});case 3:n=e.sent,me.value="update",0===n.code?(pe.value=n.data,he.value=!0):_({type:"error",message:"获取产品信息失败"});case 7:case"end":return e.stop()}var a}),e)})));return function(t){return e.apply(this,arguments)}}(),xe=function(){var e=l(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n={ID:t.ID},r({url:"/product/deleteProduct",method:"delete",params:n});case 2:0===e.sent.code&&(_({type:"success",message:"删除成功"}),1===te.value.length&&Q.value>1&&Q.value--,de(),fe());case 4:case"end":return e.stop()}var n}),e)})));return function(t){return e.apply(this,arguments)}}(),_e=function(){var e=l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=[],0!==oe.value.length){e.next=4;break}return _({type:"warning",message:"请选择要删除的数据"}),e.abrupt("return");case 4:return oe.value&&oe.value.map((function(e){t.push(e.ID)})),e.next=7,r({url:"/product/deleteProductByIds",method:"delete",params:{IDs:t}});case 7:0===e.sent.code&&(_({type:"success",message:"删除成功"}),te.value.length===t.length&&Q.value>1&&Q.value--,de(),fe());case 9:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ke=u(!1),Ve=u({actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",isActive:null}),Pe=function(){Ve.value={actualWeight:null,ozonWeight:null,costPrice:null,commissionRate:null,shopName:"",brand:"",description:"",isActive:null},ke.value=!0},Oe=function(){var e=l(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=oe.value.map((function(e){return e.ID})),n={ids:t},null!==Ve.value.actualWeight&&(n.actualWeight=Ve.value.actualWeight),null!==Ve.value.ozonWeight&&(n.ozonWeight=Ve.value.ozonWeight),Ve.value.shopName&&(n.shopName=Ve.value.shopName),Ve.value.brand&&(n.brand=Ve.value.brand),Ve.value.description&&(n.description=Ve.value.description),null!==Ve.value.isActive&&(n.isActive=Ve.value.isActive),e.next=10,r({url:"/product/batchUpdateProduct",method:"put",data:n});case 10:0===e.sent.code&&(_({type:"success",message:"批量更新成功"}),ke.value=!1,de(),fe());case 12:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ue=u(!1),ze=u(!1),je=u({forceUpdate:!1}),Le=function(){je.value={forceUpdate:!1},Ue.value=!0},We=function(){var e=l(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ze.value=!0,e.prev=1,e.next=4,n=je.value,r({url:"/product/syncProductsFromOrders",method:"post",data:n});case 4:0===(t=e.sent).code&&(_({type:"success",message:t.msg}),Ue.value=!1,de(),fe());case 6:return e.prev=6,ze.value=!1,e.finish(6);case 9:case"end":return e.stop()}var n}),e,null,[[1,,6,9]])})));return function(){return e.apply(this,arguments)}}(),Ce=function(){var e=l(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:k.confirm("确定要从订单同步产品图片吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(l(a().mark((function e(){var t,n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t=_({message:"正在同步图片...",type:"info",duration:0}),e.prev=1,e.next=4,r({url:"/product/syncProductImagesFromOrders",method:"post"});case 4:n=e.sent,t.close(),0===n.code&&(_({type:"success",message:n.msg}),de(),fe()),e.next=13;break;case 9:e.prev=9,e.t0=e.catch(1),t.close(),_({type:"error",message:"同步图片失败"});case 13:case"end":return e.stop()}}),e,null,[[1,9]])}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Se=function(e){return e?new Date(e).toLocaleDateString("zh-CN"):""},Ee=function(e){if(!e)return{color:"#999",fontSize:"12px",fontStyle:"italic"};var t=e.length,n="13px";return t>50?n="11px":t>30&&(n="12px"),{fontSize:n,lineHeight:"1.3",color:"#333",wordBreak:"break-word",display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden"}};return de(),fe(),function(e,n){var r=c("el-input"),a=c("el-form-item"),l=c("el-option"),u=c("el-select"),i=c("el-button"),V=c("el-form"),de=c("el-card"),Ne=c("el-col"),Ae=c("el-row"),De=c("el-table-column"),Re=c("el-icon"),Fe=c("el-image"),Ie=c("el-link"),Te=c("el-tag"),Be=c("el-table"),Ge=c("el-pagination"),Ke=c("el-input-number"),Ye=c("el-switch"),$e=c("el-dialog");return d(),s("div",null,[f("div",P,[p(V,{ref_key:"elSearchFormRef",ref:o,inline:!0,model:ne.value,class:"demo-form-inline",rules:re,onKeyup:m(ie,["enter"])},{default:v((function(){return[p(a,{label:"SKU",prop:"sku"},{default:v((function(){return[p(r,{modelValue:ne.value.sku,"onUpdate:modelValue":n[0]||(n[0]=function(e){return ne.value.sku=e}),placeholder:"搜索SKU"},null,8,["modelValue"])]})),_:1}),p(a,{label:"店铺",prop:"shopName"},{default:v((function(){return[p(r,{modelValue:ne.value.shopName,"onUpdate:modelValue":n[1]||(n[1]=function(e){return ne.value.shopName=e}),placeholder:"搜索店铺"},null,8,["modelValue"])]})),_:1}),p(a,{label:"品牌",prop:"brand"},{default:v((function(){return[p(r,{modelValue:ne.value.brand,"onUpdate:modelValue":n[2]||(n[2]=function(e){return ne.value.brand=e}),placeholder:"搜索品牌"},null,8,["modelValue"])]})),_:1}),p(a,{label:"状态",prop:"isActive"},{default:v((function(){return[p(u,{modelValue:ne.value.isActive,"onUpdate:modelValue":n[3]||(n[3]=function(e){return ne.value.isActive=e}),placeholder:"选择状态",clearable:""},{default:v((function(){return[p(l,{label:"启用",value:!0}),p(l,{label:"禁用",value:!1})]})),_:1},8,["modelValue"])]})),_:1}),p(a,null,{default:v((function(){return[p(i,{type:"primary",icon:"search",onClick:ie},{default:v((function(){return n[29]||(n[29]=[h("查询")])})),_:1}),p(i,{icon:"refresh",onClick:ue},{default:v((function(){return n[30]||(n[30]=[h("重置")])})),_:1})]})),_:1})]})),_:1},8,["model","rules"])]),f("div",O,[p(Ae,{gutter:20},{default:v((function(){return[p(Ne,{span:8},{default:v((function(){return[p(de,{class:"product-card"},{default:v((function(){return[f("div",U,[n[31]||(n[31]=f("div",{class:"product-label"},"总产品数",-1)),f("div",z,g(ae.value.totalProducts),1)])]})),_:1})]})),_:1}),p(Ne,{span:8},{default:v((function(){return[p(de,{class:"product-card"},{default:v((function(){return[f("div",j,[n[32]||(n[32]=f("div",{class:"product-label"},"启用产品",-1)),f("div",L,g(ae.value.activeProducts),1)])]})),_:1})]})),_:1}),p(Ne,{span:8},{default:v((function(){return[p(de,{class:"product-card"},{default:v((function(){return[f("div",W,[n[33]||(n[33]=f("div",{class:"product-label"},"禁用产品",-1)),f("div",C,g(ae.value.inactiveProducts),1)])]})),_:1})]})),_:1})]})),_:1})]),f("div",S,[f("div",E,[p(i,{type:"primary",icon:"plus",onClick:ge},{default:v((function(){return n[34]||(n[34]=[h("新增产品")])})),_:1}),p(i,{type:"success",icon:"refresh",onClick:fe},{default:v((function(){return n[35]||(n[35]=[h("刷新汇总")])})),_:1}),p(i,{type:"info",icon:"download",onClick:Le},{default:v((function(){return n[36]||(n[36]=[h("从订单同步")])})),_:1}),p(i,{type:"primary",icon:"picture",onClick:Ce},{default:v((function(){return n[37]||(n[37]=[h("同步图片")])})),_:1}),p(i,{type:"warning",icon:"edit",disabled:!oe.value.length,onClick:Pe},{default:v((function(){return n[38]||(n[38]=[h("批量编辑")])})),_:1},8,["disabled"]),p(i,{type:"danger",icon:"delete",disabled:!oe.value.length,onClick:_e},{default:v((function(){return n[39]||(n[39]=[h("批量删除")])})),_:1},8,["disabled"])]),p(Be,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:te.value,"row-key":"ID",onSelectionChange:le},{default:v((function(){return[p(De,{type:"selection",width:"55"}),p(De,{align:"left",label:"店铺",prop:"shopName",width:"120"}),p(De,{align:"left",label:"产品信息",prop:"productInfo","min-width":"280"},{default:v((function(e){return[f("div",N,[f("div",A,[e.row.imageUrl?(d(),y(Fe,{key:0,src:e.row.imageUrl,"preview-src-list":[e.row.imageUrl],fit:"cover",style:{width:"60px",height:"60px","border-radius":"4px",cursor:"pointer"},"preview-teleported":!0},{error:v((function(){return[f("div",D,[p(Re,null,{default:v((function(){return[p(b(w))]})),_:1})])]})),_:2},1032,["src","preview-src-list"])):(d(),s("div",R,[p(Re,null,{default:v((function(){return[p(b(w))]})),_:1}),n[40]||(n[40]=f("span",{style:{"font-size":"10px","margin-top":"2px"}},"无图片",-1))]))]),f("div",F,[f("div",I,[p(Ie,{type:"primary",onClick:function(t){return n=e.row.sku,void _({type:"info",message:"查看产品详情: ".concat(n)});var n},style:{"font-size":"13px","font-weight":"500"}},{default:v((function(){return[h(g(e.row.sku),1)]})),_:2},1032,["onClick"])]),f("div",{style:x(Ee(e.row.productName)),title:e.row.productName},g(e.row.productName||"未设置产品名称"),13,T)])])]})),_:1}),p(De,{align:"left",label:"实际重量(kg)",prop:"actualWeight",width:"120"},{default:v((function(e){return[e.row.actualWeight?(d(),s("span",B,g(e.row.actualWeight)+"kg",1)):(d(),y(Te,{key:1,type:"warning",size:"small"},{default:v((function(){return n[41]||(n[41]=[h("未设置")])})),_:1}))]})),_:1}),p(De,{align:"left",label:"Ozon重量(kg)",prop:"ozonWeight",width:"120"},{default:v((function(e){return[e.row.ozonWeight?(d(),s("span",G,g(e.row.ozonWeight)+"kg",1)):(d(),y(Te,{key:1,type:"info",size:"small"},{default:v((function(){return n[42]||(n[42]=[h("未设置")])})),_:1}))]})),_:1}),p(De,{align:"center",label:"成本价格",prop:"costPrice",width:"100"},{default:v((function(e){return[e.row.costPrice?(d(),s("span",K," ¥"+g(parseFloat(e.row.costPrice).toFixed(2)),1)):(d(),y(Te,{key:1,type:"warning",size:"small"},{default:v((function(){return n[43]||(n[43]=[h("未设置")])})),_:1}))]})),_:1}),p(De,{align:"center",label:"佣金比率",prop:"commissionRate",width:"100"},{default:v((function(e){return[e.row.commissionRate?(d(),s("span",Y,g((100*parseFloat(e.row.commissionRate)).toFixed(1))+"% ",1)):(d(),y(Te,{key:1,type:"warning",size:"small"},{default:v((function(){return n[44]||(n[44]=[h("未设置")])})),_:1}))]})),_:1}),p(De,{align:"left",label:"品牌",prop:"brand",width:"120"}),p(De,{align:"left",label:"状态",prop:"isActive",width:"80"},{default:v((function(e){return[p(Te,{type:e.row.isActive?"success":"danger"},{default:v((function(){return[h(g(e.row.isActive?"启用":"禁用"),1)]})),_:2},1032,["type"])]})),_:1}),p(De,{align:"left",label:"首次订单",prop:"firstOrderDate",width:"120"},{default:v((function(e){return[h(g(Se(e.row.firstOrderDate)),1)]})),_:1}),p(De,{align:"left",label:"最后订单",prop:"lastOrderDate",width:"120"},{default:v((function(e){return[h(g(Se(e.row.lastOrderDate)),1)]})),_:1}),p(De,{align:"center",label:"订单数",prop:"totalOrderCount",width:"80"},{default:v((function(e){return[p(Te,{type:"info",size:"small"},{default:v((function(){return[h(g(e.row.totalOrderCount||0),1)]})),_:2},1024)]})),_:1}),p(De,{align:"left",label:"操作",fixed:"right",width:"160"},{default:v((function(e){return[p(i,{type:"primary",link:"",icon:"edit",onClick:function(t){return we(e.row)}},{default:v((function(){return n[45]||(n[45]=[h("编辑")])})),_:2},1032,["onClick"]),p(i,{type:"danger",link:"",icon:"delete",onClick:function(t){return n=e.row,void k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){xe(n)}));var n}},{default:v((function(){return n[46]||(n[46]=[h("删除")])})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"]),f("div",$,[p(Ge,{layout:"total, sizes, prev, pager, next, jumper","current-page":Q.value,"page-size":ee.value,"page-sizes":[10,30,50,100],total:Z.value,onCurrentChange:se,onSizeChange:ce},null,8,["current-page","page-size","total"])])]),p($e,{modelValue:he.value,"onUpdate:modelValue":n[15]||(n[15]=function(e){return he.value=e}),title:"create"===me.value?"新增产品":"编辑产品",width:"600px"},{footer:v((function(){return[f("div",q,[p(i,{onClick:ye},{default:v((function(){return n[48]||(n[48]=[h("取消")])})),_:1}),p(i,{type:"primary",onClick:be},{default:v((function(){return n[49]||(n[49]=[h("确定")])})),_:1})])]})),default:v((function(){return[p(V,{ref_key:"elFormRef",ref:t,model:pe.value,rules:ve,"label-width":"100px"},{default:v((function(){return[p(a,{label:"SKU",prop:"sku"},{default:v((function(){return[p(r,{modelValue:pe.value.sku,"onUpdate:modelValue":n[4]||(n[4]=function(e){return pe.value.sku=e}),placeholder:"请输入SKU",disabled:"update"===me.value},null,8,["modelValue","disabled"])]})),_:1}),p(a,{label:"实际重量(kg)",prop:"actualWeight"},{default:v((function(){return[p(Ke,{modelValue:pe.value.actualWeight,"onUpdate:modelValue":n[5]||(n[5]=function(e){return pe.value.actualWeight=e}),precision:3,min:0,placeholder:"请输入实际重量",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(a,{label:"Ozon重量(kg)",prop:"ozonWeight"},{default:v((function(){return[p(Ke,{modelValue:pe.value.ozonWeight,"onUpdate:modelValue":n[6]||(n[6]=function(e){return pe.value.ozonWeight=e}),precision:3,min:0,placeholder:"请输入Ozon重量",style:{width:"100%"},disabled:"update"===me.value},null,8,["modelValue","disabled"])]})),_:1}),p(a,{label:"成本价格",prop:"costPrice"},{default:v((function(){return[p(Ke,{modelValue:pe.value.costPrice,"onUpdate:modelValue":n[7]||(n[7]=function(e){return pe.value.costPrice=e}),precision:2,min:0,placeholder:"请输入成本价格",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(a,{label:"佣金比率",prop:"commissionRate"},{default:v((function(){return[p(Ke,{modelValue:pe.value.commissionRate,"onUpdate:modelValue":n[8]||(n[8]=function(e){return pe.value.commissionRate=e}),precision:4,min:0,max:1,placeholder:"请输入佣金比率(0-1)",style:{width:"100%"}},null,8,["modelValue"]),n[47]||(n[47]=f("div",{style:{"font-size":"12px",color:"#909399","margin-top":"4px"}}," 例如：0.12 表示 12% ",-1))]})),_:1}),p(a,{label:"店铺",prop:"shopName"},{default:v((function(){return[p(r,{modelValue:pe.value.shopName,"onUpdate:modelValue":n[9]||(n[9]=function(e){return pe.value.shopName=e}),placeholder:"请输入店铺名称",disabled:"update"===me.value},null,8,["modelValue","disabled"])]})),_:1}),p(a,{label:"品牌",prop:"brand"},{default:v((function(){return[p(r,{modelValue:pe.value.brand,"onUpdate:modelValue":n[10]||(n[10]=function(e){return pe.value.brand=e}),placeholder:"请输入品牌"},null,8,["modelValue"])]})),_:1}),p(a,{label:"产品描述",prop:"description"},{default:v((function(){return[p(r,{modelValue:pe.value.description,"onUpdate:modelValue":n[11]||(n[11]=function(e){return pe.value.description=e}),type:"textarea",placeholder:"请输入产品描述"},null,8,["modelValue"])]})),_:1}),p(a,{label:"图片URL",prop:"imageUrl"},{default:v((function(){return[p(r,{modelValue:pe.value.imageUrl,"onUpdate:modelValue":n[12]||(n[12]=function(e){return pe.value.imageUrl=e}),placeholder:"请输入图片URL"},null,8,["modelValue"])]})),_:1}),p(a,{label:"备注",prop:"notes"},{default:v((function(){return[p(r,{modelValue:pe.value.notes,"onUpdate:modelValue":n[13]||(n[13]=function(e){return pe.value.notes=e}),type:"textarea",placeholder:"请输入备注"},null,8,["modelValue"])]})),_:1}),p(a,{label:"状态",prop:"isActive"},{default:v((function(){return[p(Ye,{modelValue:pe.value.isActive,"onUpdate:modelValue":n[14]||(n[14]=function(e){return pe.value.isActive=e}),"active-text":"启用","inactive-text":"禁用"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue","title"]),p($e,{modelValue:ke.value,"onUpdate:modelValue":n[25]||(n[25]=function(e){return ke.value=e}),title:"批量编辑产品",width:"500px"},{footer:v((function(){return[f("div",H,[p(i,{onClick:n[24]||(n[24]=function(e){return ke.value=!1})},{default:v((function(){return n[51]||(n[51]=[h("取消")])})),_:1}),p(i,{type:"primary",onClick:Oe},{default:v((function(){return n[52]||(n[52]=[h("确定")])})),_:1})])]})),default:v((function(){return[p(V,{ref_key:"batchFormRef",ref:J,model:Ve.value,"label-width":"120px"},{default:v((function(){return[p(a,{label:"实际重量(kg)"},{default:v((function(){return[p(Ke,{modelValue:Ve.value.actualWeight,"onUpdate:modelValue":n[16]||(n[16]=function(e){return Ve.value.actualWeight=e}),precision:3,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(a,{label:"Ozon重量(kg)"},{default:v((function(){return[p(Ke,{modelValue:Ve.value.ozonWeight,"onUpdate:modelValue":n[17]||(n[17]=function(e){return Ve.value.ozonWeight=e}),precision:3,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(a,{label:"成本价格"},{default:v((function(){return[p(Ke,{modelValue:Ve.value.costPrice,"onUpdate:modelValue":n[18]||(n[18]=function(e){return Ve.value.costPrice=e}),precision:2,min:0,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(a,{label:"佣金比率"},{default:v((function(){return[p(Ke,{modelValue:Ve.value.commissionRate,"onUpdate:modelValue":n[19]||(n[19]=function(e){return Ve.value.commissionRate=e}),precision:4,min:0,max:1,placeholder:"留空表示不修改",style:{width:"100%"}},null,8,["modelValue"]),n[50]||(n[50]=f("div",{style:{"font-size":"12px",color:"#909399","margin-top":"4px"}}," 例如：0.12 表示 12% ",-1))]})),_:1}),p(a,{label:"店铺"},{default:v((function(){return[p(r,{modelValue:Ve.value.shopName,"onUpdate:modelValue":n[20]||(n[20]=function(e){return Ve.value.shopName=e}),placeholder:"留空表示不修改"},null,8,["modelValue"])]})),_:1}),p(a,{label:"品牌"},{default:v((function(){return[p(r,{modelValue:Ve.value.brand,"onUpdate:modelValue":n[21]||(n[21]=function(e){return Ve.value.brand=e}),placeholder:"留空表示不修改"},null,8,["modelValue"])]})),_:1}),p(a,{label:"产品描述"},{default:v((function(){return[p(r,{modelValue:Ve.value.description,"onUpdate:modelValue":n[22]||(n[22]=function(e){return Ve.value.description=e}),type:"textarea",placeholder:"留空表示不修改"},null,8,["modelValue"])]})),_:1}),p(a,{label:"状态"},{default:v((function(){return[p(u,{modelValue:Ve.value.isActive,"onUpdate:modelValue":n[23]||(n[23]=function(e){return Ve.value.isActive=e}),placeholder:"留空表示不修改",clearable:""},{default:v((function(){return[p(l,{label:"启用",value:!0}),p(l,{label:"禁用",value:!1})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue"]),p($e,{modelValue:Ue.value,"onUpdate:modelValue":n[28]||(n[28]=function(e){return Ue.value=e}),title:"从订单同步产品",width:"400px"},{footer:v((function(){return[f("div",X,[p(i,{onClick:n[27]||(n[27]=function(e){return Ue.value=!1})},{default:v((function(){return n[54]||(n[54]=[h("取消")])})),_:1}),p(i,{type:"primary",onClick:We,loading:ze.value},{default:v((function(){return n[55]||(n[55]=[h("开始同步")])})),_:1},8,["loading"])])]})),default:v((function(){return[p(V,{ref_key:"syncFormRef",ref:M,model:je.value,"label-width":"120px"},{default:v((function(){return[p(a,{label:"强制更新"},{default:v((function(){return[p(Ye,{modelValue:je.value.forceUpdate,"onUpdate:modelValue":n[26]||(n[26]=function(e){return je.value.forceUpdate=e}),"active-text":"是","inactive-text":"否"},null,8,["modelValue"]),n[53]||(n[53]=f("div",{class:"form-tip"},"开启后会更新已存在产品的名称等信息",-1))]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue"])])}}});e("default",o(J,[["__scopeId","data-v-77a04b95"]]))}}}))}();
