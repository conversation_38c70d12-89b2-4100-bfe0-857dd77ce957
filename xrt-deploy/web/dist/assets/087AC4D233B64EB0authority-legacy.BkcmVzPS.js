/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},a=Object.prototype,o=a.hasOwnProperty,u=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},l=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",f=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(r){s=function(t,e,r){return t[e]=r}}function d(t,e,r,n){var a=e&&e.prototype instanceof w?e:w,o=Object.create(a.prototype),i=new A(n||[]);return u(o,"_invoke",{value:N(t,r,i)}),o}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=d;var y="suspendedStart",p="suspendedYield",v="executing",m="completed",g={};function w(){}function b(){}function I(){}var x={};s(x,l,(function(){return this}));var _=Object.getPrototypeOf,k=_&&_(_(O([])));k&&k!==a&&o.call(k,l)&&(x=k);var E=I.prototype=w.prototype=Object.create(x);function C(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function L(e,r){function n(a,u,i,l){var c=h(e[a],e,u);if("throw"!==c.type){var f=c.arg,s=f.value;return s&&"object"==t(s)&&o.call(s,"__await")?r.resolve(s.__await).then((function(t){n("next",t,i,l)}),(function(t){n("throw",t,i,l)})):r.resolve(s).then((function(t){f.value=t,i(f)}),(function(t){return n("throw",t,i,l)}))}l(c.arg)}var a;u(this,"_invoke",{value:function(t,e){function o(){return new r((function(r,a){n(t,e,r,a)}))}return a=a?a.then(o,o):o()}})}function N(t,e,n){var a=y;return function(o,u){if(a===v)throw Error("Generator is already running");if(a===m){if("throw"===o)throw u;return{value:r,done:!0}}for(n.method=o,n.arg=u;;){var i=n.delegate;if(i){var l=j(i,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===y)throw a=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=v;var c=h(t,e,n);if("normal"===c.type){if(a=n.done?m:p,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(a=m,n.method="throw",n.arg=c.arg)}}}function j(t,e){var n=e.method,a=t.iterator[n];if(a===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,j(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=h(a,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,g;var u=o.arg;return u?u.done?(e[t.resultName]=u.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):u:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function B(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function V(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function A(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(B,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[l];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var a=-1,u=function t(){for(;++a<e.length;)if(o.call(e,a))return t.value=e[a],t.done=!1,t;return t.value=r,t.done=!0,t};return u.next=u}}throw new TypeError(t(e)+" is not iterable")}return b.prototype=I,u(E,"constructor",{value:I,configurable:!0}),u(I,"constructor",{value:b,configurable:!0}),b.displayName=s(I,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,I):(t.__proto__=I,s(t,f,"GeneratorFunction")),t.prototype=Object.create(E),t},n.awrap=function(t){return{__await:t}},C(L.prototype),s(L.prototype,c,(function(){return this})),n.AsyncIterator=L,n.async=function(t,e,r,a,o){void 0===o&&(o=Promise);var u=new L(d(t,e,r,a),o);return n.isGeneratorFunction(e)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},C(E),s(E,f,"Generator"),s(E,l,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=O,A.prototype={constructor:A,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(V),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,a){return i.type="throw",i.arg=t,e.next=n,a&&(e.method="next",e.arg=r),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],i=u.completion;if("root"===u.tryLoc)return n("end");if(u.tryLoc<=this.prev){var l=o.call(u,"catchLoc"),c=o.call(u,"finallyLoc");if(l&&c){if(this.prev<u.catchLoc)return n(u.catchLoc,!0);if(this.prev<u.finallyLoc)return n(u.finallyLoc)}else if(l){if(this.prev<u.catchLoc)return n(u.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return n(u.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=e&&e<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=t,u.arg=e,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(u)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),V(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var a=n.arg;V(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:O(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,a,o,u){try{var i=t[o](u),l=i.value}catch(t){return void r(t)}i.done?e(l):Promise.resolve(l).then(n,a)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(a,o){var u=t.apply(e,n);function i(t){r(u,a,o,i,l,"next",t)}function l(t){r(u,a,o,i,l,"throw",t)}i(void 0)}))}}System.register(["./087AC4D233B64EB0authority-legacy.DVZAS3a8.js","./087AC4D233B64EB0menus-legacy.Bv_PZayP.js","./087AC4D233B64EB0apis-legacy.CBk5vHmG.js","./087AC4D233B64EB0datas-legacy.53wDuUbN.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0authorityBtn-legacy.cvbwrcdH.js","./087AC4D233B64EB0api-legacy.E3o43UgG.js"],(function(t,r){"use strict";var a,o,u,i,l,c,f,s,d,h,y,p,v,m,g,w,b,I,x,_,k,E,C,L;return{setters:[function(t){a=t.g,o=t.d,u=t.c,i=t.u,l=t.a},function(t){c=t.default},function(t){f=t.default},function(t){s=t.default},function(t){d=t._},function(t){h=t.a,y=t.I,p=t.g,v=t.c,m=t.o,g=t.f,w=t.b,b=t.i,I=t.d,x=t.w,_=t.h,k=t.t,E=t.v,C=t.ab,L=t.E},null,null],execute:function(){var r=document.createElement("style");r.textContent=".authority .el-input-number{margin-left:15px}.authority .el-input-number span{display:none}.tree-content{margin-top:10px;height:calc(100vh - 158px);overflow:auto}\n/*$vite$:1*/",document.head.appendChild(r);var N={class:"authority"},j={class:"gva-table-box"},B={class:"gva-btn-list"},V={class:"flex justify-between items-center"},A={class:"text-lg"};t("default",Object.assign({name:"Authority"},{__name:"authority",setup:function(t){var r=h([{authorityId:0,authorityName:"根角色/严格模式下为当前角色"}]),O=h(!1),S=h("add"),D=h({}),P=y(),G=h("新增角色"),T=h(!1),F=h(!1),z=h({}),U=h({authorityId:0,authorityName:"",parentId:0}),q=h({authorityId:[{required:!0,message:"请输入角色ID",trigger:"blur"},{validator:function(t,e,r){return/^[0-9]*[1-9][0-9]*$/.test(e)?r():r(new Error("请输入正整数"))},trigger:"blur",message:"必须为正整数"}],authorityName:[{required:!0,message:"请输入角色名",trigger:"blur"}],parentId:[{required:!0,message:"请选择父角色",trigger:"blur"}]}),R=h([]),$=function(){var t=n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a();case 2:0===(r=t.sent).code&&(R.value=r.data);case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();$();var Y=function(t,e){D.value[t]=e},H=h(null),W=h(null),X=h(null),J=function(t,e){var r=[H,W,X];e&&r[e].value.needConfirm&&(r[e].value.enterAndNext(),r[e].value.needConfirm=!1)},K=h(null),M=function(){K.value&&K.value.resetFields(),U.value={authorityId:0,authorityName:"",parentId:0}},Q=function(){M(),T.value=!1,F.value=!1},Z=function(){K.value.validate(function(){var t=n(e().mark((function t(r){var n;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!r){t.next=27;break}U.value.authorityId=Number(U.value.authorityId),t.t0=S.value,t.next="add"===t.t0?5:"edit"===t.t0?10:"copy"===t.t0?15:25;break;case 5:return t.next=7,l(U.value);case 7:return 0===t.sent.code&&(L({type:"success",message:"添加成功!"}),$(),Q()),t.abrupt("break",25);case 10:return t.next=12,i(U.value);case 12:return 0===t.sent.code&&(L({type:"success",message:"添加成功!"}),$(),Q()),t.abrupt("break",25);case 15:return(n={authority:{authorityId:0,authorityName:"",datauthorityId:[],parentId:0},oldAuthorityId:0}).authority.authorityId=U.value.authorityId,n.authority.authorityName=U.value.authorityName,n.authority.parentId=U.value.parentId,n.authority.dataAuthorityId=z.value.dataAuthorityId,n.oldAuthorityId=z.value.authorityId,t.next=23,u(n);case 23:0===t.sent.code&&(L({type:"success",message:"复制成功！"}),$());case 25:M(),T.value=!1;case 27:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},tt=function(){r.value=[{authorityId:0,authorityName:"根角色(严格模式下为当前用户角色)"}],et(R.value,r.value,!1)},et=function(t,e,r){t&&t.forEach((function(t){if(t.children&&t.children.length){var n={authorityId:t.authorityId,authorityName:t.authorityName,disabled:r||t.authorityId===U.value.authorityId,children:[]};et(t.children,n.children,r||t.authorityId===U.value.authorityId),e.push(n)}else{var a={authorityId:t.authorityId,authorityName:t.authorityName,disabled:r||t.authorityId===U.value.authorityId};e.push(a)}}))},rt=function(t){M(),G.value="新增角色",S.value="add",U.value.parentId=t,tt(),T.value=!0};return function(t,a){var u=p("el-button"),i=p("el-table-column"),l=p("el-table"),h=p("el-cascader"),y=p("el-form-item"),F=p("el-input"),M=p("el-form"),et=p("el-drawer"),nt=p("el-tab-pane"),at=p("el-tabs");return m(),v("div",N,[g(d,{title:"注：右上角头像下拉可切换角色"}),w("div",j,[w("div",B,[g(u,{type:"primary",icon:"plus",onClick:a[0]||(a[0]=function(t){return rt(0)})},{default:x((function(){return a[6]||(a[6]=[_("新增角色")])})),_:1})]),g(l,{data:R.value,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"authorityId",style:{width:"100%"}},{default:x((function(){return[g(i,{label:"角色ID","min-width":"180",prop:"authorityId"}),g(i,{align:"left",label:"角色名称","min-width":"180",prop:"authorityName"}),g(i,{align:"left",label:"操作",width:"460"},{default:x((function(t){return[g(u,{icon:"setting",type:"primary",link:"",onClick:function(e){return r=t.row,O.value=!0,void(D.value=r);var r}},{default:x((function(){return a[7]||(a[7]=[_("设置权限")])})),_:2},1032,["onClick"]),g(u,{icon:"plus",type:"primary",link:"",onClick:function(e){return rt(t.row.authorityId)}},{default:x((function(){return a[8]||(a[8]=[_("新增子角色")])})),_:2},1032,["onClick"]),g(u,{icon:"copy-document",type:"primary",link:"",onClick:function(e){return function(t){for(var e in tt(),G.value="拷贝角色",S.value="copy",U.value)U.value[e]=t[e];z.value=t,T.value=!0}(t.row)}},{default:x((function(){return a[9]||(a[9]=[_("拷贝")])})),_:2},1032,["onClick"]),g(u,{icon:"edit",type:"primary",link:"",onClick:function(e){return function(t){for(var e in tt(),G.value="编辑角色",S.value="edit",U.value)U.value[e]=t[e];tt(),K.value&&K.value.clearValidate(),T.value=!0}(t.row)}},{default:x((function(){return a[10]||(a[10]=[_("编辑")])})),_:2},1032,["onClick"]),g(u,{icon:"delete",type:"primary",link:"",onClick:function(r){return a=t.row,void C.confirm("此操作将永久删除该角色, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(n(e().mark((function t(){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,o({authorityId:a.authorityId});case 2:0===t.sent.code&&(L({type:"success",message:"删除成功!"}),$());case 4:case"end":return t.stop()}}),t)})))).catch((function(){L({type:"info",message:"已取消删除"})}));var a}},{default:x((function(){return a[11]||(a[11]=[_("删除")])})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]),g(et,{modelValue:T.value,"onUpdate:modelValue":a[4]||(a[4]=function(t){return T.value=t}),size:E(P).drawerSize,"show-close":!1},{header:x((function(){return[w("div",V,[w("span",A,k(G.value),1),w("div",null,[g(u,{onClick:Q},{default:x((function(){return a[12]||(a[12]=[_("取 消")])})),_:1}),g(u,{type:"primary",onClick:Z},{default:x((function(){return a[13]||(a[13]=[_("确 定")])})),_:1})])])]})),default:x((function(){return[g(M,{ref_key:"authorityForm",ref:K,model:U.value,rules:q.value,"label-width":"80px"},{default:x((function(){return[g(y,{label:"父级角色",prop:"parentId"},{default:x((function(){return[g(h,{modelValue:U.value.parentId,"onUpdate:modelValue":a[1]||(a[1]=function(t){return U.value.parentId=t}),style:{width:"100%"},disabled:"add"===S.value,options:r.value,props:{checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])]})),_:1}),g(y,{label:"角色ID",prop:"authorityId"},{default:x((function(){return[g(F,{modelValue:U.value.authorityId,"onUpdate:modelValue":a[2]||(a[2]=function(t){return U.value.authorityId=t}),disabled:"edit"===S.value,autocomplete:"off",maxlength:"15"},null,8,["modelValue","disabled"])]})),_:1}),g(y,{label:"角色姓名",prop:"authorityName"},{default:x((function(){return[g(F,{modelValue:U.value.authorityName,"onUpdate:modelValue":a[3]||(a[3]=function(t){return U.value.authorityName=t}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue","size"]),O.value?(m(),b(et,{key:0,modelValue:O.value,"onUpdate:modelValue":a[5]||(a[5]=function(t){return O.value=t}),size:E(P).drawerSize,title:"角色配置"},{default:x((function(){return[g(at,{"before-leave":J,type:"border-card"},{default:x((function(){return[g(nt,{label:"角色菜单"},{default:x((function(){return[g(c,{ref_key:"menus",ref:H,row:D.value,onChangeRow:Y},null,8,["row"])]})),_:1}),g(nt,{label:"角色api"},{default:x((function(){return[g(f,{ref_key:"apis",ref:W,row:D.value,onChangeRow:Y},null,8,["row"])]})),_:1}),g(nt,{label:"资源权限"},{default:x((function(){return[g(s,{ref_key:"datas",ref:X,authority:R.value,row:D.value,onChangeRow:Y},null,8,["authority","row"])]})),_:1})]})),_:1})]})),_:1},8,["modelValue","size"])):I("",!0)])}}}))}}}))}();
