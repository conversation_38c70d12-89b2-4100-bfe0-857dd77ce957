/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{I as e,a,Q as l,g as t,c as u,o,f as i,b as r,w as d,h as s,v as n,i as m,d as p,T as c,b0 as v,E as h,b1 as g,ab as y,b2 as f,b3 as b,b4 as w,b5 as V}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as I}from"./087AC4D233B64EB0authority.YPU6vVUR.js";import{C as _}from"./087AC4D233B64EB0index.DYYajVL4.js";import{_ as k}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{S as C}from"./087AC4D233B64EB0selectImage.DW9yQvJ8.js";import"./087AC4D233B64EB0image.DZXThNBc.js";import"./087AC4D233B64EB0QR-code.DGKmWLV2.js";import"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";import"./087AC4D233B64EB0logo.D8P6F9wK.js";const B={class:"gva-search-box"},N={class:"gva-table-box"},D={class:"gva-btn-list"},U={class:"gva-pagination"},x={class:"flex justify-between items-center"},z=Object.assign({name:"User"},{__name:"user",setup(z){const j=e(),E=a({username:"",nickname:"",phone:"",email:""}),S=()=>{O.value=1,Q()},A=()=>{E.value={username:"",nickname:"",phone:"",email:""},Q()},T=(e,a)=>{e&&e.forEach((e=>{if(e.children&&e.children.length){const l={authorityId:e.authorityId,authorityName:e.authorityName,children:[]};T(e.children,l.children),a.push(l)}else{const l={authorityId:e.authorityId,authorityName:e.authorityName};a.push(l)}}))},O=a(1),q=a(0),J=a(10),F=a([]),R=e=>{J.value=e,Q()},P=e=>{O.value=e,Q()},Q=async()=>{const e=await V({page:O.value,pageSize:J.value,...E.value});0===e.code&&(F.value=e.data.list,q.value=e.data.total,O.value=e.data.page,J.value=e.data.pageSize)};l((()=>F.value),(()=>{$()}));(async()=>{Q();const e=await I();M(e.data)})();const $=()=>{F.value&&F.value.forEach((e=>{e.authorityIds=e.authorities&&e.authorities.map((e=>e.authorityId))}))},G=a([]),M=e=>{G.value=[],T(e,G.value)},W=a({userName:"",password:"",nickName:"",headerImg:"",authorityId:"",authorityIds:[],enable:1}),Z=a({userName:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:5,message:"最低5位字符",trigger:"blur"}],password:[{required:!0,message:"请输入用户密码",trigger:"blur"},{min:6,message:"最低6位字符",trigger:"blur"}],nickName:[{required:!0,message:"请输入用户昵称",trigger:"blur"}],phone:[{pattern:/^1([38][0-9]|4[014-9]|[59][0-35-9]|6[2567]|7[0-8])\d{8}$/,message:"请输入合法手机号",trigger:"blur"}],email:[{pattern:/^([0-9A-Za-z\-_.]+)@([0-9a-z]+\.[a-z]{2,3}(\.[a-z]{2})?)$/g,message:"请输入正确的邮箱",trigger:"blur"}],authorityId:[{required:!0,message:"请选择用户角色",trigger:"blur"}]}),H=a(null),K=async()=>{W.value.authorityId=W.value.authorityIds[0],H.value.validate((async e=>{if(e){const e={...W.value};if("add"===Y.value){0===(await w(e)).code&&(h({type:"success",message:"创建成功"}),await Q(),X())}if("edit"===Y.value){0===(await g(e)).code&&(h({type:"success",message:"编辑成功"}),await Q(),X())}}}))},L=a(!1),X=()=>{H.value.resetFields(),W.value.headerImg="",W.value.authorityIds=[],L.value=!1},Y=a("add"),ee=()=>{Y.value="add",L.value=!0},ae={},le=async(e,a,l)=>{if(a)return void(l||(ae[e.ID]=[...e.authorityIds]));await c();0===(await v({ID:e.ID,authorityIds:e.authorityIds})).code?h({type:"success",message:"角色设置成功"}):l?e.authorityIds=[l,...e.authorityIds]:(e.authorityIds=[...ae[e.ID]],delete ae[e.ID])};return(e,a)=>{const l=t("el-input"),v=t("el-form-item"),w=t("el-button"),V=t("el-form"),I=t("el-table-column"),z=t("el-cascader"),T=t("el-switch"),$=t("el-table"),M=t("el-pagination"),ae=t("el-drawer");return o(),u("div",null,[i(k,{title:"注：右上角头像下拉可切换角色"}),r("div",B,[i(V,{ref:"searchForm",inline:!0,model:E.value},{default:d((()=>[i(v,{label:"用户名"},{default:d((()=>[i(l,{modelValue:E.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>E.value.username=e),placeholder:"用户名"},null,8,["modelValue"])])),_:1}),i(v,{label:"昵称"},{default:d((()=>[i(l,{modelValue:E.value.nickname,"onUpdate:modelValue":a[1]||(a[1]=e=>E.value.nickname=e),placeholder:"昵称"},null,8,["modelValue"])])),_:1}),i(v,{label:"手机号"},{default:d((()=>[i(l,{modelValue:E.value.phone,"onUpdate:modelValue":a[2]||(a[2]=e=>E.value.phone=e),placeholder:"手机号"},null,8,["modelValue"])])),_:1}),i(v,{label:"邮箱"},{default:d((()=>[i(l,{modelValue:E.value.email,"onUpdate:modelValue":a[3]||(a[3]=e=>E.value.email=e),placeholder:"邮箱"},null,8,["modelValue"])])),_:1}),i(v,null,{default:d((()=>[i(w,{type:"primary",icon:"search",onClick:S},{default:d((()=>a[13]||(a[13]=[s(" 查询 ")]))),_:1}),i(w,{icon:"refresh",onClick:A},{default:d((()=>a[14]||(a[14]=[s(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])]),r("div",N,[r("div",D,[i(w,{type:"primary",icon:"plus",onClick:ee},{default:d((()=>a[15]||(a[15]=[s("新增用户")]))),_:1})]),i($,{data:F.value,"row-key":"ID"},{default:d((()=>[i(I,{align:"left",label:"头像","min-width":"75"},{default:d((e=>[i(_,{style:{"margin-top":"8px"},"pic-src":e.row.headerImg},null,8,["pic-src"])])),_:1}),i(I,{align:"left",label:"ID","min-width":"50",prop:"ID"}),i(I,{align:"left",label:"用户名","min-width":"150",prop:"userName"}),i(I,{align:"left",label:"昵称","min-width":"150",prop:"nickName"}),i(I,{align:"left",label:"手机号","min-width":"180",prop:"phone"}),i(I,{align:"left",label:"邮箱","min-width":"180",prop:"email"}),i(I,{align:"left",label:"用户角色","min-width":"200"},{default:d((e=>[i(z,{modelValue:e.row.authorityIds,"onUpdate:modelValue":a=>e.row.authorityIds=a,options:G.value,"show-all-levels":!1,"collapse-tags":"",props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1,onVisibleChange:a=>{le(e.row,a,0)},onRemoveTag:a=>{le(e.row,!1,a)}},null,8,["modelValue","onUpdate:modelValue","options","onVisibleChange","onRemoveTag"])])),_:1}),i(I,{align:"left",label:"启用","min-width":"150"},{default:d((e=>[i(T,{modelValue:e.row.enable,"onUpdate:modelValue":a=>e.row.enable=a,"inline-prompt":"","active-value":1,"inactive-value":2,onChange:()=>{(async e=>{W.value=JSON.parse(JSON.stringify(e)),await c();const a={...W.value};0===(await g(a)).code&&(h({type:"success",message:"".concat(2===a.enable?"禁用":"启用","成功")}),await Q(),W.value.headerImg="",W.value.authorityIds=[])})(e.row)}},null,8,["modelValue","onUpdate:modelValue","onChange"])])),_:1}),i(I,{label:"操作","min-width":n(j).operateMinWith,fixed:"right"},{default:d((e=>[i(w,{type:"primary",link:"",icon:"delete",onClick:a=>(async e=>{y.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await f({id:e.ID})).code&&(h.success("删除成功"),await Q())}))})(e.row)},{default:d((()=>a[16]||(a[16]=[s("删除")]))),_:2},1032,["onClick"]),i(w,{type:"primary",link:"",icon:"edit",onClick:a=>{return l=e.row,Y.value="edit",W.value=JSON.parse(JSON.stringify(l)),void(L.value=!0);var l}},{default:d((()=>a[17]||(a[17]=[s("编辑")]))),_:2},1032,["onClick"]),i(w,{type:"primary",link:"",icon:"magic-stick",onClick:a=>{return l=e.row,void y.confirm("是否将此用户密码重置为123456?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await b({ID:l.ID});0===e.code?h({type:"success",message:e.msg}):h({type:"error",message:e.msg})}));var l}},{default:d((()=>a[18]||(a[18]=[s("重置密码")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),r("div",U,[i(M,{"current-page":O.value,"page-size":J.value,"page-sizes":[10,30,50,100],total:q.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:P,onSizeChange:R},null,8,["current-page","page-size","total"])])]),i(ae,{modelValue:L.value,"onUpdate:modelValue":a[12]||(a[12]=e=>L.value=e),size:n(j).drawerSize,"show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1},{header:d((()=>[r("div",x,[a[21]||(a[21]=r("span",{class:"text-lg"},"用户",-1)),r("div",null,[i(w,{onClick:X},{default:d((()=>a[19]||(a[19]=[s("取 消")]))),_:1}),i(w,{type:"primary",onClick:K},{default:d((()=>a[20]||(a[20]=[s("确 定")]))),_:1})])])])),default:d((()=>[i(V,{ref_key:"userForm",ref:H,rules:Z.value,model:W.value,"label-width":"80px"},{default:d((()=>["add"===Y.value?(o(),m(v,{key:0,label:"用户名",prop:"userName"},{default:d((()=>[i(l,{modelValue:W.value.userName,"onUpdate:modelValue":a[4]||(a[4]=e=>W.value.userName=e)},null,8,["modelValue"])])),_:1})):p("",!0),"add"===Y.value?(o(),m(v,{key:1,label:"密码",prop:"password"},{default:d((()=>[i(l,{modelValue:W.value.password,"onUpdate:modelValue":a[5]||(a[5]=e=>W.value.password=e)},null,8,["modelValue"])])),_:1})):p("",!0),i(v,{label:"昵称",prop:"nickName"},{default:d((()=>[i(l,{modelValue:W.value.nickName,"onUpdate:modelValue":a[6]||(a[6]=e=>W.value.nickName=e)},null,8,["modelValue"])])),_:1}),i(v,{label:"手机号",prop:"phone"},{default:d((()=>[i(l,{modelValue:W.value.phone,"onUpdate:modelValue":a[7]||(a[7]=e=>W.value.phone=e)},null,8,["modelValue"])])),_:1}),i(v,{label:"邮箱",prop:"email"},{default:d((()=>[i(l,{modelValue:W.value.email,"onUpdate:modelValue":a[8]||(a[8]=e=>W.value.email=e)},null,8,["modelValue"])])),_:1}),i(v,{label:"用户角色",prop:"authorityId"},{default:d((()=>[i(z,{modelValue:W.value.authorityIds,"onUpdate:modelValue":a[9]||(a[9]=e=>W.value.authorityIds=e),style:{width:"100%"},options:G.value,"show-all-levels":!1,props:{multiple:!0,checkStrictly:!0,label:"authorityName",value:"authorityId",disabled:"disabled",emitPath:!1},clearable:!1},null,8,["modelValue","options"])])),_:1}),i(v,{label:"启用",prop:"disabled"},{default:d((()=>[i(T,{modelValue:W.value.enable,"onUpdate:modelValue":a[10]||(a[10]=e=>W.value.enable=e),"inline-prompt":"","active-value":1,"inactive-value":2},null,8,["modelValue"])])),_:1}),i(v,{label:"头像","label-width":"80px"},{default:d((()=>[i(C,{modelValue:W.value.headerImg,"onUpdate:modelValue":a[11]||(a[11]=e=>W.value.headerImg=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","model"])])),_:1},8,["modelValue","size"])])}}});export{z as default};
