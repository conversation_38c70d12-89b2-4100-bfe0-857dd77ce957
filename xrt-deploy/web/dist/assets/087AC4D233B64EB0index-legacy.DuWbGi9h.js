/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},c="function"==typeof Symbol?Symbol:{},l=c.iterator||"@@iterator",u=c.asyncIterator||"@@asyncIterator",s=c.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(r){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),c=new S(n||[]);return i(a,"_invoke",{value:A(t,r,c)}),a}function y(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var d="suspendedStart",p="suspendedYield",v="executing",m="completed",g={};function w(){}function b(){}function x(){}var E={};f(E,l,(function(){return this}));var B=Object.getPrototypeOf,j=B&&B(B(T([])));j&&j!==o&&a.call(j,l)&&(E=j);var k=x.prototype=w.prototype=Object.create(E);function L(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function _(e,r){function n(o,i,c,l){var u=y(e[o],e,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==t(f)&&a.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,c,l)}),(function(t){n("throw",t,c,l)})):r.resolve(f).then((function(t){s.value=t,c(s)}),(function(t){return n("throw",t,c,l)}))}l(u.arg)}var o;i(this,"_invoke",{value:function(t,e){function a(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(a,a):a()}})}function A(t,e,n){var o=d;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:r,done:!0}}for(n.method=a,n.arg=i;;){var c=n.delegate;if(c){var l=C(c,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=y(t,e,n);if("normal"===u.type){if(o=n.done?m:p,u.arg===g)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function C(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,C(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=y(o,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,g;var i=a.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function D(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(D,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[l];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(a.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return i.next=i}}throw new TypeError(t(e)+" is not iterable")}return b.prototype=x,i(k,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,s,"GeneratorFunction")),t.prototype=Object.create(k),t},n.awrap=function(t){return{__await:t}},L(_.prototype),f(_.prototype,u,(function(){return this})),n.AsyncIterator=_,n.async=function(t,e,r,o,a){void 0===a&&(a=Promise);var i=new _(h(t,e,r,o),a);return n.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},L(k),f(k,s,"Generator"),f(k,l,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=T,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(O),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return c.type="throw",c.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],c=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),u=a.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,o,a,i){try{var c=t[a](i),l=c.value}catch(t){return void r(t)}c.done?e(l):Promise.resolve(l).then(n,o)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var i=t.apply(e,n);function c(t){r(i,o,a,c,l,"next",t)}function l(t){r(i,o,a,c,l,"throw",t)}c(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0index-legacy.EWbaRXSJ.js","./087AC4D233B64EB0index-legacy.DRYgDuz9.js","./087AC4D233B64EB0responsive-legacy.GRDgZhZ4.js","./087AC4D233B64EB0index-legacy.Da1yhhQy.js","./087AC4D233B64EB0bottomInfo-legacy.B63Z8kMh.js","./087AC4D233B64EB0normalMode-legacy.D9acfsIM.js","./087AC4D233B64EB0index-legacy.Dvj98d63.js","./087AC4D233B64EB0menuItem-legacy.fHn_s5TX.js","./087AC4D233B64EB0asyncSubmenu-legacy.BIXEZreJ.js","./087AC4D233B64EB0headMode-legacy.Cdf26N3I.js","./087AC4D233B64EB0combinationMode-legacy.1C56VBRH.js","./087AC4D233B64EB0tools-legacy.CqJup9rn.js","./087AC4D233B64EB0index-legacy.DxvfuVcx.js","./087AC4D233B64EB0title-legacy.ByWsba41.js","./087AC4D233B64EB0doc-legacy.yzTEdl6U.js","./087AC4D233B64EB0index-legacy.DaierbDO.js"],(function(t,r){"use strict";var o,a,i,c,l,u,s,f,h,y,d,p,v,m,g,w,b,x,E,B,j,k,L,_,A,C,D,O,S,T;return{setters:[function(t){o=t.I,a=t.J,i=t.r,c=t.R,l=t.u,u=t.aj,s=t.ac,f=t.p,h=t.ao,y=t.k,d=t.a,p=t.T,v=t.g,m=t.c,g=t.o,w=t.i,b=t.d,x=t.f,E=t.b,B=t.v,j=t.n,k=t.w,L=t.a9,_=t.ad,A=t.Y},function(t){C=t.default},function(t){D=t.default},function(t){O=t.u},function(t){S=t.default},function(t){T=t._},null,null,null,null,null,null,null,null,null,null,null],execute:function(){var r=document.createElement("style");r.textContent=".fade-enter-active,.fade-leave-active{transition:all .3s ease}.fade-enter-from,.fade-leave-to{opacity:0;transform:translateY(10px)}.header{border-radius:0 0 10px 10px}.body{height:calc(100% - 6rem)}@keyframes slideDown{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.zoom-enter-active,.zoom-leave-active{transition:all .5s cubic-bezier(.4,0,.2,1)}.zoom-enter-from,.zoom-leave-to{opacity:0;transform:scale(.95)}.slide-leave-active,.slide-enter-active{transition:all .3s}.slide-enter-from{opacity:0;transform:translate(-30px)}.slide-leave-to{opacity:0;transform:translate(30px)}\n/*$vite$:1*/",document.head.appendChild(r);var I={class:"bg-gray-50 text-slate-700 dark:text-slate-500 dark:bg-slate-800 w-screen h-screen"},P={class:"flex flex-row w-full gva-container pt-16 box-border h-full"},G={class:"flex-1 px-2 w-0 h-full"},N={id:"gva-base-load-dom",class:"gva-body-h bg-gray-50 dark:bg-slate-800"};t("default",Object.assign({name:"GvaLayout"},{__name:"index",setup:function(t){var r=o(),z=a(r),Y=z.config,F=z.isDark,M=z.device;O();var R=i({color:"rgba(0, 0, 0, .15)"});c((function(){R.color=F.value?"rgba(255,255,255, .15)":"rgba(0, 0, 0, .15)"}));var $=l(),q=u(),J=s();f((function(){h.on("reload",U),H.loadingInstance&&H.loadingInstance.close()}));var H=y(),K=d(!0),Q=null,U=function(){var t=n(e().mark((function t(){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:Q&&window.clearTimeout(Q),Q=window.setTimeout(n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!q.meta.keepAlive){t.next=7;break}return K.value=!1,t.next=4,p();case 4:K.value=!0,t.next=9;break;case 7:r=q.meta.title,$.push({name:"Reload",params:{title:r}});case 9:case"end":return t.stop()}}),t)}))),400);case 2:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();return function(t,e){var r=v("el-watermark"),n=v("router-view");return g(),m("div",I,[B(Y).show_watermark?(g(),w(r,{key:0,font:R,"z-index":9999,gap:[180,150],class:"absolute inset-0 pointer-events-none",content:B(H).userInfo.nickName},null,8,["font","content"])):b("",!0),x(D),E("div",P,["normal"===B(Y).side_mode||"mobile"===B(M)&&"head"==B(Y).side_mode||"mobile"===B(M)&&"combination"==B(Y).side_mode?(g(),w(C,{key:0})):b("",!0),"combination"===B(Y).side_mode&&"mobile"!==B(M)?(g(),w(C,{key:1,mode:"normal"})):b("",!0),E("div",G,[B(Y).showTabs?(g(),w(S,{key:0})):b("",!0),E("div",{class:j(["overflow-auto",B(Y).showTabs?"gva-container2":"gva-container pt-1"])},[K.value?(g(),w(n,{key:0},{default:k((function(t){var e=t.Component,r=t.route;return[E("div",N,[x(L,{mode:"out-in",name:r.meta.transitionType||B(Y).transition_type},{default:k((function(){return[(g(),w(_,{include:B(J).keepAliveRouters},[(g(),w(A(e),{key:r.fullPath}))],1032,["include"]))]})),_:2},1032,["name"])])]})),_:1})):b("",!0),x(T)],2)])])])}}}))}}}))}();
