/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import e from"./087AC4D233B64EB0index.DAKhbPae.js";import{I as a,J as t,aj as l,u as r,ac as s,a as o,K as n,R as u,g as i,c as d,o as c,d as p,f as m,w as f,F as v,D as h,v as b,i as x,X as y,n as g,b as w,ak as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0menuItem.h4t5Q0OS.js";import"./087AC4D233B64EB0asyncSubmenu.BQ2WRSj6.js";const _={class:"h-full"},A={key:0,class:"bg-white h-[calc(100%-4px)] text-slate-700 dark:text-slate-300 mx-2 dark:bg-slate-900 flex items-center w-[calc(100vw-600px)] overflow-auto"},B=Object.assign({name:"GvaAside"},{__name:"combinationMode",props:{mode:{type:String,default:"normal"}},setup(B){const C=a(),{device:S,config:j}=t(C),q=l(),D=r(),O=s(),M=localStorage.getItem("sidebarCollapsed"),E=o(!M||JSON.parse(M)),I=o(""),J=n((()=>E.value?j.value.layout_side_collapsed_width:j.value.layout_side_width));u((()=>{I.value=q.meta.activeName||q.name})),u((()=>{"mobile"===S.value&&(E.value=!0)})),u((()=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(E.value))})),k("isCollapse",E);const N=(e,a,t,l)=>{var r,s;const o={},n={};if((null==(r=O.routeMap[e])?void 0:r.parameters)&&(null==(s=O.routeMap[e])||s.parameters.forEach((e=>{"query"===e.type?o[e.key]=e.value:n[e.key]=e.value}))),e===q.name)return;if(e.indexOf("http://")>-1||e.indexOf("https://")>-1)return void window.open(e,"_blank");if(!l)return void D.push({name:e,query:o,params:n});const u=O.setLeftMenu(e);if(!u)return void D.push({name:e,query:o,params:n});const i=u.find((e=>!e.hidden&&-1===e.path.indexOf("http://")&&-1===e.path.indexOf("https://")));D.push({name:i.name,query:o,params:n})},z=()=>{E.value=!E.value};return(a,t)=>{const l=i("el-menu"),r=i("el-scrollbar"),s=i("DArrowLeft"),o=i("el-icon"),n=i("DArrowRight");return c(),d("div",_,["head"===B.mode?(c(),d("div",A,[m(l,{"default-active":b(O).topActive,mode:"horizontal",class:"border-r-0 border-b-0 w-full flex gap-1 items-center box-border h-[calc(100%-1px)]","unique-opened":"",onSelect:t[0]||(t[0]=(e,a,t)=>N(e,0,0,!0))},{default:f((()=>[(c(!0),d(v,null,h(b(O).topMenu,(a=>(c(),d(v,null,[a.hidden?p("",!0):(c(),x(e,{key:a.name,"router-info":a,mode:"horizontal"},null,8,["router-info"]))],64)))),256))])),_:1},8,["default-active"])])):p("",!0),"normal"===B.mode?(c(),d("div",{key:1,class:g(["relative h-full bg-white text-slate-700 dark:text-slate-300 dark:bg-slate-900 border-r shadow dark:shadow-gray-700",E.value?"":"  px-2"]),style:y({width:J.value+"px"})},[m(r,null,{default:f((()=>[m(l,{collapse:E.value,"collapse-transition":!1,"default-active":I.value,class:"border-r-0 w-full","unique-opened":"",onSelect:t[1]||(t[1]=(e,a,t)=>N(e,0,0,!1))},{default:f((()=>[(c(!0),d(v,null,h(b(O).leftMenu,(a=>(c(),d(v,null,[a.hidden?p("",!0):(c(),x(e,{key:a.name,"router-info":a},null,8,["router-info"]))],64)))),256))])),_:1},8,["collapse","default-active"])])),_:1}),w("div",{class:g(["absolute bottom-8 right-2 w-8 h-8 bg-gray-50 dark:bg-slate-800 flex items-center justify-center rounded cursor-pointer",E.value?"right-0 left-0 mx-auto":"right-2"]),onClick:z},[E.value?(c(),x(o,{key:1},{default:f((()=>[m(n)])),_:1})):(c(),x(o,{key:0},{default:f((()=>[m(s)])),_:1}))],2)],6)):p("",!0)])}}});export{B as default};
