/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import t from"./087AC4D233B64EB0charts-people-numbers.CfrJkwjL.js";import e from"./087AC4D233B64EB0charts-content-numbers.Yg0QuSB_.js";import{g as s,c as a,o as l,b as i,H as o,t as n,d as r,i as d,f as p,h as c,w as m}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0index.Dn-Bz6sd.js";const u={class:""},y={class:"flex items-center justify-between mb-2"},f={key:0,class:"font-bold"},h={class:"w-full relative"},b={key:0},g={class:"mt-4 text-gray-600 text-3xl font-mono"},v={class:"mt-2 text-green-600 text-sm font-bold font-mono"},x={class:"absolute top-0 right-2 w-[50%] h-20"},B={__name:"charts",props:{type:{type:Number,default:1},title:{type:String,default:""}},setup(B){const k=[[12,22,32,45,32,78,89,92],[1,2,43,5,67,78,89,12],[12,22,32,45,32,78,89,92]];return(j,w)=>{const A=s("el-statistic"),C=s("TopRight"),D=s("el-icon");return l(),a("div",u,[i("div",y,[B.title?(l(),a("div",f,n(B.title),1)):o(j.$slots,"title",{key:1})]),i("div",h,[4!==B.type?(l(),a("div",b,[i("div",g,[p(A,{value:268500})]),i("div",v,[w[0]||(w[0]=c(" +80% ")),p(D,null,{default:m((()=>[p(C)])),_:1})])])):r("",!0),i("div",x,[1===B.type?(l(),d(t,{key:0,data:k[0],height:"100%"},null,8,["data"])):r("",!0),2===B.type?(l(),d(t,{key:1,data:k[1],height:"100%"},null,8,["data"])):r("",!0),3===B.type?(l(),d(t,{key:2,data:k[2],height:"100%"},null,8,["data"])):r("",!0)]),4===B.type?(l(),d(e,{key:1,height:"14rem"})):r("",!0)])])}}};export{B as default};
