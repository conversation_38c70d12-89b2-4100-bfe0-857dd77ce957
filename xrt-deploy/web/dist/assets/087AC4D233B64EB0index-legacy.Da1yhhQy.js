/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return n};var r,n={},a=Object.prototype,o=a.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(r){f=function(e,t,r){return e[t]=r}}function v(e,t,r,n){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),u=new q(n||[]);return i(o,"_invoke",{value:L(e,r,u)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=v;var d="suspendedStart",h="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function x(){}var O={};f(O,c,(function(){return this}));var S=Object.getPrototypeOf,_=S&&S(S(T([])));_&&_!==a&&o.call(_,c)&&(O=_);var k=x.prototype=b.prototype=Object.create(O);function E(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function j(t,r){function n(a,i,u,c){var l=p(t[a],t,i);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==e(f)&&o.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,u,c)}),(function(e){n("throw",e,u,c)})):r.resolve(f).then((function(e){s.value=e,u(s)}),(function(e){return n("throw",e,u,c)}))}c(l.arg)}var a;i(this,"_invoke",{value:function(e,t){function o(){return new r((function(r,a){n(e,t,r,a)}))}return a=a?a.then(o,o):o()}})}function L(e,t,n){var a=d;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===y){if("throw"===o)throw i;return{value:r,done:!0}}for(n.method=o,n.arg=i;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===d)throw a=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=m;var l=p(e,t,n);if("normal"===l.type){if(a=n.done?y:h,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(a=y,n.method="throw",n.arg=l.arg)}}}function P(e,t){var n=t.method,a=e.iterator[n];if(a===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,P(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var o=p(a,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,g;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function N(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function q(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function T(t){if(t||""===t){var n=t[c];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,i=function e(){for(;++a<t.length;)if(o.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=r,e.done=!0,e};return i.next=i}}throw new TypeError(e(t)+" is not iterable")}return w.prototype=x,i(k,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,f(e,s,"GeneratorFunction")),e.prototype=Object.create(k),e},n.awrap=function(e){return{__await:e}},E(j.prototype),f(j.prototype,l,(function(){return this})),n.AsyncIterator=j,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var i=new j(v(e,t,r,a),o);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(k),f(k,s,"Generator"),f(k,c,(function(){return this})),f(k,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=T,q.prototype={constructor:q,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(N),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,a){return u.type="throw",u.arg=e,t.next=n,a&&(t.method="next",t.arg=r),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=o.call(i,"catchLoc"),l=o.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),N(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;N(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:T(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(e,t,r,n,a,o,i){try{var u=e[o](i),c=u.value}catch(e){return void r(e)}u.done?t(c):Promise.resolve(c).then(n,a)}function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(t,r,n){return(r=function(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var a=n.call(t,r||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,o){"use strict";var i,u,c,l,s,f,v,p,d,h,m,y,g,b,w,x,O,S,_,k,E,j,L,P,I,N,q,T;return{setters:[function(e){i=e._,u=e.aj,c=e.u,l=e.a,s=e.k,f=e.K,v=e.Q,p=e.at,d=e.ao,h=e.g,m=e.c,y=e.o,g=e.f,b=e.a7,w=e.w,x=e.F,O=e.D,S=e.i,_=e.b,k=e.n,E=e.h,j=e.t,L=e.v,P=e.al,I=e.au,N=e.a8,q=e.X,T=e.T}],execute:function(){var o=document.createElement("style");o.textContent=".contextmenu[data-v-ec146f1e]{position:absolute;z-index:50;margin:0;width:7rem;border-radius:.25rem;border-width:1px;border-style:solid;--tw-border-opacity: 1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding-top:.625rem;padding-bottom:.625rem;padding-left:0;padding-right:0;font-size:.875rem;line-height:1.25rem;--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.contextmenu[data-v-ec146f1e]:is(.dark *){--tw-border-opacity: 1;border-color:rgb(30 41 59 / var(--tw-border-opacity, 1));--tw-bg-opacity: 1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1))}.contextmenu li[data-v-ec146f1e]{cursor:pointer;list-style-type:none;padding:.375rem 1rem;font-size:1rem;line-height:1.5rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity, 1))}.contextmenu li[data-v-ec146f1e]:hover{--tw-bg-opacity: 1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.contextmenu li[data-v-ec146f1e]:is(.dark *){--tw-text-opacity: 1;color:rgb(226 232 240 / var(--tw-text-opacity, 1))}.contextmenu li[data-v-ec146f1e]:hover:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(75 85 99 / var(--tw-bg-opacity, 1))}.gva-tabs[data-v-ec146f1e] .el-tabs--card>.el-tabs__header{border:none}.gva-tabs[data-v-ec146f1e] .el-tabs__nav-scroll{padding:4px}.gva-tabs[data-v-ec146f1e] .el-tabs__nav{border:0}.gva-tabs[data-v-ec146f1e] .el-tabs__header{border-bottom:0}.gva-tabs[data-v-ec146f1e] .el-tabs__item{box-sizing:border-box;border:1px solid var(--el-border-color-darker);border-radius:2px;margin-right:5px;margin-left:2px;transition:padding .3s cubic-bezier(.645,.045,.355,1)!important;height:34px}.gva-tabs[data-v-ec146f1e] .el-tabs__item.is-active{border:1px solid var(--el-color-primary)}.gva-tabs[data-v-ec146f1e] .el-tabs__item:first-child{border:1px solid var(--el-border-color-darker)}.gva-tabs[data-v-ec146f1e] .el-tabs__item:first-child.is-active{border:1px solid var(--el-color-primary)}\n/*$vite$:1*/",document.head.appendChild(o);var C={class:"gva-tabs"},J=["tab"],A=Object.assign({name:"HistoryComponent"},{__name:"index",setup:function(e){var o=u(),i=c(),A=function(e){return e.name+JSON.stringify(e.query)+JSON.stringify(e.params)},G=l([]),V=l(""),D=l(!1),F=s(),z=l(0),R=l(0),Y=l(!1),$=l(!1),B=l(""),K=f((function(){return F.userInfo.authority.defaultRouter})),Q=function(){G.value=[{name:K.value,meta:{title:"首页"},query:{},params:{}}],i.push({name:K.value}),D.value=!1,sessionStorage.setItem("historys",JSON.stringify(G.value))},U=function(){var e,t=G.value.findIndex((function(t){return A(t)===B.value&&(e=t),A(t)===B.value})),r=G.value.findIndex((function(e){return A(e)===V.value}));G.value.splice(0,t),t>r&&i.push(e),sessionStorage.setItem("historys",JSON.stringify(G.value))},X=function(){var e,t=G.value.findIndex((function(t){return A(t)===B.value&&(e=t),A(t)===B.value})),r=G.value.findIndex((function(e){return A(e)===V.value}));G.value.splice(t+1,G.value.length),t<r&&i.push(e),sessionStorage.setItem("historys",JSON.stringify(G.value))},H=function(){var e;G.value=G.value.filter((function(t){return A(t)===B.value&&(e=t),A(t)===B.value})),i.push(e),sessionStorage.setItem("historys",JSON.stringify(G.value))},M=function(e){if(!G.value.some((function(t){return function(e,t){if(e.name!==t.name)return!1;if(Object.keys(e.query).length!==Object.keys(t.query).length||Object.keys(e.params).length!==Object.keys(t.params).length)return!1;for(var r in e.query)if(e.query[r]!==t.query[r])return!1;for(var n in e.params)if(e.params[n]!==t.params[n])return!1;return!0}(t,e)}))){var t={};t.name=e.name,t.meta=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e.meta),delete t.meta.matched,t.query=e.query,t.params=e.params,G.value.push(t)}window.sessionStorage.setItem("activeValue",A(e))},W=l({}),Z=function(e){var t,r=null==e||null===(t=e.props)||void 0===t?void 0:t.name;if(r){var n=W.value[r];i.push({name:n.name,query:n.query,params:n.params})}},ee=function(e){var t=G.value.findIndex((function(t){return A(t)===e}));A(o)===e&&(1===G.value.length?i.push({name:K.value}):t<G.value.length-1?i.push({name:G.value[t+1].name,query:G.value[t+1].query,params:G.value[t+1].params}):i.push({name:G.value[t-1].name,query:G.value[t-1].query,params:G.value[t-1].params})),G.value.splice(t,1)};v((function(){return D.value}),(function(){D.value?document.body.addEventListener("click",(function(){D.value=!1})):document.body.removeEventListener("click",(function(){D.value=!1}))})),v((function(){return o}),(function(e){"Login"!==e.name&&"Reload"!==e.name&&(G.value=G.value.filter((function(e){return!e.meta.closeTab})),M(e),sessionStorage.setItem("historys",JSON.stringify(G.value)),V.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),v((function(){return G.value}),(function(){sessionStorage.setItem("historys",JSON.stringify(G.value)),W.value={},G.value.forEach((function(e){W.value[A(e)]=e})),d.emit("setKeepAlive",G.value)}),{deep:!0});!function(){d.on("closeThisPage",(function(){ee(A(o))})),d.on("closeAllPage",(function(){Q()})),d.on("mobile",(function(e){$.value=e})),d.on("collapse",(function(e){Y.value=e})),d.on("setQuery",(function(e){var t=G.value.findIndex((function(e){return A(e)===V.value}));G.value[t].query=e,V.value=A(G.value[t]);var r=window.location.href.split("?")[0],n=new URLSearchParams(e).toString();window.history.replaceState({},"","".concat(r,"?").concat(n)),sessionStorage.setItem("historys",JSON.stringify(G.value))})),d.on("switchTab",function(){var e,n=(e=t().mark((function e(r){var n,a,o;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!((n=G.value.findIndex((function(e){return e.name===r.name})))<0)){e.next=3;break}return e.abrupt("return");case 3:for(a in r.query)r.query[a]=String(r.query[a]);for(o in r.params)r.params[o]=String(r.params[o]);return G.value[n].query=r.query||{},G.value[n].params=r.params||{},e.next=9,T();case 9:i.push(G.value[n]);case 10:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(a,o){var i=e.apply(t,n);function u(e){r(i,a,o,u,c,"next",e)}function c(e){r(i,a,o,u,c,"throw",e)}u(void 0)}))});return function(e){return n.apply(this,arguments)}}());var e=[{name:K.value,meta:{title:"首页"},query:{},params:{}}];M(o),G.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?V.value=window.sessionStorage.getItem("activeValue"):V.value=A(o),"true"===window.sessionStorage.getItem("needCloseAll")&&(Q(),window.sessionStorage.removeItem("needCloseAll"))}(),p((function(){d.off("collapse"),d.off("mobile")}));return function(e,t){var r=h("el-tab-pane"),n=h("el-tabs");return y(),m("div",C,[g(n,{modelValue:V.value,"onUpdate:modelValue":t[0]||(t[0]=function(e){return V.value=e}),closable:!(1===G.value.length&&e.$route.name===K.value),type:"card",class:"bg-white text-slate-700 dark:text-slate-500 dark:bg-slate-900",onContextmenu:t[1]||(t[1]=I((function(e){return function(e){if(1===G.value.length&&o.name===K.value)return!1;var t="";(t="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id)&&(D.value=!0,z.value=e.clientX,R.value=e.clientY+10,B.value=t.substring(4))}(e)}),["prevent"])),onTabClick:Z,onTabRemove:ee,onMouseup:t[2]||(t[2]=I((function(e){return function(e){if(1===G.value.length&&o.name===K.value)return!1;var t="";(t="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id)&&ee(t.substring(4))}(e)}),["middle","prevent"]))},{default:w((function(){return[(y(!0),m(x,null,O(G.value,(function(e){return y(),S(r,{key:A(e),label:e.meta.title,name:A(e),tab:e,class:"border-none"},{label:w((function(){return[_("span",{tab:e,class:k(V.value===A(e)?"text-active":"text-gray-600 dark:text-slate-400 ")},[_("i",{class:k(V.value===A(e)?"text-active":"text-gray-600 dark:text-slate-400")},null,2),E(" "+j(L(P)(e.meta.title,e)),1)],10,J)]})),_:2},1032,["label","name","tab"])})),128))]})),_:1},8,["modelValue","closable"]),b(_("ul",{style:q({left:z.value+"px",top:R.value+"px"}),class:"contextmenu"},[_("li",{onClick:Q},"关闭所有"),_("li",{onClick:U},"关闭左侧"),_("li",{onClick:X},"关闭右侧"),_("li",{onClick:H},"关闭其他")],4),[[N,D.value]])])}}});e("default",i(A,[["__scopeId","data-v-ec146f1e"]]))}}}))}();
