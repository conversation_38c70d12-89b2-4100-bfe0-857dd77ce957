/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{a as e,r as a,g as l,c as t,o as u,b as n,f as d,w as o,d as r,h as s,F as i,i as c,l as v,t as p,v as m,aa as f,bk as g,D as y,ab as b,E as h}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as C,a as w,g as _,b as k,f as A,c as V,u as D,d as I,e as x}from"./087AC4D233B64EB0rich-edit.cR_Skjw9.js";import{g as T}from"./087AC4D233B64EB0image.DZXThNBc.js";const B={class:"gva-search-box"},z={class:"gva-table-box"},E={class:"gva-btn-list"},U={class:"file-list"},j={class:"gva-pagination"},S={class:"flex justify-between items-center"},F={class:"text-lg"},R=Object.assign({name:"Info"},{__name:"info",setup(R){const K=e(!1),N=e({title:"",content:"",userID:void 0,attachments:[]}),O=e([]);(async()=>{const e=await _();0===e.code&&(O.value=e.data)})();const Q=a({}),q=a({createdAt:[{validator:(e,a,l)=>{W.value.startCreatedAt&&!W.value.endCreatedAt?l(new Error("请填写结束日期")):!W.value.startCreatedAt&&W.value.endCreatedAt?l(new Error("请填写开始日期")):W.value.startCreatedAt&&W.value.endCreatedAt&&(W.value.startCreatedAt.getTime()===W.value.endCreatedAt.getTime()||W.value.startCreatedAt.getTime()>W.value.endCreatedAt.getTime())?l(new Error("开始日期应当早于结束日期")):l()},trigger:"change"}]}),G=e(),H=e(),J=e(1),L=e(0),M=e(10),P=e([]),W=e({}),X=()=>{W.value={},ee()},Y=()=>{var e;null==(e=H.value)||e.validate((async e=>{e&&(J.value=1,ee())}))},Z=e=>{M.value=e,ee()},$=e=>{J.value=e,ee()},ee=async()=>{const e=await k({page:J.value,pageSize:M.value,...W.value});0===e.code&&(P.value=e.data.list,L.value=e.data.total,J.value=e.data.page,M.value=e.data.pageSize)};ee();(async()=>{})();const ae=e([]),le=e=>{ae.value=e},te=async()=>{b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===ae.value.length)return void h({type:"warning",message:"请选择要删除的数据"});ae.value&&ae.value.map((a=>{e.push(a.ID)}));0===(await x({IDs:e})).code&&(h({type:"success",message:"删除成功"}),P.value.length===e.length&&J.value>1&&J.value--,ee())}))},ue=e(""),ne=async e=>{0===(await I({ID:e.ID})).code&&(h({type:"success",message:"删除成功"}),1===P.value.length&&J.value>1&&J.value--,ee())},de=e(!1),oe=()=>{ue.value="create",de.value=!0},re=()=>{de.value=!1,N.value={title:"",content:"",userID:void 0,attachments:[]}},se=async()=>{var e;null==(e=G.value)||e.validate((async e=>{if(!e)return;let a;switch(ue.value){case"create":default:a=await V(N.value);break;case"update":a=await D(N.value)}0===a.code&&(h({type:"success",message:"创建/更改成功"}),re(),ee())}))};return(e,a)=>{const h=l("QuestionFilled"),_=l("el-icon"),k=l("el-tooltip"),V=l("el-date-picker"),D=l("el-form-item"),I=l("el-button"),x=l("el-form"),R=l("el-table-column"),ee=l("el-tag"),ie=l("el-table"),ce=l("el-pagination"),ve=l("el-input"),pe=l("el-option"),me=l("el-select"),fe=l("el-drawer");return u(),t("div",null,[n("div",B,[d(x,{ref_key:"elSearchFormRef",ref:H,inline:!0,model:W.value,class:"demo-form-inline",rules:q,onKeyup:v(Y,["enter"])},{default:o((()=>[d(D,{label:"创建日期",prop:"createdAt"},{label:o((()=>[n("span",null,[a[9]||(a[9]=s(" 创建日期 ")),d(k,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:o((()=>[d(_,null,{default:o((()=>[d(h)])),_:1})])),_:1})])])),default:o((()=>[d(V,{modelValue:W.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>W.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!W.value.endCreatedAt&&e.getTime()>W.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[10]||(a[10]=s(" — ")),d(V,{modelValue:W.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>W.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!W.value.startCreatedAt&&e.getTime()<W.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),K.value?(u(),t(i,{key:0},[],64)):r("",!0),d(D,null,{default:o((()=>[d(I,{type:"primary",icon:"search",onClick:Y},{default:o((()=>a[11]||(a[11]=[s(" 查询 ")]))),_:1}),d(I,{icon:"refresh",onClick:X},{default:o((()=>a[12]||(a[12]=[s(" 重置 ")]))),_:1}),K.value?(u(),c(I,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[3]||(a[3]=e=>K.value=!1)},{default:o((()=>a[14]||(a[14]=[s(" 收起 ")]))),_:1})):(u(),c(I,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[2]||(a[2]=e=>K.value=!0)},{default:o((()=>a[13]||(a[13]=[s(" 展开 ")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),n("div",z,[n("div",E,[d(I,{type:"primary",icon:"plus",onClick:oe},{default:o((()=>a[15]||(a[15]=[s(" 新增 ")]))),_:1}),d(I,{icon:"delete",style:{"margin-left":"10px"},disabled:!ae.value.length,onClick:te},{default:o((()=>a[16]||(a[16]=[s(" 删除 ")]))),_:1},8,["disabled"])]),d(ie,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:P.value,"row-key":"ID",onSelectionChange:le},{default:o((()=>[d(R,{type:"selection",width:"55"}),d(R,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:o((e=>[s(p(m(f)(e.row.CreatedAt)),1)])),_:1}),d(R,{align:"left",label:"标题",prop:"title",width:"120"}),d(R,{align:"left",label:"作者",prop:"userID",width:"120"},{default:o((e=>[n("span",null,p(m(g)(O.value.userID,e.row.userID)),1)])),_:1}),d(R,{label:"附件",prop:"attachments",width:"200"},{default:o((e=>[n("div",U,[(u(!0),t(i,null,y(e.row.attachments,(e=>(u(),c(ee,{key:e.uid,onClick:a=>{return l=e.url,void window.open(T(l),"_blank");var l}},{default:o((()=>[s(p(e.name),1)])),_:2},1032,["onClick"])))),128))])])),_:1}),d(R,{align:"left",label:"操作",fixed:"right","min-width":"240"},{default:o((e=>[d(I,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await A({ID:e.ID});ue.value="update",0===a.code&&(N.value=a.data,de.value=!0)})(e.row)},{default:o((()=>a[17]||(a[17]=[s(" 变更 ")]))),_:2},1032,["onClick"]),d(I,{type:"primary",link:"",icon:"delete",onClick:a=>{return l=e.row,void b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ne(l)}));var l}},{default:o((()=>a[18]||(a[18]=[s(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),n("div",j,[d(ce,{layout:"total, sizes, prev, pager, next, jumper","current-page":J.value,"page-size":M.value,"page-sizes":[10,30,50,100],total:L.value,onCurrentChange:$,onSizeChange:Z},null,8,["current-page","page-size","total"])])]),d(fe,{modelValue:de.value,"onUpdate:modelValue":a[8]||(a[8]=e=>de.value=e),"destroy-on-close":"",size:"800","show-close":!1,"before-close":re},{header:o((()=>[n("div",S,[n("span",F,p("create"===ue.value?"添加":"修改"),1),n("div",null,[d(I,{type:"primary",onClick:se},{default:o((()=>a[19]||(a[19]=[s(" 确 定 ")]))),_:1}),d(I,{onClick:re},{default:o((()=>a[20]||(a[20]=[s(" 取 消 ")]))),_:1})])])])),default:o((()=>[d(x,{ref_key:"elFormRef",ref:G,model:N.value,"label-position":"top",rules:Q,"label-width":"80px"},{default:o((()=>[d(D,{label:"标题:",prop:"title"},{default:o((()=>[d(ve,{modelValue:N.value.title,"onUpdate:modelValue":a[4]||(a[4]=e=>N.value.title=e),clearable:!0,placeholder:"请输入标题"},null,8,["modelValue"])])),_:1}),d(D,{label:"内容:",prop:"content"},{default:o((()=>[d(C,{modelValue:N.value.content,"onUpdate:modelValue":a[5]||(a[5]=e=>N.value.content=e)},null,8,["modelValue"])])),_:1}),d(D,{label:"作者:",prop:"userID"},{default:o((()=>[d(me,{modelValue:N.value.userID,"onUpdate:modelValue":a[6]||(a[6]=e=>N.value.userID=e),placeholder:"请选择作者",style:{width:"100%"},clearable:!0},{default:o((()=>[(u(!0),t(i,null,y(O.value.userID,((e,a)=>(u(),c(pe,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),d(D,{label:"附件:",prop:"attachments"},{default:o((()=>[d(w,{modelValue:N.value.attachments,"onUpdate:modelValue":a[7]||(a[7]=e=>N.value.attachments=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}});export{R as default};
