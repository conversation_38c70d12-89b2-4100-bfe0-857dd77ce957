/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,a,g as l,f as t,c as u,u as s}from"./087AC4D233B64EB0rich-edit.cR_Skjw9.js";import{aj as o,u as n,a as d,r,g as c,c as i,o as m,b as p,f as v,w as f,F as b,D as y,i as _,h,E as V}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0image.DZXThNBc.js";const D={class:"gva-form-box"},g=Object.assign({name:"InfoForm"},{__name:"info",setup(g){const w=o(),I=n(),B=d(""),j=d({title:"",content:"",userID:void 0,attachments:[]}),k=r({}),C=d(),x=d([]);(async()=>{const e=await l();0===e.code&&(x.value=e.data)})();(async()=>{if(w.query.id){const e=await t({ID:w.query.id});0===e.code&&(j.value=e.data,B.value="update")}else B.value="create"})();const E=async()=>{var e;null==(e=C.value)||e.validate((async e=>{if(!e)return;let a;switch(B.value){case"create":default:a=await u(j.value);break;case"update":a=await s(j.value)}0===a.code&&V({type:"success",message:"创建/更改成功"})}))},U=()=>{I.go(-1)};return(l,t)=>{const u=c("el-input"),s=c("el-form-item"),o=c("el-option"),n=c("el-select"),d=c("el-button"),r=c("el-form");return m(),i("div",null,[p("div",D,[v(r,{model:j.value,ref_key:"elFormRef",ref:C,"label-position":"right",rules:k,"label-width":"80px"},{default:f((()=>[v(s,{label:"标题:",prop:"title"},{default:f((()=>[v(u,{modelValue:j.value.title,"onUpdate:modelValue":t[0]||(t[0]=e=>j.value.title=e),clearable:!0,placeholder:"请输入标题"},null,8,["modelValue"])])),_:1}),v(s,{label:"内容:",prop:"content"},{default:f((()=>[v(e,{modelValue:j.value.content,"onUpdate:modelValue":t[1]||(t[1]=e=>j.value.content=e)},null,8,["modelValue"])])),_:1}),v(s,{label:"作者:",prop:"userID"},{default:f((()=>[v(n,{modelValue:j.value.userID,"onUpdate:modelValue":t[2]||(t[2]=e=>j.value.userID=e),placeholder:"请选择作者",style:{width:"100%"},clearable:!0},{default:f((()=>[(m(!0),i(b,null,y(x.value.userID,((e,a)=>(m(),_(o,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),v(s,{label:"附件:",prop:"attachments"},{default:f((()=>[v(a,{modelValue:j.value.attachments,"onUpdate:modelValue":t[3]||(t[3]=e=>j.value.attachments=e)},null,8,["modelValue"])])),_:1}),v(s,null,{default:f((()=>[v(d,{type:"primary",onClick:E},{default:f((()=>t[4]||(t[4]=[h("保存")]))),_:1}),v(d,{type:"primary",onClick:U},{default:f((()=>t[5]||(t[5]=[h("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])}}});export{g as default};
