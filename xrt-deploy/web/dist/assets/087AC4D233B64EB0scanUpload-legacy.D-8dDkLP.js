/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0vue-cropper.es-legacy.CQhcf0py.js"],(function(e,t){"use strict";var a,o,r,n,l,u,i,c,d,s,p,f,v,g,h,b,m,w,x,y,k,C,_,R,j;return{setters:[function(e){a=e._,o=e.a,r=e.p,n=e.u,l=e.q,u=e.c,i=e.b,c=e.f,d=e.w,s=e.v,p=e.x,f=e.g,v=e.i,g=e.d,h=e.F,b=e.o,m=e.y,w=e.z,x=e.A,y=e.B,k=e.h,C=e.t,_=e.E,R=e.j},function(e){j=e.M}],execute:function(){var t=document.createElement("style");t.textContent=".h5-uploader .el-upload{position:relative;cursor:pointer;overflow:hidden;border-radius:.25rem;border:1px dashed var(--el-border-color);border-radius:6px;transition:var(--el-transition-duration-fast)}.h5-uploader .el-upload:hover{border-color:var(--el-color-primary)}.el-icon.h5-uploader-icon{height:8rem;width:8rem;text-align:center;font-size:1.5rem;line-height:2rem;--tw-text-opacity: 1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.toolbar[data-v-71b771ce]{position:fixed;bottom:0;z-index:1000;margin:0;display:flex;width:100vw;justify-content:space-between;border-radius:0;--tw-bg-opacity: 1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1));padding:.625rem;--tw-shadow: 0 -2px 10px rgba(0,0,0,.1);--tw-shadow-colored: 0 -2px 10px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.toolbar .el-button-group[data-v-71b771ce]{display:flex}.toolbar .el-button-group[data-v-71b771ce]{gap:.5rem}.toolbar .el-button-group .el-button[data-v-71b771ce]{width:2.5rem}.toolbar .el-button-group .el-button[data-v-71b771ce]{padding:.5rem}[data-v-71b771ce] .vue-cropper{background-color:transparent}\n/*$vite$:1*/",document.head.appendChild(t);var B={class:"flex justify-center w-full pt-2"},z={class:"flex flex-col w-full h-auto p-0 pt-4"},$={class:"flex-1 min-h-[60vh]"},A={class:"w-screen h-[calc(100vh-175px)] rounded"},D={key:1,class:"flex justify-center items-center w-full h-[calc(100vh-175px)]"},E={class:"toolbar"};e("default",a(Object.assign({name:"scanUpload"},{__name:"scanUpload",setup:function(e){var t=o(0),a=o(""),S=o(!1),U=o(300),F=function(){U.value=window.innerWidth};r((function(){F(),window.addEventListener("resize",F)}));var I=n();I.isReady().then((function(){var e=I.currentRoute.value.query;t.value=e.id,a.value=e.token})).catch((function(e){}));var L=o(null),M=o(""),V=o(null),W=l().proxy,q=o({}),H=o(!1),N=function(e){W.$refs.cropperRef.changeScale(e)},O=o([1,1]),T=o(300),G=o(300),J=o(!1),K=function(e){if(e.raw.type.includes("image")){if(e.raw.size/1024/1024>8)return _.error("文件大小不能超过8MB!"),!1;var t=R.service({lock:!0,text:"请稍后",background:"rgba(0, 0, 0, 0.7)"}),a=new FileReader;a.onload=function(e){M.value=e.target.result,t.close()},a.readAsDataURL(e.raw)}else _.error("请选择图片文件")},P=function(e){-90===e?W.$refs.cropperRef.rotateLeft():W.$refs.cropperRef.rotateRight()},Q=function(){if(H.value=!0,!1===S.value)return L.value.submit(),!0;W.$refs.cropperRef.getCropBlob((function(e){try{var t=new File([e],"".concat(Date.now(),".jpg"),{type:"image/jpeg"});L.value.clearFiles(),L.value.handleStart(t),L.value.submit()}catch(a){H.value=!1,_.error("上传失败: "+a.message)}}))},X=function(e){e.data&&(M.value=null,H.value=!1,q.value={},_.success("上传成功"))};return function(e,o){var r=f("el-icon"),n=f("el-upload"),l=f("el-image"),_=f("el-button"),R=f("el-tooltip"),F=f("el-button-group"),I=f("el-switch");return b(),u(h,null,[i("div",B,[c(n,{ref_key:"uploadRef",ref:L,class:"h5-uploader",action:"".concat(s(p)(),"/fileUploadAndDownload/upload"),accept:"image/*","show-file-list":!1,"auto-upload":!1,headers:{"x-token":a.value},data:{classId:t.value},"on-success":X,"on-change":K},{default:d((function(){return[c(r,{class:"h5-uploader-icon"},{default:d((function(){return[c(s(m))]})),_:1})]})),_:1},8,["action","headers","data"])]),i("div",z,[i("div",$,[i("div",A,[S.value?(b(),v(s(j),{key:0,ref_key:"cropperRef",ref:V,img:M.value,mode:"contain",outputType:"jpeg",autoCrop:!0,autoCropWidth:T.value,autoCropHeight:G.value,fixedBox:!1,fixed:J.value,fixedNumber:O.value,centerBox:!0,canMoveBox:!0,full:!1,maxImgSize:U.value,original:!0},null,8,["img","autoCropWidth","autoCropHeight","fixed","fixedNumber","maxImgSize"])):(b(),u("div",D,[M.value?(b(),v(l,{key:0,src:M.value,class:"max-w-full max-h-full",mode:"cover"},null,8,["src"])):g("",!0)]))])])]),i("div",E,[S.value?(b(),v(F,{key:0},{default:d((function(){return[c(R,{content:"向左旋转"},{default:d((function(){return[c(_,{onClick:o[0]||(o[0]=function(e){return P(-90)}),icon:s(w)},null,8,["icon"])]})),_:1}),c(R,{content:"向右旋转"},{default:d((function(){return[c(_,{onClick:o[1]||(o[1]=function(e){return P(90)}),icon:s(x)},null,8,["icon"])]})),_:1}),c(_,{icon:s(m),onClick:o[2]||(o[2]=function(e){return N(1)})},null,8,["icon"]),c(_,{icon:s(y),onClick:o[3]||(o[3]=function(e){return N(-1)})},null,8,["icon"])]})),_:1})):g("",!0),c(I,{size:"large",modelValue:S.value,"onUpdate:modelValue":o[4]||(o[4]=function(e){return S.value=e}),"inline-prompt":"","active-text":"裁剪","inactive-text":"裁剪"},null,8,["modelValue"]),c(_,{type:"primary",onClick:Q,loading:H.value},{default:d((function(){return[k(C(H.value?"上传中...":"上 传"),1)]})),_:1},8,["loading"])])],64)}}}),[["__scopeId","data-v-71b771ce"]]))}}}));
