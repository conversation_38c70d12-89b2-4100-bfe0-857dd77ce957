/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{m as e,d as a,n as l,o as t}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";import{_ as o}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{a as u,g as s,c as d,o as i,f as n,b as p,w as r,h as c,F as m,D as g,i as f,ab as v,E as b}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const w={class:"gva-table-box"},k={class:"gva-btn-list gap-3 flex items-center"},y={class:"flex justify-between items-center"},V=Object.assign({name:"AutoPkg"},{__name:"autoPkg",setup(V){const _=u({packageName:"",template:"",label:"",desc:""}),h=u([]);(async()=>{const a=await e();0===a.code&&(h.value=a.data)})();const B=(e,a,l)=>{/[\u4E00-\u9FA5]/g.test(a)?l(new Error("不能为中文")):/^\d+$/.test(a[0])?l(new Error("不能够以数字开头")):l()},C=u({packageName:[{required:!0,message:"请输入包名",trigger:"blur"},{validator:B,trigger:"blur"}],template:[{required:!0,message:"请选择模板",trigger:"change"},{validator:B,trigger:"blur"}]}),x=u(!1),E=()=>{x.value=!1,_.value={packageName:"",template:"",label:"",desc:""}},N=u(null),j=async()=>{N.value.validate((async e=>{if(e){0===(await t(_.value)).code&&b({type:"success",message:"添加成功",showClose:!0}),D(),E()}}))},A=u([]),D=async()=>{const e=await a();0===e.code&&(A.value=e.data.pkgs)};return D(),(e,a)=>{const t=s("el-button"),u=s("el-table-column"),V=s("el-table"),B=s("el-input"),U=s("el-form-item"),F=s("el-option"),T=s("el-select"),P=s("el-form"),q=s("el-drawer");return i(),d("div",null,[n(o,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请看视频https://www.bilibili.com/video/BV1kv4y1g7nT?p=3"}),p("div",w,[p("div",k,[n(t,{type:"primary",icon:"plus",onClick:a[0]||(a[0]=e=>{x.value=!0})},{default:r((()=>a[6]||(a[6]=[c(" 新增 ")]))),_:1})]),n(V,{data:A.value},{default:r((()=>[n(u,{align:"left",label:"id",width:"120",prop:"ID"}),n(u,{align:"left",label:"包名",width:"150",prop:"packageName"}),n(u,{align:"left",label:"模板",width:"150",prop:"template"}),n(u,{align:"left",label:"展示名",width:"150",prop:"label"}),n(u,{align:"left",label:"描述","min-width":"150",prop:"desc"}),n(u,{align:"left",label:"操作",width:"200"},{default:r((e=>[n(t,{icon:"delete",type:"primary",link:"",onClick:a=>(async e=>{v.confirm("此操作仅删除数据库中的pkg存储，后端相应目录结构请自行删除与数据库保持一致！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await l(e)).code&&(b({type:"success",message:"删除成功!"}),D())}))})(e.row)},{default:r((()=>a[7]||(a[7]=[c(" 删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])]),n(q,{modelValue:x.value,"onUpdate:modelValue":a[5]||(a[5]=e=>x.value=e),size:"40%","show-close":!1},{header:r((()=>[p("div",y,[a[10]||(a[10]=p("span",{class:"text-lg"},"创建Package",-1)),p("div",null,[n(t,{onClick:E},{default:r((()=>a[8]||(a[8]=[c(" 取 消 ")]))),_:1}),n(t,{type:"primary",onClick:j},{default:r((()=>a[9]||(a[9]=[c(" 确 定 ")]))),_:1})])])])),default:r((()=>[n(o,{title:"模板package会创建集成于项目本体中的代码包，模板plugin会创建插件包"}),n(P,{ref_key:"pkgForm",ref:N,model:_.value,rules:C.value,"label-width":"80px"},{default:r((()=>[n(U,{label:"包名",prop:"packageName"},{default:r((()=>[n(B,{modelValue:_.value.packageName,"onUpdate:modelValue":a[1]||(a[1]=e=>_.value.packageName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(U,{label:"模板",prop:"template"},{default:r((()=>[n(T,{modelValue:_.value.template,"onUpdate:modelValue":a[2]||(a[2]=e=>_.value.template=e)},{default:r((()=>[(i(!0),d(m,null,g(h.value,(e=>(i(),f(F,{label:e,value:e,key:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(U,{label:"展示名",prop:"label"},{default:r((()=>[n(B,{modelValue:_.value.label,"onUpdate:modelValue":a[3]||(a[3]=e=>_.value.label=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(U,{label:"描述",prop:"desc"},{default:r((()=>[n(B,{modelValue:_.value.desc,"onUpdate:modelValue":a[4]||(a[4]=e=>_.value.desc=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])}}});export{V as default};
