/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,e){"use strict";var n;return{setters:[function(t){n=t.s}],execute:function(){t("g",(function(t){return n({url:"/api/getApiList",method:"post",data:t})})),t("c",(function(t){return n({url:"/api/createApi",method:"post",data:t})})),t("b",(function(t){return n({url:"/api/getApiById",method:"post",data:t})})),t("u",(function(t){return n({url:"/api/updateApi",method:"post",data:t})})),t("j",(function(t){return n({url:"/api/getAllApis",method:"post",data:t})})),t("d",(function(t){return n({url:"/api/deleteApi",method:"post",data:t})})),t("f",(function(t){return n({url:"/api/deleteApisByIds",method:"delete",data:t})})),t("h",(function(){return n({url:"/api/freshCasbin",method:"get"})})),t("s",(function(){return n({url:"/api/syncApi",method:"get"})})),t("a",(function(){return n({url:"/api/getApiGroups",method:"get"})})),t("i",(function(t){return n({url:"/api/ignoreApi",method:"post",data:t})})),t("e",(function(t){return n({url:"/api/enterSyncApi",method:"post",data:t})}))}}}));
