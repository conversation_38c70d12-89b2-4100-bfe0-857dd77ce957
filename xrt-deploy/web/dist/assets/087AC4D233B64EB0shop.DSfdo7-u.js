/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{a as e,I as a,r as l,g as t,c as o,o as u,b as d,f as s,w as n,d as r,h as i,F as p,i as c,l as v,t as m,v as f,aa as h,ab as y,E as g}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as b,f as C,c as w,u as A,d as _,a as I}from"./087AC4D233B64EB0shop.DuFD14Dz.js";const k={class:"gva-search-box"},D={class:"gva-table-box"},V={class:"gva-btn-list"},x={class:"gva-pagination"},z={class:"flex justify-between items-center"},T={class:"text-lg"},K=Object.assign({name:"Shop"},{__name:"shop",setup(K){const P=e(!1),B=a(),S=e(!1),N=e({shopName:"",shopID:"",shopAPIKey:""}),U=l({}),E=l({createdAt:[{validator:(e,a,l)=>{W.value.startCreatedAt&&!W.value.endCreatedAt?l(new Error("请填写结束日期")):!W.value.startCreatedAt&&W.value.endCreatedAt?l(new Error("请填写开始日期")):W.value.startCreatedAt&&W.value.endCreatedAt&&(W.value.startCreatedAt.getTime()===W.value.endCreatedAt.getTime()||W.value.startCreatedAt.getTime()>W.value.endCreatedAt.getTime())?l(new Error("开始日期应当早于结束日期")):l()},trigger:"change"}]}),j=e(),F=e(),R=e(1),M=e(0),O=e(10),Q=e([]),W=e({}),q=()=>{W.value={},L()},G=()=>{var e;null==(e=F.value)||e.validate((async e=>{e&&(R.value=1,L())}))},H=e=>{O.value=e,L()},J=e=>{R.value=e,L()},L=async()=>{const e=await b({page:R.value,pageSize:O.value,...W.value});0===e.code&&(Q.value=e.data.list,M.value=e.data.total,R.value=e.data.page,O.value=e.data.pageSize)};L();(async()=>{})();const X=e([]),Y=e=>{X.value=e},Z=async()=>{y.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===X.value.length)return void g({type:"warning",message:"请选择要删除的数据"});X.value&&X.value.map((a=>{e.push(a.ID)}));0===(await I({IDs:e})).code&&(g({type:"success",message:"删除成功"}),Q.value.length===e.length&&R.value>1&&R.value--,L())}))},$=e(""),ee=async e=>{0===(await _({ID:e.ID})).code&&(g({type:"success",message:"删除成功"}),1===Q.value.length&&R.value>1&&R.value--,L())},ae=e(!1),le=()=>{ae.value=!1,N.value={shopName:"",shopID:"",shopAPIKey:""}},te=async()=>{var e;P.value=!0,null==(e=j.value)||e.validate((async e=>{if(!e)return P.value=!1;let a;switch($.value){case"create":default:a=await w(N.value);break;case"update":a=await A(N.value)}P.value=!1,0===a.code&&(g({type:"success",message:"创建/更改成功"}),le(),L())}))},oe=e({}),ue=e(!1),de=async e=>{const a=await C({ID:e.ID});0===a.code&&(oe.value=a.data,ue.value=!0)},se=()=>{ue.value=!1,oe.value={}};return(e,a)=>{const l=t("QuestionFilled"),g=t("el-icon"),b=t("el-tooltip"),w=t("el-date-picker"),A=t("el-form-item"),_=t("el-button"),I=t("el-form"),K=t("el-table-column"),L=t("InfoFilled"),ne=t("el-table"),re=t("el-pagination"),ie=t("el-input"),pe=t("el-drawer"),ce=t("el-descriptions-item"),ve=t("el-descriptions");return u(),o("div",null,[d("div",k,[s(I,{ref_key:"elSearchFormRef",ref:F,inline:!0,model:W.value,class:"demo-form-inline",rules:E,onKeyup:v(G,["enter"])},{default:n((()=>[s(A,{label:"创建日期",prop:"createdAt"},{label:n((()=>[d("span",null,[a[10]||(a[10]=i(" 创建日期 ")),s(b,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:n((()=>[s(g,null,{default:n((()=>[s(l)])),_:1})])),_:1})])])),default:n((()=>[s(w,{modelValue:W.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>W.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!W.value.endCreatedAt&&e.getTime()>W.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[11]||(a[11]=i(" — ")),s(w,{modelValue:W.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>W.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!W.value.startCreatedAt&&e.getTime()<W.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),S.value?(u(),o(p,{key:0},[],64)):r("",!0),s(A,null,{default:n((()=>[s(_,{type:"primary",icon:"search",onClick:G},{default:n((()=>a[12]||(a[12]=[i("查询")]))),_:1}),s(_,{icon:"refresh",onClick:q},{default:n((()=>a[13]||(a[13]=[i("重置")]))),_:1}),S.value?(u(),c(_,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[3]||(a[3]=e=>S.value=!1)},{default:n((()=>a[15]||(a[15]=[i("收起")]))),_:1})):(u(),c(_,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[2]||(a[2]=e=>S.value=!0)},{default:n((()=>a[14]||(a[14]=[i("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),d("div",D,[d("div",V,[s(_,{type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>($.value="create",void(ae.value=!0)))},{default:n((()=>a[16]||(a[16]=[i("新增")]))),_:1}),s(_,{icon:"delete",style:{"margin-left":"10px"},disabled:!X.value.length,onClick:Z},{default:n((()=>a[17]||(a[17]=[i("删除")]))),_:1},8,["disabled"])]),s(ne,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:Q.value,"row-key":"ID",onSelectionChange:Y},{default:n((()=>[s(K,{type:"selection",width:"55"}),s(K,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:n((e=>[i(m(f(h)(e.row.CreatedAt)),1)])),_:1}),s(K,{align:"left",label:"商店名字",prop:"shopName",width:"120"}),s(K,{align:"left",label:"商店ID",prop:"shopID",width:"120"}),s(K,{align:"left",label:"商店API Key",prop:"shopAPIKey",width:"120"}),s(K,{align:"left",label:"操作",fixed:"right","min-width":f(B).operateMinWith},{default:n((e=>[s(_,{type:"primary",link:"",class:"table-button",onClick:a=>de(e.row)},{default:n((()=>[s(g,{style:{"margin-right":"5px"}},{default:n((()=>[s(L)])),_:1}),a[18]||(a[18]=i("查看"))])),_:2},1032,["onClick"]),s(_,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await C({ID:e.ID});$.value="update",0===a.code&&(N.value=a.data,ae.value=!0)})(e.row)},{default:n((()=>a[19]||(a[19]=[i("编辑")]))),_:2},1032,["onClick"]),s(_,{type:"primary",link:"",icon:"delete",onClick:a=>{return l=e.row,void y.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ee(l)}));var l}},{default:n((()=>a[20]||(a[20]=[i("删除")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),d("div",x,[s(re,{layout:"total, sizes, prev, pager, next, jumper","current-page":R.value,"page-size":O.value,"page-sizes":[10,30,50,100],total:M.value,onCurrentChange:J,onSizeChange:H},null,8,["current-page","page-size","total"])])]),s(pe,{"destroy-on-close":"",size:f(B).drawerSize,modelValue:ae.value,"onUpdate:modelValue":a[8]||(a[8]=e=>ae.value=e),"show-close":!1,"before-close":le},{header:n((()=>[d("div",z,[d("span",T,m("create"===$.value?"新增":"编辑"),1),d("div",null,[s(_,{loading:P.value,type:"primary",onClick:te},{default:n((()=>a[21]||(a[21]=[i("确 定")]))),_:1},8,["loading"]),s(_,{onClick:le},{default:n((()=>a[22]||(a[22]=[i("取 消")]))),_:1})])])])),default:n((()=>[s(I,{model:N.value,"label-position":"top",ref_key:"elFormRef",ref:j,rules:U,"label-width":"80px"},{default:n((()=>[s(A,{label:"商店名字:",prop:"shopName"},{default:n((()=>[s(ie,{modelValue:N.value.shopName,"onUpdate:modelValue":a[5]||(a[5]=e=>N.value.shopName=e),clearable:!0,placeholder:"请输入商店名字"},null,8,["modelValue"])])),_:1}),s(A,{label:"商店ID:",prop:"shopID"},{default:n((()=>[s(ie,{modelValue:N.value.shopID,"onUpdate:modelValue":a[6]||(a[6]=e=>N.value.shopID=e),clearable:!0,placeholder:"请输入商店ID"},null,8,["modelValue"])])),_:1}),s(A,{label:"商店API Key:",prop:"shopAPIKey"},{default:n((()=>[s(ie,{modelValue:N.value.shopAPIKey,"onUpdate:modelValue":a[7]||(a[7]=e=>N.value.shopAPIKey=e),clearable:!0,placeholder:"请输入商店API Key"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["size","modelValue"]),s(pe,{"destroy-on-close":"",size:f(B).drawerSize,modelValue:ue.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ue.value=e),"show-close":!0,"before-close":se,title:"查看"},{default:n((()=>[s(ve,{column:1,border:""},{default:n((()=>[s(ce,{label:"商店名字"},{default:n((()=>[i(m(oe.value.shopName),1)])),_:1}),s(ce,{label:"商店ID"},{default:n((()=>[i(m(oe.value.shopID),1)])),_:1}),s(ce,{label:"商店API Key"},{default:n((()=>[i(m(oe.value.shopAPIKey),1)])),_:1})])),_:1})])),_:1},8,["size","modelValue"])])}}});export{K as default};
