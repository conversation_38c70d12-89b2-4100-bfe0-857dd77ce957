/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,i as a}from"./087AC4D233B64EB0initdb.P7dovK8r.js";import{_ as l,r as s,a as t,c as d,b as o,d as r,n as m,e as i,f as n,w as p,g as u,u as b,o as c,h as g,i as h,E as y,j as w}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const f={class:"rounded-lg flex items-center justify-evenly w-full h-full relative md:w-screen md:h-screen md:bg-[#194bfb] overflow-hidden"},v={class:"rounded-md w-full h-full flex items-center justify-center overflow-hidden"},k={class:"text-lg"},q={class:"flex items-center justify-between mt-8"},x={style:{"text-align":"right"}},V=l(Object.assign({name:"Init"},{__name:"index",setup(l){const V=b(),N=s({showReadme:!1,showForm:!1}),_=()=>{N.showReadme=!1,setTimeout((()=>{N.showForm=!0}),20)},P=()=>{window.open("https://www.gin-vue-admin.com/guide/start-quickly/env.html")},T=t(!1),j=s({adminPassword:"123456",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""}),U=e=>{switch(e){case"mysql":Object.assign(j,{adminPassword:"123456",reAdminPassword:"",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""});break;case"pgsql":Object.assign(j,{adminPassword:"123456",dbType:"pgsql",host:"127.0.0.1",port:"5432",userName:"postgres",password:"",dbName:"gva",dbPath:"",template:"template0"});break;case"oracle":Object.assign(j,{adminPassword:"123456",dbType:"oracle",host:"127.0.0.1",port:"1521",userName:"oracle",password:"",dbName:"gva",dbPath:""});break;case"mssql":Object.assign(j,{adminPassword:"123456",dbType:"mssql",host:"127.0.0.1",port:"1433",userName:"mssql",password:"",dbName:"gva",dbPath:""});break;case"sqlite":Object.assign(j,{adminPassword:"123456",dbType:"sqlite",host:"",port:"",userName:"",password:"",dbName:"gva",dbPath:""});break;default:Object.assign(j,{adminPassword:"123456",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""})}},O=async()=>{if(j.adminPassword.length<6)return void y({type:"error",message:"密码长度不能小于6位"});const e=w.service({lock:!0,text:"正在初始化数据库，请稍候",spinner:"loading",background:"rgba(0, 0, 0, 0.7)"});try{const l=await a(j);0===l.code&&(T.value=!0,y({type:"success",message:l.msg}),V.push({name:"Login"})),e.close()}catch(l){e.close()}};return(a,l)=>{const s=u("el-button"),t=u("el-input"),b=u("el-form-item"),y=u("el-option"),w=u("el-select"),V=u("el-form");return c(),d("div",f,[o("div",v,[l[13]||(l[13]=o("div",{class:"oblique h-[130%] w-3/5 bg-white dark:bg-slate-900 transform -rotate-12 absolute -ml-80"},null,-1)),N.showForm?r("",!0):(c(),d("div",{key:0,class:m([N.showReadme?"slide-out-right":"slide-in-fwd-top"])},[o("div",k,[l[11]||(l[11]=i('<div class="font-sans text-4xl font-bold text-center mb-4 dark:text-white" data-v-e790578a> GIN-VUE-ADMIN </div><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a>初始化须知</p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 1.您需有用一定的VUE和GOLANG基础 </p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 2.请您确认是否已经阅读过<a class="text-blue-600 font-bold" href="https://www.gin-vue-admin.com" target="_blank" data-v-e790578a>官方文档</a><a class="text-blue-600 font-bold" href="https://www.bilibili.com/video/BV1kv4y1g7nT?p=2" target="_blank" data-v-e790578a>初始化视频</a></p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 3.请您确认是否了解后续的配置流程 </p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 4.如果您使用mysql数据库，请确认数据库引擎为<span class="text-red-600 font-bold text-3xl ml-2" data-v-e790578a>innoDB</span></p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 注：开发组不为文档中书写过的内容提供无偿服务 </p>',7)),o("p",q,[n(s,{type:"primary",size:"large",onClick:P},{default:p((()=>l[9]||(l[9]=[g(" 阅读文档 ")]))),_:1}),n(s,{type:"primary",size:"large",onClick:_},{default:p((()=>l[10]||(l[10]=[g(" 我已确认 ")]))),_:1})])])],2)),N.showForm?(c(),d("div",{key:1,class:m([[N.showForm?"slide-in-left":"slide-out-right"],"w-96"])},[n(V,{ref:"formRef",model:j,"label-width":"100px",size:"large"},{default:p((()=>[n(b,{label:"管理员密码"},{default:p((()=>[n(t,{modelValue:j.adminPassword,"onUpdate:modelValue":l[0]||(l[0]=e=>j.adminPassword=e),placeholder:"admin账号的默认密码"},null,8,["modelValue"])])),_:1}),n(b,{label:"数据库类型"},{default:p((()=>[n(w,{modelValue:j.dbType,"onUpdate:modelValue":l[1]||(l[1]=e=>j.dbType=e),placeholder:"请选择",class:"w-full",onChange:U},{default:p((()=>[n(y,{key:"mysql",label:"mysql",value:"mysql"}),n(y,{key:"pgsql",label:"pgsql",value:"pgsql"}),n(y,{key:"oracle",label:"oracle",value:"oracle"}),n(y,{key:"mssql",label:"mssql",value:"mssql"}),n(y,{key:"sqlite",label:"sqlite",value:"sqlite"})])),_:1},8,["modelValue"])])),_:1}),"sqlite"!==j.dbType?(c(),h(b,{key:0,label:"host"},{default:p((()=>[n(t,{modelValue:j.host,"onUpdate:modelValue":l[2]||(l[2]=e=>j.host=e),placeholder:"请输入数据库链接"},null,8,["modelValue"])])),_:1})):r("",!0),"sqlite"!==j.dbType?(c(),h(b,{key:1,label:"port"},{default:p((()=>[n(t,{modelValue:j.port,"onUpdate:modelValue":l[3]||(l[3]=e=>j.port=e),placeholder:"请输入数据库端口"},null,8,["modelValue"])])),_:1})):r("",!0),"sqlite"!==j.dbType?(c(),h(b,{key:2,label:"userName"},{default:p((()=>[n(t,{modelValue:j.userName,"onUpdate:modelValue":l[4]||(l[4]=e=>j.userName=e),placeholder:"请输入数据库用户名"},null,8,["modelValue"])])),_:1})):r("",!0),"sqlite"!==j.dbType?(c(),h(b,{key:3,label:"password"},{default:p((()=>[n(t,{modelValue:j.password,"onUpdate:modelValue":l[5]||(l[5]=e=>j.password=e),placeholder:"请输入数据库密码（没有则为空）"},null,8,["modelValue"])])),_:1})):r("",!0),n(b,{label:"dbName"},{default:p((()=>[n(t,{modelValue:j.dbName,"onUpdate:modelValue":l[6]||(l[6]=e=>j.dbName=e),placeholder:"请输入数据库名称"},null,8,["modelValue"])])),_:1}),"sqlite"===j.dbType?(c(),h(b,{key:4,label:"dbPath"},{default:p((()=>[n(t,{modelValue:j.dbPath,"onUpdate:modelValue":l[7]||(l[7]=e=>j.dbPath=e),placeholder:"请输入sqlite数据库文件存放路径"},null,8,["modelValue"])])),_:1})):r("",!0),"pgsql"===j.dbType?(c(),h(b,{key:5,label:"template"},{default:p((()=>[n(t,{modelValue:j.template,"onUpdate:modelValue":l[8]||(l[8]=e=>j.template=e),placeholder:"请输入postgresql指定template"},null,8,["modelValue"])])),_:1})):r("",!0),n(b,null,{default:p((()=>[o("div",x,[n(s,{type:"primary",onClick:O},{default:p((()=>l[12]||(l[12]=[g("立即初始化")]))),_:1})])])),_:1})])),_:1},8,["model"])],2)):r("",!0)]),l[14]||(l[14]=o("div",{class:"hidden md:block w-1/2 h-full float-right bg-[#194bfb]"},[o("img",{class:"h-full",src:e,alt:"banner"})],-1))])}}}),[["__scopeId","data-v-e790578a"]]);export{V as default};
