/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return n};var t,n={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},l=u.iterator||"@@iterator",s=u.asyncIterator||"@@asyncIterator",c=u.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function g(e,t,n,r){var a=t&&t.prototype instanceof k?t:k,o=Object.create(a.prototype),u=new N(r||[]);return i(o,"_invoke",{value:F(e,n,u)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=g;var p="suspendedStart",h="suspendedYield",m="executing",v="completed",x={};function k(){}function b(){}function y(){}var w={};d(w,l,(function(){return this}));var _=Object.getPrototypeOf,C=_&&_(_(I([])));C&&C!==r&&o.call(C,l)&&(w=C);var R=y.prototype=k.prototype=Object.create(w);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function T(t,n){function r(a,i,u,l){var s=f(t[a],t,i);if("throw"!==s.type){var c=s.arg,d=c.value;return d&&"object"==e(d)&&o.call(d,"__await")?n.resolve(d.__await).then((function(e){r("next",e,u,l)}),(function(e){r("throw",e,u,l)})):n.resolve(d).then((function(e){c.value=e,u(c)}),(function(e){return r("throw",e,u,l)}))}l(s.arg)}var a;i(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(o,o):o()}})}function F(e,n,r){var a=p;return function(o,i){if(a===m)throw Error("Generator is already running");if(a===v){if("throw"===o)throw i;return{value:t,done:!0}}for(r.method=o,r.arg=i;;){var u=r.delegate;if(u){var l=E(u,r);if(l){if(l===x)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===p)throw a=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=m;var s=f(e,n,r);if("normal"===s.type){if(a=r.done?v:h,s.arg===x)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(a=v,r.method="throw",r.arg=s.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),x;var o=f(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,x;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,x):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,x)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function I(n){if(n||""===n){var r=n[l];if(r)return r.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var a=-1,i=function e(){for(;++a<n.length;)if(o.call(n,a))return e.value=n[a],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(e(n)+" is not iterable")}return b.prototype=y,i(R,"constructor",{value:y,configurable:!0}),i(y,"constructor",{value:b,configurable:!0}),b.displayName=d(y,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,y):(e.__proto__=y,d(e,c,"GeneratorFunction")),e.prototype=Object.create(R),e},n.awrap=function(e){return{__await:e}},S(T.prototype),d(T.prototype,s,(function(){return this})),n.AsyncIterator=T,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var i=new T(g(e,t,r,a),o);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},S(R),d(R,c,"Generator"),d(R,l,(function(){return this})),d(R,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=I,N.prototype={constructor:N,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(A),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,a){return u.type="throw",u.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),s=o.call(i,"finallyLoc");if(l&&s){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,x):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),x},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),x}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;A(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:I(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),x}},n}function o(e,t,n,r,a,o,i){try{var u=e[o](i),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,a)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function u(e){o(i,r,a,u,l,"next",e)}function l(e){o(i,r,a,u,l,"throw",e)}u(void 0)}))}}System.register(["./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0stringFun-legacy.2vIcgB7Q.js","./087AC4D233B64EB0theme-github_dark-legacy.C-iZFals.js"],(function(e,t){"use strict";var r,o,u,l,s,c,d,g,f,p,h,m,v,x,k,b,y,w,_,C,R,S,T,F,E,L,A;return{setters:[function(e){r=e.i,o=e.j,u=e.r,l=e.k,s=e.b},function(e){c=e.a,d=e.u,g=e.g,f=e.ae,p=e.c,h=e.o,m=e.b,v=e.f,x=e.w,k=e.h,b=e.t,y=e.v,w=e.aa,_=e.i,C=e.a7,R=e.d,S=e.F,T=e.D,F=e.ab,E=e.E},function(e){L=e.b},function(e){A=e.V}],execute:function(){var t,N={exports:{}};t||(t=1,function(e){ace.define("ace/mode/jsdoc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var r=e("../lib/oop"),a=e("./text_highlight_rules").TextHighlightRules,o=function(){this.$rules={start:[{token:["comment.doc.tag","comment.doc.text","lparen.doc"],regex:"(@(?:param|member|typedef|property|namespace|var|const|callback))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:["rparen.doc","text.doc","variable.parameter.doc","lparen.doc","variable.parameter.doc","rparen.doc"],regex:/(})(\s*)(?:([\w=:\/\.]+)|(?:(\[)([\w=:\/\.\-\'\" ]+)(\])))/,next:"pop"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","lparen.doc"],regex:"(@(?:returns?|yields|type|this|suppress|public|protected|private|package|modifies|implements|external|exception|throws|enum|define|extends))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:'(@(?:alias|memberof|instance|module|name|lends|namespace|external|this|template|requires|param|implements|function|extends|typedef|mixes|constructor|var|memberof\\!|event|listens|exports|class|constructs|interface|emits|fires|throws|const|callback|borrows|augments))(\\s+)(\\w[\\w#.:/~"\\-]*)?'},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:"(@method)(\\s+)(\\w[\\w.\\(\\)]*)"},{token:"comment.doc.tag",regex:"@access\\s+(?:private|public|protected)"},{token:"comment.doc.tag",regex:"@kind\\s+(?:class|constant|event|external|file|function|member|mixin|module|namespace|typedef)"},{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},o.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}],"doc-syntax":[{token:"operator.doc",regex:/[|:]/},{token:"paren.doc",regex:/[\[\]]/}]},this.normalizeRules()};r.inherits(o,a),o.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},o.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},o.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.JsDocCommentHighlightRules=o})),ace.define("ace/mode/javascript_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/jsdoc_comment_highlight_rules","ace/mode/text_highlight_rules"],(function(e,t,n){var r=e("../lib/oop"),a=e("./jsdoc_comment_highlight_rules").JsDocCommentHighlightRules,o=e("./text_highlight_rules").TextHighlightRules,i="[a-zA-Z\\$_¡-￿][a-zA-Z\\d\\$_¡-￿]*",u=function(e){var t={"variable.language":"Array|Boolean|Date|Function|Iterator|Number|Object|RegExp|String|Proxy|Symbol|Namespace|QName|XML|XMLList|ArrayBuffer|Float32Array|Float64Array|Int16Array|Int32Array|Int8Array|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray|Error|EvalError|InternalError|RangeError|ReferenceError|StopIteration|SyntaxError|TypeError|URIError|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|eval|isFinite|isNaN|parseFloat|parseInt|JSON|Math|this|arguments|prototype|window|document",keyword:"const|yield|import|get|set|async|await|break|case|catch|continue|default|delete|do|else|finally|for|if|in|of|instanceof|new|return|switch|throw|try|typeof|let|var|while|with|debugger|__parent__|__count__|escape|unescape|with|__proto__|class|enum|extends|super|export|implements|private|public|interface|package|protected|static|constructor","storage.type":"const|let|var|function","constant.language":"null|Infinity|NaN|undefined","support.function":"alert","constant.language.boolean":"true|false"},n=this.createKeywordMapper(t,"identifier"),r="\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|u{[0-9a-fA-F]{1,6}}|[0-2][0-7]{0,2}|3[0-7][0-7]?|[4-7][0-7]?|.)",o="(function)(\\s*)(\\*?)",u={token:["identifier","text","paren.lparen"],regex:"(\\b(?!"+Object.values(t).join("|")+"\\b)"+i+")(\\s*)(\\()"};this.$rules={no_regex:[a.getStartRule("doc-start"),s("no_regex"),u,{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:["entity.name.function","text","keyword.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(=)(\\s*)"+o+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))("+i+")(\\s*)(\\()",next:"function_arguments"},{token:["entity.name.function","text","punctuation.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(:)(\\s*)"+o+"(\\s*)(\\()",next:"function_arguments"},{token:["text","text","storage.type","text","storage.type","text","paren.lparen"],regex:"(:)(\\s*)"+o+"(\\s*)(\\()",next:"function_arguments"},{token:"keyword",regex:"from(?=\\s*('|\"))"},{token:"keyword",regex:"(?:case|do|else|finally|in|instanceof|return|throw|try|typeof|yield|void)\\b",next:"start"},{token:"support.constant",regex:/that\b/},{token:["storage.type","punctuation.operator","support.function.firebug"],regex:/(console)(\.)(warn|info|log|error|debug|time|trace|timeEnd|assert)\b/},{token:n,regex:i},{token:"punctuation.operator",regex:/[.](?![.])/,next:"property"},{token:"storage.type",regex:/=>/,next:"start"},{token:"keyword.operator",regex:/--|\+\+|\.{3}|===|==|=|!=|!==|<+=?|>+=?|!|&&|\|\||\?:|[!$%&*+\-~\/^]=?/,next:"start"},{token:"punctuation.operator",regex:/[?:,;.]/,next:"start"},{token:"paren.lparen",regex:/[\[({]/,next:"start"},{token:"paren.rparen",regex:/[\])}]/},{token:"comment",regex:/^#!.*$/}],property:[{token:"text",regex:"\\s+"},{token:"keyword.operator",regex:/=/},{token:["storage.type","text","storage.type","text","paren.lparen"],regex:o+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))(\\w+)(\\s*)(\\()",next:"function_arguments"},{token:"punctuation.operator",regex:/[.](?![.])/},{token:"support.function",regex:"prototype"},{token:"support.function",regex:/(s(?:h(?:ift|ow(?:Mod(?:elessDialog|alDialog)|Help))|croll(?:X|By(?:Pages|Lines)?|Y|To)?|t(?:op|rike)|i(?:n|zeToContent|debar|gnText)|ort|u(?:p|b(?:str(?:ing)?)?)|pli(?:ce|t)|e(?:nd|t(?:Re(?:sizable|questHeader)|M(?:i(?:nutes|lliseconds)|onth)|Seconds|Ho(?:tKeys|urs)|Year|Cursor|Time(?:out)?|Interval|ZOptions|Date|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Date|FullYear)|FullYear|Active)|arch)|qrt|lice|avePreferences|mall)|h(?:ome|andleEvent)|navigate|c(?:har(?:CodeAt|At)|o(?:s|n(?:cat|textual|firm)|mpile)|eil|lear(?:Timeout|Interval)?|a(?:ptureEvents|ll)|reate(?:StyleSheet|Popup|EventObject))|t(?:o(?:GMTString|S(?:tring|ource)|U(?:TCString|pperCase)|Lo(?:caleString|werCase))|est|a(?:n|int(?:Enabled)?))|i(?:s(?:NaN|Finite)|ndexOf|talics)|d(?:isableExternalCapture|ump|etachEvent)|u(?:n(?:shift|taint|escape|watch)|pdateCommands)|j(?:oin|avaEnabled)|p(?:o(?:p|w)|ush|lugins.refresh|a(?:ddings|rse(?:Int|Float)?)|r(?:int|ompt|eference))|e(?:scape|nableExternalCapture|val|lementFromPoint|x(?:p|ec(?:Script|Command)?))|valueOf|UTC|queryCommand(?:State|Indeterm|Enabled|Value)|f(?:i(?:nd|lter|le(?:ModifiedDate|Size|CreatedDate|UpdatedDate)|xed)|o(?:nt(?:size|color)|rward|rEach)|loor|romCharCode)|watch|l(?:ink|o(?:ad|g)|astIndexOf)|a(?:sin|nchor|cos|t(?:tachEvent|ob|an(?:2)?)|pply|lert|b(?:s|ort))|r(?:ou(?:nd|teEvents)|e(?:size(?:By|To)|calc|turnValue|place|verse|l(?:oad|ease(?:Capture|Events)))|andom)|g(?:o|et(?:ResponseHeader|M(?:i(?:nutes|lliseconds)|onth)|Se(?:conds|lection)|Hours|Year|Time(?:zoneOffset)?|Da(?:y|te)|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Da(?:y|te)|FullYear)|FullYear|A(?:ttention|llResponseHeaders)))|m(?:in|ove(?:B(?:y|elow)|To(?:Absolute)?|Above)|ergeAttributes|a(?:tch|rgins|x))|b(?:toa|ig|o(?:ld|rderWidths)|link|ack))\b(?=\()/},{token:"support.function.dom",regex:/(s(?:ub(?:stringData|mit)|plitText|e(?:t(?:NamedItem|Attribute(?:Node)?)|lect))|has(?:ChildNodes|Feature)|namedItem|c(?:l(?:ick|o(?:se|neNode))|reate(?:C(?:omment|DATASection|aption)|T(?:Head|extNode|Foot)|DocumentFragment|ProcessingInstruction|E(?:ntityReference|lement)|Attribute))|tabIndex|i(?:nsert(?:Row|Before|Cell|Data)|tem)|open|delete(?:Row|C(?:ell|aption)|T(?:Head|Foot)|Data)|focus|write(?:ln)?|a(?:dd|ppend(?:Child|Data))|re(?:set|place(?:Child|Data)|move(?:NamedItem|Child|Attribute(?:Node)?)?)|get(?:NamedItem|Element(?:sBy(?:Name|TagName|ClassName)|ById)|Attribute(?:Node)?)|blur)\b(?=\()/},{token:"support.constant",regex:/(s(?:ystemLanguage|cr(?:ipts|ollbars|een(?:X|Y|Top|Left))|t(?:yle(?:Sheets)?|atus(?:Text|bar)?)|ibling(?:Below|Above)|ource|uffixes|e(?:curity(?:Policy)?|l(?:ection|f)))|h(?:istory|ost(?:name)?|as(?:h|Focus))|y|X(?:MLDocument|SLDocument)|n(?:ext|ame(?:space(?:s|URI)|Prop))|M(?:IN_VALUE|AX_VALUE)|c(?:haracterSet|o(?:n(?:structor|trollers)|okieEnabled|lorDepth|mp(?:onents|lete))|urrent|puClass|l(?:i(?:p(?:boardData)?|entInformation)|osed|asses)|alle(?:e|r)|rypto)|t(?:o(?:olbar|p)|ext(?:Transform|Indent|Decoration|Align)|ags)|SQRT(?:1_2|2)|i(?:n(?:ner(?:Height|Width)|put)|ds|gnoreCase)|zIndex|o(?:scpu|n(?:readystatechange|Line)|uter(?:Height|Width)|p(?:sProfile|ener)|ffscreenBuffering)|NEGATIVE_INFINITY|d(?:i(?:splay|alog(?:Height|Top|Width|Left|Arguments)|rectories)|e(?:scription|fault(?:Status|Ch(?:ecked|arset)|View)))|u(?:ser(?:Profile|Language|Agent)|n(?:iqueID|defined)|pdateInterval)|_content|p(?:ixelDepth|ort|ersonalbar|kcs11|l(?:ugins|atform)|a(?:thname|dding(?:Right|Bottom|Top|Left)|rent(?:Window|Layer)?|ge(?:X(?:Offset)?|Y(?:Offset)?))|r(?:o(?:to(?:col|type)|duct(?:Sub)?|mpter)|e(?:vious|fix)))|e(?:n(?:coding|abledPlugin)|x(?:ternal|pando)|mbeds)|v(?:isibility|endor(?:Sub)?|Linkcolor)|URLUnencoded|P(?:I|OSITIVE_INFINITY)|f(?:ilename|o(?:nt(?:Size|Family|Weight)|rmName)|rame(?:s|Element)|gColor)|E|whiteSpace|l(?:i(?:stStyleType|n(?:eHeight|kColor))|o(?:ca(?:tion(?:bar)?|lName)|wsrc)|e(?:ngth|ft(?:Context)?)|a(?:st(?:M(?:odified|atch)|Index|Paren)|yer(?:s|X)|nguage))|a(?:pp(?:MinorVersion|Name|Co(?:deName|re)|Version)|vail(?:Height|Top|Width|Left)|ll|r(?:ity|guments)|Linkcolor|bove)|r(?:ight(?:Context)?|e(?:sponse(?:XML|Text)|adyState))|global|x|m(?:imeTypes|ultiline|enubar|argin(?:Right|Bottom|Top|Left))|L(?:N(?:10|2)|OG(?:10E|2E))|b(?:o(?:ttom|rder(?:Width|RightWidth|BottomWidth|Style|Color|TopWidth|LeftWidth))|ufferDepth|elow|ackground(?:Color|Image)))\b/},{token:"identifier",regex:i},{regex:"",token:"empty",next:"no_regex"}],start:[a.getStartRule("doc-start"),s("start"),{token:"string.regexp",regex:"\\/",next:"regex"},{token:"text",regex:"\\s+|^$",next:"start"},{token:"empty",regex:"",next:"no_regex"}],regex:[{token:"regexp.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"string.regexp",regex:"/[sxngimy]*",next:"no_regex"},{token:"invalid",regex:/\{\d+\b,?\d*\}[+*]|[+*$^?][+*]|[$^][?]|\?{3,}/},{token:"constant.language.escape",regex:/\(\?[:=!]|\)|\{\d+\b,?\d*\}|[+*]\?|[()$^+*?.]/},{token:"constant.language.delimiter",regex:/\|/},{token:"constant.language.escape",regex:/\[\^?/,next:"regex_character_class"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp"}],regex_character_class:[{token:"regexp.charclass.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"constant.language.escape",regex:"]",next:"regex"},{token:"constant.language.escape",regex:"-"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp.charachterclass"}],default_parameter:[{token:"string",regex:"'(?=.)",push:[{token:"string",regex:"'|$",next:"pop"},{include:"qstring"}]},{token:"string",regex:'"(?=.)',push:[{token:"string",regex:'"|$',next:"pop"},{include:"qqstring"}]},{token:"constant.language",regex:"null|Infinity|NaN|undefined"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:"punctuation.operator",regex:",",next:"function_arguments"},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],function_arguments:[s("function_arguments"),{token:"variable.parameter",regex:i},{token:"punctuation.operator",regex:","},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],qqstring:[{token:"constant.language.escape",regex:r},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:'"|$',next:"no_regex"},{defaultToken:"string"}],qstring:[{token:"constant.language.escape",regex:r},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:"'|$",next:"no_regex"},{defaultToken:"string"}]},e&&e.noES6||(this.$rules.no_regex.unshift({regex:"[{}]",onMatch:function(e,t,n){if(this.next="{"==e?this.nextState:"","{"==e&&n.length)n.unshift("start",t);else if("}"==e&&n.length&&(n.shift(),this.next=n.shift(),-1!=this.next.indexOf("string")||-1!=this.next.indexOf("jsx")))return"paren.quasi.end";return"{"==e?"paren.lparen":"paren.rparen"},nextState:"start"},{token:"string.quasi.start",regex:/`/,push:[{token:"constant.language.escape",regex:r},{token:"paren.quasi.start",regex:/\${/,push:"start"},{token:"string.quasi.end",regex:/`/,next:"pop"},{defaultToken:"string.quasi"}]},{token:["variable.parameter","text"],regex:"("+i+")(\\s*)(?=\\=>)"},{token:"paren.lparen",regex:"(\\()(?=[^\\(]+\\s*=>)",next:"function_arguments"},{token:"variable.language",regex:"(?:(?:(?:Weak)?(?:Set|Map))|Promise)\\b"}),this.$rules.function_arguments.unshift({token:"keyword.operator",regex:"=",next:"default_parameter"},{token:"keyword.operator",regex:"\\.{3}"}),this.$rules.property.unshift({token:"support.function",regex:"(findIndex|repeat|startsWith|endsWith|includes|isSafeInteger|trunc|cbrt|log2|log10|sign|then|catch|finally|resolve|reject|race|any|all|allSettled|keys|entries|isInteger)\\b(?=\\()"},{token:"constant.language",regex:"(?:MAX_SAFE_INTEGER|MIN_SAFE_INTEGER|EPSILON)\\b"}),e&&0==e.jsx||l.call(this)),this.embedRules(a,"doc-",[a.getEndRule("no_regex")]),this.normalizeRules()};function l(){var e=i.replace("\\d","\\d\\-"),t={onMatch:function(e,t,n){var r="/"==e.charAt(1)?2:1;return 1==r?(t!=this.nextState?n.unshift(this.next,this.nextState,0):n.unshift(this.next),n[2]++):2==r&&t==this.nextState&&(n[1]--,(!n[1]||n[1]<0)&&(n.shift(),n.shift())),[{type:"meta.tag.punctuation."+(1==r?"":"end-")+"tag-open.xml",value:e.slice(0,r)},{type:"meta.tag.tag-name.xml",value:e.substr(r)}]},regex:"</?(?:"+e+"|(?=>))",next:"jsxAttributes",nextState:"jsx"};this.$rules.start.unshift(t);var n={regex:"{",token:"paren.quasi.start",push:"start"};this.$rules.jsx=[n,t,{include:"reference"},{defaultToken:"string.xml"}],this.$rules.jsxAttributes=[{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",onMatch:function(e,t,n){return t==n[0]&&n.shift(),2==e.length&&(n[0]==this.nextState&&n[1]--,(!n[1]||n[1]<0)&&n.splice(0,2)),this.next=n[0]||"start",[{type:this.token,value:e}]},nextState:"jsx"},n,s("jsxAttributes"),{token:"entity.other.attribute-name.xml",regex:e},{token:"keyword.operator.attribute-equals.xml",regex:"="},{token:"text.tag-whitespace.xml",regex:"\\s+"},{token:"string.attribute-value.xml",regex:"'",stateName:"jsx_attr_q",push:[{token:"string.attribute-value.xml",regex:"'",next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},{token:"string.attribute-value.xml",regex:'"',stateName:"jsx_attr_qq",push:[{token:"string.attribute-value.xml",regex:'"',next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},t],this.$rules.reference=[{token:"constant.language.escape.reference.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}]}function s(e){return[{token:"comment",regex:/\/\*/,next:[a.getTagRule(),{token:"comment",regex:"\\*\\/",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]},{token:"comment",regex:"\\/\\/",next:[a.getTagRule(),{token:"comment",regex:"$|^",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]}]}r.inherits(u,o),t.JavaScriptHighlightRules=u})),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],(function(e,t,n){var r=e("../range").Range,a=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var a=n[1].length,o=e.findMatchingBracket({row:t,column:a});if(!o||o.row==t)return 0;var i=this.$getIndent(e.getLine(o.row));e.replace(new r(t,0,t,a-1),i)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(a.prototype),t.MatchingBraceOutdent=a})),ace.define("ace/mode/behaviour/xml",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/token_iterator"],(function(e,t,n){var r=e("../../lib/oop"),a=e("../behaviour").Behaviour,o=e("../../token_iterator").TokenIterator;function i(e,t){return e&&e.type.lastIndexOf(t+".xml")>-1}var u=function(){this.add("string_dquotes","insertion",(function(e,t,n,r,a){if('"'==a||"'"==a){var u=a,l=r.doc.getTextRange(n.getSelectionRange());if(""!==l&&"'"!==l&&'"'!=l&&n.getWrapBehavioursEnabled())return{text:u+l+u,selection:!1};var s=n.getCursorPosition(),c=r.doc.getLine(s.row).substring(s.column,s.column+1),d=new o(r,s.row,s.column),g=d.getCurrentToken();if(c==u&&(i(g,"attribute-value")||i(g,"string")))return{text:"",selection:[1,1]};if(g||(g=d.stepBackward()),!g)return;for(;i(g,"tag-whitespace")||i(g,"whitespace");)g=d.stepBackward();var f=!c||c.match(/\s/);if(i(g,"attribute-equals")&&(f||">"==c)||i(g,"decl-attribute-equals")&&(f||"?"==c))return{text:u+u,selection:[1,1]}}})),this.add("string_dquotes","deletion",(function(e,t,n,r,a){var o=r.doc.getTextRange(a);if(!a.isMultiLine()&&('"'==o||"'"==o)&&r.doc.getLine(a.start.row).substring(a.start.column+1,a.start.column+2)==o)return a.end.column++,a})),this.add("autoclosing","insertion",(function(e,t,n,r,a){if(">"==a){var u=n.getSelectionRange().start,l=new o(r,u.row,u.column),s=l.getCurrentToken()||l.stepBackward();if(!s||!(i(s,"tag-name")||i(s,"tag-whitespace")||i(s,"attribute-name")||i(s,"attribute-equals")||i(s,"attribute-value")))return;if(i(s,"reference.attribute-value"))return;if(i(s,"attribute-value")){var c=l.getCurrentTokenColumn()+s.value.length;if(u.column<c)return;if(u.column==c){var d=l.stepForward();if(d&&i(d,"attribute-value"))return;l.stepBackward()}}if(/^\s*>/.test(r.getLine(u.row).slice(u.column)))return;for(;!i(s,"tag-name");)if("<"==(s=l.stepBackward()).value){s=l.stepForward();break}var g=l.getCurrentTokenRow(),f=l.getCurrentTokenColumn();if(i(l.stepBackward(),"end-tag-open"))return;var p=s.value;if(g==u.row&&(p=p.substring(0,u.column-f)),this.voidElements&&this.voidElements.hasOwnProperty(p.toLowerCase()))return;return{text:"></"+p+">",selection:[1,1]}}})),this.add("autoindent","insertion",(function(e,t,n,r,a){if("\n"==a){var u=n.getCursorPosition(),l=r.getLine(u.row),s=new o(r,u.row,u.column),c=s.getCurrentToken();if(i(c,"")&&-1!==c.type.indexOf("tag-close")){if("/>"==c.value)return;for(;c&&-1===c.type.indexOf("tag-name");)c=s.stepBackward();if(!c)return;var d=c.value,g=s.getCurrentTokenRow();if(!(c=s.stepBackward())||-1!==c.type.indexOf("end-tag"))return;if(this.voidElements&&!this.voidElements[d]||!this.voidElements){var f=r.getTokenAt(u.row,u.column+1),p=(l=r.getLine(g),this.$getIndent(l)),h=p+r.getTabString();return f&&"</"===f.value?{text:"\n"+h+"\n"+p,selection:[1,h.length,1,h.length]}:{text:"\n"+h}}}}}))};r.inherits(u,a),t.XmlBehaviour=u})),ace.define("ace/mode/behaviour/javascript",["require","exports","module","ace/lib/oop","ace/token_iterator","ace/mode/behaviour/cstyle","ace/mode/behaviour/xml"],(function(e,t,n){var r=e("../../lib/oop"),a=e("../../token_iterator").TokenIterator,o=e("../behaviour/cstyle").CstyleBehaviour,i=e("../behaviour/xml").XmlBehaviour,u=function(){var e=new i({closeCurlyBraces:!0}).getBehaviours();this.addBehaviours(e),this.inherit(o),this.add("autoclosing-fragment","insertion",(function(e,t,n,r,o){if(">"==o){var i=n.getSelectionRange().start,u=new a(r,i.row,i.column),l=u.getCurrentToken()||u.stepBackward();if(!l)return;if("<"==l.value)return{text:"></>",selection:[1,1]}}}))};r.inherits(u,o),t.JavaScriptBehaviour=u})),ace.define("ace/mode/folding/xml",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var r=e("../../lib/oop"),a=e("../../range").Range,o=e("./fold_mode").FoldMode,i=t.FoldMode=function(e,t){o.call(this),this.voidElements=e||{},this.optionalEndTags=r.mixin({},this.voidElements),t&&r.mixin(this.optionalEndTags,t)};r.inherits(i,o);var u=function(){this.tagName="",this.closing=!1,this.selfClosing=!1,this.start={row:0,column:0},this.end={row:0,column:0}};function l(e,t){return e&&e.type&&e.type.lastIndexOf(t+".xml")>-1}(function(){this.getFoldWidget=function(e,t,n){var r=this._getFirstTagInLine(e,n);return r?r.closing||!r.tagName&&r.selfClosing?"markbeginend"===t?"end":"":!r.tagName||r.selfClosing||this.voidElements.hasOwnProperty(r.tagName.toLowerCase())||this._findEndTagInLine(e,n,r.tagName,r.end.column)?"":"start":this.getCommentFoldWidget(e,n)},this.getCommentFoldWidget=function(e,t){return/comment/.test(e.getState(t))&&/<!-/.test(e.getLine(t))?"start":""},this._getFirstTagInLine=function(e,t){for(var n=e.getTokens(t),r=new u,a=0;a<n.length;a++){var o=n[a];if(l(o,"tag-open")){if(r.end.column=r.start.column+o.value.length,r.closing=l(o,"end-tag-open"),!(o=n[++a]))return null;if(r.tagName=o.value,""===o.value){if(!(o=n[++a]))return null;r.tagName=o.value}for(r.end.column+=o.value.length,a++;a<n.length;a++)if(o=n[a],r.end.column+=o.value.length,l(o,"tag-close")){r.selfClosing="/>"==o.value;break}return r}if(l(o,"tag-close"))return r.selfClosing="/>"==o.value,r;r.start.column+=o.value.length}return null},this._findEndTagInLine=function(e,t,n,r){for(var a=e.getTokens(t),o=0,i=0;i<a.length;i++){var u=a[i];if(!((o+=u.value.length)<r-1)&&l(u,"end-tag-open")&&(l(u=a[i+1],"tag-name")&&""===u.value&&(u=a[i+2]),u&&u.value==n))return!0}return!1},this.getFoldWidgetRange=function(e,t,n){if(!this._getFirstTagInLine(e,n))return this.getCommentFoldWidget(e,n)&&e.getCommentFoldRange(n,e.getLine(n).length);var r=e.getMatchingTags({row:n,column:0});return r?new a(r.openTag.end.row,r.openTag.end.column,r.closeTag.start.row,r.closeTag.start.column):void 0}}).call(i.prototype)})),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var r=e("../../lib/oop"),a=e("../../range").Range,o=e("./fold_mode").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};r.inherits(i,o),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var r=e.getLine(n);if(this.singleLineBlockCommentRe.test(r)&&!this.startRegionRe.test(r)&&!this.tripleStarBlockCommentRe.test(r))return"";var a=this._getFoldWidgetBase(e,t,n);return!a&&this.startRegionRe.test(r)?"start":a},this.getFoldWidgetRange=function(e,t,n,r){var a,o=e.getLine(n);if(this.startRegionRe.test(o))return this.getCommentRegionBlock(e,o,n);if(a=o.match(this.foldingStartMarker)){var i=a.index;if(a[1])return this.openingBracketBlock(e,a[1],n,i);var u=e.getCommentFoldRange(n,i+a[0].length,1);return u&&!u.isMultiLine()&&(r?u=this.getSectionRange(e,n):"all"!=t&&(u=null)),u}return"markbegin"!==t&&(a=o.match(this.foldingStopMarker))?(i=a.index+a[0].length,a[1]?this.closingBracketBlock(e,a[1],n,i):e.getCommentFoldRange(n,i,-1)):void 0},this.getSectionRange=function(e,t){for(var n=e.getLine(t),r=n.search(/\S/),o=t,i=n.length,u=t+=1,l=e.getLength();++t<l;){var s=(n=e.getLine(t)).search(/\S/);if(-1!==s){if(r>s)break;var c=this.getFoldWidgetRange(e,"all",t);if(c){if(c.start.row<=o)break;if(c.isMultiLine())t=c.end.row;else if(r==s)break}u=t}}return new a(o,i,u,e.getLine(u).length)},this.getCommentRegionBlock=function(e,t,n){for(var r=t.search(/\s*$/),o=e.getLength(),i=n,u=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,l=1;++n<o;){t=e.getLine(n);var s=u.exec(t);if(s&&(s[1]?l--:l++,!l))break}if(n>i)return new a(i,r,n,t.length)}}.call(i.prototype)})),ace.define("ace/mode/folding/javascript",["require","exports","module","ace/lib/oop","ace/mode/folding/xml","ace/mode/folding/cstyle"],(function(e,t,n){var r=e("../../lib/oop"),a=e("./xml").FoldMode,o=e("./cstyle").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end))),this.xmlFoldMode=new a};r.inherits(i,o),function(){this.getFoldWidgetRangeBase=this.getFoldWidgetRange,this.getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var r=this.getFoldWidgetBase(e,t,n);return r||this.xmlFoldMode.getFoldWidget(e,t,n)},this.getFoldWidgetRange=function(e,t,n,r){var a=this.getFoldWidgetRangeBase(e,t,n,r);return a||this.xmlFoldMode.getFoldWidgetRange(e,t,n)}}.call(i.prototype)})),ace.define("ace/mode/javascript",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/javascript_highlight_rules","ace/mode/matching_brace_outdent","ace/worker/worker_client","ace/mode/behaviour/javascript","ace/mode/folding/javascript"],(function(e,t,n){var r=e("../lib/oop"),a=e("./text").Mode,o=e("./javascript_highlight_rules").JavaScriptHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,u=e("../worker/worker_client").WorkerClient,l=e("./behaviour/javascript").JavaScriptBehaviour,s=e("./folding/javascript").FoldMode,c=function(){this.HighlightRules=o,this.$outdent=new i,this.$behaviour=new l,this.foldingRules=new s};r.inherits(c,a),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.$quotes={'"':'"',"'":"'","`":"`"},this.$pairQuotesAfter={"`":/\w/},this.getNextLineIndent=function(e,t,n){var r=this.$getIndent(t),a=this.getTokenizer().getLineTokens(t,e),o=a.tokens,i=a.state;if(o.length&&"comment"==o[o.length-1].type)return r;if("start"==e||"no_regex"==e)t.match(/^.*(?:\bcase\b.*:|[\{\(\[])\s*$/)&&(r+=n);else if("doc-start"==e&&("start"==i||"no_regex"==i))return"";return r},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.createWorker=function(e){var t=new u(["ace"],"ace/mode/javascript_worker","JavaScriptWorker");return t.attachToDocument(e.getDocument()),t.on("annotate",(function(t){e.setAnnotations(t.data)})),t.on("terminate",(function(){e.clearAnnotations()})),t},this.$id="ace/mode/javascript",this.snippetFileId="ace/snippets/javascript"}.call(c.prototype),t.Mode=c})),ace.require(["ace/mode/javascript"],(function(t){e&&(e.exports=t)}))}(N));var I,B={exports:{}};I||(I=1,function(e){ace.define("ace/mode/doc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var r=e("../lib/oop"),a=e("./text_highlight_rules").TextHighlightRules,o=function(){this.$rules={start:[{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},o.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}]}};r.inherits(o,a),o.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},o.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},o.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.DocCommentHighlightRules=o})),ace.define("ace/mode/golang_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/doc_comment_highlight_rules","ace/mode/text_highlight_rules"],(function(e,t,n){var r=e("../lib/oop"),a=e("./doc_comment_highlight_rules").DocCommentHighlightRules,o=e("./text_highlight_rules").TextHighlightRules,i=function(){var e=this.createKeywordMapper({keyword:"else|break|case|return|goto|if|const|select|continue|struct|default|switch|for|range|func|import|package|chan|defer|fallthrough|go|interface|map|range|select|type|var","constant.language":"nil|true|false|iota","support.function":"new|close|cap|copy|panic|panicln|print|println|len|make|delete|real|recover|imag|append","support.type":"string|uint8|uint16|uint32|uint64|int8|int16|int32|int64|float32|float64|complex64|complex128|byte|rune|uint|int|uintptr|bool|error"},""),t="\\\\(?:[0-7]{3}|x\\h{2}|u{4}|U\\h{6}|[abfnrtv'\"\\\\])".replace(/\\h/g,"[a-fA-F\\d]");this.$rules={start:[{token:"comment",regex:"\\/\\/.*$"},a.getStartRule("doc-start"),{token:"comment.start",regex:"\\/\\*",next:"comment"},{token:"string",regex:/"(?:[^"\\]|\\.)*?"/},{token:"string",regex:"`",next:"bqstring"},{token:"constant.numeric",regex:"'(?:[^\\'\ud800-\udbff]|[\ud800-\udbff][\udc00-\udfff]|"+t.replace('"',"")+")'"},{token:"constant.numeric",regex:"0[xX][0-9a-fA-F]+\\b"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?\\b"},{token:["keyword","text","entity.name.function"],regex:"(func)(\\s+)([a-zA-Z_$][a-zA-Z0-9_$]*)\\b"},{token:function(t){return"("==t[t.length-1]?[{type:e(t.slice(0,-1))||"support.function",value:t.slice(0,-1)},{type:"paren.lparen",value:t.slice(-1)}]:e(t)||"identifier"},regex:"[a-zA-Z_$][a-zA-Z0-9_$]*\\b\\(?"},{token:"keyword.operator",regex:"!|\\$|%|&|\\*|\\-\\-|\\-|\\+\\+|\\+|~|==|=|!=|<=|>=|<<=|>>=|>>>=|<>|<|>|!|&&|\\|\\||\\?\\:|\\*=|%=|\\+=|\\-=|&=|\\^="},{token:"punctuation.operator",regex:"\\?|\\:|\\,|\\;|\\."},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:"\\s+"}],comment:[{token:"comment.end",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}],bqstring:[{token:"string",regex:"`",next:"start"},{defaultToken:"string"}]},this.embedRules(a,"doc-",[a.getEndRule("start")])};r.inherits(i,o),t.GolangHighlightRules=i})),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],(function(e,t,n){var r=e("../range").Range,a=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var a=n[1].length,o=e.findMatchingBracket({row:t,column:a});if(!o||o.row==t)return 0;var i=this.$getIndent(e.getLine(o.row));e.replace(new r(t,0,t,a-1),i)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(a.prototype),t.MatchingBraceOutdent=a})),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var r=e("../../lib/oop"),a=e("../../range").Range,o=e("./fold_mode").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};r.inherits(i,o),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var r=e.getLine(n);if(this.singleLineBlockCommentRe.test(r)&&!this.startRegionRe.test(r)&&!this.tripleStarBlockCommentRe.test(r))return"";var a=this._getFoldWidgetBase(e,t,n);return!a&&this.startRegionRe.test(r)?"start":a},this.getFoldWidgetRange=function(e,t,n,r){var a,o=e.getLine(n);if(this.startRegionRe.test(o))return this.getCommentRegionBlock(e,o,n);if(a=o.match(this.foldingStartMarker)){var i=a.index;if(a[1])return this.openingBracketBlock(e,a[1],n,i);var u=e.getCommentFoldRange(n,i+a[0].length,1);return u&&!u.isMultiLine()&&(r?u=this.getSectionRange(e,n):"all"!=t&&(u=null)),u}return"markbegin"!==t&&(a=o.match(this.foldingStopMarker))?(i=a.index+a[0].length,a[1]?this.closingBracketBlock(e,a[1],n,i):e.getCommentFoldRange(n,i,-1)):void 0},this.getSectionRange=function(e,t){for(var n=e.getLine(t),r=n.search(/\S/),o=t,i=n.length,u=t+=1,l=e.getLength();++t<l;){var s=(n=e.getLine(t)).search(/\S/);if(-1!==s){if(r>s)break;var c=this.getFoldWidgetRange(e,"all",t);if(c){if(c.start.row<=o)break;if(c.isMultiLine())t=c.end.row;else if(r==s)break}u=t}}return new a(o,i,u,e.getLine(u).length)},this.getCommentRegionBlock=function(e,t,n){for(var r=t.search(/\s*$/),o=e.getLength(),i=n,u=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,l=1;++n<o;){t=e.getLine(n);var s=u.exec(t);if(s&&(s[1]?l--:l++,!l))break}if(n>i)return new a(i,r,n,t.length)}}.call(i.prototype)})),ace.define("ace/mode/golang",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/golang_highlight_rules","ace/mode/matching_brace_outdent","ace/mode/folding/cstyle"],(function(e,t,n){var r=e("../lib/oop"),a=e("./text").Mode,o=e("./golang_highlight_rules").GolangHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,u=e("./folding/cstyle").FoldMode,l=function(){this.HighlightRules=o,this.$outdent=new i,this.foldingRules=new u,this.$behaviour=this.$defaultBehaviour};r.inherits(l,a),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.getNextLineIndent=function(e,t,n){var r=this.$getIndent(t),a=this.getTokenizer().getLineTokens(t,e),o=a.tokens;return a.state,o.length&&"comment"==o[o.length-1].type||"start"==e&&t.match(/^.*[\{\(\[]\s*$/)&&(r+=n),r},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.$id="ace/mode/golang"}.call(l.prototype),t.Mode=l})),ace.require(["ace/mode/golang"],(function(t){e&&(e.exports=t)}))}(B));var O={class:"gva-table-box"},M={class:"gva-btn-list"},$={class:"gva-pagination"},j={class:"dialog-footer"},V={class:"flex justify-between items-center"},D={class:""},q={class:"relative w-full"},P={class:"flex w-full gap-2"};e("default",Object.assign({name:"AutoCodeAdmin"},{__name:"index",setup:function(e){var t=c(!1),N=c({id:void 0,deleteApi:!0,deleteMenu:!0,deleteTable:!1}),I=d(),B=c(!1),W=c(""),U=c(1),H=c(0),z=c(10),X=c([]),G=c(""),J=c({package:"",funcName:"",structName:"",packageName:"",description:"",abbreviation:"",humpPackageName:"",businessDB:"",method:"",funcDesc:"",isAuth:!1,isAi:!1,apiFunc:"",serverFunc:"",jsFunc:""}),Y=c(!1),Z=function(){Y.value=!1},K=function(){var e=i(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(J.value.funcName=L(J.value.funcName),J.value.funcName){e.next=4;break}return E.error("请输入方法名"),e.abrupt("return");case 4:if(J.value.method){e.next=7;break}return E.error("请选择方法"),e.abrupt("return");case 7:if(J.value.router){e.next=10;break}return E.error("请输入路由"),e.abrupt("return");case 10:if(J.value.funcDesc){e.next=13;break}return E.error("请输入方法介绍"),e.abrupt("return");case 13:if(!J.value.isAi){e.next=17;break}if(J.value.apiFunc&&J.value.serverFunc&&J.value.jsFunc){e.next=17;break}return E.error("请先使用AI帮写完成基础代码，如果生成失败请重新调用"),e.abrupt("return");case 17:return e.next=19,l(J.value);case 19:0===e.sent.code&&(E.success("增加方法成功"),Z());case 21:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Q=function(e){z.value=e,te()},ee=function(e){U.value=e,te()},te=function(){var e=i(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r({page:U.value,pageSize:z.value});case 2:0===(t=e.sent).code&&(X.value=t.data.list,H.value=t.data.total,U.value=t.data.page,z.value=t.data.pageSize);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();te();var ne=function(){var e=i(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:F.confirm("此操作将删除本历史, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(i(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o({id:Number(t.ID)});case 2:0===e.sent.code&&(E.success("删除成功"),te());case 4:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),re=function(){B.value=!1,N.value={id:void 0,deleteApi:!0,deleteMenu:!0,deleteTable:!1}},ae=function(e){e&&F.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 是否继续?","提示",{closeOnClickModal:!1,distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){F.confirm("此操作将删除自动创建的文件和api（会删除表！！！）, 请继续确认！！！","会删除表",{closeOnClickModal:!1,distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).catch((function(){N.value.deleteTable=!1}))})).catch((function(){N.value.deleteTable=!1}))},oe=function(){var e=i(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,u(N.value);case 2:0===e.sent.code&&(E.success("回滚成功"),te());case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ie=function(e,t){e?I.push({name:"autoCodeEdit",params:{id:e.ID},query:{isAdd:t}}):I.push({name:"autoCode"})},ue=function(){var e=i(a().mark((function e(){var r,o,i,u;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t.value=!0,J.value.apiFunc="",J.value.serverFunc="",J.value.jsFunc="",J.value.prompt){e.next=7;break}return E.error("请输入提示信息"),e.abrupt("return");case 7:return e.next=9,l(n(n({},J.value),{},{isPreview:!0}));case 9:if(0===(r=e.sent).code){e.next=14;break}return t.value=!1,E.error(r.msg),e.abrupt("return");case 14:return e.next=16,s({structInfo:G.value,template:JSON.stringify(r.data),prompt:J.value.prompt,command:"addFunc"});case 16:if(o=e.sent,t.value=!1,0===o.code)try{i=JSON.parse(o.data),J.value.apiFunc=i.api,J.value.serverFunc=i.server,J.value.jsFunc=i.js,J.value.method=i.method,J.value.funcName=i.funcName,u=i.router.split("/"),J.value.router=u[u.length-1],J.value.funcDesc=J.value.prompt}catch(a){E.error("小淼忙碌，请重新调用")}case 19:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),le=function(){var e=i(a().mark((function e(){var n,r;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.value=!0,e.next=3,s({prompt:J.value.funcDesc,command:"autoCompleteFunc"});case 3:if(n=e.sent,t.value=!1,0===n.code)try{r=JSON.parse(n.data),J.value.method=r.method,J.value.funcName=r.funcName,J.value.router=r.router,J.value.prompt=J.value.funcDesc}catch(a){E.error("小淼开小差了，请重新调用")}case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,n){var r=g("el-button"),a=g("el-table-column"),o=g("el-tag"),i=g("el-table"),u=g("el-pagination"),l=g("el-checkbox"),s=g("el-form-item"),c=g("el-form"),d=g("el-popconfirm"),F=g("el-dialog"),E=g("el-input"),I=g("el-col"),te=g("el-row"),se=g("el-switch"),ce=g("ai-gva"),de=g("el-option"),ge=g("el-select"),fe=g("el-drawer"),pe=f("loading");return h(),p("div",null,[m("div",O,[m("div",M,[v(r,{type:"primary",icon:"plus",onClick:n[0]||(n[0]=function(e){return ie(null)})},{default:x((function(){return n[25]||(n[25]=[k(" 新增 ")])})),_:1})]),v(i,{data:X.value},{default:x((function(){return[v(a,{type:"selection",width:"55"}),v(a,{align:"left",label:"id",width:"60",prop:"ID"}),v(a,{align:"left",label:"日期",width:"180"},{default:x((function(e){return[k(b(y(w)(e.row.CreatedAt)),1)]})),_:1}),v(a,{align:"left",label:"结构体名","min-width":"150",prop:"structName"}),v(a,{align:"left",label:"结构体描述","min-width":"150",prop:"description"}),v(a,{align:"left",label:"表名称","min-width":"150",prop:"tableName"}),v(a,{align:"left",label:"回滚标记","min-width":"150",prop:"flag"},{default:x((function(e){return[e.row.flag?(h(),_(o,{key:0,type:"danger",effect:"dark"},{default:x((function(){return n[26]||(n[26]=[k(" 已回滚 ")])})),_:1})):(h(),_(o,{key:1,type:"success",effect:"dark"},{default:x((function(){return n[27]||(n[27]=[k(" 未回滚 ")])})),_:1}))]})),_:1}),v(a,{align:"left",label:"操作","min-width":"240"},{default:x((function(e){return[m("div",null,[v(r,{type:"primary",link:"",disabled:1===e.row.flag,onClick:function(t){return n=e.row,r=JSON.parse(n.request),G.value=n.request,J.value.package=r.package,J.value.structName=r.structName,J.value.packageName=r.packageName,J.value.description=r.description,J.value.abbreviation=r.abbreviation,J.value.humpPackageName=r.humpPackageName,J.value.businessDB=r.businessDB,J.value.method="",J.value.funcName="",J.value.router="",J.value.funcDesc="",J.value.isAuth=!1,J.value.isAi=!1,J.value.apiFunc="",J.value.serverFunc="",J.value.jsFunc="",void(Y.value=!0);var n,r}},{default:x((function(){return n[28]||(n[28]=[k(" 增加方法 ")])})),_:2},1032,["disabled","onClick"]),v(r,{type:"primary",link:"",onClick:function(t){return ie(e.row,1)}},{default:x((function(){return n[29]||(n[29]=[k(" 增加字段 ")])})),_:2},1032,["onClick"]),v(r,{type:"primary",link:"",disabled:1===e.row.flag,onClick:function(t){return n=e.row,W.value="回滚："+n.structName,N.value.id=n.ID,void(B.value=!0);var n}},{default:x((function(){return n[30]||(n[30]=[k(" 回滚 ")])})),_:2},1032,["disabled","onClick"]),v(r,{type:"primary",link:"",onClick:function(t){return ie(e.row)}},{default:x((function(){return n[31]||(n[31]=[k(" 复用 ")])})),_:2},1032,["onClick"]),v(r,{type:"primary",link:"",onClick:function(t){return ne(e.row)}},{default:x((function(){return n[32]||(n[32]=[k(" 删除 ")])})),_:2},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"]),m("div",$,[v(u,{"current-page":U.value,"page-size":z.value,"page-sizes":[10,30,50,100],total:H.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:ee,onSizeChange:Q},null,8,["current-page","page-size","total"])])]),v(F,{modelValue:B.value,"onUpdate:modelValue":n[4]||(n[4]=function(e){return B.value=e}),title:W.value,"before-close":re,width:"600px"},{footer:x((function(){return[m("div",j,[v(r,{onClick:re},{default:x((function(){return n[33]||(n[33]=[k(" 取 消 ")])})),_:1}),v(d,{title:"此操作将回滚生成文件和勾选项目, 是否继续?",onConfirm:oe},{reference:x((function(){return[v(r,{type:"primary"},{default:x((function(){return n[34]||(n[34]=[k(" 确 定 ")])})),_:1})]})),_:1})])]})),default:x((function(){return[v(c,{inline:!0,model:N.value,"label-width":"80px"},{default:x((function(){return[v(s,{label:"选项："},{default:x((function(){return[v(l,{modelValue:N.value.deleteApi,"onUpdate:modelValue":n[1]||(n[1]=function(e){return N.value.deleteApi=e}),label:"删除接口"},null,8,["modelValue"]),v(l,{modelValue:N.value.deleteMenu,"onUpdate:modelValue":n[2]||(n[2]=function(e){return N.value.deleteMenu=e}),label:"删除菜单"},null,8,["modelValue"]),v(l,{modelValue:N.value.deleteTable,"onUpdate:modelValue":n[3]||(n[3]=function(e){return N.value.deleteTable=e}),label:"删除表",onChange:ae},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue","title"]),v(fe,{modelValue:Y.value,"onUpdate:modelValue":n[24]||(n[24]=function(e){return Y.value=e}),size:"60%","show-close":!1,"close-on-click-modal":!1},{header:x((function(){return[m("div",V,[n[37]||(n[37]=m("span",{class:"text-lg"},"操作栏",-1)),m("div",null,[v(r,{type:"primary",onClick:K,loading:t.value},{default:x((function(){return n[35]||(n[35]=[k(" 生成 ")])})),_:1},8,["loading"]),v(r,{type:"primary",onClick:Z,loading:t.value},{default:x((function(){return n[36]||(n[36]=[k(" 取消 ")])})),_:1},8,["loading"])])])]})),default:x((function(){return[m("div",D,[C((h(),_(c,{"label-position":"top","element-loading-text":"小淼正在思考，请稍候...",model:J.value,"label-width":"80px"},{default:x((function(){return[v(te,{gutter:12},{default:x((function(){return[v(I,{span:8},{default:x((function(){return[v(s,{label:"包名："},{default:x((function(){return[v(E,{modelValue:J.value.package,"onUpdate:modelValue":n[5]||(n[5]=function(e){return J.value.package=e}),placeholder:"请输入包名",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1}),v(I,{span:8},{default:x((function(){return[v(s,{label:"结构体名："},{default:x((function(){return[v(E,{modelValue:J.value.structName,"onUpdate:modelValue":n[6]||(n[6]=function(e){return J.value.structName=e}),placeholder:"请输入结构体名",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1}),v(I,{span:8},{default:x((function(){return[v(s,{label:"前端文件名："},{default:x((function(){return[v(E,{modelValue:J.value.packageName,"onUpdate:modelValue":n[7]||(n[7]=function(e){return J.value.packageName=e}),placeholder:"请输入文件名",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),v(te,{gutter:12},{default:x((function(){return[v(I,{span:8},{default:x((function(){return[v(s,{label:"后端文件名："},{default:x((function(){return[v(E,{modelValue:J.value.humpPackageName,"onUpdate:modelValue":n[8]||(n[8]=function(e){return J.value.humpPackageName=e}),placeholder:"请输入文件名",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1}),v(I,{span:8},{default:x((function(){return[v(s,{label:"描述："},{default:x((function(){return[v(E,{modelValue:J.value.description,"onUpdate:modelValue":n[9]||(n[9]=function(e){return J.value.description=e}),placeholder:"请输入描述",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1}),v(I,{span:8},{default:x((function(){return[v(s,{label:"缩写："},{default:x((function(){return[v(E,{modelValue:J.value.abbreviation,"onUpdate:modelValue":n[10]||(n[10]=function(e){return J.value.abbreviation=e}),placeholder:"请输入缩写",disabled:""},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),v(s,{label:"是否AI填充："},{default:x((function(){return[v(se,{modelValue:J.value.isAi,"onUpdate:modelValue":n[11]||(n[11]=function(e){return J.value.isAi=e})},null,8,["modelValue"]),n[38]||(n[38]=m("span",{class:"text-sm text-red-600 p-2"},"当前ai帮写存在不稳定因素，生成代码后请注意手动调整部分内容",-1))]})),_:1}),J.value.isAi?(h(),p(S,{key:0},[v(s,{label:"Ai帮写:"},{default:x((function(){return[m("div",q,[v(E,{type:"textarea",placeholder:"AI帮写功能，输入提示信息，自动生成代码",modelValue:J.value.prompt,"onUpdate:modelValue":n[12]||(n[12]=function(e){return J.value.prompt=e}),rows:5,onInput:n[13]||(n[13]=function(e){return J.value.router=J.value.router.replace(/\//g,"")})},null,8,["modelValue"]),v(r,{onClick:ue,type:"primary",class:"absolute right-2 bottom-2"},{default:x((function(){return[v(ce),n[39]||(n[39]=k("帮写"))]})),_:1})])]})),_:1}),v(s,{label:"Api方法:"},{default:x((function(){return[v(y(A),{value:J.value.apiFunc,"onUpdate:value":n[14]||(n[14]=function(e){return J.value.apiFunc=e}),lang:"golang",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])]})),_:1}),v(s,{label:"Server方法:"},{default:x((function(){return[v(y(A),{value:J.value.serverFunc,"onUpdate:value":n[15]||(n[15]=function(e){return J.value.serverFunc=e}),lang:"golang",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])]})),_:1}),v(s,{label:"前端JSAPI方法:"},{default:x((function(){return[v(y(A),{value:J.value.jsFunc,"onUpdate:value":n[16]||(n[16]=function(e){return J.value.jsFunc=e}),lang:"javascript",theme:"github_dark",class:"h-80 w-full"},null,8,["value"])]})),_:1})],64)):R("",!0),v(s,{label:"方法介绍："},{default:x((function(){return[m("div",P,[v(E,{class:"flex-1",modelValue:J.value.funcDesc,"onUpdate:modelValue":n[17]||(n[17]=function(e){return J.value.funcDesc=e}),placeholder:"请输入方法介绍"},null,8,["modelValue"]),v(r,{type:"primary",onClick:le},{default:x((function(){return[v(ce),n[40]||(n[40]=k("补全"))]})),_:1})])]})),_:1}),v(s,{label:"方法名："},{default:x((function(){return[v(E,{onBlur:n[18]||(n[18]=function(e){return J.value.funcName=y(L)(J.value.funcName)}),modelValue:J.value.funcName,"onUpdate:modelValue":n[19]||(n[19]=function(e){return J.value.funcName=e}),placeholder:"请输入方法名"},null,8,["modelValue"])]})),_:1}),v(s,{label:"方法："},{default:x((function(){return[v(ge,{modelValue:J.value.method,"onUpdate:modelValue":n[20]||(n[20]=function(e){return J.value.method=e}),placeholder:"请选择方法"},{default:x((function(){return[(h(),p(S,null,T(["GET","POST","PUT","DELETE"],(function(e){return v(de,{key:e,label:e,value:e},null,8,["label","value"])})),64))]})),_:1},8,["modelValue"])]})),_:1}),v(s,{label:"是否鉴权："},{default:x((function(){return[v(se,{modelValue:J.value.isAuth,"onUpdate:modelValue":n[21]||(n[21]=function(e){return J.value.isAuth=e}),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]})),_:1}),v(s,{label:"路由path:"},{default:x((function(){return[v(E,{modelValue:J.value.router,"onUpdate:modelValue":n[22]||(n[22]=function(e){return J.value.router=e}),placeholder:"路由path",onInput:n[23]||(n[23]=function(e){return J.value.router=J.value.router.replace(/\//g,"")})},null,8,["modelValue"]),m("div",null," API路径: ["+b(J.value.method)+"] /"+b(J.value.abbreviation)+"/"+b(J.value.router),1)]})),_:1})]})),_:1},8,["model"])),[[pe,t.value]])])]})),_:1},8,["modelValue"])])}}}))}}}))}();
