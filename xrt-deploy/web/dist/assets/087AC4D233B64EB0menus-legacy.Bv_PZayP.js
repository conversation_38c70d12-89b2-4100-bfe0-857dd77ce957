/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,u=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(r){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),i=new D(n||[]);return u(a,"_invoke",{value:C(t,r,i)}),a}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var p="suspendedStart",y="suspendedYield",v="executing",m="completed",g={};function w(){}function b(){}function x(){}var k={};f(k,c,(function(){return this}));var E=Object.getPrototypeOf,_=E&&E(E(R([])));_&&_!==o&&a.call(_,c)&&(k=_);var L=x.prototype=w.prototype=Object.create(k);function I(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function j(e,r){function n(o,u,i,c){var l=d(e[o],e,u);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==t(f)&&a.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,i,c)}),(function(t){n("throw",t,i,c)})):r.resolve(f).then((function(t){s.value=t,i(s)}),(function(t){return n("throw",t,i,c)}))}c(l.arg)}var o;u(this,"_invoke",{value:function(t,e){function a(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(a,a):a()}})}function C(t,e,n){var o=p;return function(a,u){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===a)throw u;return{value:r,done:!0}}for(n.method=a,n.arg=u;;){var i=n.delegate;if(i){var c=O(i,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=d(t,e,n);if("normal"===l.type){if(o=n.done?m:y,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function O(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,O(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=d(o,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,g;var u=a.arg;return u?u.done?(e[t.resultName]=u.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):u:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function N(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function D(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,u=function t(){for(;++o<e.length;)if(a.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return u.next=u}}throw new TypeError(t(e)+" is not iterable")}return b.prototype=x,u(L,"constructor",{value:x,configurable:!0}),u(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},n.awrap=function(t){return{__await:t}},I(j.prototype),f(j.prototype,l,(function(){return this})),n.AsyncIterator=j,n.async=function(t,e,r,o,a){void 0===a&&(a=Promise);var u=new j(h(t,e,r,o),a);return n.isGeneratorFunction(e)?u:u.next().then((function(t){return t.done?t.value:u.next()}))},I(L),f(L,s,"Generator"),f(L,c,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=R,D.prototype={constructor:D,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(N),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return i.type="throw",i.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var u=this.tryEntries[o],i=u.completion;if("root"===u.tryLoc)return n("end");if(u.tryLoc<=this.prev){var c=a.call(u,"catchLoc"),l=a.call(u,"finallyLoc");if(c&&l){if(this.prev<u.catchLoc)return n(u.catchLoc,!0);if(this.prev<u.finallyLoc)return n(u.finallyLoc)}else if(c){if(this.prev<u.catchLoc)return n(u.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return n(u.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=t,u.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(u)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),N(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;N(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:R(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,o,a,u){try{var i=t[a](u),c=i.value}catch(t){return void r(t)}i.done?e(c):Promise.resolve(c).then(n,o)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var u=t.apply(e,n);function i(t){r(u,o,a,i,c,"next",t)}function c(t){r(u,o,a,i,c,"throw",t)}i(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0authority-legacy.DVZAS3a8.js","./087AC4D233B64EB0authorityBtn-legacy.cvbwrcdH.js"],(function(t,r){"use strict";var o,a,u,i,c,l,s,f,h,d,p,y,v,m,g,w,b,x,k,E,_,L;return{setters:[function(t){o=t._,a=t.a,u=t.Q,i=t.g,c=t.c,l=t.o,s=t.b,f=t.f,h=t.w,d=t.h,p=t.d,y=t.t,v=t.au,m=t.X,g=t.aK,w=t.aL,b=t.E,x=t.T,k=t.aM},function(t){E=t.u},function(t){_=t.g,L=t.s}],execute:function(){var r=document.createElement("style");r.textContent=".custom-tree-node span+span[data-v-a4aa9771]{margin-left:.75rem}\n/*$vite$:1*/",document.head.appendChild(r);var I={class:"sticky top-0.5 z-10"},j={class:"tree-content clear-both"},C={class:"custom-tree-node"},O={key:0},S={key:1},N={class:"dialog-footer"},D=Object.assign({name:"Menus"},{__name:"menus",props:{row:{default:function(){return{}},type:Object}},emits:["changeRow"],setup:function(t,r){var o=r.expose,D=r.emit,R=t,B=D,G=a(""),P=a([]),T=a([]),A=a(!1),V=a({children:"children",label:function(t){return t.meta.title},disabled:function(t){return R.row.defaultRouter===t.name}}),F=function(){var t=n(e().mark((function t(){var r,n,o,a;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,g();case 2:return r=t.sent,P.value=r.data.menus,t.next=6,w({authorityId:R.row.authorityId});case 6:n=t.sent,o=n.data.menus,a=[],o.forEach((function(t){o.some((function(e){return e.parentId===t.menuId}))||a.push(Number(t.menuId))})),T.value=a;case 11:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();F();var M=function(){var t=n(e().mark((function t(r){var n;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,E({authorityId:R.row.authorityId,AuthorityName:R.row.authorityName,parentId:R.row.parentId,defaultRouter:r.name});case 2:0===(n=t.sent).code&&(b({type:"success",message:"设置成功"}),B("changeRow","defaultRouter",n.data.authority.defaultRouter));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),U=function(){A.value=!0},Y=a(null),$=function(){var t=n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=Y.value.getCheckedNodes(!1,!0),t.next=3,k({menus:r,authorityId:R.row.authorityId});case 3:0===t.sent.code&&b({type:"success",message:"菜单设置成功!"});case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();o({enterAndNext:function(){$()},needConfirm:A});var z=a(!1),H=a([]),K=a([]),Q=a(),X="",q=function(){var t=n(e().mark((function t(r){var n;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return X=r.ID,t.next=3,_({menuID:X,authorityId:R.row.authorityId});case 3:if(0!==(n=t.sent).code){t.next=9;break}return W(r),t.next=8,x();case 8:n.data.selected&&n.data.selected.forEach((function(t){H.value.some((function(e){e.ID===t&&Q.value.toggleRowSelection(e,!0)}))}));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),J=function(t){K.value=t},W=function(t){z.value=!0,H.value=t.menuBtn},Z=function(){z.value=!1},tt=function(){var t=n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=K.value.map((function(t){return t.ID})),t.next=3,L({menuID:X,selected:r,authorityId:R.row.authorityId});case 3:0===t.sent.code&&(b({type:"success",message:"设置成功"}),z.value=!1);case 5:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),et=function(t,e){return!t||-1!==e.meta.title.indexOf(t)};return u(G,(function(t){Y.value.filter(t)})),function(e,r){var n=i("el-input"),o=i("el-button"),a=i("el-tree"),u=i("el-scrollbar"),g=i("el-table-column"),w=i("el-table"),b=i("el-dialog");return l(),c("div",null,[s("div",I,[f(n,{modelValue:G.value,"onUpdate:modelValue":r[0]||(r[0]=function(t){return G.value=t}),class:"w-3/5",placeholder:"筛选"},null,8,["modelValue"]),f(o,{class:"float-right",type:"primary",onClick:$},{default:h((function(){return r[2]||(r[2]=[d("确 定")])})),_:1})]),s("div",j,[f(u,null,{default:h((function(){return[f(a,{ref_key:"menuTree",ref:Y,data:P.value,"default-checked-keys":T.value,props:V.value,"default-expand-all":"","highlight-current":"","node-key":"ID","show-checkbox":"","filter-node-method":et,onCheck:U},{default:h((function(e){var n=e.node,a=e.data;return[s("span",C,[s("span",null,y(n.label),1),n.checked?(l(),c("span",O,[f(o,{type:"primary",link:"",style:m({color:t.row.defaultRouter===a.name?"#E6A23C":"#85ce61"}),onClick:v((function(){return M(a)}),["stop"])},{default:h((function(){return[d(y(t.row.defaultRouter===a.name?"首页":"设为首页"),1)]})),_:2},1032,["style","onClick"])])):p("",!0),a.menuBtn.length?(l(),c("span",S,[f(o,{type:"primary",link:"",onClick:function(){return q(a)}},{default:h((function(){return r[3]||(r[3]=[d(" 分配按钮 ")])})),_:2},1032,["onClick"])])):p("",!0)])]})),_:1},8,["data","default-checked-keys","props"])]})),_:1})]),f(b,{modelValue:z.value,"onUpdate:modelValue":r[1]||(r[1]=function(t){return z.value=t}),title:"分配按钮","destroy-on-close":""},{footer:h((function(){return[s("div",N,[f(o,{onClick:Z},{default:h((function(){return r[4]||(r[4]=[d("取 消")])})),_:1}),f(o,{type:"primary",onClick:tt},{default:h((function(){return r[5]||(r[5]=[d("确 定")])})),_:1})])]})),default:h((function(){return[f(w,{ref_key:"btnTableRef",ref:Q,data:H.value,"row-key":"ID",onSelectionChange:J},{default:h((function(){return[f(g,{type:"selection",width:"55"}),f(g,{label:"按钮名称",prop:"name"}),f(g,{label:"按钮备注",prop:"desc"})]})),_:1},8,["data"])]})),_:1},8,["modelValue"])])}}});t("default",o(D,[["__scopeId","data-v-a4aa9771"]]))}}}))}();
