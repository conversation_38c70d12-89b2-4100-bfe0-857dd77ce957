/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,n){"use strict";var e,i,l,r,c;return{setters:[function(t){e=t.g,i=t.c,l=t.o,r=t.f,c=t.w}],execute:function(){t("default",{__name:"table",setup:function(t){var n=[{ranking:1,title:"更简洁的使用界面，更快速的操作体验",click_num:523,hot:263},{ranking:2,title:"更优质的服务，更便捷的使用体验",click_num:416,hot:223},{ranking:3,title:"更快速的创意实现，更高效的工作效率",click_num:337,hot:176},{ranking:4,title:"更多的创意资源，更多的创意灵感",click_num:292,hot:145},{ranking:5,title:"更合理的代码结构，更清晰的代码逻辑",click_num:173,hot:110}];return function(t,a){var o=e("el-table-column"),u=e("el-table");return l(),i("div",null,[r(u,{data:n,stripe:"",style:{width:"100%"}},{default:c((function(){return[r(o,{prop:"ranking",label:"排名",width:"80",align:"center"}),r(o,{prop:"title",label:"内容标题","show-overflow-tooltip":""}),r(o,{prop:"click_num",label:"关注度",width:"100"}),r(o,{prop:"hot",label:"热度值",width:"100"})]})),_:1})])}}})}}}));
