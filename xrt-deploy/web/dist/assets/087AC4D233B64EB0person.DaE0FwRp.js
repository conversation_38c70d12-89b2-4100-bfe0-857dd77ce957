/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{k as e,a as l,r as a,Q as s,g as t,c as d,o,b as r,f as i,v as u,h as n,t as c,w as m,F as p,D as f,aI as g,E as v,aJ as x}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{S as y}from"./087AC4D233B64EB0selectImage.DW9yQvJ8.js";import"./087AC4D233B64EB0image.DZXThNBc.js";import"./087AC4D233B64EB0QR-code.DGKmWLV2.js";import"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";import"./087AC4D233B64EB0logo.D8P6F9wK.js";const b={class:"profile-container"},w={class:"bg-white dark:bg-slate-800 rounded-2xl shadow-sm mb-8"},_={class:"px-8 -mt-20 pb-8"},h={class:"flex flex-col lg:flex-row items-start gap-8"},k={class:"profile-avatar-wrapper flex-shrink-0 mx-auto lg:mx-0"},V={class:"flex-1 pt-12 lg:pt-20 w-full"},I={class:"flex flex-col lg:flex-row items-start lg:items-start justify-between gap-4"},C={class:"lg:mt-4"},U={class:"flex items-center gap-4 mb-4"},P={key:0,class:"text-2xl font-bold flex items-center gap-3 text-gray-800 dark:text-gray-100"},B={key:1,class:"flex items-center"},j={class:"flex flex-col lg:flex-row items-start lg:items-center gap-4 lg:gap-8 text-gray-500 dark:text-gray-400"},E={class:"flex items-center gap-2"},D={class:"flex items-center gap-2"},A={class:"flex items-center gap-2"},R={class:"flex gap-4 mt-4"},N={class:"grid lg:grid-cols-12 md:grid-cols-1 gap-8"},q={class:"lg:col-span-4"},F={class:"bg-white dark:bg-slate-800 rounded-xl p-6 mb-6 profile-card"},G={class:"text-lg font-semibold mb-4 flex items-center gap-2"},J={class:"space-y-4"},Q={class:"flex items-center gap-1 lg:gap-3 text-gray-600 dark:text-gray-300"},S={class:"flex items-center gap-1 lg:gap-3 text-gray-600 dark:text-gray-300"},L={class:"flex items-center gap-1 lg:gap-3 text-gray-600 dark:text-gray-300"},O={class:"bg-white dark:bg-slate-800 rounded-xl p-6 profile-card"},z={class:"text-lg font-semibold mb-4 flex items-center gap-2"},H={class:"flex flex-wrap gap-2"},K={class:"lg:col-span-8"},M={class:"bg-white dark:bg-slate-800 rounded-xl p-6 profile-card"},T={class:"flex items-center gap-2"},W={class:"flex items-center gap-2"},X={class:"py-6"},Y={class:"text-base font-medium mb-1"},Z={class:"text-gray-500 text-sm"},$={class:"dialog-footer"},ee={class:"flex gap-4"},le={class:"dialog-footer"},ae={class:"flex gap-4"},se={class:"dialog-footer"},te=Object.assign({name:"Person"},{__name:"person",setup(te){const de=e(),oe=l(null),re=l(!1),ie=l({}),ue=l(""),ne=l(!1),ce=a({password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"最少6个字符",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:6,message:"最少6个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请输入确认密码",trigger:"blur"},{min:6,message:"最少6个字符",trigger:"blur"},{validator:(e,l,a)=>{l!==ie.value.newPassword?a(new Error("两次密码不一致")):a()},trigger:"blur"}]}),me=async()=>{oe.value.validate((e=>{e&&g({password:ie.value.password,newPassword:ie.value.newPassword}).then((e=>{0===e.code&&v.success("修改密码成功！"),re.value=!1}))}))},pe=()=>{var e;ie.value={password:"",newPassword:"",confirmPassword:""},null==(e=oe.value)||e.clearValidate()},fe=()=>{ue.value=de.userInfo.nickName,ne.value=!0},ge=()=>{ue.value="",ne.value=!1},ve=async()=>{0===(await x({nickName:ue.value})).code&&(de.ResetUserInfo({nickName:ue.value}),v.success("修改成功")),ue.value="",ne.value=!1},xe=l(!1),ye=l(0),be=a({phone:"",code:""}),we=async()=>{ye.value=60;let e=setInterval((()=>{ye.value--,ye.value<=0&&(clearInterval(e),e=null)}),1e3)},_e=()=>{xe.value=!1,be.phone="",be.code=""},he=async()=>{0===(await x({phone:be.phone})).code&&(v.success("修改成功"),de.ResetUserInfo({phone:be.phone}),_e())},ke=l(!1),Ve=l(0),Ie=a({email:"",code:""}),Ce=async()=>{Ve.value=60;let e=setInterval((()=>{Ve.value--,Ve.value<=0&&(clearInterval(e),e=null)}),1e3)},Ue=()=>{ke.value=!1,Ie.email="",Ie.code=""},Pe=async()=>{0===(await x({email:Ie.email})).code&&(v.success("修改成功"),de.ResetUserInfo({email:Ie.email}),Ue())};s((()=>de.userInfo.headerImg),(async e=>{0===(await x({headerImg:e})).code&&(de.ResetUserInfo({headerImg:e}),v({type:"success",message:"设置成功"}))}));const Be=[{timestamp:"2024-01-10",title:"完成项目里程碑",content:"成功完成第三季度主要项目开发任务，获得团队一致好评",type:"primary"},{timestamp:"2024-01-11",title:"代码审核完成",content:"完成核心模块代码审核，提出多项改进建议并获采纳",type:"success"},{timestamp:"2024-01-12",title:"技术分享会",content:"主持团队技术分享会，分享前端性能优化经验",type:"warning"},{timestamp:"2024-01-13",title:"新功能上线",content:"成功上线用户反馈的新特性，显著提升用户体验",type:"danger"}];return(e,l)=>{const a=t("edit"),s=t("el-icon"),g=t("el-input"),v=t("el-button"),x=t("location"),te=t("office-building"),je=t("user"),Ee=t("info-filled"),De=t("phone"),Ae=t("message"),Re=t("lock"),Ne=t("medal"),qe=t("el-tag"),Fe=t("plus"),Ge=t("data-line"),Je=t("el-tab-pane"),Qe=t("calendar"),Se=t("el-timeline-item"),Le=t("el-timeline"),Oe=t("el-tabs"),ze=t("el-form-item"),He=t("el-form"),Ke=t("el-dialog"),Me=t("key");return o(),d("div",b,[r("div",w,[l[23]||(l[23]=r("div",{class:"h-48 bg-blue-50 dark:bg-slate-600 relative"},[r("div",{class:"absolute inset-0 bg-pattern opacity-7"})],-1)),r("div",_,[r("div",h,[r("div",k,[i(y,{modelValue:u(de).userInfo.headerImg,"onUpdate:modelValue":l[0]||(l[0]=e=>u(de).userInfo.headerImg=e),"file-type":"image",rounded:""},null,8,["modelValue"])]),r("div",V,[r("div",I,[r("div",C,[r("div",U,[ne.value?(o(),d("div",B,[i(g,{modelValue:ue.value,"onUpdate:modelValue":l[1]||(l[1]=e=>ue.value=e),class:"w-48 mr-4"},null,8,["modelValue"]),i(v,{type:"primary",plain:"",onClick:ve},{default:m((()=>l[16]||(l[16]=[n(" 确认 ")]))),_:1}),i(v,{type:"danger",plain:"",onClick:ge},{default:m((()=>l[17]||(l[17]=[n(" 取消 ")]))),_:1})])):(o(),d("div",P,[n(c(u(de).userInfo.nickName)+" ",1),i(s,{class:"cursor-pointer text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors duration-200",onClick:fe},{default:m((()=>[i(a)])),_:1})]))]),r("div",j,[r("div",E,[i(s,null,{default:m((()=>[i(x)])),_:1}),l[18]||(l[18]=r("span",null,"中国·北京市·朝阳区",-1))]),r("div",D,[i(s,null,{default:m((()=>[i(te)])),_:1}),l[19]||(l[19]=r("span",null,"北京翻转极光科技有限公司",-1))]),r("div",A,[i(s,null,{default:m((()=>[i(je)])),_:1}),l[20]||(l[20]=r("span",null,"技术部·前端事业群",-1))])])]),r("div",R,[i(v,{type:"primary",plain:"",icon:"message"},{default:m((()=>l[21]||(l[21]=[n(" 发送消息 ")]))),_:1}),i(v,{icon:"share"},{default:m((()=>l[22]||(l[22]=[n(" 分享主页 ")]))),_:1})])])])])])]),r("div",N,[r("div",q,[r("div",F,[r("h2",G,[i(s,{class:"text-blue-500"},{default:m((()=>[i(Ee)])),_:1}),l[24]||(l[24]=n(" 基本信息 "))]),r("div",J,[r("div",Q,[i(s,{class:"text-blue-500"},{default:m((()=>[i(De)])),_:1}),l[26]||(l[26]=r("span",{class:"font-medium"},"手机号码：",-1)),r("span",null,c(u(de).userInfo.phone||"未设置"),1),i(v,{link:"",type:"primary",class:"ml-auto",onClick:l[2]||(l[2]=e=>xe.value=!0)},{default:m((()=>l[25]||(l[25]=[n(" 修改 ")]))),_:1})]),r("div",S,[i(s,{class:"text-green-500"},{default:m((()=>[i(Ae)])),_:1}),l[28]||(l[28]=r("span",{class:"font-medium flex-shrink-0"},"邮箱地址：",-1)),r("span",null,c(u(de).userInfo.email||"未设置"),1),i(v,{link:"",type:"primary",class:"ml-auto",onClick:l[3]||(l[3]=e=>ke.value=!0)},{default:m((()=>l[27]||(l[27]=[n(" 修改 ")]))),_:1})]),r("div",L,[i(s,{class:"text-purple-500"},{default:m((()=>[i(Re)])),_:1}),l[30]||(l[30]=r("span",{class:"font-medium"},"账号密码：",-1)),l[31]||(l[31]=r("span",null,"已设置",-1)),i(v,{link:"",type:"primary",class:"ml-auto",onClick:l[4]||(l[4]=e=>re.value=!0)},{default:m((()=>l[29]||(l[29]=[n(" 修改 ")]))),_:1})])])]),r("div",O,[r("h2",z,[i(s,{class:"text-blue-500"},{default:m((()=>[i(Ne)])),_:1}),l[32]||(l[32]=n(" 技能特长 "))]),r("div",H,[i(qe,{effect:"plain",type:"success"},{default:m((()=>l[33]||(l[33]=[n("GoLang")]))),_:1}),i(qe,{effect:"plain",type:"warning"},{default:m((()=>l[34]||(l[34]=[n("JavaScript")]))),_:1}),i(qe,{effect:"plain",type:"danger"},{default:m((()=>l[35]||(l[35]=[n("Vue")]))),_:1}),i(qe,{effect:"plain",type:"info"},{default:m((()=>l[36]||(l[36]=[n("Gorm")]))),_:1}),i(v,{link:"",class:"text-sm"},{default:m((()=>[i(s,null,{default:m((()=>[i(Fe)])),_:1}),l[37]||(l[37]=n(" 添加技能 "))])),_:1})])])]),r("div",K,[r("div",M,[i(Oe,{class:"custom-tabs"},{default:m((()=>[i(Je,null,{label:m((()=>[r("div",T,[i(s,null,{default:m((()=>[i(Ge)])),_:1}),l[38]||(l[38]=n(" 数据统计 "))])])),default:m((()=>[l[39]||(l[39]=r("div",{class:"grid grid-cols-2 md:grid-cols-4 gap-4 lg:gap-6 py-6"},[r("div",{class:"stat-card"},[r("div",{class:"text-2xl lg:text-4xl font-bold text-blue-500 mb-2"}," 138 "),r("div",{class:"text-gray-500 text-sm"},"项目参与")]),r("div",{class:"stat-card"},[r("div",{class:"text-2xl lg:text-4xl font-bold text-green-500 mb-2"}," 2.3k "),r("div",{class:"text-gray-500 text-sm"},"代码提交")]),r("div",{class:"stat-card"},[r("div",{class:"text-2xl lg:text-4xl font-bold text-purple-500 mb-2"}," 95% "),r("div",{class:"text-gray-500 text-sm"},"任务完成")]),r("div",{class:"stat-card"},[r("div",{class:"text-2xl lg:text-4xl font-bold text-yellow-500 mb-2"}," 12 "),r("div",{class:"text-gray-500 text-sm"},"获得勋章")])],-1))])),_:1}),i(Je,null,{label:m((()=>[r("div",W,[i(s,null,{default:m((()=>[i(Qe)])),_:1}),l[40]||(l[40]=n(" 近期动态 "))])])),default:m((()=>[r("div",X,[i(Le,null,{default:m((()=>[(o(),d(p,null,f(Be,((e,l)=>i(Se,{key:l,type:e.type,timestamp:e.timestamp,hollow:!0,class:"pb-6"},{default:m((()=>[r("h3",Y,c(e.title),1),r("p",Z,c(e.content),1)])),_:2},1032,["type","timestamp"]))),64))])),_:1})])])),_:1})])),_:1})])])]),i(Ke,{modelValue:re.value,"onUpdate:modelValue":l[9]||(l[9]=e=>re.value=e),title:"修改密码",width:"400px",class:"custom-dialog",onClose:pe},{footer:m((()=>[r("div",$,[i(v,{onClick:l[8]||(l[8]=e=>re.value=!1)},{default:m((()=>l[41]||(l[41]=[n("取 消")]))),_:1}),i(v,{type:"primary",onClick:me},{default:m((()=>l[42]||(l[42]=[n("确 定")]))),_:1})])])),default:m((()=>[i(He,{ref_key:"modifyPwdForm",ref:oe,model:ie.value,rules:ce,"label-width":"90px",class:"py-4"},{default:m((()=>[i(ze,{minlength:6,label:"原密码",prop:"password"},{default:m((()=>[i(g,{modelValue:ie.value.password,"onUpdate:modelValue":l[5]||(l[5]=e=>ie.value.password=e),"show-password":""},null,8,["modelValue"])])),_:1}),i(ze,{minlength:6,label:"新密码",prop:"newPassword"},{default:m((()=>[i(g,{modelValue:ie.value.newPassword,"onUpdate:modelValue":l[6]||(l[6]=e=>ie.value.newPassword=e),"show-password":""},null,8,["modelValue"])])),_:1}),i(ze,{minlength:6,label:"确认密码",prop:"confirmPassword"},{default:m((()=>[i(g,{modelValue:ie.value.confirmPassword,"onUpdate:modelValue":l[7]||(l[7]=e=>ie.value.confirmPassword=e),"show-password":""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),i(Ke,{modelValue:xe.value,"onUpdate:modelValue":l[12]||(l[12]=e=>xe.value=e),title:"修改手机号",width:"400px",class:"custom-dialog"},{footer:m((()=>[r("div",le,[i(v,{onClick:_e},{default:m((()=>l[43]||(l[43]=[n("取 消")]))),_:1}),i(v,{type:"primary",onClick:he},{default:m((()=>l[44]||(l[44]=[n("确 定")]))),_:1})])])),default:m((()=>[i(He,{model:be,"label-width":"80px",class:"py-4"},{default:m((()=>[i(ze,{label:"手机号"},{default:m((()=>[i(g,{modelValue:be.phone,"onUpdate:modelValue":l[10]||(l[10]=e=>be.phone=e),placeholder:"请输入新的手机号码"},{prefix:m((()=>[i(s,null,{default:m((()=>[i(De)])),_:1})])),_:1},8,["modelValue"])])),_:1}),i(ze,{label:"验证码"},{default:m((()=>[r("div",ee,[i(g,{modelValue:be.code,"onUpdate:modelValue":l[11]||(l[11]=e=>be.code=e),placeholder:"请输入验证码[模拟]",class:"flex-1"},{prefix:m((()=>[i(s,null,{default:m((()=>[i(Me)])),_:1})])),_:1},8,["modelValue"]),i(v,{type:"primary",disabled:ye.value>0,class:"w-32",onClick:we},{default:m((()=>[n(c(ye.value>0?"".concat(ye.value,"s"):"获取验证码"),1)])),_:1},8,["disabled"])])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),i(Ke,{modelValue:ke.value,"onUpdate:modelValue":l[15]||(l[15]=e=>ke.value=e),title:"修改邮箱",width:"400px",class:"custom-dialog"},{footer:m((()=>[r("div",se,[i(v,{onClick:Ue},{default:m((()=>l[45]||(l[45]=[n("取 消")]))),_:1}),i(v,{type:"primary",onClick:Pe},{default:m((()=>l[46]||(l[46]=[n("确 定")]))),_:1})])])),default:m((()=>[i(He,{model:Ie,"label-width":"80px",class:"py-4"},{default:m((()=>[i(ze,{label:"邮箱"},{default:m((()=>[i(g,{modelValue:Ie.email,"onUpdate:modelValue":l[13]||(l[13]=e=>Ie.email=e),placeholder:"请输入新的邮箱地址"},{prefix:m((()=>[i(s,null,{default:m((()=>[i(Ae)])),_:1})])),_:1},8,["modelValue"])])),_:1}),i(ze,{label:"验证码"},{default:m((()=>[r("div",ae,[i(g,{modelValue:Ie.code,"onUpdate:modelValue":l[14]||(l[14]=e=>Ie.code=e),placeholder:"请输入验证码[模拟]",class:"flex-1"},{prefix:m((()=>[i(s,null,{default:m((()=>[i(Me)])),_:1})])),_:1},8,["modelValue"]),i(v,{type:"primary",disabled:Ve.value>0,class:"w-32",onClick:Ce},{default:m((()=>[n(c(Ve.value>0?"".concat(Ve.value,"s"):"获取验证码"),1)])),_:1},8,["disabled"])])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}}});export{te as default};
