/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,o)}return r}function r(t){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?e(Object(n),!0).forEach((function(e){o(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function o(e,r,o){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,r||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[r]=o,e}System.register(["./087AC4D233B64EB0index-legacy.DsTCTXwW.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,e){"use strict";var o,n,i,c,u,l,a,s,f;return{setters:[function(t){o=t.u,n=t.L,i=t._},function(t){c=t.I,u=t.J,l=t.a,a=t.i,s=t.o,f=t.v}],execute:function(){t("default",{__name:"charts-people-numbers",props:{height:{type:String,default:"128px"},data:{type:Array,default:function(){return[]}}},setup:function(t){var e=c(),y=u(e).config,p=t,b=function(t){return r(r({type:"text",bottom:"8"},t),{},{style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}})},m=l([b({left:"5%"}),b({right:0})]),h=o((function(){return{grid:{left:"40",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,show:!1,boundaryGap:!1,axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}},yAxis:{type:"value",show:!1,axisLine:{show:!1},axisLabel:{show:!1},splitLine:{show:!1}},graphic:{elements:m.value},series:[{data:p.data,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new n(0,0,1,0,[{offset:0,color:"".concat(y.value.primaryColor,"32")},{offset:.5,color:"".concat(y.value.primaryColor,"64")},{offset:1,color:"".concat(y.value.primaryColor,"FF")}])},showSymbol:!1,areaStyle:{opacity:.8,color:new n(0,0,0,1,[{offset:0,color:"".concat(y.value.primaryColor,"20")},{offset:1,color:"".concat(y.value.primaryColor,"08")}])}}]}})).chartOption;return function(e,r){return s(),a(i,{height:t.height,option:f(h)},null,8,["height","option"])}}})}}}))}();
