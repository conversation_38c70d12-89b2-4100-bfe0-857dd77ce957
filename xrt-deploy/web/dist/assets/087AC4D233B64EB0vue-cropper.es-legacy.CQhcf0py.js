/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(t,o){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var r,i,n,a,s=[],c=!0,h=!1;try{if(n=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;c=!1}else for(;!(c=(r=n.call(o)).done)&&(s.push(r.value),s.length!==e);c=!0);}catch(t){h=!0,i=t}finally{try{if(!c&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(h)throw i}}return s}}(t,o)||e(t,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e(t,e){if(t){if("string"==typeof t)return o(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,e):void 0}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,r=Array(e);o<e;o++)r[o]=t[o];return r}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(o,r){"use strict";var i,n,a,s,c,h,p,u,l,f;return{setters:[function(t){i=t.L,n=t.c,a=t.o,s=t.d,c=t.b,h=t.a7,p=t.a8,u=t.X,l=t.n,f=t.t}],execute:function(){var r=document.createElement("style");r.textContent=".vue-cropper[data-v-a742df44]{position:relative;width:100%;height:100%;box-sizing:border-box;user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;direction:ltr;touch-action:none;text-align:left;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC)}.cropper-box[data-v-a742df44],.cropper-box-canvas[data-v-a742df44],.cropper-drag-box[data-v-a742df44],.cropper-crop-box[data-v-a742df44],.cropper-face[data-v-a742df44]{position:absolute;top:0;right:0;bottom:0;left:0;-webkit-user-select:none;-moz-user-select:none;user-select:none}.cropper-box-canvas img[data-v-a742df44]{position:relative;text-align:left;-webkit-user-select:none;-moz-user-select:none;user-select:none;transform:none;max-width:none;max-height:none}.cropper-box[data-v-a742df44]{overflow:hidden}.cropper-move[data-v-a742df44]{cursor:move}.cropper-crop[data-v-a742df44]{cursor:crosshair}.cropper-modal[data-v-a742df44]{background:rgba(0,0,0,.5)}.cropper-view-box[data-v-a742df44]{display:block;overflow:hidden;width:100%;height:100%;outline:1px solid #39f;outline-color:rgba(51,153,255,.75);-webkit-user-select:none;-moz-user-select:none;user-select:none}.cropper-view-box img[data-v-a742df44]{-webkit-user-select:none;-moz-user-select:none;user-select:none;text-align:left;max-width:none;max-height:none}.cropper-face[data-v-a742df44]{top:0;left:0;background-color:#fff;opacity:.1}.crop-info[data-v-a742df44]{position:absolute;left:0;min-width:65px;text-align:center;color:#fff;line-height:20px;background-color:rgba(0,0,0,.8);font-size:12px}.crop-line[data-v-a742df44]{position:absolute;display:block;width:100%;height:100%;opacity:.1}.line-w[data-v-a742df44]{top:-3px;left:0;height:5px;cursor:n-resize}.line-a[data-v-a742df44]{top:0;left:-3px;width:5px;cursor:w-resize}.line-s[data-v-a742df44]{bottom:-3px;left:0;height:5px;cursor:s-resize}.line-d[data-v-a742df44]{top:0;right:-3px;width:5px;cursor:e-resize}.crop-point[data-v-a742df44]{position:absolute;width:8px;height:8px;opacity:.75;background-color:#39f;border-radius:100%}.point1[data-v-a742df44]{top:-4px;left:-4px;cursor:nw-resize}.point2[data-v-a742df44]{top:-5px;left:50%;margin-left:-3px;cursor:n-resize}.point3[data-v-a742df44]{top:-4px;right:-4px;cursor:ne-resize}.point4[data-v-a742df44]{top:50%;left:-4px;margin-top:-3px;cursor:w-resize}.point5[data-v-a742df44]{top:50%;right:-4px;margin-top:-3px;cursor:e-resize}.point6[data-v-a742df44]{bottom:-5px;left:-4px;cursor:sw-resize}.point7[data-v-a742df44]{bottom:-5px;left:50%;margin-left:-3px;cursor:s-resize}.point8[data-v-a742df44]{bottom:-5px;right:-4px;cursor:se-resize}@media screen and (max-width: 500px){.crop-point[data-v-a742df44]{position:absolute;width:20px;height:20px;opacity:.45;background-color:#39f;border-radius:100%}.point1[data-v-a742df44]{top:-10px;left:-10px}.point2[data-v-a742df44],.point4[data-v-a742df44],.point5[data-v-a742df44],.point7[data-v-a742df44]{display:none}.point3[data-v-a742df44]{top:-10px;right:-10px}.point4[data-v-a742df44]{top:0;left:0}.point6[data-v-a742df44]{bottom:-10px;left:-10px}.point8[data-v-a742df44]{bottom:-10px;right:-10px}}\n/*$vite$:1*/",document.head.appendChild(r);var d={};d.getData=function(t){return new Promise((function(e,o){var r={};(function(t){var e=null;return new Promise((function(o,r){if(t.src)if(/^data\:/i.test(t.src))e=function(t,e){e=e||t.match(/^data\:([^\;]+)\;base64,/im)[1]||"",t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var o=atob(t),r=o.length%2==0?o.length:o.length+1,i=new ArrayBuffer(r),n=new Uint16Array(i),a=0;a<r;a++)n[a]=o.charCodeAt(a);return i}(t.src),o(e);else if(/^blob\:/i.test(t.src)){var i=new FileReader;i.onload=function(t){e=t.target.result,o(e)},function(t,e){var o=new XMLHttpRequest;o.open("GET",t,!0),o.responseType="blob",o.onload=function(t){(200==this.status||0===this.status)&&e(this.response)},o.send()}(t.src,(function(t){i.readAsArrayBuffer(t)}))}else{var n=new XMLHttpRequest;n.onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";e=n.response,o(e),n=null},n.open("GET",t.src,!0),n.responseType="arraybuffer",n.send(null)}else r("img error")}))})(t).then((function(t){r.arrayBuffer=t;try{r.orientation=function(t){var e,o,r,i,n,a,s,c,h,p,u=new DataView(t),l=u.byteLength;if(255===u.getUint8(0)&&216===u.getUint8(1))for(h=2;h<l;){if(255===u.getUint8(h)&&225===u.getUint8(h+1)){s=h;break}h++}if(s&&(o=s+4,r=s+10,"Exif"===function(t,e,o){var r,i="";for(r=e,o+=e;r<o;r++)i+=String.fromCharCode(t.getUint8(r));return i}(u,o,4)&&(a=u.getUint16(r),((n=18761===a)||19789===a)&&42===u.getUint16(r+2,n)&&(i=u.getUint32(r+4,n),i>=8&&(c=r+i)))),c)for(l=u.getUint16(c,n),p=0;p<l;p++)if(h=c+12*p+2,274===u.getUint16(h,n)){h+=8,e=u.getUint16(h,n);break}return e}(t)}catch(o){r.orientation=-1}e(r)})).catch((function(t){o(t)}))}))};var g=function(o,r){var i,n=o.__vccOpts||o,a=function(t,o){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=e(t))||o&&t&&"number"==typeof t.length){r&&(t=r);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,c=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){c=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(c)throw a}}}}(r);try{for(a.s();!(i=a.n()).done;){var s=t(i.value,2),c=s[0],h=s[1];n[c]=h}}catch(p){a.e(p)}finally{a.f()}return n},v=i({data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0,imgIsQqualCrop:!1}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:function(){return 10},validator:function(t){return Array.isArray(t)?Number(t[0])>=0&&Number(t[1])>=0:Number(t)>=0}},fillColor:{type:String,default:""}},computed:{cropInfo:function(){var t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){var e=1;this.high&&!this.full&&(e=window.devicePixelRatio),1!==this.enlarge&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:function(){return!!window.ActiveXObject||"ActiveXObject"in window},passive:function(){return this.isIE?null:{passive:!1}},isRotateRightOrLeft:function(){return[1,-1,3,-3].includes(this.rotate)}},watch:{img:function(){this.checkedImg()},imgs:function(t){""!==t&&this.reload()},cropW:function(){this.showPreview()},cropH:function(){this.showPreview()},cropOffsertX:function(){this.showPreview()},cropOffsertY:function(){this.showPreview()},scale:function(t,e){this.showPreview()},x:function(){this.showPreview()},y:function(){this.showPreview()},autoCrop:function(t){t&&this.goAutoCrop()},autoCropWidth:function(){this.autoCrop&&this.goAutoCrop()},autoCropHeight:function(){this.autoCrop&&this.goAutoCrop()},mode:function(){this.checkedImg()},rotate:function(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion:function(t){for(var e=navigator.userAgent.split(" "),o="",r=new RegExp(t,"i"),i=0;i<e.length;i++)r.test(e[i])&&(o=e[i]);return o?o.split("/")[1].split("."):["0","0","0"]},checkOrientationImage:function(t,e,o,r){var i=this;if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){var n=this.getVersion("version");n[0]>13&&n[1]>1&&(e=-1)}else{var a=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(a){var s=a[1];((s=s.split("_"))[0]>13||s[0]>=13&&s[1]>=4)&&(e=-1)}}var c=document.createElement("canvas"),h=c.getContext("2d");switch(h.save(),e){case 2:c.width=o,c.height=r,h.translate(o,0),h.scale(-1,1);break;case 3:c.width=o,c.height=r,h.translate(o/2,r/2),h.rotate(180*Math.PI/180),h.translate(-o/2,-r/2);break;case 4:c.width=o,c.height=r,h.translate(0,r),h.scale(1,-1);break;case 5:c.height=o,c.width=r,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:c.width=r,c.height=o,h.translate(r/2,o/2),h.rotate(90*Math.PI/180),h.translate(-o/2,-r/2);break;case 7:c.height=o,c.width=r,h.rotate(.5*Math.PI),h.translate(o,-r),h.scale(-1,1);break;case 8:c.height=o,c.width=r,h.translate(r/2,o/2),h.rotate(-90*Math.PI/180),h.translate(-o/2,-r/2);break;default:c.width=o,c.height=r}h.drawImage(t,0,0,o,r),h.restore(),c.toBlob((function(t){var e=URL.createObjectURL(t);URL.revokeObjectURL(i.imgs),i.imgs=e}),"image/"+this.outputType,1)},checkedImg:function(){var t=this;if(null===this.img||""===this.img)return this.imgs="",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.imgIsQqualCrop=!1,this.clearCrop();var e=new Image;if(e.onload=function(){if(""===t.img)return t.$emit("img-load",new Error("图片不能为空")),!1;var o=e.width,r=e.height;d.getData(e).then((function(i){t.orientation=i.orientation||1;var n=Number(t.maxImgSize);!t.orientation&&o<n&r<n?t.imgs=t.img:(o>n&&(r=r/o*n,o=n),r>n&&(o=o/r*n,r=n),t.checkOrientationImage(e,t.orientation,o,r))})).catch((function(e){t.$emit("img-load","error"),t.$emit("img-load-error",e)}))},e.onerror=function(e){t.$emit("img-load","error"),t.$emit("img-load-error",e)},"data"!==this.img.substr(0,4)&&(e.crossOrigin=""),this.isIE){var o=new XMLHttpRequest;o.onload=function(){var t=URL.createObjectURL(this.response);e.src=t},o.open("GET",this.img,!0),o.responseType="blob",o.send()}else e.src=this.img},startMove:function(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=("clientX"in t?t.clientX:t.touches[0].clientX)-this.x,this.moveY=("clientY"in t?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale:function(t){var e=this;t.preventDefault();var o=this.scale,r=this.touches[0].clientX,i=this.touches[0].clientY,n=t.touches[0].clientX,a=t.touches[0].clientY,s=this.touches[1].clientX,c=this.touches[1].clientY,h=t.touches[1].clientX,p=t.touches[1].clientY,u=Math.sqrt(Math.pow(r-s,2)+Math.pow(i-c,2)),l=Math.sqrt(Math.pow(n-h,2)+Math.pow(a-p,2))-u,f=1,d=(f=(f=f/this.trueWidth>f/this.trueHeight?f/this.trueHeight:f/this.trueWidth)>.1?.1:f)*l;if(!this.touchNow){if(this.touchNow=!0,l>0?o+=Math.abs(d):l<0&&o>Math.abs(d)&&(o-=Math.abs(d)),this.touches=t.touches,setTimeout((function(){e.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o}},cancelTouchScale:function(t){window.removeEventListener("touchmove",this.touchScale)},moveImg:function(t){var e=this;if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;var o,r,i="clientX"in t?t.clientX:t.touches[0].clientX,n="clientY"in t?t.clientY:t.touches[0].clientY;o=i-this.moveX,r=n-this.moveY,this.$nextTick((function(){if(e.centerBox){var t,i,n,a,s=e.getImgAxis(o,r,e.scale),c=e.getCropAxis(),h=e.trueHeight*e.scale,p=e.trueWidth*e.scale;switch(e.rotate){case 1:case-1:case 3:case-3:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2+(h-p)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2+(p-h)/2,n=t-h+e.cropW,a=i-p+e.cropH;break;default:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2,i=e.cropOffsertY-e.trueHeight*(1-e.scale)/2,n=t-p+e.cropW,a=i-h+e.cropH}s.x1>=c.x1&&(o=t),s.y1>=c.y1&&(r=i),s.x2<=c.x2&&(o=n),s.y2<=c.y2&&(r=a)}e.x=o,e.y=r,e.$emit("img-moving",{moving:!0,axis:e.getImgAxis()})}))},leaveImg:function(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg:function(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale:function(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize:function(t){var e=this;t.preventDefault();var o=this.scale,r=t.deltaY||t.wheelDelta;r=navigator.userAgent.indexOf("Firefox")>0?30*r:r,this.isIE&&(r=-r);var i=this.coe,n=(i=i/this.trueWidth>i/this.trueHeight?i/this.trueHeight:i/this.trueWidth)*r;n<0?o+=Math.abs(n):o>Math.abs(n)&&(o-=Math.abs(n));var a=n<0?"add":"reduce";if(a!==this.coeStatus&&(this.coeStatus=a,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((function(){e.scaling=!1,e.coe=e.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,o))return!1;this.scale=o},changeScale:function(t){var e=this.scale;t=t||1;var o=20;if((t*=o=o/this.trueWidth>o/this.trueHeight?o/this.trueHeight:o/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop:function(t){var e=this;t.preventDefault();var o="clientX"in t?t.clientX:t.touches?t.touches[0].clientX:0,r="clientY"in t?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((function(){var t=o-e.cropX,i=r-e.cropY;if(t>0?(e.cropW=t+e.cropChangeX>e.w?e.w-e.cropChangeX:t,e.cropOffsertX=e.cropChangeX):(e.cropW=e.w-e.cropChangeX+Math.abs(t)>e.w?e.cropChangeX:Math.abs(t),e.cropOffsertX=e.cropChangeX+t>0?e.cropChangeX+t:0),e.fixed){var n=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];n+e.cropOffsertY>e.h?(e.cropH=e.h-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],e.cropOffsertX=t>0?e.cropChangeX:e.cropChangeX-e.cropW):e.cropH=n,e.cropOffsertY=e.cropOffsertY}else i>0?(e.cropH=i+e.cropChangeY>e.h?e.h-e.cropChangeY:i,e.cropOffsertY=e.cropChangeY):(e.cropH=e.h-e.cropChangeY+Math.abs(i)>e.h?e.cropChangeY:Math.abs(i),e.cropOffsertY=e.cropChangeY+i>0?e.cropChangeY+i:0)}))},changeCropSize:function(t,e,o,r,i){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=o,this.changeCropTypeX=r,this.changeCropTypeY=i,this.cropX="clientX"in t?t.clientX:t.touches[0].clientX,this.cropY="clientY"in t?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow:function(e){var o=this;e.preventDefault();var r="clientX"in e?e.clientX:e.touches?e.touches[0].clientX:0,i="clientY"in e?e.clientY:e.touches?e.touches[0].clientY:0,n=this.w,a=this.h,s=0,c=0;if(this.centerBox){var h=this.getImgAxis(),p=h.x2,u=h.y2;s=h.x1>0?h.x1:0,c=h.y1>0?h.y1:0,n>p&&(n=p),a>u&&(a=u)}var l=t(this.checkCropLimitSize(),2),f=l[0],d=l[1];this.$nextTick((function(){var t=r-o.cropX,e=i-o.cropY;if(o.canChangeX&&(1===o.changeCropTypeX?o.cropOldW-t<f?(o.cropW=f,o.cropOffsertX=o.cropOldW+o.cropChangeX-s-f):o.cropOldW-t>0?(o.cropW=n-o.cropChangeX-t<=n-s?o.cropOldW-t:o.cropOldW+o.cropChangeX-s,o.cropOffsertX=n-o.cropChangeX-t<=n-s?o.cropChangeX+t:s):(o.cropW=Math.abs(t)+o.cropChangeX<=n?Math.abs(t)-o.cropOldW:n-o.cropOldW-o.cropChangeX,o.cropOffsertX=o.cropChangeX+o.cropOldW):2===o.changeCropTypeX&&(o.cropOldW+t<f?o.cropW=f:o.cropOldW+t>0?(o.cropW=o.cropOldW+t+o.cropOffsertX<=n?o.cropOldW+t:n-o.cropOffsertX,o.cropOffsertX=o.cropChangeX):(o.cropW=n-o.cropChangeX+Math.abs(t+o.cropOldW)<=n-s?Math.abs(t+o.cropOldW):o.cropChangeX-s,o.cropOffsertX=n-o.cropChangeX+Math.abs(t+o.cropOldW)<=n-s?o.cropChangeX-Math.abs(t+o.cropOldW):s))),o.canChangeY&&(1===o.changeCropTypeY?o.cropOldH-e<d?(o.cropH=d,o.cropOffsertY=o.cropOldH+o.cropChangeY-c-d):o.cropOldH-e>0?(o.cropH=a-o.cropChangeY-e<=a-c?o.cropOldH-e:o.cropOldH+o.cropChangeY-c,o.cropOffsertY=a-o.cropChangeY-e<=a-c?o.cropChangeY+e:c):(o.cropH=Math.abs(e)+o.cropChangeY<=a?Math.abs(e)-o.cropOldH:a-o.cropOldH-o.cropChangeY,o.cropOffsertY=o.cropChangeY+o.cropOldH):2===o.changeCropTypeY&&(o.cropOldH+e<d?o.cropH=d:o.cropOldH+e>0?(o.cropH=o.cropOldH+e+o.cropOffsertY<=a?o.cropOldH+e:a-o.cropOffsertY,o.cropOffsertY=o.cropChangeY):(o.cropH=a-o.cropChangeY+Math.abs(e+o.cropOldH)<=a-c?Math.abs(e+o.cropOldH):o.cropChangeY-c,o.cropOffsertY=a-o.cropChangeY+Math.abs(e+o.cropOldH)<=a-c?o.cropChangeY-Math.abs(e+o.cropOldH):c))),o.canChangeX&&o.fixed){var h=o.cropW/o.fixedNumber[0]*o.fixedNumber[1];h<d?(o.cropH=d,o.cropW=o.fixedNumber[0]*d/o.fixedNumber[1],1===o.changeCropTypeX&&(o.cropOffsertX=o.cropChangeX+(o.cropOldW-o.cropW))):h+o.cropOffsertY>a?(o.cropH=a-o.cropOffsertY,o.cropW=o.cropH/o.fixedNumber[1]*o.fixedNumber[0],1===o.changeCropTypeX&&(o.cropOffsertX=o.cropChangeX+(o.cropOldW-o.cropW))):o.cropH=h}if(o.canChangeY&&o.fixed){var p=o.cropH/o.fixedNumber[1]*o.fixedNumber[0];p<f?(o.cropW=f,o.cropH=o.fixedNumber[1]*f/o.fixedNumber[0],o.cropOffsertY=o.cropOldH+o.cropChangeY-o.cropH):p+o.cropOffsertX>n?(o.cropW=n-o.cropOffsertX,o.cropH=o.cropW/o.fixedNumber[0]*o.fixedNumber[1]):o.cropW=p}}))},checkCropLimitSize:function(){this.cropW,this.cropH;var t=this.limitMinSize,e=new Array;return e=Array.isArray(t)?t:[t,t],[parseFloat(e[0]),parseFloat(e[1])]},changeCropEnd:function(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},calculateSize:function(t,e,o,r,i,n){var a=t/e,s=i,c=n;return s<o&&(s=o,c=Math.ceil(s/a)),c<r&&(c=r,(s=Math.ceil(c*a))<o&&(s=o,c=Math.ceil(s/a))),s<i&&(s=i,c=Math.ceil(s/a)),c<n&&(c=n,s=Math.ceil(c*a)),{width:s,height:c}},endCrop:function(){0===this.cropW&&0===this.cropH&&(this.cropping=!1);var e=t(this.checkCropLimitSize(),2),o=e[0],r=e[1],i=this.fixed?this.calculateSize(this.fixedNumber[0],this.fixedNumber[1],o,r,this.cropW,this.cropH):{width:o,height:r},n=i.width,a=i.height;n>this.cropW&&(this.cropW=n,this.cropOffsertX+n>this.w&&(this.cropOffsertX=this.w-n)),a>this.cropH&&(this.cropH=a,this.cropOffsertY+a>this.h&&(this.cropOffsertY=this.h-a)),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop:function(){this.crop=!0},stopCrop:function(){this.crop=!1},clearCrop:function(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove:function(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);var e,o,r="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY;e=r-this.cropOffsertX,o=i-this.cropOffsertY,this.cropX=e,this.cropY=o,this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop:function(t,e){var o=this,r=0,i=0;t&&(t.preventDefault(),r="clientX"in t?t.clientX:t.touches[0].clientX,i="clientY"in t?t.clientY:t.touches[0].clientY),this.$nextTick((function(){var t,n,a=r-o.cropX,s=i-o.cropY;if(e&&(a=o.cropOffsertX,s=o.cropOffsertY),t=a<=0?0:a+o.cropW>o.w?o.w-o.cropW:a,n=s<=0?0:s+o.cropH>o.h?o.h-o.cropH:s,o.centerBox){var c=o.getImgAxis();t<=c.x1&&(t=c.x1),t+o.cropW>c.x2&&(t=c.x2-o.cropW),n<=c.y1&&(n=c.y1),n+o.cropH>c.y2&&(n=c.y2-o.cropH)}o.cropOffsertX=t,o.cropOffsertY=n,o.$emit("crop-moving",{moving:!0,axis:o.getCropAxis()})}))},getImgAxis:function(t,e,o){t=t||this.x,e=e||this.y,o=o||this.scale;var r={x1:0,x2:0,y1:0,y2:0},i=this.trueWidth*o,n=this.trueHeight*o;switch(this.rotate){case 0:r.x1=t+this.trueWidth*(1-o)/2,r.x2=r.x1+this.trueWidth*o,r.y1=e+this.trueHeight*(1-o)/2,r.y2=r.y1+this.trueHeight*o;break;case 1:case-1:case 3:case-3:r.x1=t+this.trueWidth*(1-o)/2+(i-n)/2,r.x2=r.x1+this.trueHeight*o,r.y1=e+this.trueHeight*(1-o)/2+(n-i)/2,r.y2=r.y1+this.trueWidth*o;break;default:r.x1=t+this.trueWidth*(1-o)/2,r.x2=r.x1+this.trueWidth*o,r.y1=e+this.trueHeight*(1-o)/2,r.y2=r.y1+this.trueHeight*o}return r},getCropAxis:function(){var t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop:function(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked:function(t){var e=this,o=document.createElement("canvas"),r=o.getContext("2d"),i=new Image,n=this.rotate,a=this.trueWidth,s=this.trueHeight,c=this.cropOffsertX,h=this.cropOffsertY;i.onload=function(){if(0!==e.cropW){var p=1;e.high&!e.full&&(p=window.devicePixelRatio),1!==e.enlarge&!e.full&&(p=Math.abs(Number(e.enlarge)));var l=e.cropW*p,f=e.cropH*p,d=a*e.scale*p,g=s*e.scale*p,v=(e.x-c+e.trueWidth*(1-e.scale)/2)*p,m=(e.y-h+e.trueHeight*(1-e.scale)/2)*p;switch(u(l,f),r.save(),n){case 0:e.full?(u(l/e.scale,f/e.scale),r.drawImage(i,v/e.scale,m/e.scale,d/e.scale,g/e.scale)):r.drawImage(i,v,m,d,g);break;case 1:case-3:e.full?(u(l/e.scale,f/e.scale),v=v/e.scale+(d/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-d/e.scale)/2,r.rotate(90*n*Math.PI/180),r.drawImage(i,m,-v-g/e.scale,d/e.scale,g/e.scale)):(v+=(d-g)/2,m+=(g-d)/2,r.rotate(90*n*Math.PI/180),r.drawImage(i,m,-v-g,d,g));break;case 2:case-2:e.full?(u(l/e.scale,f/e.scale),r.rotate(90*n*Math.PI/180),v/=e.scale,m/=e.scale,r.drawImage(i,-v-d/e.scale,-m-g/e.scale,d/e.scale,g/e.scale)):(r.rotate(90*n*Math.PI/180),r.drawImage(i,-v-d,-m-g,d,g));break;case 3:case-1:e.full?(u(l/e.scale,f/e.scale),v=v/e.scale+(d/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-d/e.scale)/2,r.rotate(90*n*Math.PI/180),r.drawImage(i,-m-d/e.scale,v,d/e.scale,g/e.scale)):(v+=(d-g)/2,m+=(g-d)/2,r.rotate(90*n*Math.PI/180),r.drawImage(i,-m-d,v,d,g));break;default:e.full?(u(l/e.scale,f/e.scale),r.drawImage(i,v/e.scale,m/e.scale,d/e.scale,g/e.scale)):r.drawImage(i,v,m,d,g)}r.restore()}else{var w=a*e.scale,x=s*e.scale;switch(r.save(),n){case 0:u(w,x),r.drawImage(i,0,0,w,x);break;case 1:case-3:u(x,w),r.rotate(90*n*Math.PI/180),r.drawImage(i,0,-x,w,x);break;case 2:case-2:u(w,x),r.rotate(90*n*Math.PI/180),r.drawImage(i,-w,-x,w,x);break;case 3:case-1:u(x,w),r.rotate(90*n*Math.PI/180),r.drawImage(i,-w,0,w,x);break;default:u(w,x),r.drawImage(i,0,0,w,x)}r.restore()}t(o)},"data"!==this.img.substr(0,4)&&(i.crossOrigin="Anonymous"),i.src=this.imgs;var p=this.fillColor;function u(t,e){o.width=Math.round(t),o.height=Math.round(e),p&&(r.fillStyle=p,r.fillRect(0,0,o.width,o.height))}},getCropData:function(t){var e=this;this.getCropChecked((function(o){t(o.toDataURL("image/"+e.outputType,e.outputSize))}))},getCropBlob:function(t){var e=this;this.getCropChecked((function(o){o.toBlob((function(e){return t(e)}),"image/"+e.outputType,e.outputSize)}))},showPreview:function(){var t=this;if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((function(){t.isCanShow=!0}),16);var e=this.cropW,o=this.cropH,r=this.scale,i={};i.div={width:"".concat(e,"px"),height:"".concat(o,"px")};var n=(this.x-this.cropOffsertX)/r,a=(this.y-this.cropOffsertY)/r;i.w=e,i.h=o,i.url=this.imgs,i.img={width:"".concat(this.trueWidth,"px"),height:"".concat(this.trueHeight,"px"),transform:"scale(".concat(r,")translate3d(").concat(n,"px, ").concat(a,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,"deg)")},i.html='\n      <div class="show-preview" style="width: '.concat(i.w,"px; height: ").concat(i.h,'px,; overflow: hidden">\n        <div style="width: ').concat(e,"px; height: ").concat(o,'px">\n          <img src=').concat(i.url,' style="width: ').concat(this.trueWidth,"px; height: ").concat(this.trueHeight,"px; transform:\n          scale(").concat(r,")translate3d(").concat(n,"px, ").concat(a,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,'deg)">\n        </div>\n      </div>'),this.$emit("real-time",i)},reload:function(){var t=this,e=new Image;e.onload=function(){t.w=parseFloat(window.getComputedStyle(t.$refs.cropper).width),t.h=parseFloat(window.getComputedStyle(t.$refs.cropper).height),t.trueWidth=e.width,t.trueHeight=e.height,t.original?t.scale=1:t.scale=t.checkedMode(),t.$nextTick((function(){t.x=-(t.trueWidth-t.trueWidth*t.scale)/2+(t.w-t.trueWidth*t.scale)/2,t.y=-(t.trueHeight-t.trueHeight*t.scale)/2+(t.h-t.trueHeight*t.scale)/2,t.loading=!1,t.autoCrop&&t.goAutoCrop(),t.$emit("img-load","success"),setTimeout((function(){t.showPreview()}),20)}))},e.onerror=function(){t.$emit("img-load","error")},e.src=this.imgs},checkedMode:function(){var t=1,e=(this.trueWidth,this.trueHeight),o=this.mode.split(" ");switch(o[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":(e*=t=this.w/this.trueWidth)<this.h&&(t=(e=this.h)/this.trueHeight);break;default:try{var r=o[0];if(-1!==r.search("px")){r=r.replace("px","");var i=parseFloat(r)/this.trueWidth,n=1,a=o[1];-1!==a.search("px")&&(a=a.replace("px",""),n=(e=parseFloat(a))/this.trueHeight),t=Math.min(i,n)}if(-1!==r.search("%")&&(r=r.replace("%",""),t=parseFloat(r)/100*this.w/this.trueWidth),2===o.length&&"auto"===r){var s=o[1];-1!==s.search("px")&&(s=s.replace("px",""),t=(e=parseFloat(s))/this.trueHeight),-1!==s.search("%")&&(s=s.replace("%",""),t=(e=parseFloat(s)/100*this.h)/this.trueHeight)}}catch(c){t=1}}return t},goAutoCrop:function(t,e){if(""!==this.imgs&&null!==this.imgs){this.clearCrop(),this.cropping=!0;var o=this.w,r=this.h;if(this.centerBox){var i=Math.abs(this.rotate)%2>0,n=(i?this.trueHeight:this.trueWidth)*this.scale,a=(i?this.trueWidth:this.trueHeight)*this.scale;o=n<o?n:o,r=a<r?a:r}var s=t||parseFloat(this.autoCropWidth),c=e||parseFloat(this.autoCropHeight);(0===s||0===c)&&(s=.8*o,c=.8*r),s=s>o?o:s,c=c>r?r:c,this.fixed&&(c=s/this.fixedNumber[0]*this.fixedNumber[1]),c>this.h&&(s=(c=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(s,c)}},changeCrop:function(t,e){var o=this;if(this.centerBox){var r=this.getImgAxis();t>r.x2-r.x1&&(e=(t=r.x2-r.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>r.y2-r.y1&&(t=(e=r.y2-r.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((function(){o.cropOffsertX=(o.w-o.cropW)/2,o.cropOffsertY=(o.h-o.cropH)/2,o.centerBox&&o.moveCrop(null,!0)}))},refresh:function(){var t=this;this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.imgIsQqualCrop=!1,this.clearCrop(),this.$nextTick((function(){t.checkedImg()}))},rotateLeft:function(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight:function(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear:function(){this.rotate=0},checkoutImgAxis:function(t,e,o){t=t||this.x,e=e||this.y,o=o||this.scale;var r=!0;if(this.centerBox){var i=this.getImgAxis(t,e,o),n=this.getCropAxis();i.x1>=n.x1&&(r=!1),i.x2<=n.x2&&(r=!1),i.y1>=n.y1&&(r=!1),i.y2<=n.y2&&(r=!1),r||this.changeImgScale(i,n,o)}return r},changeImgScale:function(t,e,o){var r=this.trueWidth,i=this.trueHeight,n=r*o,a=i*o;if(n>=this.cropW&&a>=this.cropH)this.scale=o;else{var s=this.cropW/r,c=this.cropH/i,h=this.cropH<=i*s?s:c;this.scale=h,n=r*h,a=i*h}this.imgIsQqualCrop||(t.x1>=e.x1&&(this.isRotateRightOrLeft?this.x=e.x1-(r-n)/2-(n-a)/2:this.x=e.x1-(r-n)/2),t.x2<=e.x2&&(this.isRotateRightOrLeft?this.x=e.x1-(r-n)/2-(n-a)/2-a+this.cropW:this.x=e.x2-(r-n)/2-n),t.y1>=e.y1&&(this.isRotateRightOrLeft?this.y=e.y1-(i-a)/2-(a-n)/2:this.y=e.y1-(i-a)/2),t.y2<=e.y2&&(this.isRotateRightOrLeft?this.y=e.y2-(i-a)/2-(a-n)/2-n:this.y=e.y2-(i-a)/2-a)),(n<this.cropW||a<this.cropH)&&(this.imgIsQqualCrop=!0)}},mounted:function(){this.support="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";var t=this,e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,o,r){for(var i=atob(this.toDataURL(o,r).split(",")[1]),n=i.length,a=new Uint8Array(n),s=0;s<n;s++)a[s]=i.charCodeAt(s);e(new Blob([a],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},unmounted:function(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.cancelScale()}}),m={key:0,class:"cropper-box"},w=["src"],x={class:"cropper-view-box"},C=["src"],y={key:1};o("M",g(v,[["render",function(t,e,o,r,i,d){return a(),n("div",{class:"vue-cropper",ref:"cropper",onMouseover:e[28]||(e[28]=function(){return t.scaleImg&&t.scaleImg.apply(t,arguments)}),onMouseout:e[29]||(e[29]=function(){return t.cancelScale&&t.cancelScale.apply(t,arguments)})},[t.imgs?(a(),n("div",m,[h(c("div",{class:"cropper-box-canvas",style:u({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"})},[c("img",{src:t.imgs,alt:"cropper-img",ref:"cropperImg"},null,8,w)],4),[[p,!t.loading]])])):s("",!0),c("div",{class:l(["cropper-drag-box",{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping}]),onMousedown:e[0]||(e[0]=function(){return t.startMove&&t.startMove.apply(t,arguments)}),onTouchstart:e[1]||(e[1]=function(){return t.startMove&&t.startMove.apply(t,arguments)})},null,34),h(c("div",{class:"cropper-crop-box",style:u({width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"})},[c("span",x,[c("img",{style:u({width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}),src:t.imgs,alt:"cropper-img"},null,12,C)]),c("span",{class:"cropper-face cropper-move",onMousedown:e[2]||(e[2]=function(){return t.cropMove&&t.cropMove.apply(t,arguments)}),onTouchstart:e[3]||(e[3]=function(){return t.cropMove&&t.cropMove.apply(t,arguments)})},null,32),t.info?(a(),n("span",{key:0,class:"crop-info",style:u({top:t.cropInfo.top})},f(t.cropInfo.width)+" × "+f(t.cropInfo.height),5)):s("",!0),t.fixedBox?s("",!0):(a(),n("span",y,[c("span",{class:"crop-line line-w",onMousedown:e[4]||(e[4]=function(e){return t.changeCropSize(e,!1,!0,0,1)}),onTouchstart:e[5]||(e[5]=function(e){return t.changeCropSize(e,!1,!0,0,1)})},null,32),c("span",{class:"crop-line line-a",onMousedown:e[6]||(e[6]=function(e){return t.changeCropSize(e,!0,!1,1,0)}),onTouchstart:e[7]||(e[7]=function(e){return t.changeCropSize(e,!0,!1,1,0)})},null,32),c("span",{class:"crop-line line-s",onMousedown:e[8]||(e[8]=function(e){return t.changeCropSize(e,!1,!0,0,2)}),onTouchstart:e[9]||(e[9]=function(e){return t.changeCropSize(e,!1,!0,0,2)})},null,32),c("span",{class:"crop-line line-d",onMousedown:e[10]||(e[10]=function(e){return t.changeCropSize(e,!0,!1,2,0)}),onTouchstart:e[11]||(e[11]=function(e){return t.changeCropSize(e,!0,!1,2,0)})},null,32),c("span",{class:"crop-point point1",onMousedown:e[12]||(e[12]=function(e){return t.changeCropSize(e,!0,!0,1,1)}),onTouchstart:e[13]||(e[13]=function(e){return t.changeCropSize(e,!0,!0,1,1)})},null,32),c("span",{class:"crop-point point2",onMousedown:e[14]||(e[14]=function(e){return t.changeCropSize(e,!1,!0,0,1)}),onTouchstart:e[15]||(e[15]=function(e){return t.changeCropSize(e,!1,!0,0,1)})},null,32),c("span",{class:"crop-point point3",onMousedown:e[16]||(e[16]=function(e){return t.changeCropSize(e,!0,!0,2,1)}),onTouchstart:e[17]||(e[17]=function(e){return t.changeCropSize(e,!0,!0,2,1)})},null,32),c("span",{class:"crop-point point4",onMousedown:e[18]||(e[18]=function(e){return t.changeCropSize(e,!0,!1,1,0)}),onTouchstart:e[19]||(e[19]=function(e){return t.changeCropSize(e,!0,!1,1,0)})},null,32),c("span",{class:"crop-point point5",onMousedown:e[20]||(e[20]=function(e){return t.changeCropSize(e,!0,!1,2,0)}),onTouchstart:e[21]||(e[21]=function(e){return t.changeCropSize(e,!0,!1,2,0)})},null,32),c("span",{class:"crop-point point6",onMousedown:e[22]||(e[22]=function(e){return t.changeCropSize(e,!0,!0,1,2)}),onTouchstart:e[23]||(e[23]=function(e){return t.changeCropSize(e,!0,!0,1,2)})},null,32),c("span",{class:"crop-point point7",onMousedown:e[24]||(e[24]=function(e){return t.changeCropSize(e,!1,!0,0,2)}),onTouchstart:e[25]||(e[25]=function(e){return t.changeCropSize(e,!1,!0,0,2)})},null,32),c("span",{class:"crop-point point8",onMousedown:e[26]||(e[26]=function(e){return t.changeCropSize(e,!0,!0,2,2)}),onTouchstart:e[27]||(e[27]=function(e){return t.changeCropSize(e,!0,!0,2,2)})},null,32)]))],4),[[p,t.cropping]])],544)}],["__scopeId","data-v-a742df44"]]))}}}))}();
