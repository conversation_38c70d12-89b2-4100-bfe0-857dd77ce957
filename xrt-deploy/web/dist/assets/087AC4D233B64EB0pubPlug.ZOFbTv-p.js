/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{q as a,s as t,t as l}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";import{j as n}from"./087AC4D233B64EB0api.DOS2t6hl.js";import{a as s,g as o,c as i,o as r,b as u,f as c,w as d,h as p,t as m,aX as f,E as v,ab as g}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const h={class:"gva-form-box"},x={class:"p-4 bg-white dark:bg-slate-900"},y={class:"flex items-center gap-3"},B={class:"flex justify-end mt-2"},A={class:"flex justify-end mt-2"},b={class:"flex justify-end"},V={__name:"pubPlug",setup(V){const _=s(""),w=s([]),C=s([]),I=s([]),P=s([]),j=s(""),T=e=>{const a=[];return e.forEach((e=>{e.children?a.push(...T(e.children)):a.push(e)})),a},k=(e,a)=>a.meta.title.indexOf(e)>-1||a.component.indexOf(e)>-1,D=(e,a)=>a.description.indexOf(e)>-1||a.path.indexOf(e)>-1;(async()=>{const e=await f();0===e.code&&(C.value=T(e.data));const a=await n();0===a.code&&(P.value=a.data.apis)})();const E=async()=>{g.confirm("请检查server下的/plugin/".concat(_.value,"/plugin.go是否已放开需要的 initialize.Api(ctx) 和 initialize.Menu(ctx)?"),"打包",{confirmButtonText:"打包",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await l({plugName:_.value});0===e.code&&v.success(e.msg)})).catch((()=>{v({type:"info",message:"关闭打包"})}))},z=()=>{j.value?0!==w.value.length?""!==_.value?g.confirm("点击后将会覆盖server下的/plugin/".concat(_.value,"/initialize/menu. 是否继续?"),"生成初始菜单",{confirmButtonText:"生成",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={plugName:_.value,parentMenu:j.value,menus:w.value};a(e)})).catch((()=>{v({type:"info",message:"关闭生成菜单"})})):v.error("请填写插件名"):v.error("请至少选择一个菜单"):v.error("请填写菜单组名")},O=()=>{0!==I.value.length?""!==_.value?g.confirm("点击后将会覆盖server下的/plugin/".concat(_.value,"/initialize/api. 是否继续?"),"生成初始API",{confirmButtonText:"生成",cancelButtonText:"取消",type:"warning"}).then((()=>{const e={plugName:_.value,apis:I.value};t(e)})).catch((()=>{v({type:"info",message:"关闭生成API"})})):v.error("请填写插件名"):v.error("请至少选择一个API")};return(a,t)=>{const l=o("el-input"),n=o("el-transfer"),s=o("el-button"),f=o("el-card");return r(),i("div",h,[u("div",x,[c(e,{title:"目前只支持标准插件（通过插件模板生成的标准目录插件），非标准插件请自行打包"}),u("div",y,[c(l,{modelValue:_.value,"onUpdate:modelValue":t[0]||(t[0]=e=>_.value=e),placeholder:"插件模板处填写的【插件名】"},null,8,["modelValue"])]),c(f,{class:"mt-2 text-center"},{default:d((()=>[c(e,{title:"穿梭框请只选择子级菜单即可"}),c(l,{modelValue:j.value,"onUpdate:modelValue":t[1]||(t[1]=e=>j.value=e),placeholder:"请输入菜单组名，例：公告管理",class:"mb-2"},null,8,["modelValue"]),c(n,{modelValue:w.value,"onUpdate:modelValue":t[2]||(t[2]=e=>w.value=e),props:{key:"ID"},class:"plugin-transfer",data:C.value,filterable:"","filter-method":k,"filter-placeholder":"请输入菜单名称/路径",titles:["可选菜单","使用菜单"],"button-texts":["移除","选中"]},{default:d((({option:e})=>[p(m(e.meta.title)+" "+m(e.component),1)])),_:1},8,["modelValue","data"]),u("div",B,[c(s,{type:"primary",onClick:z},{default:d((()=>t[4]||(t[4]=[p(" 定义安装菜单 ")]))),_:1})])])),_:1}),c(f,{class:"mt-2 text-center"},{default:d((()=>[c(n,{modelValue:I.value,"onUpdate:modelValue":t[3]||(t[3]=e=>I.value=e),props:{key:"ID"},class:"plugin-transfer",data:P.value,filterable:"","filter-method":D,"filter-placeholder":"请输入API描述/PATH",titles:["可选API","使用API"],"button-texts":["移除","选中"]},{default:d((({option:e})=>[p(m(e.description)+" "+m(e.path),1)])),_:1},8,["modelValue","data"]),u("div",A,[c(s,{type:"primary",onClick:O},{default:d((()=>t[5]||(t[5]=[p(" 定义安装API ")]))),_:1})])])),_:1})]),u("div",b,[c(s,{type:"primary",onClick:E},{default:d((()=>t[6]||(t[6]=[p(" 打包插件 ")]))),_:1})])])}}};export{V as default};
