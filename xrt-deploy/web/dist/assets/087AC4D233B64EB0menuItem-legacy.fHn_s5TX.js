/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,t){"use strict";var n,u,r,i,o,a,f,c,l,s,m,d;return{setters:[function(e){n=e.I,u=e.J,r=e.K,i=e.g,o=e.i,a=e.o,f=e.w,c=e.d,l=e.Y,s=e.h,m=e.t,d=e.X}],execute:function(){e("default",Object.assign({name:"MenuItem"},{__name:"menuItem",props:{routerInfo:{default:function(){return null},type:Object}},setup:function(e){var t=n(),I=u(t).config,g=r((function(){return I.value.layout_side_item_height+"px"}));return function(t,n){var u=i("el-icon"),r=i("el-menu-item");return a(),o(r,{index:e.routerInfo.name,class:"dark:text-slate-300 overflow-hidden",style:d({height:g.value})},{title:f((function(){return[s(m(e.routerInfo.meta.title),1)]})),default:f((function(){return[e.routerInfo.meta.icon?(a(),o(u,{key:0},{default:f((function(){return[(a(),o(l(e.routerInfo.meta.icon)))]})),_:1})):c("",!0)]})),_:1},8,["index","style"])}}}))}}}));
