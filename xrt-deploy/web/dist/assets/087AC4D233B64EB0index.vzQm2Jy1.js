/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import e from"./087AC4D233B64EB0normalMode.COduuhDZ.js";import o from"./087AC4D233B64EB0headMode.JNM20hnA.js";import m from"./087AC4D233B64EB0combinationMode.Bv0q0o5q.js";import{I as i,J as d,c as s,o as n,i as a,d as t,v as r}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0index.DAKhbPae.js";import"./087AC4D233B64EB0menuItem.h4t5Q0OS.js";import"./087AC4D233B64EB0asyncSubmenu.BQ2WRSj6.js";const B={__name:"index",props:{mode:{type:String,default:"normal"}},setup(B){const l=i(),{config:p,device:c}=d(l);return(i,d)=>(n(),s("div",null,["normal"===r(p).side_mode||"mobile"===r(c)&&"head"==r(p).side_mode||"mobile"===r(c)&&"combination"==r(p).side_mode?(n(),a(e,{key:0})):t("",!0),"head"===r(p).side_mode&&"mobile"!==r(c)?(n(),a(o,{key:1})):t("",!0),"combination"===r(p).side_mode&&"mobile"!==r(c)?(n(),a(m,{key:2,mode:B.mode},null,8,["mode"])):t("",!0)]))}};export{B as default};
