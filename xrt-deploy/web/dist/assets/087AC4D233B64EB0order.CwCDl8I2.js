/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,a,I as t,r as l,Q as o,g as r,c as s,o as i,b as n,f as u,w as d,d as c,F as p,D as m,i as g,h as v,l as b,t as f,v as y,ay as w,n as h,az as k,aA as _,E as x,ab as C}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as N,f as V,c as I,u as S,d as D,p as P,a as z,b as B}from"./087AC4D233B64EB0order.DGIa0hT6.js";import{g as A}from"./087AC4D233B64EB0image.DZXThNBc.js";import{S as j}from"./087AC4D233B64EB0selectImage.DW9yQvJ8.js";import"./087AC4D233B64EB0QR-code.DGKmWLV2.js";import"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";import"./087AC4D233B64EB0logo.D8P6F9wK.js";const U={class:"order-container"},E={class:"gva-search-box"},T={class:"gva-table-box"},q={class:"gva-btn-list"},F={class:"order-info"},L={class:"order-info-row"},O=["onClick","title"],R={class:"posting-info"},K=["onClick","title"],M={key:0,class:"tracking-info"},Q=["onClick","title"],G={class:"logistics-info"},J=["title","data-long","data-very-long"],Y=["title"],H={class:"products-container"},W={class:"product-image-wrapper"},X={key:1,class:"no-image"},Z={class:"product-quantity"},$={class:"product-info"},ee={class:"product-info-row"},ae=["onClick","title"],te={class:"product-info-row"},le=["title"],oe={key:0,class:"product-row"},re={class:"product-info"},se={class:"product-info-row"},ie={class:"product-info-row"},ne={class:"time-info"},ue={class:"order-time"},de={class:"time-value"},ce={class:"ship-time"},pe={class:"time-value"},me={class:"operation-buttons"},ge={class:"gva-pagination"},ve={class:"flex justify-between items-center"},be={class:"text-lg"},fe=e(Object.assign({name:"Order"},{__name:"order",setup(e){const fe=a(!1),ye=a(!1),we=a(!1),he=t(),ke=a(),_e=a(!1),xe=a([]),Ce=a([]),Ne=a({orderImg:[],postingNumber:"",inProcessAt:new Date,orderNumber:"",tplProvider:"",status:"",distributionStatus:"",trackingNumber:"",shipmentDate:new Date}),Ve=l({}),Ie=l({}),Se=a(),De=a(),Pe=localStorage.getItem("orderListPage"),ze=localStorage.getItem("orderListPageSize"),Be=a(Pe?parseInt(Pe):1),Ae=a(0),je=a(ze?parseInt(ze):10),Ue=a([]);o(Be,(e=>{localStorage.setItem("orderListPage",e.toString())})),o(je,(e=>{localStorage.setItem("orderListPageSize",e.toString())}));const Ee=a({distributionStatus:"unprocessed"}),Te=()=>{Ee.value={distributionStatus:"unprocessed"},Oe()},qe=()=>{var e;null==(e=De.value)||e.validate((async e=>{e&&(Be.value=1,Oe())}))},Fe=e=>{je.value=e,Oe()},Le=e=>{Be.value=e,Oe()},Oe=async()=>{const e=await N({page:Be.value,pageSize:je.value,...Ee.value});0===e.code&&(Ue.value=e.data.list,Ae.value=e.data.total)};Oe();(async()=>{xe.value=await _("order_status"),Ce.value=await _("distribution_status")})();const Re=a([]),Ke=e=>{Re.value=e},Me=async()=>{C.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===Re.value.length)return void x({type:"warning",message:"请选择要删除的数据"});Re.value&&Re.value.map((a=>{e.push(a.ID)}));0===(await B({IDs:e})).code&&(x({type:"success",message:"删除成功"}),Ue.value.length===e.length&&Be.value>1&&Be.value--,Oe())}))},Qe=a(""),Ge=async e=>{0===(await D({ID:e.ID})).code&&(x({type:"success",message:"删除成功"}),1===Ue.value.length&&Be.value>1&&Be.value--,Oe())},Je=a(!1),Ye=()=>{Je.value=!1,Ne.value={orderImg:[],postingNumber:"",inProcessAt:new Date,orderNumber:"",tplProvider:"",status:"",distributionStatus:"",trackingNumber:"",shipmentDate:new Date}},He=async()=>{var e;fe.value=!0,null==(e=Se.value)||e.validate((async e=>{if(!e)return fe.value=!1;let a;switch(Qe.value){case"create":default:a=await I(Ne.value);break;case"update":a=await S(Ne.value)}fe.value=!1,0===a.code&&(x({type:"success",message:"创建/更改成功"}),Ye(),Oe())}))},We=a({}),Xe=a(!1),Ze=async e=>{const a=await V({ID:e.ID});0===a.code&&(We.value=a.data,Xe.value=!0)},$e=()=>{Xe.value=!1,We.value={}},ea=async()=>{try{ye.value=!0,x({type:"info",message:"正在从Ozon同步订单..."});const e=await P();0===e.code?(x({type:"success",message:"同步成功！共拉取 ".concat(e.data.count," 个订单")}),Oe()):x({type:"error",message:"同步失败: "+e.msg})}catch(e){x({type:"error",message:"同步订单失败: "+e.message})}finally{ye.value=!1}},aa=async()=>{try{we.value=!0,x({type:"info",message:"正在拉取今年一整年的订单..."});const e=await z();0===e.code?(x({type:"success",message:"拉取成功！共拉取今年 ".concat(e.data.count," 个订单")}),Oe()):x({type:"error",message:"拉取今年订单失败: "+e.msg})}catch(e){x({type:"error",message:"拉取今年订单失败: "+e.message})}finally{we.value=!1}},ta=e=>{if(!e)return"";try{const a=new Date(e);if(isNaN(a.getTime()))return"";const t=a.getFullYear().toString().slice(-2),l=(a.getMonth()+1).toString().padStart(2,"0"),o=a.getDate().toString().padStart(2,"0");return"".concat(t,"/").concat(l,"/").concat(o)}catch(a){return""}},la=e=>{try{if(e.jsonData){const a="string"==typeof e.jsonData?JSON.parse(e.jsonData):e.jsonData;if(a.products&&a.products.length>0){const t=new Map;let l=0;return a.products.forEach(((a,o)=>{const r="".concat(a.sku||"unknown","_").concat(a.name||"unknown");if(t.has(r)){const e=t.get(r);e.quantity=(e.quantity||1)+(a.quantity||1)}else{let o=null;e.orderImg&&e.orderImg.length>l&&(o=e.orderImg[l],l++),t.set(r,{name:a.name||"未知产品",sku:a.sku||"",offerId:a.offer_id||"",quantity:a.quantity||1,image:o,imageIndex:l-1})}})),Array.from(t.values()).slice(0,3)}}if(e.orderImg&&e.orderImg.length>0){return[...new Set(e.orderImg)].slice(0,3).map(((e,a)=>({name:"未知产品",sku:"",offerId:"",image:e,imageIndex:a})))}return[]}catch(a){return[]}},oa=e=>{if(!e)return"";const a=e.length;return a>80?"very-long-text":a>50?"long-text":""},ra=e=>{const a=e.trackingNumber,t=e.postingNumber;return!(!a||"-"===a||a===t)},sa=e=>e?e.length:0;return(e,a)=>{const t=r("el-input"),l=r("el-form-item"),o=r("el-option"),_=r("el-select"),N=r("el-button"),I=r("el-form"),S=r("el-table-column"),D=r("el-image"),P=r("Goods"),z=r("el-icon"),B=r("Document"),Pe=r("InfoFilled"),ze=r("el-table"),Oe=r("el-pagination"),ia=r("el-date-picker"),na=r("el-drawer"),ua=r("el-descriptions-item"),da=r("el-descriptions");return i(),s("div",U,[n("div",E,[u(I,{ref_key:"elSearchFormRef",ref:De,inline:!0,model:Ee.value,class:"demo-form-inline",rules:Ie,onKeyup:b(qe,["enter"])},{default:d((()=>[u(l,{label:"货件号",prop:"postingNumber"},{default:d((()=>[u(t,{modelValue:Ee.value.postingNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>Ee.value.postingNumber=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),u(l,{label:"订单号",prop:"orderNumber"},{default:d((()=>[u(t,{modelValue:Ee.value.orderNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>Ee.value.orderNumber=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),u(l,{label:"配货状态",prop:"distributionStatus"},{default:d((()=>[u(_,{modelValue:Ee.value.distributionStatus,"onUpdate:modelValue":a[2]||(a[2]=e=>Ee.value.distributionStatus=e),clearable:"",placeholder:"请选择",onClear:a[3]||(a[3]=()=>{Ee.value.distributionStatus=void 0})},{default:d((()=>[(i(!0),s(p,null,m(Ce.value,((e,a)=>(i(),g(o,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(l,{label:"国际单号",prop:"trackingNumber"},{default:d((()=>[u(t,{modelValue:Ee.value.trackingNumber,"onUpdate:modelValue":a[4]||(a[4]=e=>Ee.value.trackingNumber=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),_e.value?(i(),s(p,{key:0},[],64)):c("",!0),u(l,null,{default:d((()=>[u(N,{type:"primary",icon:"search",onClick:qe},{default:d((()=>a[19]||(a[19]=[v("查询")]))),_:1}),u(N,{icon:"refresh",onClick:Te},{default:d((()=>a[20]||(a[20]=[v("重置")]))),_:1}),_e.value?(i(),g(N,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[6]||(a[6]=e=>_e.value=!1)},{default:d((()=>a[22]||(a[22]=[v("收起")]))),_:1})):(i(),g(N,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[5]||(a[5]=e=>_e.value=!0)},{default:d((()=>a[21]||(a[21]=[v("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),n("div",T,[n("div",q,[u(N,{type:"success",icon:"download",loading:ye.value,onClick:ea},{default:d((()=>a[23]||(a[23]=[v("同步订单")]))),_:1},8,["loading"]),u(N,{type:"warning",icon:"calendar",loading:we.value,onClick:aa},{default:d((()=>a[24]||(a[24]=[v("拉取今年订单")]))),_:1},8,["loading"]),u(N,{type:"primary",icon:"plus",onClick:a[7]||(a[7]=e=>(Qe.value="create",void(Je.value=!0)))},{default:d((()=>a[25]||(a[25]=[v("新增")]))),_:1}),u(N,{icon:"delete",style:{"margin-left":"10px"},disabled:!Re.value.length,onClick:Me},{default:d((()=>a[26]||(a[26]=[v("删除")]))),_:1},8,["disabled"])]),u(ze,{ref_key:"multipleTable",ref:ke,style:{width:"100%"},"tooltip-effect":"dark",data:Ue.value,"row-key":"ID",onSelectionChange:Ke},{default:d((()=>[u(S,{type:"selection",width:"55"}),u(S,{align:"left",label:"订单信息",prop:"postingNumber","min-width":"180","show-overflow-tooltip":!1},{default:d((e=>[n("div",F,[n("div",L,[a[27]||(a[27]=n("span",{class:"info-badge order-badge"},"订单",-1)),n("span",{class:"order-number",onClick:a=>(async e=>{if(e)try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(e);else{const a=document.createElement("textarea");a.value=e,a.style.position="fixed",a.style.left="-999999px",a.style.top="-999999px",document.body.appendChild(a),a.focus(),a.select(),document.execCommand("copy"),document.body.removeChild(a)}x({type:"success",message:"订单号已复制: ".concat(e),duration:2e3})}catch(a){x({type:"error",message:"复制失败，请手动复制"})}else x({type:"warning",message:"订单号为空，无法复制"})})(e.row.orderNumber),title:"点击复制订单号: "+e.row.orderNumber},f(e.row.orderNumber),9,O)]),n("div",R,[a[28]||(a[28]=n("span",{class:"info-badge posting-badge"},"货件",-1)),n("span",{class:"posting-number",onClick:a=>(async e=>{if(e)try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(e);else{const a=document.createElement("textarea");a.value=e,a.style.position="fixed",a.style.left="-999999px",a.style.top="-999999px",document.body.appendChild(a),a.focus(),a.select(),document.execCommand("copy"),document.body.removeChild(a)}x({type:"success",message:"货件号已复制: ".concat(e),duration:2e3})}catch(a){x({type:"error",message:"复制失败，请手动复制"})}else x({type:"warning",message:"货件号为空，无法复制"})})(e.row.postingNumber),title:"点击复制货件号: "+e.row.postingNumber},f(e.row.postingNumber),9,K)]),ra(e.row)?(i(),s("div",M,[a[29]||(a[29]=n("span",{class:"info-badge tracking-badge"},"国际",-1)),n("span",{class:"tracking-number",onClick:a=>(async e=>{if(e&&"-"!==e)try{if(navigator.clipboard&&window.isSecureContext)await navigator.clipboard.writeText(e);else{const a=document.createElement("textarea");a.value=e,a.style.position="fixed",a.style.left="-999999px",a.style.top="-999999px",document.body.appendChild(a),a.focus(),a.select(),document.execCommand("copy"),document.body.removeChild(a)}x({type:"success",message:"国际单号已复制: ".concat(e),duration:2e3})}catch(a){x({type:"error",message:"复制失败，请手动复制"})}else x({type:"warning",message:"国际单号为空，无法复制"})})(e.row.trackingNumber),title:"点击复制国际单号: "+e.row.trackingNumber},f(e.row.trackingNumber),9,Q)])):c("",!0),n("div",G,[a[30]||(a[30]=n("span",{class:"info-badge logistics-badge"},"物流",-1)),n("span",{class:"logistics-text",title:e.row.tplProvider||"未知物流","data-long":sa(e.row.tplProvider)>15,"data-very-long":sa(e.row.tplProvider)>25},f(e.row.tplProvider||"未知物流"),9,J)])])])),_:1}),u(S,{align:"left",label:"店铺",prop:"shopName","min-width":"180"},{default:d((e=>[n("span",{class:"shop-name",title:e.row.shopName||"未知店铺"},f(e.row.shopName||"未知店铺"),9,Y)])),_:1}),u(S,{label:"产品信息",prop:"orderImg","min-width":"180"},{default:d((e=>[n("div",H,[(i(!0),s(p,null,m(la(e.row),((t,l)=>(i(),s("div",{key:l,class:"product-row"},[n("div",W,[t.image?(i(),g(D,{key:0,"preview-teleported":"",style:{width:"50px",height:"50px",cursor:"pointer"},src:y(A)(t.image),"preview-src-list":y(w)(e.row.orderImg),"initial-index":t.imageIndex,fit:"cover",onClick:a=>(e.row.orderImg,void t.imageIndex)},null,8,["src","preview-src-list","initial-index","onClick"])):(i(),s("div",X,a[31]||(a[31]=[n("span",null,"无图",-1)]))),n("div",Z," x"+f(t.quantity||1),1)]),n("div",$,[n("div",ee,[u(z,{class:"product-icon sku-icon",size:12},{default:d((()=>[u(P)])),_:1}),n("span",{class:"product-text product-sku",onClick:e=>(e=>{if(e&&"-"!==e)try{const a="https://www.ozon.ru/product/".concat(e,"/");window.open(a,"_blank"),x({type:"success",message:"正在打开产品页面: ".concat(e),duration:2e3})}catch(a){x({type:"error",message:"打开产品页面失败"})}else x({type:"warning",message:"SKU为空，无法跳转"})})(t.sku),title:"点击查看产品: "+(t.sku||"-")},f(t.sku||"-"),9,ae)]),n("div",te,[u(z,{class:"product-icon name-icon",size:12},{default:d((()=>[u(B)])),_:1}),n("span",{class:h(["product-text product-name",oa(t.name)]),title:t.name},f(t.name),11,le)])])])))),128)),0===la(e.row).length?(i(),s("div",oe,[a[34]||(a[34]=n("div",{class:"product-image-wrapper"},[n("div",{class:"no-image"},[n("span",null,"无图")]),n("div",{class:"product-quantity"}," x- ")],-1)),n("div",re,[n("div",se,[u(z,{class:"product-icon sku-icon",size:12},{default:d((()=>[u(P)])),_:1}),a[32]||(a[32]=n("span",{class:"product-text"},"-",-1))]),n("div",ie,[u(z,{class:"product-icon name-icon",size:12},{default:d((()=>[u(B)])),_:1}),a[33]||(a[33]=n("span",{class:"product-text product-name"},"未知产品",-1))])])])):c("",!0)])])),_:1}),u(S,{align:"left",label:"配货状态",prop:"distributionStatus","min-width":"180"},{default:d((e=>{return[n("span",{class:h(["status-text",(a=e.row.distributionStatus,["unprocessed","cancelled","error","failed","rejected"].includes(a)?"status-error":"status-normal")])},f(y(k)(e.row.distributionStatus,Ce.value)),3)];var a})),_:1}),u(S,{align:"left",label:"订单状态",prop:"status","min-width":"180"},{default:d((e=>{return[n("span",{class:h(["status-text",(a=e.row.status,["cancelled","canceled","error","failed","rejected","not_accepted","client_arbitration","arbitration"].includes(a)?"status-error":"status-normal")])},f(y(k)(e.row.status,xe.value)),3)];var a})),_:1}),u(S,{align:"left",label:"时间",prop:"inProcessAt","min-width":"180"},{default:d((e=>[n("div",ne,[n("div",ue,[a[35]||(a[35]=n("span",{class:"time-badge order-time-badge"},"下单",-1)),n("span",de,f(ta(e.row.inProcessAt)||"-"),1)]),n("div",ce,[a[36]||(a[36]=n("span",{class:"time-badge ship-time-badge"},"发货",-1)),n("span",pe,f(ta(e.row.shipmentDate)||"-"),1)])])])),_:1}),u(S,{align:"left",label:"操作",fixed:"right",width:"80"},{default:d((e=>[n("div",me,[u(N,{type:"primary",link:"",class:"table-button",onClick:a=>Ze(e.row)},{default:d((()=>[u(z,{style:{"margin-right":"3px"}},{default:d((()=>[u(Pe)])),_:1}),a[37]||(a[37]=v("查看 "))])),_:2},1032,["onClick"]),u(N,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await V({ID:e.ID});Qe.value="update",0===a.code&&(Ne.value=a.data,Je.value=!0)})(e.row)},{default:d((()=>a[38]||(a[38]=[v(" 编辑 ")]))),_:2},1032,["onClick"]),u(N,{type:"primary",link:"",icon:"delete",onClick:a=>{return t=e.row,void C.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{Ge(t)}));var t}},{default:d((()=>a[39]||(a[39]=[v(" 删除 ")]))),_:2},1032,["onClick"])])])),_:1})])),_:1},8,["data"]),n("div",ge,[u(Oe,{layout:"total, sizes, prev, pager, next, jumper","current-page":Be.value,"page-size":je.value,"page-sizes":[10,30,50,100],total:Ae.value,onCurrentChange:Le,onSizeChange:Fe},null,8,["current-page","page-size","total"])])]),u(na,{"destroy-on-close":"",size:y(he).drawerSize,modelValue:Je.value,"onUpdate:modelValue":a[17]||(a[17]=e=>Je.value=e),"show-close":!1,"before-close":Ye},{header:d((()=>[n("div",ve,[n("span",be,f("create"===Qe.value?"新增":"编辑"),1),n("div",null,[u(N,{loading:fe.value,type:"primary",onClick:He},{default:d((()=>a[40]||(a[40]=[v("确 定")]))),_:1},8,["loading"]),u(N,{onClick:Ye},{default:d((()=>a[41]||(a[41]=[v("取 消")]))),_:1})])])])),default:d((()=>[u(I,{model:Ne.value,"label-position":"top",ref_key:"elFormRef",ref:Se,rules:Ve,"label-width":"80px"},{default:d((()=>[u(l,{label:"产品图:",prop:"orderImg"},{default:d((()=>[u(j,{multiple:"",modelValue:Ne.value.orderImg,"onUpdate:modelValue":a[8]||(a[8]=e=>Ne.value.orderImg=e),"file-type":"image"},null,8,["modelValue"])])),_:1}),u(l,{label:"货件号:",prop:"postingNumber"},{default:d((()=>[u(t,{modelValue:Ne.value.postingNumber,"onUpdate:modelValue":a[9]||(a[9]=e=>Ne.value.postingNumber=e),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])])),_:1}),u(l,{label:"下单时间:",prop:"inProcessAt"},{default:d((()=>[u(ia,{modelValue:Ne.value.inProcessAt,"onUpdate:modelValue":a[10]||(a[10]=e=>Ne.value.inProcessAt=e),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1}),u(l,{label:"订单号:",prop:"orderNumber"},{default:d((()=>[u(t,{modelValue:Ne.value.orderNumber,"onUpdate:modelValue":a[11]||(a[11]=e=>Ne.value.orderNumber=e),clearable:!0,placeholder:"请输入订单号"},null,8,["modelValue"])])),_:1}),u(l,{label:"物流:",prop:"tplProvider"},{default:d((()=>[u(t,{modelValue:Ne.value.tplProvider,"onUpdate:modelValue":a[12]||(a[12]=e=>Ne.value.tplProvider=e),clearable:!0,placeholder:"请输入物流"},null,8,["modelValue"])])),_:1}),u(l,{label:"订单状态:",prop:"status"},{default:d((()=>[u(_,{modelValue:Ne.value.status,"onUpdate:modelValue":a[13]||(a[13]=e=>Ne.value.status=e),placeholder:"请选择订单状态",style:{width:"100%"},clearable:!0},{default:d((()=>[(i(!0),s(p,null,m(xe.value,((e,a)=>(i(),g(o,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(l,{label:"配货状态:",prop:"distributionStatus"},{default:d((()=>[u(_,{modelValue:Ne.value.distributionStatus,"onUpdate:modelValue":a[14]||(a[14]=e=>Ne.value.distributionStatus=e),placeholder:"请选择配货状态",style:{width:"100%"},clearable:!0},{default:d((()=>[(i(!0),s(p,null,m(Ce.value,((e,a)=>(i(),g(o,{key:a,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),u(l,{label:"国际单号:",prop:"trackingNumber"},{default:d((()=>[u(t,{modelValue:Ne.value.trackingNumber,"onUpdate:modelValue":a[15]||(a[15]=e=>Ne.value.trackingNumber=e),clearable:!0,placeholder:"请输入国际单号"},null,8,["modelValue"])])),_:1}),u(l,{label:"发货日期:",prop:"shipmentDate"},{default:d((()=>[u(ia,{modelValue:Ne.value.shipmentDate,"onUpdate:modelValue":a[16]||(a[16]=e=>Ne.value.shipmentDate=e),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["size","modelValue"]),u(na,{"destroy-on-close":"",size:y(he).drawerSize,modelValue:Xe.value,"onUpdate:modelValue":a[18]||(a[18]=e=>Xe.value=e),"show-close":!0,"before-close":$e,title:"查看"},{default:d((()=>[u(da,{column:1,border:""},{default:d((()=>[u(ua,{label:"产品图"},{default:d((()=>[(i(!0),s(p,null,m(We.value.orderImg,((e,a)=>(i(),g(D,{style:{width:"50px",height:"50px","margin-right":"10px"},"preview-src-list":y(w)(We.value.orderImg),"initial-index":a,key:a,src:y(A)(e),fit:"cover"},null,8,["preview-src-list","initial-index","src"])))),128))])),_:1}),u(ua,{label:"货件号"},{default:d((()=>[v(f(We.value.postingNumber),1)])),_:1}),u(ua,{label:"下单时间"},{default:d((()=>[v(f(We.value.inProcessAt),1)])),_:1}),u(ua,{label:"订单号"},{default:d((()=>[v(f(We.value.orderNumber),1)])),_:1}),u(ua,{label:"物流"},{default:d((()=>[v(f(We.value.tplProvider),1)])),_:1}),u(ua,{label:"订单状态"},{default:d((()=>[v(f(y(k)(We.value.status,xe.value)),1)])),_:1}),u(ua,{label:"配货状态"},{default:d((()=>[v(f(y(k)(We.value.distributionStatus,Ce.value)),1)])),_:1}),u(ua,{label:"国际单号"},{default:d((()=>[v(f(We.value.trackingNumber),1)])),_:1}),u(ua,{label:"发货日期"},{default:d((()=>[v(f(We.value.shipmentDate),1)])),_:1})])),_:1})])),_:1},8,["size","modelValue"])])}}}),[["__scopeId","data-v-7cba740f"]]);export{fe as default};
