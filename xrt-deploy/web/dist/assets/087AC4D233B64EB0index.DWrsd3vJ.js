/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as e,aj as a,u as t,a as s,k as l,K as n,Q as u,at as r,ao as o,g as i,c as v,o as m,f as c,a7 as d,w as p,F as y,D as g,i as f,b as h,n as b,h as S,t as I,v as q,al as x,au as w,a8 as k,X as O,T as N}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const C={class:"gva-tabs"},E=["tab"],J=e(Object.assign({name:"HistoryComponent"},{__name:"index",setup(e){const J=a(),j=t(),A=e=>e.name+JSON.stringify(e.query)+JSON.stringify(e.params),P=s([]),V=s(""),_=s(!1),T=l(),L=s(0),R=s(0),B=s(!1),D=s(!1),K=s(""),Q=n((()=>T.userInfo.authority.defaultRouter)),U=()=>{P.value=[{name:Q.value,meta:{title:"首页"},query:{},params:{}}],j.push({name:Q.value}),_.value=!1,sessionStorage.setItem("historys",JSON.stringify(P.value))},X=()=>{let e;const a=P.value.findIndex((a=>(A(a)===K.value&&(e=a),A(a)===K.value))),t=P.value.findIndex((e=>A(e)===V.value));P.value.splice(0,a),a>t&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},F=()=>{let e;const a=P.value.findIndex((a=>(A(a)===K.value&&(e=a),A(a)===K.value))),t=P.value.findIndex((e=>A(e)===V.value));P.value.splice(a+1,P.value.length),a<t&&j.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},H=()=>{let e;P.value=P.value.filter((a=>(A(a)===K.value&&(e=a),A(a)===K.value))),j.push(e),sessionStorage.setItem("historys",JSON.stringify(P.value))},M=e=>{if(!P.value.some((a=>((e,a)=>{if(e.name!==a.name)return!1;if(Object.keys(e.query).length!==Object.keys(a.query).length||Object.keys(e.params).length!==Object.keys(a.params).length)return!1;for(const t in e.query)if(e.query[t]!==a.query[t])return!1;for(const t in e.params)if(e.params[t]!==a.params[t])return!1;return!0})(a,e)))){const a={};a.name=e.name,a.meta={...e.meta},delete a.meta.matched,a.query=e.query,a.params=e.params,P.value.push(a)}window.sessionStorage.setItem("activeValue",A(e))},Y=s({}),$=e=>{var a;const t=null==(a=null==e?void 0:e.props)?void 0:a.name;if(!t)return;const s=Y.value[t];j.push({name:s.name,query:s.query,params:s.params})},z=e=>{const a=P.value.findIndex((a=>A(a)===e));A(J)===e&&(1===P.value.length?j.push({name:Q.value}):a<P.value.length-1?j.push({name:P.value[a+1].name,query:P.value[a+1].query,params:P.value[a+1].params}):j.push({name:P.value[a-1].name,query:P.value[a-1].query,params:P.value[a-1].params})),P.value.splice(a,1)};u((()=>_.value),(()=>{_.value?document.body.addEventListener("click",(()=>{_.value=!1})):document.body.removeEventListener("click",(()=>{_.value=!1}))})),u((()=>J),(e=>{"Login"!==e.name&&"Reload"!==e.name&&(P.value=P.value.filter((e=>!e.meta.closeTab)),M(e),sessionStorage.setItem("historys",JSON.stringify(P.value)),V.value=window.sessionStorage.getItem("activeValue"))}),{deep:!0}),u((()=>P.value),(()=>{sessionStorage.setItem("historys",JSON.stringify(P.value)),Y.value={},P.value.forEach((e=>{Y.value[A(e)]=e})),o.emit("setKeepAlive",P.value)}),{deep:!0});(()=>{o.on("closeThisPage",(()=>{z(A(J))})),o.on("closeAllPage",(()=>{U()})),o.on("mobile",(e=>{D.value=e})),o.on("collapse",(e=>{B.value=e})),o.on("setQuery",(e=>{const a=P.value.findIndex((e=>A(e)===V.value));P.value[a].query=e,V.value=A(P.value[a]);const t=window.location.href.split("?")[0],s=new URLSearchParams(e).toString();window.history.replaceState({},"","".concat(t,"?").concat(s)),sessionStorage.setItem("historys",JSON.stringify(P.value))})),o.on("switchTab",(async e=>{const a=P.value.findIndex((a=>a.name===e.name));if(!(a<0)){for(const a in e.query)e.query[a]=String(e.query[a]);for(const a in e.params)e.params[a]=String(e.params[a]);P.value[a].query=e.query||{},P.value[a].params=e.params||{},await N(),j.push(P.value[a])}}));const e=[{name:Q.value,meta:{title:"首页"},query:{},params:{}}];M(J),P.value=JSON.parse(sessionStorage.getItem("historys"))||e,window.sessionStorage.getItem("activeValue")?V.value=window.sessionStorage.getItem("activeValue"):V.value=A(J),"true"===window.sessionStorage.getItem("needCloseAll")&&(U(),window.sessionStorage.removeItem("needCloseAll"))})(),r((()=>{o.off("collapse"),o.off("mobile")}));return(e,a)=>{const t=i("el-tab-pane"),s=i("el-tabs");return m(),v("div",C,[c(s,{modelValue:V.value,"onUpdate:modelValue":a[0]||(a[0]=e=>V.value=e),closable:!(1===P.value.length&&e.$route.name===Q.value),type:"card",class:"bg-white text-slate-700 dark:text-slate-500 dark:bg-slate-900",onContextmenu:a[1]||(a[1]=w((e=>(e=>{if(1===P.value.length&&J.name===Q.value)return!1;let a="";a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a&&(_.value=!0,L.value=e.clientX,R.value=e.clientY+10,K.value=a.substring(4))})(e)),["prevent"])),onTabClick:$,onTabRemove:z,onMouseup:a[2]||(a[2]=w((e=>(e=>{if(1===P.value.length&&J.name===Q.value)return!1;let a="";a="SPAN"===e.srcElement.nodeName?e.srcElement.offsetParent.id:e.srcElement.id,a&&z(a.substring(4))})(e)),["middle","prevent"]))},{default:p((()=>[(m(!0),v(y,null,g(P.value,(e=>(m(),f(t,{key:A(e),label:e.meta.title,name:A(e),tab:e,class:"border-none"},{label:p((()=>[h("span",{tab:e,class:b(V.value===A(e)?"text-active":"text-gray-600 dark:text-slate-400 ")},[h("i",{class:b(V.value===A(e)?"text-active":"text-gray-600 dark:text-slate-400")},null,2),S(" "+I(q(x)(e.meta.title,e)),1)],10,E)])),_:2},1032,["label","name","tab"])))),128))])),_:1},8,["modelValue","closable"]),d(h("ul",{style:O({left:L.value+"px",top:R.value+"px"}),class:"contextmenu"},[h("li",{onClick:U},"关闭所有"),h("li",{onClick:X},"关闭左侧"),h("li",{onClick:F},"关闭右侧"),h("li",{onClick:H},"关闭其他")],4),[[k,_.value]])])}}}),[["__scopeId","data-v-ec146f1e"]]);export{J as default};
