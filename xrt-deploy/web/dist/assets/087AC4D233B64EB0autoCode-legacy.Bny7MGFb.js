/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){var o=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,r)}return o}function o(t){for(var o=1;o<arguments.length;o++){var n=null!=arguments[o]?arguments[o]:{};o%2?e(Object(n),!0).forEach((function(e){r(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):e(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function r(e,o,r){return(o=function(e){var o=function(e,o){if("object"!=t(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,o||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(e)}(e,"string");return"symbol"==t(o)?o:o+""}(o))in e?Object.defineProperty(e,o,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[o]=r,e}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,e){"use strict";var r;return{setters:[function(t){r=t.s}],execute:function(){t("p",(function(t){return r({url:"/autoCode/preview",method:"post",data:t})})),t("f",(function(t){return r({url:"/autoCode/createTemp",method:"post",data:t})})),t("g",(function(t){return r({url:"/autoCode/getDB",method:"get",params:t})})),t("c",(function(t){return r({url:"/autoCode/getTables",method:"get",params:t})})),t("a",(function(t){return r({url:"/autoCode/getColumn",method:"get",params:t})})),t("i",(function(t){return r({url:"/autoCode/getSysHistory",method:"post",data:t})})),t("r",(function(t){return r({url:"/autoCode/rollback",method:"post",data:t})})),t("h",(function(t){return r({url:"/autoCode/getMeta",method:"post",data:t})})),t("j",(function(t){return r({url:"/autoCode/delSysHistory",method:"post",data:t})})),t("o",(function(t){return r({url:"/autoCode/createPackage",method:"post",data:t})})),t("d",(function(){return r({url:"/autoCode/getPackage",method:"post"})})),t("n",(function(t){return r({url:"/autoCode/delPackage",method:"post",data:t})})),t("m",(function(){return r({url:"/autoCode/getTemplates",method:"get"})})),t("t",(function(t){return r({url:"/autoCode/pubPlug",method:"post",params:t})})),t("l",(function(t){return r({url:"/autoCode/llmAuto",method:"post",data:o(o({},t),{},{mode:"ai"}),timeout:6e5,loadingOption:{lock:!0,fullscreen:!0,text:"小淼正在思考，请稍候..."}})})),t("b",(function(t){return r({url:"/autoCode/llmAuto",method:"post",data:o(o({},t),{},{mode:"butler"}),timeout:6e5})})),t("e",(function(t){return r({url:"/autoCode/llmAuto",method:"post",data:o(o({},t),{},{mode:"eye"}),timeout:6e5})})),t("k",(function(t){return r({url:"/autoCode/addFunc",method:"post",data:t})})),t("q",(function(t){return r({url:"/autoCode/initMenu",method:"post",data:t})})),t("s",(function(t){return r({url:"/autoCode/initAPI",method:"post",data:t})}))}}}))}();
