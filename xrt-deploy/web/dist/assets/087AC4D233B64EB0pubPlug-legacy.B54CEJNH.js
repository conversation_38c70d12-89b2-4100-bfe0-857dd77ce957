/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(r){s=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),u=new I(n||[]);return i(a,"_invoke",{value:P(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=p;var v="suspendedStart",d="suspendedYield",y="executing",m="completed",g={};function w(){}function x(){}function b(){}var E={};s(E,c,(function(){return this}));var L=Object.getPrototypeOf,_=L&&L(L(T([])));_&&_!==o&&a.call(_,c)&&(E=_);var j=b.prototype=w.prototype=Object.create(E);function A(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function O(e,r){function n(o,i,u,c){var l=h(e[o],e,i);if("throw"!==l.type){var f=l.arg,s=f.value;return s&&"object"==t(s)&&a.call(s,"__await")?r.resolve(s.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(s).then((function(t){f.value=t,u(f)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(t,e){function a(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(a,a):a()}})}function P(t,e,n){var o=v;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:r,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var c=k(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=h(t,e,n);if("normal"===l.type){if(o=n.done?m:d,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function k(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,k(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=h(o,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,g;var i=a.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function T(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(a.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return i.next=i}}throw new TypeError(t(e)+" is not iterable")}return x.prototype=b,i(j,"constructor",{value:b,configurable:!0}),i(b,"constructor",{value:x,configurable:!0}),x.displayName=s(b,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,s(t,f,"GeneratorFunction")),t.prototype=Object.create(j),t},n.awrap=function(t){return{__await:t}},A(O.prototype),s(O.prototype,l,(function(){return this})),n.AsyncIterator=O,n.async=function(t,e,r,o,a){void 0===a&&(a=Promise);var i=new O(p(t,e,r,o),a);return n.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},A(j),s(j,f,"Generator"),s(j,c,(function(){return this})),s(j,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=T,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(B),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return u.type="throw",u.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),B(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;B(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:T(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var i=t.apply(e,n);function u(t){r(i,o,a,u,c,"next",t)}function c(t){r(i,o,a,u,c,"throw",t)}u(void 0)}))}}function o(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return a(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}System.register(["./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js","./087AC4D233B64EB0api-legacy.E3o43UgG.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,r){"use strict";var a,i,u,c,l,f,s,p,h,v,d,y,m,g,w,x,b;return{setters:[function(t){a=t._},function(t){i=t.q,u=t.s,c=t.t},function(t){l=t.j},function(t){f=t.a,s=t.g,p=t.c,h=t.o,v=t.b,d=t.f,y=t.w,m=t.h,g=t.t,w=t.aX,x=t.E,b=t.ab}],execute:function(){var r=document.createElement("style");r.textContent=".plugin-transfer .el-transfer-panel{width:400px!important}\n/*$vite$:1*/",document.head.appendChild(r);var E={class:"gva-form-box"},L={class:"p-4 bg-white dark:bg-slate-900"},_={class:"flex items-center gap-3"},j={class:"flex justify-end mt-2"},A={class:"flex justify-end mt-2"},O={class:"flex justify-end"};t("default",{__name:"pubPlug",setup:function(t){var r=f(""),P=f([]),k=f([]),S=f([]),B=f([]),I=f(""),T=function(t){var e=[];return t.forEach((function(t){t.children?e.push.apply(e,o(T(t.children))):e.push(t)})),e},C=function(){var t=n(e().mark((function t(){var r,n;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,w();case 2:return 0===(r=t.sent).code&&(k.value=T(r.data)),t.next=6,l();case 6:0===(n=t.sent).code&&(B.value=n.data.apis);case 8:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),V=function(t,e){return e.meta.title.indexOf(t)>-1||e.component.indexOf(t)>-1},N=function(t,e){return e.description.indexOf(t)>-1||e.path.indexOf(t)>-1};C();var G=function(){var t=n(e().mark((function t(){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:b.confirm("请检查server下的/plugin/".concat(r.value,"/plugin.go是否已放开需要的 initialize.Api(ctx) 和 initialize.Menu(ctx)?"),"打包",{confirmButtonText:"打包",cancelButtonText:"取消",type:"warning"}).then(n(e().mark((function t(){var n;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,c({plugName:r.value});case 2:0===(n=t.sent).code&&x.success(n.msg);case 4:case"end":return t.stop()}}),t)})))).catch((function(){x({type:"info",message:"关闭打包"})}));case 1:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),D=function(){I.value?0!==P.value.length?""!==r.value?b.confirm("点击后将会覆盖server下的/plugin/".concat(r.value,"/initialize/menu. 是否继续?"),"生成初始菜单",{confirmButtonText:"生成",cancelButtonText:"取消",type:"warning"}).then((function(){var t={plugName:r.value,parentMenu:I.value,menus:P.value};i(t)})).catch((function(){x({type:"info",message:"关闭生成菜单"})})):x.error("请填写插件名"):x.error("请至少选择一个菜单"):x.error("请填写菜单组名")},F=function(){0!==S.value.length?""!==r.value?b.confirm("点击后将会覆盖server下的/plugin/".concat(r.value,"/initialize/api. 是否继续?"),"生成初始API",{confirmButtonText:"生成",cancelButtonText:"取消",type:"warning"}).then((function(){var t={plugName:r.value,apis:S.value};u(t)})).catch((function(){x({type:"info",message:"关闭生成API"})})):x.error("请填写插件名"):x.error("请至少选择一个API")};return function(t,e){var n=s("el-input"),o=s("el-transfer"),i=s("el-button"),u=s("el-card");return h(),p("div",E,[v("div",L,[d(a,{title:"目前只支持标准插件（通过插件模板生成的标准目录插件），非标准插件请自行打包"}),v("div",_,[d(n,{modelValue:r.value,"onUpdate:modelValue":e[0]||(e[0]=function(t){return r.value=t}),placeholder:"插件模板处填写的【插件名】"},null,8,["modelValue"])]),d(u,{class:"mt-2 text-center"},{default:y((function(){return[d(a,{title:"穿梭框请只选择子级菜单即可"}),d(n,{modelValue:I.value,"onUpdate:modelValue":e[1]||(e[1]=function(t){return I.value=t}),placeholder:"请输入菜单组名，例：公告管理",class:"mb-2"},null,8,["modelValue"]),d(o,{modelValue:P.value,"onUpdate:modelValue":e[2]||(e[2]=function(t){return P.value=t}),props:{key:"ID"},class:"plugin-transfer",data:k.value,filterable:"","filter-method":V,"filter-placeholder":"请输入菜单名称/路径",titles:["可选菜单","使用菜单"],"button-texts":["移除","选中"]},{default:y((function(t){var e=t.option;return[m(g(e.meta.title)+" "+g(e.component),1)]})),_:1},8,["modelValue","data"]),v("div",j,[d(i,{type:"primary",onClick:D},{default:y((function(){return e[4]||(e[4]=[m(" 定义安装菜单 ")])})),_:1})])]})),_:1}),d(u,{class:"mt-2 text-center"},{default:y((function(){return[d(o,{modelValue:S.value,"onUpdate:modelValue":e[3]||(e[3]=function(t){return S.value=t}),props:{key:"ID"},class:"plugin-transfer",data:B.value,filterable:"","filter-method":N,"filter-placeholder":"请输入API描述/PATH",titles:["可选API","使用API"],"button-texts":["移除","选中"]},{default:y((function(t){var e=t.option;return[m(g(e.description)+" "+g(e.path),1)]})),_:1},8,["modelValue","data"]),v("div",A,[d(i,{type:"primary",onClick:F},{default:y((function(){return e[5]||(e[5]=[m(" 定义安装API ")])})),_:1})])]})),_:1})]),v("div",O,[d(i,{type:"primary",onClick:G},{default:y((function(){return e[6]||(e[6]=[m(" 打包插件 ")])})),_:1})])])}}})}}}))}();
