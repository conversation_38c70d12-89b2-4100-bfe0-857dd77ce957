/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,t){"use strict";var n,u,r,a,o,i,l,s,c,f,m,d,v,y,p,b,g,_;return{setters:[function(e){n=e.ai,u=e.I,r=e.J,a=e.O,o=e.K,i=e.g,l=e.i,s=e.o,c=e.w,f=e.H,m=e.c,d=e.v,v=e.X,y=e.d,p=e.b,b=e.Y,g=e.t,_=e.F}],execute:function(){var t=document.createElement("style");t.textContent=".gva-sub-menu .el-sub-menu__title{height:var(--e7e064e6)!important}\n/*$vite$:1*/",document.head.appendChild(t),e("default",Object.assign({name:"AsyncSubmenu"},{__name:"asyncSubmenu",props:{routerInfo:{default:function(){return null},type:Object}},setup:function(e){n((function(e){return{e7e064e6:x.value}}));var t=u(),I=r(t).config,h=a("isCollapse",{default:!1}),x=o((function(){return I.value.layout_side_item_height+"px"}));return function(t,n){var u=i("el-icon"),r=i("el-sub-menu");return s(),l(r,{ref:"subMenu",index:e.routerInfo.name,class:"gva-sub-menu dark:text-slate-300 relative"},{title:c((function(){return[d(h)?(s(),m(_,{key:1},[e.routerInfo.meta.icon?(s(),l(u,{key:0},{default:c((function(){return[(s(),l(b(e.routerInfo.meta.icon)))]})),_:1})):y("",!0),p("span",null,g(e.routerInfo.meta.title),1)],64)):(s(),m("div",{key:0,class:"flex items-center",style:v({height:x.value})},[e.routerInfo.meta.icon?(s(),l(u,{key:0},{default:c((function(){return[(s(),l(b(e.routerInfo.meta.icon)))]})),_:1})):y("",!0),p("span",null,g(e.routerInfo.meta.title),1)],4))]})),default:c((function(){return[f(t.$slots,"default")]})),_:3},8,["index"])}}}))}}}));
