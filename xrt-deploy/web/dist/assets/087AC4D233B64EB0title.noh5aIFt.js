/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{_ as t,c as e,o as s,b as a,t as i}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const l={class:"title relative my-2"},n={class:"flex-shrink-0 text-center text-xl text-gray-600"},r=t(Object.assign({name:"layoutSettingTitle"},{__name:"title",props:{title:String},setup:t=>(r,d)=>(s(),e("div",l,[a("div",n,i(t.title),1)]))}),[["__scopeId","data-v-b670d9db"]]);export{r as default};
