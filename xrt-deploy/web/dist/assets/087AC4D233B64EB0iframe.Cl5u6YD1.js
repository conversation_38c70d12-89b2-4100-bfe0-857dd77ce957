/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import{u as a}from"./087AC4D233B64EB0responsive.HqR9hpRK.js";import{I as s,J as e,r,R as t,u as o,aj as l,p as n,ao as c,k as i,a as d,T as u,c as m,o as g,d as b,v}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const w={class:"bg-gray-50 text-slate-700 dark:text-slate-500 dark:bg-slate-800 w-screen h-screen"},p=["src"],y=Object.assign({name:"GvaLayoutIframe"},{__name:"iframe",setup(y){const f=s(),{isDark:k}=e(f);a();const h=r({color:"rgba(0, 0, 0, .15)"});t((()=>{h.color=k.value?"rgba(255,255,255, .15)":"rgba(0, 0, 0, .15)"}));const j=o(),x=l(),B=x.query.url||"https://www.gin-vue-admin.com";n((()=>{c.on("reload",T),I.loadingInstance&&I.loadingInstance.close()}));const I=i(),A=d(!0);let D=null;const T=async()=>{D&&window.clearTimeout(D),D=window.setTimeout((async()=>{if(x.meta.keepAlive)A.value=!1,await u(),A.value=!0;else{const a=x.meta.title;j.push({name:"Reload",params:{title:a}})}}),400)};return(a,s)=>(g(),m("div",w,[A.value?(g(),m("iframe",{key:0,id:"gva-base-load-dom",class:"gva-body-h bg-gray-50 dark:bg-slate-800 w-full border-t border-gray-200 dark:border-slate-700",src:v(B)},null,8,p)):b("",!0)]))}});export{y as default};
