/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,u=Object.defineProperty||function(e,t,r){e[t]=r.value},l="function"==typeof Symbol?Symbol:{},i=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",s=l.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(r){f=function(e,t,r){return e[t]=r}}function p(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),l=new D(n||[]);return u(a,"_invoke",{value:N(e,r,l)}),a}function d(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var h="suspendedStart",v="suspendedYield",m="executing",y="completed",g={};function b(){}function w(){}function x(){}var _={};f(_,i,(function(){return this}));var E=Object.getPrototypeOf,k=E&&E(E(A([])));k&&k!==o&&a.call(k,i)&&(_=k);var L=x.prototype=b.prototype=Object.create(_);function V(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function j(t,r){function n(o,u,l,i){var c=d(t[o],t,u);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==e(f)&&a.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,l,i)}),(function(e){n("throw",e,l,i)})):r.resolve(f).then((function(e){s.value=e,l(s)}),(function(e){return n("throw",e,l,i)}))}i(c.arg)}var o;u(this,"_invoke",{value:function(e,t){function a(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(a,a):a()}})}function N(e,t,n){var o=h;return function(a,u){if(o===m)throw Error("Generator is already running");if(o===y){if("throw"===a)throw u;return{value:r,done:!0}}for(n.method=a,n.arg=u;;){var l=n.delegate;if(l){var i=P(l,n);if(i){if(i===g)continue;return i}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===h)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var c=d(e,t,n);if("normal"===c.type){if(o=n.done?y:v,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=y,n.method="throw",n.arg=c.arg)}}}function P(e,t){var n=t.method,o=e.iterator[n];if(o===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,P(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=d(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,g;var u=a.arg;return u?u.done?(t[e.resultName]=u.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,g):u:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function D(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function A(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,u=function e(){for(;++o<t.length;)if(a.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=r,e.done=!0,e};return u.next=u}}throw new TypeError(e(t)+" is not iterable")}return w.prototype=x,u(L,"constructor",{value:x,configurable:!0}),u(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,f(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},n.awrap=function(e){return{__await:e}},V(j.prototype),f(j.prototype,c,(function(){return this})),n.AsyncIterator=j,n.async=function(e,t,r,o,a){void 0===a&&(a=Promise);var u=new j(p(e,t,r,o),a);return n.isGeneratorFunction(t)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},V(L),f(L,s,"Generator"),f(L,i,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=A,D.prototype={constructor:D,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,o){return l.type="throw",l.arg=e,t.next=n,o&&(t.method="next",t.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var u=this.tryEntries[o],l=u.completion;if("root"===u.tryLoc)return n("end");if(u.tryLoc<=this.prev){var i=a.call(u,"catchLoc"),c=a.call(u,"finallyLoc");if(i&&c){if(this.prev<u.catchLoc)return n(u.catchLoc,!0);if(this.prev<u.finallyLoc)return n(u.finallyLoc)}else if(i){if(this.prev<u.catchLoc)return n(u.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return n(u.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var u=o?o.completion:{};return u.type=e,u.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;O(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:A(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(e,t,r,n,o,a,u){try{var l=e[a](u),i=l.value}catch(e){return void r(e)}l.done?t(i):Promise.resolve(i).then(n,o)}function n(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var u=e.apply(t,n);function l(e){r(u,o,a,l,i,"next",e)}function i(e){r(u,o,a,l,i,"throw",e)}l(void 0)}))}}System.register(["./087AC4D233B64EB0order-legacy.BqaGmf2o.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0selectImage-legacy.B9If-S4b.js","./087AC4D233B64EB0image-legacy.CRPxTu2y.js","./087AC4D233B64EB0QR-code-legacy.DcvYsf4S.js","./087AC4D233B64EB0vue-cropper.es-legacy.CQhcf0py.js","./087AC4D233B64EB0logo-legacy.BFIBdCh0.js"],(function(e,r){"use strict";var o,a,u,l,i,c,s,f,p,d,h,v,m,y,g,b,w,x,_,E;return{setters:[function(e){o=e.f,a=e.c,u=e.u},function(e){l=e.aj,i=e.u,c=e.a,s=e.r,f=e.g,p=e.c,d=e.o,h=e.b,v=e.f,m=e.w,y=e.F,g=e.D,b=e.i,w=e.h,x=e.aA,_=e.E},function(e){E=e.S},null,null,null,null],execute:function(){var r={class:"gva-form-box"};e("default",Object.assign({name:"OrderForm"},{__name:"orderForm",setup:function(e){var k=l(),L=i(),V=c(!1),j=c(""),N=c([]),P=c([]),S=c({orderImg:[],postingNumber:"",inProcessAt:new Date,orderNumber:"",tplProvider:"",status:"",distributionStatus:"",trackingNumber:"",shipmentDate:new Date}),O=s({}),D=c(),A=function(){var e=n(t().mark((function e(){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!k.query.id){e.next=7;break}return e.next=3,o({ID:k.query.id});case 3:0===(r=e.sent).code&&(S.value=r.data,j.value="update"),e.next=8;break;case 7:j.value="create";case 8:return e.next=10,x("order_status");case 10:return N.value=e.sent,e.next=13,x("distribution_status");case 13:P.value=e.sent;case 14:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();A();var B=function(){var e=n(t().mark((function e(){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:V.value=!0,null===(r=D.value)||void 0===r||r.validate(function(){var e=n(t().mark((function e(r){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r){e.next=2;break}return e.abrupt("return",V.value=!1);case 2:e.t0=j.value,e.next="create"===e.t0?5:"update"===e.t0?9:13;break;case 5:return e.next=7,a(S.value);case 7:return n=e.sent,e.abrupt("break",17);case 9:return e.next=11,u(S.value);case 11:return n=e.sent,e.abrupt("break",17);case 13:return e.next=15,a(S.value);case 15:return n=e.sent,e.abrupt("break",17);case 17:V.value=!1,0===n.code&&_({type:"success",message:"创建/更改成功"});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=function(){L.go(-1)};return function(e,t){var n=f("el-form-item"),o=f("el-input"),a=f("el-date-picker"),u=f("el-option"),l=f("el-select"),i=f("el-button"),c=f("el-form");return d(),p("div",null,[h("div",r,[v(c,{model:S.value,ref_key:"elFormRef",ref:D,"label-position":"right",rules:O,"label-width":"80px"},{default:m((function(){return[v(n,{label:"产品图:",prop:"orderImg"},{default:m((function(){return[v(E,{modelValue:S.value.orderImg,"onUpdate:modelValue":t[0]||(t[0]=function(e){return S.value.orderImg=e}),multiple:"","file-type":"image"},null,8,["modelValue"])]})),_:1}),v(n,{label:"货件号:",prop:"postingNumber"},{default:m((function(){return[v(o,{modelValue:S.value.postingNumber,"onUpdate:modelValue":t[1]||(t[1]=function(e){return S.value.postingNumber=e}),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"下单时间:",prop:"inProcessAt"},{default:m((function(){return[v(a,{modelValue:S.value.inProcessAt,"onUpdate:modelValue":t[2]||(t[2]=function(e){return S.value.inProcessAt=e}),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),v(n,{label:"订单号:",prop:"orderNumber"},{default:m((function(){return[v(o,{modelValue:S.value.orderNumber,"onUpdate:modelValue":t[3]||(t[3]=function(e){return S.value.orderNumber=e}),clearable:!0,placeholder:"请输入订单号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"物流:",prop:"tplProvider"},{default:m((function(){return[v(o,{modelValue:S.value.tplProvider,"onUpdate:modelValue":t[4]||(t[4]=function(e){return S.value.tplProvider=e}),clearable:!0,placeholder:"请输入物流"},null,8,["modelValue"])]})),_:1}),v(n,{label:"订单状态:",prop:"status"},{default:m((function(){return[v(l,{modelValue:S.value.status,"onUpdate:modelValue":t[5]||(t[5]=function(e){return S.value.status=e}),placeholder:"请选择订单状态",style:{width:"100%"},clearable:!0},{default:m((function(){return[(d(!0),p(y,null,g(N.value,(function(e,t){return d(),b(u,{key:t,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),v(n,{label:"配货状态:",prop:"distributionStatus"},{default:m((function(){return[v(l,{modelValue:S.value.distributionStatus,"onUpdate:modelValue":t[6]||(t[6]=function(e){return S.value.distributionStatus=e}),placeholder:"请选择配货状态",style:{width:"100%"},clearable:!0},{default:m((function(){return[(d(!0),p(y,null,g(P.value,(function(e,t){return d(),b(u,{key:t,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),v(n,{label:"国际单号:",prop:"trackingNumber"},{default:m((function(){return[v(o,{modelValue:S.value.trackingNumber,"onUpdate:modelValue":t[7]||(t[7]=function(e){return S.value.trackingNumber=e}),clearable:!0,placeholder:"请输入国际单号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"发货日期:",prop:"shipmentDate"},{default:m((function(){return[v(a,{modelValue:S.value.shipmentDate,"onUpdate:modelValue":t[8]||(t[8]=function(e){return S.value.shipmentDate=e}),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),v(n,null,{default:m((function(){return[v(i,{loading:V.value,type:"primary",onClick:B},{default:m((function(){return t[9]||(t[9]=[w("保存")])})),_:1},8,["loading"]),v(i,{type:"primary",onClick:C},{default:m((function(){return t[10]||(t[10]=[w("返回")])})),_:1})]})),_:1})]})),_:1},8,["model","rules"])])])}}}))}}}))}();
