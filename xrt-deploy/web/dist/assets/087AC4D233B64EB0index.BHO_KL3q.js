/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
import e from"./087AC4D233B64EB0fieldDialog.BJ47QWbf.js";import t from"./087AC4D233B64EB0previewCodeDialog.CyPfx7eJ.js";import{b as a,c as l,a as o,t as n}from"./087AC4D233B64EB0stringFun.BxqK0MAg.js";import{e as i,l as r,g as d,c as u,a as s,d as c,p as f,f as p,h as m}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";import{k as v,a as h,p as g,aj as b,u as y,Q as w,g as _,c as T,o as N,f as D,d as E,b as x,h as S,w as C,F as V,D as k,i as A,t as O,v as I,T as M,E as B,ab as U,b7 as P,b8 as R}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as j}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";
/**!
 * Sortable 1.15.6
 * <AUTHOR>   <<EMAIL>>
 * <AUTHOR>    <<EMAIL>>
 * @license MIT
 */function L(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);t&&(l=l.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,l)}return a}function X(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?L(Object(a),!0).forEach((function(t){Y(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):L(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function F(e){return(F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function W(){return W=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var l in a)Object.prototype.hasOwnProperty.call(a,l)&&(e[l]=a[l])}return e},W.apply(this,arguments)}function q(e,t){if(null==e)return{};var a,l,o=function(e,t){if(null==e)return{};var a,l,o={},n=Object.keys(e);for(l=0;l<n.length;l++)a=n[l],t.indexOf(a)>=0||(o[a]=e[a]);return o}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(l=0;l<n.length;l++)a=n[l],t.indexOf(a)>=0||Object.prototype.propertyIsEnumerable.call(e,a)&&(o[a]=e[a])}return o}function J(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var z=J(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),H=J(/Edge/i),K=J(/firefox/i),G=J(/safari/i)&&!J(/chrome/i)&&!J(/android/i),Q=J(/iP(ad|od|hone)/i),Z=J(/chrome/i)&&J(/android/i),$={capture:!1,passive:!1};function ee(e,t,a){e.addEventListener(t,a,!z&&$)}function te(e,t,a){e.removeEventListener(t,a,!z&&$)}function ae(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(a){return!1}return!1}}function le(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function oe(e,t,a,l){if(e){a=a||document;do{if(null!=t&&(">"===t[0]?e.parentNode===a&&ae(e,t):ae(e,t))||l&&e===a)return e;if(e===a)break}while(e=le(e))}return null}var ne,ie=/\s+/g;function re(e,t,a){if(e&&t)if(e.classList)e.classList[a?"add":"remove"](t);else{var l=(" "+e.className+" ").replace(ie," ").replace(" "+t+" "," ");e.className=(l+(a?" "+t:"")).replace(ie," ")}}function de(e,t,a){var l=e&&e.style;if(l){if(void 0===a)return document.defaultView&&document.defaultView.getComputedStyle?a=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(a=e.currentStyle),void 0===t?a:a[t];t in l||-1!==t.indexOf("webkit")||(t="-webkit-"+t),l[t]=a+("string"==typeof a?"":"px")}}function ue(e,t){var a="";if("string"==typeof e)a=e;else do{var l=de(e,"transform");l&&"none"!==l&&(a=l+" "+a)}while(!t&&(e=e.parentNode));var o=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return o&&new o(a)}function se(e,t,a){if(e){var l=e.getElementsByTagName(t),o=0,n=l.length;if(a)for(;o<n;o++)a(l[o],o);return l}return[]}function ce(){var e=document.scrollingElement;return e||document.documentElement}function fe(e,t,a,l,o){if(e.getBoundingClientRect||e===window){var n,i,r,d,u,s,c;if(e!==window&&e.parentNode&&e!==ce()?(i=(n=e.getBoundingClientRect()).top,r=n.left,d=n.bottom,u=n.right,s=n.height,c=n.width):(i=0,r=0,d=window.innerHeight,u=window.innerWidth,s=window.innerHeight,c=window.innerWidth),(t||a)&&e!==window&&(o=o||e.parentNode,!z))do{if(o&&o.getBoundingClientRect&&("none"!==de(o,"transform")||a&&"static"!==de(o,"position"))){var f=o.getBoundingClientRect();i-=f.top+parseInt(de(o,"border-top-width")),r-=f.left+parseInt(de(o,"border-left-width")),d=i+n.height,u=r+n.width;break}}while(o=o.parentNode);if(l&&e!==window){var p=ue(o||e),m=p&&p.a,v=p&&p.d;p&&(d=(i/=v)+(s/=v),u=(r/=m)+(c/=m))}return{top:i,left:r,bottom:d,right:u,width:c,height:s}}}function pe(e,t,a){for(var l=be(e,!0),o=fe(e)[t];l;){if(!(o>=fe(l)[a]))return l;if(l===ce())break;l=be(l,!1)}return!1}function me(e,t,a,l){for(var o=0,n=0,i=e.children;n<i.length;){if("none"!==i[n].style.display&&i[n]!==Tt.ghost&&(l||i[n]!==Tt.dragged)&&oe(i[n],a.draggable,e,!1)){if(o===t)return i[n];o++}n++}return null}function ve(e,t){for(var a=e.lastElementChild;a&&(a===Tt.ghost||"none"===de(a,"display")||t&&!ae(a,t));)a=a.previousElementSibling;return a||null}function he(e,t){var a=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===Tt.clone||t&&!ae(e,t)||a++;return a}function ge(e){var t=0,a=0,l=ce();if(e)do{var o=ue(e),n=o.a,i=o.d;t+=e.scrollLeft*n,a+=e.scrollTop*i}while(e!==l&&(e=e.parentNode));return[t,a]}function be(e,t){if(!e||!e.getBoundingClientRect)return ce();var a=e,l=!1;do{if(a.clientWidth<a.scrollWidth||a.clientHeight<a.scrollHeight){var o=de(a);if(a.clientWidth<a.scrollWidth&&("auto"==o.overflowX||"scroll"==o.overflowX)||a.clientHeight<a.scrollHeight&&("auto"==o.overflowY||"scroll"==o.overflowY)){if(!a.getBoundingClientRect||a===document.body)return ce();if(l||t)return a;l=!0}}}while(a=a.parentNode);return ce()}function ye(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function we(e,t){return function(){if(!ne){var a=arguments;1===a.length?e.call(this,a[0]):e.apply(this,a),ne=setTimeout((function(){ne=void 0}),t)}}}function _e(e,t,a){e.scrollLeft+=t,e.scrollTop+=a}function Te(e){var t=window.Polymer,a=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):a?a(e).clone(!0)[0]:e.cloneNode(!0)}function Ne(e,t,a){var l={};return Array.from(e.children).forEach((function(o){var n,i,r,d;if(oe(o,t.draggable,e,!1)&&!o.animated&&o!==a){var u=fe(o);l.left=Math.min(null!==(n=l.left)&&void 0!==n?n:1/0,u.left),l.top=Math.min(null!==(i=l.top)&&void 0!==i?i:1/0,u.top),l.right=Math.max(null!==(r=l.right)&&void 0!==r?r:-1/0,u.right),l.bottom=Math.max(null!==(d=l.bottom)&&void 0!==d?d:-1/0,u.bottom)}})),l.width=l.right-l.left,l.height=l.bottom-l.top,l.x=l.left,l.y=l.top,l}var De="Sortable"+(new Date).getTime();function Ee(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==de(e,"display")&&e!==Tt.ghost){t.push({target:e,rect:fe(e)});var a=X({},t[t.length-1].rect);if(e.thisAnimationDuration){var l=ue(e,!0);l&&(a.top-=l.f,a.left-=l.e)}e.fromRect=a}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var a in e)if(e.hasOwnProperty(a))for(var l in t)if(t.hasOwnProperty(l)&&t[l]===e[a][l])return Number(a);return-1}(t,{target:e}),1)},animateAll:function(a){var l=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof a&&a());var o=!1,n=0;t.forEach((function(e){var t=0,a=e.target,i=a.fromRect,r=fe(a),d=a.prevFromRect,u=a.prevToRect,s=e.rect,c=ue(a,!0);c&&(r.top-=c.f,r.left-=c.e),a.toRect=r,a.thisAnimationDuration&&ye(d,r)&&!ye(i,r)&&(s.top-r.top)/(s.left-r.left)==(i.top-r.top)/(i.left-r.left)&&(t=function(e,t,a,l){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-a.top,2)+Math.pow(t.left-a.left,2))*l.animation}(s,d,u,l.options)),ye(r,i)||(a.prevFromRect=i,a.prevToRect=r,t||(t=l.options.animation),l.animate(a,s,r,t)),t&&(o=!0,n=Math.max(n,t),clearTimeout(a.animationResetTimer),a.animationResetTimer=setTimeout((function(){a.animationTime=0,a.prevFromRect=null,a.fromRect=null,a.prevToRect=null,a.thisAnimationDuration=null}),t),a.thisAnimationDuration=t)})),clearTimeout(e),o?e=setTimeout((function(){"function"==typeof a&&a()}),n):"function"==typeof a&&a(),t=[]},animate:function(e,t,a,l){if(l){de(e,"transition",""),de(e,"transform","");var o=ue(this.el),n=o&&o.a,i=o&&o.d,r=(t.left-a.left)/(n||1),d=(t.top-a.top)/(i||1);e.animatingX=!!r,e.animatingY=!!d,de(e,"transform","translate3d("+r+"px,"+d+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),de(e,"transition","transform "+l+"ms"+(this.options.easing?" "+this.options.easing:"")),de(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){de(e,"transition",""),de(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),l)}}}}var xe=[],Se={initializeByDefault:!0},Ce={mount:function(e){for(var t in Se)Se.hasOwnProperty(t)&&!(t in e)&&(e[t]=Se[t]);xe.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),xe.push(e)},pluginEvent:function(e,t,a){var l=this;this.eventCanceled=!1,a.cancel=function(){l.eventCanceled=!0};var o=e+"Global";xe.forEach((function(l){t[l.pluginName]&&(t[l.pluginName][o]&&t[l.pluginName][o](X({sortable:t},a)),t.options[l.pluginName]&&t[l.pluginName][e]&&t[l.pluginName][e](X({sortable:t},a)))}))},initializePlugins:function(e,t,a,l){for(var o in xe.forEach((function(l){var o=l.pluginName;if(e.options[o]||l.initializeByDefault){var n=new l(e,t,e.options);n.sortable=e,n.options=e.options,e[o]=n,W(a,n.defaults)}})),e.options)if(e.options.hasOwnProperty(o)){var n=this.modifyOption(e,o,e.options[o]);void 0!==n&&(e.options[o]=n)}},getEventProperties:function(e,t){var a={};return xe.forEach((function(l){"function"==typeof l.eventProperties&&W(a,l.eventProperties.call(t[l.pluginName],e))})),a},modifyOption:function(e,t,a){var l;return xe.forEach((function(o){e[o.pluginName]&&o.optionListeners&&"function"==typeof o.optionListeners[t]&&(l=o.optionListeners[t].call(e[o.pluginName],a))})),l}};var Ve=["evt"],ke=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=a.evt,o=q(a,Ve);Ce.pluginEvent.bind(Tt)(e,t,X({dragEl:Oe,parentEl:Ie,ghostEl:Me,rootEl:Be,nextEl:Ue,lastDownEl:Pe,cloneEl:Re,cloneHidden:je,dragStarted:Ze,putSortable:qe,activeSortable:Tt.active,originalEvent:l,oldIndex:Le,oldDraggableIndex:Fe,newIndex:Xe,newDraggableIndex:Ye,hideGhostForTarget:bt,unhideGhostForTarget:yt,cloneNowHidden:function(){je=!0},cloneNowShown:function(){je=!1},dispatchSortableEvent:function(e){Ae({sortable:t,name:e,originalEvent:l})}},o))};function Ae(e){!function(e){var t=e.sortable,a=e.rootEl,l=e.name,o=e.targetEl,n=e.cloneEl,i=e.toEl,r=e.fromEl,d=e.oldIndex,u=e.newIndex,s=e.oldDraggableIndex,c=e.newDraggableIndex,f=e.originalEvent,p=e.putSortable,m=e.extraEventProperties;if(t=t||a&&a[De]){var v,h=t.options,g="on"+l.charAt(0).toUpperCase()+l.substr(1);!window.CustomEvent||z||H?(v=document.createEvent("Event")).initEvent(l,!0,!0):v=new CustomEvent(l,{bubbles:!0,cancelable:!0}),v.to=i||a,v.from=r||a,v.item=o||a,v.clone=n,v.oldIndex=d,v.newIndex=u,v.oldDraggableIndex=s,v.newDraggableIndex=c,v.originalEvent=f,v.pullMode=p?p.lastPutMode:void 0;var b=X(X({},m),Ce.getEventProperties(l,t));for(var y in b)v[y]=b[y];a&&a.dispatchEvent(v),h[g]&&h[g].call(t,v)}}(X({putSortable:qe,cloneEl:Re,targetEl:Oe,rootEl:Be,oldIndex:Le,oldDraggableIndex:Fe,newIndex:Xe,newDraggableIndex:Ye},e))}var Oe,Ie,Me,Be,Ue,Pe,Re,je,Le,Xe,Fe,Ye,We,qe,Je,ze,He,Ke,Ge,Qe,Ze,$e,et,tt,at,lt=!1,ot=!1,nt=[],it=!1,rt=!1,dt=[],ut=!1,st=[],ct="undefined"!=typeof document,ft=Q,pt=H||z?"cssFloat":"float",mt=ct&&!Z&&!Q&&"draggable"in document.createElement("div"),vt=function(){if(ct){if(z)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),ht=function(e,t){var a=de(e),l=parseInt(a.width)-parseInt(a.paddingLeft)-parseInt(a.paddingRight)-parseInt(a.borderLeftWidth)-parseInt(a.borderRightWidth),o=me(e,0,t),n=me(e,1,t),i=o&&de(o),r=n&&de(n),d=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+fe(o).width,u=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+fe(n).width;if("flex"===a.display)return"column"===a.flexDirection||"column-reverse"===a.flexDirection?"vertical":"horizontal";if("grid"===a.display)return a.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(o&&i.float&&"none"!==i.float){var s="left"===i.float?"left":"right";return!n||"both"!==r.clear&&r.clear!==s?"horizontal":"vertical"}return o&&("block"===i.display||"flex"===i.display||"table"===i.display||"grid"===i.display||d>=l&&"none"===a[pt]||n&&"none"===a[pt]&&d+u>l)?"vertical":"horizontal"},gt=function(e){function t(e,a){return function(l,o,n,i){var r=l.options.group.name&&o.options.group.name&&l.options.group.name===o.options.group.name;if(null==e&&(a||r))return!0;if(null==e||!1===e)return!1;if(a&&"clone"===e)return e;if("function"==typeof e)return t(e(l,o,n,i),a)(l,o,n,i);var d=(a?l:o).options.group.name;return!0===e||"string"==typeof e&&e===d||e.join&&e.indexOf(d)>-1}}var a={},l=e.group;l&&"object"==F(l)||(l={name:l}),a.name=l.name,a.checkPull=t(l.pull,!0),a.checkPut=t(l.put),a.revertClone=l.revertClone,e.group=a},bt=function(){!vt&&Me&&de(Me,"display","none")},yt=function(){!vt&&Me&&de(Me,"display","")};ct&&!Z&&document.addEventListener("click",(function(e){if(ot)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),ot=!1,!1}),!0);var wt=function(e){if(Oe){e=e.touches?e.touches[0]:e;var t=(o=e.clientX,n=e.clientY,nt.some((function(e){var t=e[De].options.emptyInsertThreshold;if(t&&!ve(e)){var a=fe(e),l=o>=a.left-t&&o<=a.right+t,r=n>=a.top-t&&n<=a.bottom+t;return l&&r?i=e:void 0}})),i);if(t){var a={};for(var l in e)e.hasOwnProperty(l)&&(a[l]=e[l]);a.target=a.rootEl=t,a.preventDefault=void 0,a.stopPropagation=void 0,t[De]._onDragOver(a)}}var o,n,i},_t=function(e){Oe&&Oe.parentNode[De]._isOutsideThisEl(e.target)};function Tt(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=W({},t),e[De]=this;var a={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return ht(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Tt.supportPointer&&"PointerEvent"in window&&(!G||Q),emptyInsertThreshold:5};for(var l in Ce.initializePlugins(this,e,a),a)!(l in t)&&(t[l]=a[l]);for(var o in gt(t),this)"_"===o.charAt(0)&&"function"==typeof this[o]&&(this[o]=this[o].bind(this));this.nativeDraggable=!t.forceFallback&&mt,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?ee(e,"pointerdown",this._onTapStart):(ee(e,"mousedown",this._onTapStart),ee(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(ee(e,"dragover",this),ee(e,"dragenter",this)),nt.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),W(this,Ee())}function Nt(e,t,a,l,o,n,i,r){var d,u,s=e[De],c=s.options.onMove;return!window.CustomEvent||z||H?(d=document.createEvent("Event")).initEvent("move",!0,!0):d=new CustomEvent("move",{bubbles:!0,cancelable:!0}),d.to=t,d.from=e,d.dragged=a,d.draggedRect=l,d.related=o||t,d.relatedRect=n||fe(t),d.willInsertAfter=r,d.originalEvent=i,e.dispatchEvent(d),c&&(u=c.call(s,d,i)),u}function Dt(e){e.draggable=!1}function Et(){ut=!1}function xt(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,a=t.length,l=0;a--;)l+=t.charCodeAt(a);return l.toString(36)}function St(e){return setTimeout(e,0)}function Ct(e){return clearTimeout(e)}Tt.prototype={constructor:Tt,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||($e=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,Oe):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,a=this.el,l=this.options,o=l.preventOnFilter,n=e.type,i=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,r=(i||e).target,d=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||r,u=l.filter;if(function(e){st.length=0;var t=e.getElementsByTagName("input"),a=t.length;for(;a--;){var l=t[a];l.checked&&st.push(l)}}(a),!Oe&&!(/mousedown|pointerdown/.test(n)&&0!==e.button||l.disabled)&&!d.isContentEditable&&(this.nativeDraggable||!G||!r||"SELECT"!==r.tagName.toUpperCase())&&!((r=oe(r,l.draggable,a,!1))&&r.animated||Pe===r)){if(Le=he(r),Fe=he(r,l.draggable),"function"==typeof u){if(u.call(this,e,r,this))return Ae({sortable:t,rootEl:d,name:"filter",targetEl:r,toEl:a,fromEl:a}),ke("filter",t,{evt:e}),void(o&&e.preventDefault())}else if(u&&(u=u.split(",").some((function(l){if(l=oe(d,l.trim(),a,!1))return Ae({sortable:t,rootEl:l,name:"filter",targetEl:r,fromEl:a,toEl:a}),ke("filter",t,{evt:e}),!0}))))return void(o&&e.preventDefault());l.handle&&!oe(d,l.handle,a,!1)||this._prepareDragStart(e,i,r)}}},_prepareDragStart:function(e,t,a){var l,o=this,n=o.el,i=o.options,r=n.ownerDocument;if(a&&!Oe&&a.parentNode===n){var d=fe(a);if(Be=n,Ie=(Oe=a).parentNode,Ue=Oe.nextSibling,Pe=a,We=i.group,Tt.dragged=Oe,Je={target:Oe,clientX:(t||e).clientX,clientY:(t||e).clientY},Ge=Je.clientX-d.left,Qe=Je.clientY-d.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,Oe.style["will-change"]="all",l=function(){ke("delayEnded",o,{evt:e}),Tt.eventCanceled?o._onDrop():(o._disableDelayedDragEvents(),!K&&o.nativeDraggable&&(Oe.draggable=!0),o._triggerDragStart(e,t),Ae({sortable:o,name:"choose",originalEvent:e}),re(Oe,i.chosenClass,!0))},i.ignore.split(",").forEach((function(e){se(Oe,e.trim(),Dt)})),ee(r,"dragover",wt),ee(r,"mousemove",wt),ee(r,"touchmove",wt),i.supportPointer?(ee(r,"pointerup",o._onDrop),!this.nativeDraggable&&ee(r,"pointercancel",o._onDrop)):(ee(r,"mouseup",o._onDrop),ee(r,"touchend",o._onDrop),ee(r,"touchcancel",o._onDrop)),K&&this.nativeDraggable&&(this.options.touchStartThreshold=4,Oe.draggable=!0),ke("delayStart",this,{evt:e}),!i.delay||i.delayOnTouchOnly&&!t||this.nativeDraggable&&(H||z))l();else{if(Tt.eventCanceled)return void this._onDrop();i.supportPointer?(ee(r,"pointerup",o._disableDelayedDrag),ee(r,"pointercancel",o._disableDelayedDrag)):(ee(r,"mouseup",o._disableDelayedDrag),ee(r,"touchend",o._disableDelayedDrag),ee(r,"touchcancel",o._disableDelayedDrag)),ee(r,"mousemove",o._delayedDragTouchMoveHandler),ee(r,"touchmove",o._delayedDragTouchMoveHandler),i.supportPointer&&ee(r,"pointermove",o._delayedDragTouchMoveHandler),o._dragStartTimer=setTimeout(l,i.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){Oe&&Dt(Oe),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;te(e,"mouseup",this._disableDelayedDrag),te(e,"touchend",this._disableDelayedDrag),te(e,"touchcancel",this._disableDelayedDrag),te(e,"pointerup",this._disableDelayedDrag),te(e,"pointercancel",this._disableDelayedDrag),te(e,"mousemove",this._delayedDragTouchMoveHandler),te(e,"touchmove",this._delayedDragTouchMoveHandler),te(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?ee(document,"pointermove",this._onTouchMove):ee(document,t?"touchmove":"mousemove",this._onTouchMove):(ee(Oe,"dragend",this),ee(Be,"dragstart",this._onDragStart));try{document.selection?St((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(a){}},_dragStarted:function(e,t){if(lt=!1,Be&&Oe){ke("dragStarted",this,{evt:t}),this.nativeDraggable&&ee(document,"dragover",_t);var a=this.options;!e&&re(Oe,a.dragClass,!1),re(Oe,a.ghostClass,!0),Tt.active=this,e&&this._appendGhost(),Ae({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(ze){this._lastX=ze.clientX,this._lastY=ze.clientY,bt();for(var e=document.elementFromPoint(ze.clientX,ze.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(ze.clientX,ze.clientY))!==t;)t=e;if(Oe.parentNode[De]._isOutsideThisEl(e),t)do{if(t[De]){if(t[De]._onDragOver({clientX:ze.clientX,clientY:ze.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=le(t));yt()}},_onTouchMove:function(e){if(Je){var t=this.options,a=t.fallbackTolerance,l=t.fallbackOffset,o=e.touches?e.touches[0]:e,n=Me&&ue(Me,!0),i=Me&&n&&n.a,r=Me&&n&&n.d,d=ft&&at&&ge(at),u=(o.clientX-Je.clientX+l.x)/(i||1)+(d?d[0]-dt[0]:0)/(i||1),s=(o.clientY-Je.clientY+l.y)/(r||1)+(d?d[1]-dt[1]:0)/(r||1);if(!Tt.active&&!lt){if(a&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<a)return;this._onDragStart(e,!0)}if(Me){n?(n.e+=u-(He||0),n.f+=s-(Ke||0)):n={a:1,b:0,c:0,d:1,e:u,f:s};var c="matrix(".concat(n.a,",").concat(n.b,",").concat(n.c,",").concat(n.d,",").concat(n.e,",").concat(n.f,")");de(Me,"webkitTransform",c),de(Me,"mozTransform",c),de(Me,"msTransform",c),de(Me,"transform",c),He=u,Ke=s,ze=o}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!Me){var e=this.options.fallbackOnBody?document.body:Be,t=fe(Oe,!0,ft,!0,e),a=this.options;if(ft){for(at=e;"static"===de(at,"position")&&"none"===de(at,"transform")&&at!==document;)at=at.parentNode;at!==document.body&&at!==document.documentElement?(at===document&&(at=ce()),t.top+=at.scrollTop,t.left+=at.scrollLeft):at=ce(),dt=ge(at)}re(Me=Oe.cloneNode(!0),a.ghostClass,!1),re(Me,a.fallbackClass,!0),re(Me,a.dragClass,!0),de(Me,"transition",""),de(Me,"transform",""),de(Me,"box-sizing","border-box"),de(Me,"margin",0),de(Me,"top",t.top),de(Me,"left",t.left),de(Me,"width",t.width),de(Me,"height",t.height),de(Me,"opacity","0.8"),de(Me,"position",ft?"absolute":"fixed"),de(Me,"zIndex","100000"),de(Me,"pointerEvents","none"),Tt.ghost=Me,e.appendChild(Me),de(Me,"transform-origin",Ge/parseInt(Me.style.width)*100+"% "+Qe/parseInt(Me.style.height)*100+"%")}},_onDragStart:function(e,t){var a=this,l=e.dataTransfer,o=a.options;ke("dragStart",this,{evt:e}),Tt.eventCanceled?this._onDrop():(ke("setupClone",this),Tt.eventCanceled||((Re=Te(Oe)).removeAttribute("id"),Re.draggable=!1,Re.style["will-change"]="",this._hideClone(),re(Re,this.options.chosenClass,!1),Tt.clone=Re),a.cloneId=St((function(){ke("clone",a),Tt.eventCanceled||(a.options.removeCloneOnHide||Be.insertBefore(Re,Oe),a._hideClone(),Ae({sortable:a,name:"clone"}))})),!t&&re(Oe,o.dragClass,!0),t?(ot=!0,a._loopId=setInterval(a._emulateDragOver,50)):(te(document,"mouseup",a._onDrop),te(document,"touchend",a._onDrop),te(document,"touchcancel",a._onDrop),l&&(l.effectAllowed="move",o.setData&&o.setData.call(a,l,Oe)),ee(document,"drop",a),de(Oe,"transform","translateZ(0)")),lt=!0,a._dragStartId=St(a._dragStarted.bind(a,t,e)),ee(document,"selectstart",a),Ze=!0,window.getSelection().removeAllRanges(),G&&de(document.body,"user-select","none"))},_onDragOver:function(e){var t,a,l,o,n=this.el,i=e.target,r=this.options,d=r.group,u=Tt.active,s=We===d,c=r.sort,f=qe||u,p=this,m=!1;if(!ut){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),i=oe(i,r.draggable,n,!0),V("dragOver"),Tt.eventCanceled)return m;if(Oe.contains(e.target)||i.animated&&i.animatingX&&i.animatingY||p._ignoreWhileAnimating===i)return A(!1);if(ot=!1,u&&!r.disabled&&(s?c||(l=Ie!==Be):qe===this||(this.lastPutMode=We.checkPull(this,u,Oe,e))&&d.checkPut(this,u,Oe,e))){if(o="vertical"===this._getDirection(e,i),t=fe(Oe),V("dragOverValid"),Tt.eventCanceled)return m;if(l)return Ie=Be,k(),this._hideClone(),V("revert"),Tt.eventCanceled||(Ue?Be.insertBefore(Oe,Ue):Be.appendChild(Oe)),A(!0);var v=ve(n,r.draggable);if(!v||function(e,t,a){var l=fe(ve(a.el,a.options.draggable)),o=Ne(a.el,a.options,Me),n=10;return t?e.clientX>o.right+n||e.clientY>l.bottom&&e.clientX>l.left:e.clientY>o.bottom+n||e.clientX>l.right&&e.clientY>l.top}(e,o,this)&&!v.animated){if(v===Oe)return A(!1);if(v&&n===e.target&&(i=v),i&&(a=fe(i)),!1!==Nt(Be,n,Oe,t,i,a,e,!!i))return k(),v&&v.nextSibling?n.insertBefore(Oe,v.nextSibling):n.appendChild(Oe),Ie=n,O(),A(!0)}else if(v&&function(e,t,a){var l=fe(me(a.el,0,a.options,!0)),o=Ne(a.el,a.options,Me),n=10;return t?e.clientX<o.left-n||e.clientY<l.top&&e.clientX<l.right:e.clientY<o.top-n||e.clientY<l.bottom&&e.clientX<l.left}(e,o,this)){var h=me(n,0,r,!0);if(h===Oe)return A(!1);if(a=fe(i=h),!1!==Nt(Be,n,Oe,t,i,a,e,!1))return k(),n.insertBefore(Oe,h),Ie=n,O(),A(!0)}else if(i.parentNode===n){a=fe(i);var g,b,y,w=Oe.parentNode!==n,_=!function(e,t,a){var l=a?e.left:e.top,o=a?e.right:e.bottom,n=a?e.width:e.height,i=a?t.left:t.top,r=a?t.right:t.bottom,d=a?t.width:t.height;return l===i||o===r||l+n/2===i+d/2}(Oe.animated&&Oe.toRect||t,i.animated&&i.toRect||a,o),T=o?"top":"left",N=pe(i,"top","top")||pe(Oe,"top","top"),D=N?N.scrollTop:void 0;if($e!==i&&(b=a[T],it=!1,rt=!_&&r.invertSwap||w),g=function(e,t,a,l,o,n,i,r){var d=l?e.clientY:e.clientX,u=l?a.height:a.width,s=l?a.top:a.left,c=l?a.bottom:a.right,f=!1;if(!i)if(r&&tt<u*o){if(!it&&(1===et?d>s+u*n/2:d<c-u*n/2)&&(it=!0),it)f=!0;else if(1===et?d<s+tt:d>c-tt)return-et}else if(d>s+u*(1-o)/2&&d<c-u*(1-o)/2)return function(e){return he(Oe)<he(e)?1:-1}(t);if((f=f||i)&&(d<s+u*n/2||d>c-u*n/2))return d>s+u/2?1:-1;return 0}(e,i,a,o,_?1:r.swapThreshold,null==r.invertedSwapThreshold?r.swapThreshold:r.invertedSwapThreshold,rt,$e===i),0!==g){var E=he(Oe);do{E-=g,y=Ie.children[E]}while(y&&("none"===de(y,"display")||y===Me))}if(0===g||y===i)return A(!1);$e=i,et=g;var x=i.nextElementSibling,S=!1,C=Nt(Be,n,Oe,t,i,a,e,S=1===g);if(!1!==C)return 1!==C&&-1!==C||(S=1===C),ut=!0,setTimeout(Et,30),k(),S&&!x?n.appendChild(Oe):i.parentNode.insertBefore(Oe,S?x:i),N&&_e(N,0,D-N.scrollTop),Ie=Oe.parentNode,void 0===b||rt||(tt=Math.abs(b-fe(i)[T])),O(),A(!0)}if(n.contains(Oe))return A(!1)}return!1}function V(r,d){ke(r,p,X({evt:e,isOwner:s,axis:o?"vertical":"horizontal",revert:l,dragRect:t,targetRect:a,canSort:c,fromSortable:f,target:i,completed:A,onMove:function(a,l){return Nt(Be,n,Oe,t,a,fe(a),e,l)},changed:O},d))}function k(){V("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function A(t){return V("dragOverCompleted",{insertion:t}),t&&(s?u._hideClone():u._showClone(p),p!==f&&(re(Oe,qe?qe.options.ghostClass:u.options.ghostClass,!1),re(Oe,r.ghostClass,!0)),qe!==p&&p!==Tt.active?qe=p:p===Tt.active&&qe&&(qe=null),f===p&&(p._ignoreWhileAnimating=i),p.animateAll((function(){V("dragOverAnimationComplete"),p._ignoreWhileAnimating=null})),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(i===Oe&&!Oe.animated||i===n&&!i.animated)&&($e=null),r.dragoverBubble||e.rootEl||i===document||(Oe.parentNode[De]._isOutsideThisEl(e.target),!t&&wt(e)),!r.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),m=!0}function O(){Xe=he(Oe),Ye=he(Oe,r.draggable),Ae({sortable:p,name:"change",toEl:n,newIndex:Xe,newDraggableIndex:Ye,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){te(document,"mousemove",this._onTouchMove),te(document,"touchmove",this._onTouchMove),te(document,"pointermove",this._onTouchMove),te(document,"dragover",wt),te(document,"mousemove",wt),te(document,"touchmove",wt)},_offUpEvents:function(){var e=this.el.ownerDocument;te(e,"mouseup",this._onDrop),te(e,"touchend",this._onDrop),te(e,"pointerup",this._onDrop),te(e,"pointercancel",this._onDrop),te(e,"touchcancel",this._onDrop),te(document,"selectstart",this)},_onDrop:function(e){var t=this.el,a=this.options;Xe=he(Oe),Ye=he(Oe,a.draggable),ke("drop",this,{evt:e}),Ie=Oe&&Oe.parentNode,Xe=he(Oe),Ye=he(Oe,a.draggable),Tt.eventCanceled||(lt=!1,rt=!1,it=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Ct(this.cloneId),Ct(this._dragStartId),this.nativeDraggable&&(te(document,"drop",this),te(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),G&&de(document.body,"user-select",""),de(Oe,"transform",""),e&&(Ze&&(e.cancelable&&e.preventDefault(),!a.dropBubble&&e.stopPropagation()),Me&&Me.parentNode&&Me.parentNode.removeChild(Me),(Be===Ie||qe&&"clone"!==qe.lastPutMode)&&Re&&Re.parentNode&&Re.parentNode.removeChild(Re),Oe&&(this.nativeDraggable&&te(Oe,"dragend",this),Dt(Oe),Oe.style["will-change"]="",Ze&&!lt&&re(Oe,qe?qe.options.ghostClass:this.options.ghostClass,!1),re(Oe,this.options.chosenClass,!1),Ae({sortable:this,name:"unchoose",toEl:Ie,newIndex:null,newDraggableIndex:null,originalEvent:e}),Be!==Ie?(Xe>=0&&(Ae({rootEl:Ie,name:"add",toEl:Ie,fromEl:Be,originalEvent:e}),Ae({sortable:this,name:"remove",toEl:Ie,originalEvent:e}),Ae({rootEl:Ie,name:"sort",toEl:Ie,fromEl:Be,originalEvent:e}),Ae({sortable:this,name:"sort",toEl:Ie,originalEvent:e})),qe&&qe.save()):Xe!==Le&&Xe>=0&&(Ae({sortable:this,name:"update",toEl:Ie,originalEvent:e}),Ae({sortable:this,name:"sort",toEl:Ie,originalEvent:e})),Tt.active&&(null!=Xe&&-1!==Xe||(Xe=Le,Ye=Fe),Ae({sortable:this,name:"end",toEl:Ie,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){ke("nulling",this),Be=Oe=Ie=Me=Ue=Re=Pe=je=Je=ze=Ze=Xe=Ye=Le=Fe=$e=et=qe=We=Tt.dragged=Tt.ghost=Tt.clone=Tt.active=null,st.forEach((function(e){e.checked=!0})),st.length=He=Ke=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":Oe&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],a=this.el.children,l=0,o=a.length,n=this.options;l<o;l++)oe(e=a[l],n.draggable,this.el,!1)&&t.push(e.getAttribute(n.dataIdAttr)||xt(e));return t},sort:function(e,t){var a={},l=this.el;this.toArray().forEach((function(e,t){var o=l.children[t];oe(o,this.options.draggable,l,!1)&&(a[e]=o)}),this),t&&this.captureAnimationState(),e.forEach((function(e){a[e]&&(l.removeChild(a[e]),l.appendChild(a[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return oe(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var a=this.options;if(void 0===t)return a[e];var l=Ce.modifyOption(this,e,t);a[e]=void 0!==l?l:t,"group"===e&&gt(a)},destroy:function(){ke("destroy",this);var e=this.el;e[De]=null,te(e,"mousedown",this._onTapStart),te(e,"touchstart",this._onTapStart),te(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(te(e,"dragover",this),te(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),nt.splice(nt.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!je){if(ke("hideClone",this),Tt.eventCanceled)return;de(Re,"display","none"),this.options.removeCloneOnHide&&Re.parentNode&&Re.parentNode.removeChild(Re),je=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(je){if(ke("showClone",this),Tt.eventCanceled)return;Oe.parentNode!=Be||this.options.group.revertClone?Ue?Be.insertBefore(Re,Ue):Be.appendChild(Re):Be.insertBefore(Re,Oe),this.options.group.revertClone&&this.animate(Oe,Re),de(Re,"display",""),je=!1}}else this._hideClone()}},ct&&ee(document,"touchmove",(function(e){(Tt.active||lt)&&e.cancelable&&e.preventDefault()})),Tt.utils={on:ee,off:te,css:de,find:se,is:function(e,t){return!!oe(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var a in t)t.hasOwnProperty(a)&&(e[a]=t[a]);return e},throttle:we,closest:oe,toggleClass:re,clone:Te,index:he,nextTick:St,cancelNextTick:Ct,detectDirection:ht,getChild:me,expando:De},Tt.get=function(e){return e[De]},Tt.mount=function(){for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Tt.utils=X(X({},Tt.utils),e.utils)),Ce.mount(e)}))},Tt.create=function(e,t){return new Tt(e,t)},Tt.version="1.15.6";var Vt,kt,At,Ot,It,Mt,Bt=[],Ut=!1;function Pt(){Bt.forEach((function(e){clearInterval(e.pid)})),Bt=[]}function Rt(){clearInterval(Mt)}var jt=we((function(e,t,a,l){if(t.scroll){var o,n=(e.touches?e.touches[0]:e).clientX,i=(e.touches?e.touches[0]:e).clientY,r=t.scrollSensitivity,d=t.scrollSpeed,u=ce(),s=!1;kt!==a&&(kt=a,Pt(),Vt=t.scroll,o=t.scrollFn,!0===Vt&&(Vt=be(a,!0)));var c=0,f=Vt;do{var p=f,m=fe(p),v=m.top,h=m.bottom,g=m.left,b=m.right,y=m.width,w=m.height,_=void 0,T=void 0,N=p.scrollWidth,D=p.scrollHeight,E=de(p),x=p.scrollLeft,S=p.scrollTop;p===u?(_=y<N&&("auto"===E.overflowX||"scroll"===E.overflowX||"visible"===E.overflowX),T=w<D&&("auto"===E.overflowY||"scroll"===E.overflowY||"visible"===E.overflowY)):(_=y<N&&("auto"===E.overflowX||"scroll"===E.overflowX),T=w<D&&("auto"===E.overflowY||"scroll"===E.overflowY));var C=_&&(Math.abs(b-n)<=r&&x+y<N)-(Math.abs(g-n)<=r&&!!x),V=T&&(Math.abs(h-i)<=r&&S+w<D)-(Math.abs(v-i)<=r&&!!S);if(!Bt[c])for(var k=0;k<=c;k++)Bt[k]||(Bt[k]={});Bt[c].vx==C&&Bt[c].vy==V&&Bt[c].el===p||(Bt[c].el=p,Bt[c].vx=C,Bt[c].vy=V,clearInterval(Bt[c].pid),0==C&&0==V||(s=!0,Bt[c].pid=setInterval(function(){l&&0===this.layer&&Tt.active._onTouchMove(It);var t=Bt[this.layer].vy?Bt[this.layer].vy*d:0,a=Bt[this.layer].vx?Bt[this.layer].vx*d:0;"function"==typeof o&&"continue"!==o.call(Tt.dragged.parentNode[De],a,t,e,It,Bt[this.layer].el)||_e(Bt[this.layer].el,a,t)}.bind({layer:c}),24))),c++}while(t.bubbleScroll&&f!==u&&(f=be(f,!1)));Ut=s}}),30),Lt=function(e){var t=e.originalEvent,a=e.putSortable,l=e.dragEl,o=e.activeSortable,n=e.dispatchSortableEvent,i=e.hideGhostForTarget,r=e.unhideGhostForTarget;if(t){var d=a||o;i();var u=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,s=document.elementFromPoint(u.clientX,u.clientY);r(),d&&!d.el.contains(s)&&(n("spill"),this.onSpill({dragEl:l,putSortable:a}))}};function Xt(){}function Ft(){}Xt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,a=e.putSortable;this.sortable.captureAnimationState(),a&&a.captureAnimationState();var l=me(this.sortable.el,this.startIndex,this.options);l?this.sortable.el.insertBefore(t,l):this.sortable.el.appendChild(t),this.sortable.animateAll(),a&&a.animateAll()},drop:Lt},W(Xt,{pluginName:"revertOnSpill"}),Ft.prototype={onSpill:function(e){var t=e.dragEl,a=e.putSortable||this.sortable;a.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),a.animateAll()},drop:Lt},W(Ft,{pluginName:"removeOnSpill"}),Tt.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?ee(document,"dragover",this._handleAutoScroll):this.options.supportPointer?ee(document,"pointermove",this._handleFallbackAutoScroll):t.touches?ee(document,"touchmove",this._handleFallbackAutoScroll):ee(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?te(document,"dragover",this._handleAutoScroll):(te(document,"pointermove",this._handleFallbackAutoScroll),te(document,"touchmove",this._handleFallbackAutoScroll),te(document,"mousemove",this._handleFallbackAutoScroll)),Rt(),Pt(),clearTimeout(ne),ne=void 0},nulling:function(){It=kt=Vt=Ut=Mt=At=Ot=null,Bt.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var a=this,l=(e.touches?e.touches[0]:e).clientX,o=(e.touches?e.touches[0]:e).clientY,n=document.elementFromPoint(l,o);if(It=e,t||this.options.forceAutoScrollFallback||H||z||G){jt(e,this.options,n,t);var i=be(n,!0);!Ut||Mt&&l===At&&o===Ot||(Mt&&Rt(),Mt=setInterval((function(){var n=be(document.elementFromPoint(l,o),!0);n!==i&&(i=n,Pt()),jt(e,a.options,n,t)}),10),At=l,Ot=o)}else{if(!this.options.bubbleScroll||be(n,!0)===ce())return void Pt();jt(e,this.options,be(n,!1),!1)}}},W(e,{pluginName:"scroll",initializeByDefault:!0})}),Tt.mount(Ft,Xt);const Yt={key:0,class:"gva-search-box"},Wt={class:"relative"},qt={class:"flex absolute right-28 bottom-2"},Jt={class:"flex absolute right-2 bottom-2"},zt={key:1,class:"gva-search-box"},Ht={style:{float:"right",color:"#8492a6","font-size":"13px"}},Kt={class:"flex justify-end w-full"},Gt={class:"gva-search-box"},Qt={class:"flex gap-2 w-full"},Zt={class:"absolute right-0"},$t={style:{float:"right",color:"#8492a6","font-size":"13px"}},ea={class:"gva-search-box"},ta={class:"text-lg ml-auto mr-4 font-normal"},aa={class:"p-4"},la={class:"border-b border-gray-200 last:border-0"},oa={class:"border-b border-gray-200 last:border-0"},na={class:"border-b border-gray-200 last:border-0"},ia={class:"last:pb-0"},ra={class:"flex items-center gap-4"},da={class:"gva-table-box"},ua={class:"gva-btn-list"},sa={class:"draggable"},ca={class:"gva-btn-list justify-end mt-4"},fa={class:"flex justify-between items-center"},pa={class:"flex justify-between items-center"},ma=Object.assign({name:"AutoCode"},{__name:"index",setup(L){const X=v().token,F=()=>{document.addEventListener("keydown",W),document.addEventListener("paste",q)},Y=()=>{document.removeEventListener("keydown",W),document.removeEventListener("paste",q)},W=e=>{(e.ctrlKey||e.metaKey)&&"Enter"===e.key&&H()},q=e=>{const t=e.clipboardData.items;for(let a=0;a<t.length;a++)if(-1!==t[a].type.indexOf("image")){const e=t[a].getAsFile(),l=new FileReader;l.onload=async e=>{const t=e.target.result,a=await i({picture:t,command:"eye"});0===a.code&&(z.value=a.data,H())},l.readAsDataURL(e)}},J=()=>{let e="";for(;e.length<16;)e+=Math.random().toString(16).substring(2);return e.substring(0,16)},z=h(""),H=async e=>{var t;if(e&&!ue.value.structName)return void B.error("请输入结构体名称");if(!e&&!z.value)return void B.error("请输入描述");if(ue.value.fields.length>0){if("confirm"!==await U.confirm("AI生成会清空当前数据，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}))return}const l=await r({prompt:e?"结构体名称为"+ue.value.structName:z.value});if(0===l.code){ue.value.fields=[];const e=JSON.parse(l.data);null==(t=e.fields)||t.forEach((e=>{e.fieldName=a(e.fieldName)}));for(let t in e)ue.value[t]=e[t];ue.value.generateServer=!0,ue.value.generateWeb=!0}},K=h(!1);g((()=>{(()=>{const e=document.querySelector(".draggable .el-table__body-wrapper tbody");Tt.create(e,{draggable:".draggable .el-table__row",handle:".drag-column",onEnd:async({newIndex:e,oldIndex:t})=>{await M();const a=ue.value.fields.splice(t,1)[0];ue.value.fields.splice(e,0,a)}})})()}));const G=["id","created_at","updated_at","deleted_at"],Q=["created_by","updated_by","deleted_by"],Z=h([{label:"字符串",value:"string"},{label:"富文本",value:"richtext"},{label:"整型",value:"int"},{label:"布尔值",value:"bool"},{label:"浮点型",value:"float64"},{label:"时间",value:"time.Time"},{label:"枚举",value:"enum"},{label:"单图片（字符串）",value:"picture"},{label:"多图片（json字符串）",value:"pictures"},{label:"视频（字符串）",value:"video"},{label:"文件（json字符串）",value:"file"},{label:"JSON",value:"json"},{label:"数组",value:"array"}]),$=h([{label:"=",value:"="},{label:"<>",value:"<>"},{label:">",value:">"},{label:"<",value:"<"},{label:"LIKE",value:"LIKE"},{label:"BETWEEN",value:"BETWEEN"},{label:"NOT BETWEEN",value:"NOT BETWEEN"}]),ee=h([{label:"index",value:"index"},{label:"uniqueIndex",value:"uniqueIndex"}]),te={fieldName:"",fieldDesc:"",fieldType:"",dataType:"",fieldJson:"",columnName:"",dataTypeLong:"",comment:"",defaultValue:"",require:!1,sort:!1,form:!0,desc:!0,table:!0,excel:!1,errorText:"",primaryKey:!1,clearable:!0,fieldSearchType:"",fieldIndexType:"",dictType:"",dataSource:{dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}},ae=b(),le=y(),oe=h({}),ne=h({businessDB:"",dbName:"",tableName:""}),ie=h([]),re=h(""),de=h({}),ue=h({structName:"",tableName:"",packageName:"",package:"",abbreviation:"",description:"",businessDB:"",autoCreateApiToSql:!0,autoCreateMenuToSql:!0,autoCreateBtnAuth:!1,autoMigrate:!0,gvaModel:!0,autoCreateResource:!1,onlyTemplate:!1,isTree:!1,generateWeb:!0,generateServer:!0,treeJson:"",fields:[]}),se=h({structName:[{required:!0,message:"请输入结构体名称",trigger:"blur"}],abbreviation:[{required:!0,message:"请输入结构体简称",trigger:"blur"}],description:[{required:!0,message:"请输入结构体描述",trigger:"blur"}],packageName:[{required:!0,message:"文件名称：sysXxxxXxxx",trigger:"blur"}],package:[{required:!0,message:"请选择package",trigger:"blur"}]}),ce=h({}),fe=h({}),pe=h(!1),me=h(!1),ve=e=>{e&&ue.value.fields.length&&U.confirm("如果您开启GVA默认结构，会自动添加ID,CreatedAt,UpdatedAt,DeletedAt字段，此行为将自动清除您目前在下方创建的重名字段，是否继续？","注意",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((()=>{ue.value.fields=ue.value.fields.filter((e=>!G.some((t=>t===e.columnName))))})).catch((()=>{ue.value.gvaModel=!1}))},he=h(null),ge=()=>{he.value.selectText()},be=()=>{he.value.copy()},ye=e=>{pe.value=!0,e?(re.value="edit",e.dataSource||(e.dataSource={dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}),fe.value=JSON.parse(JSON.stringify(e)),ce.value=e):(re.value="add",te.onlyNumber=J(),ce.value=JSON.parse(JSON.stringify(te)))},we=h(null),_e=()=>{we.value.fieldDialogForm.validate((e=>{if(!e)return!1;ce.value.fieldName=a(ce.value.fieldName),"add"===re.value&&ue.value.fields.push(ce.value),pe.value=!1}))},Te=()=>{"edit"===re.value&&(ce.value=fe.value),pe.value=!1},Ne=h(null),De=async e=>{if(ue.value.isTree&&!ue.value.treeJson)return B({type:"error",message:"请填写树型结构的前端展示json属性"}),!1;if(!ue.value.generateWeb&&!ue.value.generateServer)return B({type:"error",message:"请至少选择一个生成项"}),!1;if(!ue.value.onlyTemplate){if(ue.value.fields.length<=0)return B({type:"error",message:"请填写至少一个field"}),!1;if(!ue.value.gvaModel&&ue.value.fields.every((e=>!e.primaryKey)))return B({type:"error",message:"您至少需要创建一个主键才能保证自动化代码的可行性"}),!1;if(ue.value.fields.some((e=>e.fieldName===ue.value.structName)))return B({type:"error",message:"存在与结构体同名的字段"}),!1;if(ue.value.fields.some((e=>e.fieldJson===ue.value.package)))return B({type:"error",message:"存在与模板同名的的字段JSON"}),!1;if(ue.value.fields.some((e=>!e.fieldType)))return B({type:"error",message:"请填写所有字段类型后进行提交"}),!1;if(ue.value.package===ue.value.abbreviation)return B({type:"error",message:"package和结构体简称不可同名"}),!1}Ne.value.validate((async t=>{var l;if(t){for(const e in ue.value)"string"==typeof ue.value[e]&&(ue.value[e]=ue.value[e].trim());if(ue.value.structName=a(ue.value.structName),ue.value.tableName=ue.value.tableName.replace(" ",""),ue.value.tableName||(ue.value.tableName=n(o(ue.value.structName))),ue.value.structName===ue.value.abbreviation)return B({type:"error",message:"structName和struct简称不能相同"}),!1;if(ue.value.humpPackageName=n(ue.value.packageName),null==(l=ue.value.fields)||l.forEach((e=>{if(e.fieldName=a(e.fieldName),"enum"===e.fieldType){e.dataTypeLong=e.dataTypeLong.replace(/[\[\]{}()]/g,"");const t=e.dataTypeLong.split(",");t.forEach(((e,a)=>{-1===e.indexOf("'")&&(t[a]="'".concat(e,"'"))})),e.dataTypeLong=t.join(",")}})),delete ue.value.primaryField,e){const e=await f({...ue.value,isAdd:!!K.value,fields:ue.value.fields.filter((e=>!e.disabled))});if(0!==e.code)return;oe.value=e.data.autoCode,me.value=!0}else{if(0!==(await p(ue.value)).code)return;B({type:"success",message:"自动化代码创建成功，自动移动成功"}),Be()}}}))},Ee=h([]),xe=h([]),Se=async()=>{ne.value.dbName="",ne.value.tableName="";const e=await d({businessDB:ne.value.businessDB});0===e.code&&(xe.value=e.data.dbs,Ee.value=e.data.dbList)},Ce=async()=>{const e=await u({businessDB:ne.value.businessDB,dbName:ne.value.dbName});0===e.code&&(ie.value=e.data.tables),ne.value.tableName=""},Ve=async()=>{const e=await s(ne.value);if(0===e.code){let t="";if(""!==ne.value.businessDB){const e=Ee.value.find((e=>e.aliasName===ne.value.businessDB)),a=P(e);t=a.dbtype}ue.value.gvaModel=!1;const n=l(ne.value.tableName);ue.value.structName=a(n),ue.value.tableName=ne.value.tableName,ue.value.packageName=o(n),ue.value.abbreviation=o(n),ue.value.description=n+"表",ue.value.autoCreateApiToSql=!0,ue.value.generateServer=!0,ue.value.generateWeb=!0,ue.value.fields=[],e.data.columns&&e.data.columns.forEach((e=>{if(ke(e)){const o=l(e.columnName);ue.value.fields.push({onlyNumber:J(),fieldName:a(o),fieldDesc:e.columnComment||o+"字段",fieldType:de.value[e.dataType],dataType:e.dataType,fieldJson:o,primaryKey:e.primaryKey,dataTypeLong:e.dataTypeLong&&e.dataTypeLong.split(",")[0],columnName:"oracle"===t?e.columnName.toUpperCase():e.columnName,comment:e.columnComment,require:!1,errorText:"",clearable:!0,fieldSearchType:"",fieldIndexType:"",dictType:"",form:!0,table:!0,excel:!1,desc:!0,dataSource:{dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}})}}))}},ke=e=>{let t=!0;return ue.value.gvaModel&&G.some((t=>t===e.columnName))&&(t=!1),ue.value.autoCreateResource&&Q.some((t=>t===e.columnName))&&(t=!1),t},Ae=h([]),Oe=async()=>{const e=await c();0===e.code&&(Ae.value=e.data.pkgs)},Ie=()=>{le.push({name:"autoPkg"})},Me=()=>{Se(),(async()=>{["string","int","bool","float64","time.Time"].forEach((async e=>{const t=await R(e);t&&t.forEach((t=>{de.value[t.label]=e}))}))})(),Oe();const e=ae.params.id;e&&(async e=>{const t=await m({id:Number(e)});if(0===t.code){const e=ae.query.isAdd;K.value=e,ue.value=JSON.parse(t.data.meta),K.value&&ue.value.fields.forEach((e=>{e.disabled=!0}))}})(e)};Me(),w((()=>ae.params.id),(()=>{"autoCodeEdit"===ae.name&&Me()})),w((()=>ue.value.generateServer),(()=>{ue.value.generateServer||(ue.value.autoCreateApiToSql=!1,ue.value.autoMigrate=!1)})),w((()=>ue.value.generateWeb),(()=>{ue.value.generateWeb||(ue.value.autoCreateMenuToSql=!1,ue.value.autoCreateBtnAuth=!1)}));const Be=async()=>{ue.value={structName:"",tableName:"",packageName:"",package:"",abbreviation:"",description:"",businessDB:"",autoCreateApiToSql:!0,autoCreateMenuToSql:!0,autoCreateBtnAuth:!1,autoMigrate:!0,gvaModel:!0,autoCreateResource:!1,onlyTemplate:!1,isTree:!1,treeJson:"",fields:[]},await M(),window.sessionStorage.removeItem("autoCode")};(()=>{const e=window.sessionStorage.getItem("autoCode");e&&(ue.value=JSON.parse(e))})();const Ue=e=>{const t=new FileReader;return t.onload=e=>{try{ue.value=JSON.parse(e.target.result),B.success("JSON 文件导入成功")}catch(t){B.error("无效的 JSON 文件")}},t.readAsText(e),!1};w((()=>ue.value.onlyTemplate),(e=>{e&&U.confirm("使用基础模板将不会生成任何结构体和CURD,仅仅配置enter等属性方便自行开发非CURD逻辑","注意",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((()=>{ue.value.fields=[]})).catch((()=>{ue.value.onlyTemplate=!1}))}));const Pe=(e,t)=>{if("richtext"===e)return"LIKE"!==t;if("string"!==e&&"LIKE"===t)return!0;return!(["int","time.Time","float64"].includes(e)||!["BETWEEN","NOT BETWEEN"].includes(t))};return(a,l)=>{const n=_("el-input"),r=_("ai-gva"),d=_("el-icon"),u=_("el-button"),s=_("el-tooltip"),c=_("QuestionFilled"),f=_("el-option"),p=_("el-select"),m=_("el-form-item"),v=_("el-col"),h=_("el-row"),g=_("el-form"),b=_("refresh"),y=_("document-add"),w=_("el-checkbox"),M=_("el-collapse-item"),B=_("el-collapse"),P=_("MoreFilled"),R=_("el-table-column"),L=_("el-table"),W=_("el-upload"),q=_("el-drawer");return N(),T("div",null,[D(j,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请点我观看。"}),K.value?E("",!0):(N(),T("div",Yt,[l[38]||(l[38]=x("div",{class:"text-lg mb-2 text-gray-600"},[S(" 使用AI创建"),x("a",{class:"text-blue-600 text-sm ml-4",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"获取AiPath")],-1)),x("div",Wt,[D(n,{modelValue:z.value,"onUpdate:modelValue":l[0]||(l[0]=e=>z.value=e),type:"textarea",rows:5,maxlength:2e3,placeholder:"现已完全免费\n试试复制一张图片然后按下ctrl+v或者commend+v\n试试描述你的表，让AI帮你完成。\n此功能需要到插件市场个人中心获取自己的AI-Path，把AI-Path填入config.yaml下的autocode--\x3eai-path，重启项目即可使用。\n按下 Ctrl+Enter 或 Cmd+Enter 直接生成",resize:"none",onFocus:F,onBlur:Y},null,8,["modelValue"]),x("div",qt,[D(s,{effect:"light"},{content:C((()=>l[34]||(l[34]=[x("div",null,[S(" 【完全免费】前往"),x("a",{class:"text-blue-600",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"插件市场个人中心"),S("申请AIPath，填入config.yaml的ai-path属性即可使用。 ")],-1)]))),default:C((()=>[D(u,{disabled:ue.value.onlyTemplate,type:"primary",onClick:l[1]||(l[1]=e=>(async()=>{const e=document.createElement("input");e.type="file",e.accept="image/*",e.onchange=e=>{const t=e.target.files[0];if(t){const e=new FileReader;e.onload=async e=>{const t=e.target.result,a=await i({picture:t,command:"eye"});0===a.code&&(z.value=a.data,H())},e.readAsDataURL(t)}},e.click()})())},{default:C((()=>[D(d,{size:"18"},{default:C((()=>[D(r)])),_:1}),l[35]||(l[35]=S(" 识图 "))])),_:1},8,["disabled"])])),_:1})]),x("div",Jt,[D(s,{effect:"light"},{content:C((()=>l[36]||(l[36]=[x("div",null,[S(" 【完全免费】前往"),x("a",{class:"text-blue-600",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"插件市场个人中心"),S("申请AIPath，填入config.yaml的ai-path属性即可使用。 ")],-1)]))),default:C((()=>[D(u,{disabled:ue.value.onlyTemplate,type:"primary",onClick:l[2]||(l[2]=e=>H())},{default:C((()=>[D(d,{size:"18"},{default:C((()=>[D(r)])),_:1}),l[37]||(l[37]=S(" 生成 "))])),_:1},8,["disabled"])])),_:1})])])])),K.value?E("",!0):(N(),T("div",zt,[l[41]||(l[41]=x("div",{class:"text-lg mb-2 text-gray-600"},"从数据库创建",-1)),D(g,{ref:"getTableForm",inline:!0,model:ne.value,"label-width":"120px"},{default:C((()=>[D(h,{class:"w-full"},{default:C((()=>[D(v,{span:6},{default:C((()=>[D(m,{label:"业务库",prop:"selectDBtype",class:"w-full"},{label:C((()=>[D(s,{content:"注：需要提前到db-list自行配置多数据库，如未配置需配置后重启服务方可使用。（此处可选择对应库表，可理解为从哪个库选择表）",placement:"bottom",effect:"light"},{default:C((()=>[x("div",null,[l[39]||(l[39]=S(" 业务库 ")),D(d,null,{default:C((()=>[D(c)])),_:1})])])),_:1})])),default:C((()=>[D(p,{modelValue:ne.value.businessDB,"onUpdate:modelValue":l[3]||(l[3]=e=>ne.value.businessDB=e),clearable:"",placeholder:"选择业务库",onChange:Se,class:"w-full"},{default:C((()=>[(N(!0),T(V,null,k(Ee.value,(e=>(N(),A(f,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:C((()=>[x("div",null,[x("span",null,O(e.aliasName),1),x("span",Ht,O(e.dbName),1)])])),_:2},1032,["value","label","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"数据库名",prop:"structName",class:"w-full"},{default:C((()=>[D(p,{modelValue:ne.value.dbName,"onUpdate:modelValue":l[4]||(l[4]=e=>ne.value.dbName=e),clearable:"",filterable:"",placeholder:"请选择数据库",class:"w-full",onChange:Ce},{default:C((()=>[(N(!0),T(V,null,k(xe.value,(e=>(N(),A(f,{key:e.database,label:e.database,value:e.database},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"表名",prop:"structName",class:"w-full"},{default:C((()=>[D(p,{modelValue:ne.value.tableName,"onUpdate:modelValue":l[5]||(l[5]=e=>ne.value.tableName=e),disabled:!ne.value.dbName,class:"w-full",filterable:"",placeholder:"请选择表"},{default:C((()=>[(N(!0),T(V,null,k(ie.value,(e=>(N(),A(f,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{class:"w-full"},{default:C((()=>[x("div",Kt,[D(u,{type:"primary",onClick:Ve},{default:C((()=>l[40]||(l[40]=[S(" 使用此表 ")]))),_:1})])])),_:1})])),_:1})])),_:1})])),_:1},8,["model"])])),x("div",Gt,[l[46]||(l[46]=x("div",{class:"text-lg mb-2 text-gray-600"},"自动化结构",-1)),D(g,{disabled:K.value,ref_key:"autoCodeForm",ref:Ne,rules:se.value,model:ue.value,"label-width":"120px",inline:!0},{default:C((()=>[D(h,{class:"w-full"},{default:C((()=>[D(v,{span:6},{default:C((()=>[D(m,{label:"结构名称",prop:"structName",class:"w-full"},{default:C((()=>[x("div",Qt,[D(n,{modelValue:ue.value.structName,"onUpdate:modelValue":l[6]||(l[6]=e=>ue.value.structName=e),placeholder:"首字母自动转换大写"},null,8,["modelValue"]),D(u,{disabled:ue.value.onlyTemplate,type:"primary",onClick:l[7]||(l[7]=e=>H(!0))},{default:C((()=>[D(d,{size:"18"},{default:C((()=>[D(r)])),_:1}),l[42]||(l[42]=S(" 生成 "))])),_:1},8,["disabled"])])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"TableName",class:"w-full"},{label:C((()=>[D(s,{content:"简称会作为入参对象名和路由group",placement:"bottom",effect:"light"},{default:C((()=>[x("div",null,[l[43]||(l[43]=S(" 结构简称 ")),D(d,null,{default:C((()=>[D(c)])),_:1})])])),_:1})])),default:C((()=>[D(n,{modelValue:ue.value.abbreviation,"onUpdate:modelValue":l[8]||(l[8]=e=>ue.value.abbreviation=e),placeholder:"请输入Struct简称"},null,8,["modelValue"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"中文名称",prop:"description",class:"w-full"},{default:C((()=>[D(n,{modelValue:ue.value.description,"onUpdate:modelValue":l[9]||(l[9]=e=>ue.value.description=e),placeholder:"中文描述作为自动api描述"},null,8,["modelValue"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"表名",prop:"tableName",class:"w-full"},{default:C((()=>[D(n,{modelValue:ue.value.tableName,"onUpdate:modelValue":l[10]||(l[10]=e=>ue.value.tableName=e),placeholder:"指定表名（非必填）"},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),D(h,{class:"w-full"},{default:C((()=>[D(v,{span:6},{default:C((()=>[D(m,{prop:"packageName",class:"w-full"},{label:C((()=>[D(s,{content:"生成文件的默认名称(建议为驼峰格式,首字母小写,如sysXxxXxxx)",placement:"bottom",effect:"light"},{default:C((()=>[x("div",null,[l[44]||(l[44]=S(" 文件名称 ")),D(d,null,{default:C((()=>[D(c)])),_:1})])])),_:1})])),default:C((()=>[D(n,{modelValue:ue.value.packageName,"onUpdate:modelValue":l[11]||(l[11]=e=>ue.value.packageName=e),placeholder:"请输入文件名称",onBlur:l[12]||(l[12]=e=>{var t,a;(t=ue.value)[a="packageName"]=o(t[a])})},null,8,["modelValue"])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"选择模板",prop:"package",class:"w-full relative"},{default:C((()=>[D(p,{modelValue:ue.value.package,"onUpdate:modelValue":l[13]||(l[13]=e=>ue.value.package=e),class:"w-full pr-12"},{default:C((()=>[(N(!0),T(V,null,k(Ae.value,(e=>(N(),A(f,{key:e.ID,value:e.packageName,label:e.packageName},null,8,["value","label"])))),128))])),_:1},8,["modelValue"]),x("span",Zt,[D(d,{class:"cursor-pointer ml-2 text-gray-600",onClick:Oe},{default:C((()=>[D(b)])),_:1}),D(d,{class:"cursor-pointer ml-2 text-gray-600",onClick:Ie},{default:C((()=>[D(y)])),_:1})])])),_:1})])),_:1}),D(v,{span:6},{default:C((()=>[D(m,{label:"业务库",prop:"businessDB",class:"w-full"},{label:C((()=>[D(s,{content:"注：需要提前到db-list自行配置多数据库，此项为空则会使用gva本库创建自动化代码(global.GVA_DB),填写后则会创建指定库的代码(global.MustGetGlobalDBByDBName(dbname))",placement:"bottom",effect:"light"},{default:C((()=>[x("div",null,[l[45]||(l[45]=S(" 业务库 ")),D(d,null,{default:C((()=>[D(c)])),_:1})])])),_:1})])),default:C((()=>[D(p,{modelValue:ue.value.businessDB,"onUpdate:modelValue":l[14]||(l[14]=e=>ue.value.businessDB=e),placeholder:"选择业务库",class:"w-full"},{default:C((()=>[(N(!0),T(V,null,k(Ee.value,(e=>(N(),A(f,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:C((()=>[x("div",null,[x("span",null,O(e.aliasName),1),x("span",$t,O(e.dbName),1)])])),_:2},1032,["value","label","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["disabled","rules","model"])]),x("div",ea,[D(B,{class:"no-border-collapse"},{default:C((()=>[D(M,null,{title:C((()=>l[47]||(l[47]=[x("div",{class:"text-lg text-gray-600 font-normal"}," 专家模式 ",-1)]))),icon:C((({isActive:e})=>[x("span",ta,O(e?"收起":"展开"),1)])),default:C((()=>[x("div",aa,[x("div",la,[l[48]||(l[48]=x("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"基础设置",-1)),D(h,{gutter:20},{default:C((()=>[D(v,{span:3},{default:C((()=>[D(s,{content:"注：会自动在结构体global.Model其中包含主键和软删除相关操作配置",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"使用GVA结构"},{default:C((()=>[D(w,{modelValue:ue.value.gvaModel,"onUpdate:modelValue":l[15]||(l[15]=e=>ue.value.gvaModel=e),onChange:ve},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(s,{content:"注：会自动产生页面内的按钮权限配置，若不在角色管理中进行按钮分配则按钮不可见",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"创建按钮权限"},{default:C((()=>[D(w,{disabled:!ue.value.generateWeb,modelValue:ue.value.autoCreateBtnAuth,"onUpdate:modelValue":l[16]||(l[16]=e=>ue.value.autoCreateBtnAuth=e)},null,8,["disabled","modelValue"])])),_:1})])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(m,{label:"生成前端"},{default:C((()=>[D(w,{modelValue:ue.value.generateWeb,"onUpdate:modelValue":l[17]||(l[17]=e=>ue.value.generateWeb=e)},null,8,["modelValue"])])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(m,{label:"生成后端"},{default:C((()=>[D(w,{disabled:"",modelValue:ue.value.generateServer,"onUpdate:modelValue":l[18]||(l[18]=e=>ue.value.generateServer=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})]),x("div",oa,[l[49]||(l[49]=x("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"自动化设置",-1)),D(h,{gutter:20},{default:C((()=>[D(v,{span:3},{default:C((()=>[D(s,{content:"注：把自动生成的API注册进数据库",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"自动创建API"},{default:C((()=>[D(w,{disabled:!ue.value.generateServer,modelValue:ue.value.autoCreateApiToSql,"onUpdate:modelValue":l[19]||(l[19]=e=>ue.value.autoCreateApiToSql=e)},null,8,["disabled","modelValue"])])),_:1})])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(s,{content:"注：把自动生成的菜单注册进数据库",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"自动创建菜单"},{default:C((()=>[D(w,{disabled:!ue.value.generateWeb,modelValue:ue.value.autoCreateMenuToSql,"onUpdate:modelValue":l[20]||(l[20]=e=>ue.value.autoCreateMenuToSql=e)},null,8,["disabled","modelValue"])])),_:1})])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(s,{content:"注：自动同步数据库表结构，如果不需要可以选择关闭",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"同步表结构"},{default:C((()=>[D(w,{disabled:!ue.value.generateServer,modelValue:ue.value.autoMigrate,"onUpdate:modelValue":l[21]||(l[21]=e=>ue.value.autoMigrate=e)},null,8,["disabled","modelValue"])])),_:1})])),_:1})])),_:1})])),_:1})]),x("div",na,[l[50]||(l[50]=x("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"高级设置",-1)),D(h,{gutter:20},{default:C((()=>[D(v,{span:3},{default:C((()=>[D(s,{content:"注：会自动在结构体添加 created_by updated_by deleted_by，方便用户进行资源权限控制",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"创建资源标识"},{default:C((()=>[D(w,{modelValue:ue.value.autoCreateResource,"onUpdate:modelValue":l[22]||(l[22]=e=>ue.value.autoCreateResource=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),D(v,{span:3},{default:C((()=>[D(s,{content:"注：使用基础模板将不会生成任何结构体和CURD,仅仅配置enter等属性方便自行开发非CURD逻辑",placement:"top",effect:"light"},{default:C((()=>[D(m,{label:"基础模板"},{default:C((()=>[D(w,{modelValue:ue.value.onlyTemplate,"onUpdate:modelValue":l[23]||(l[23]=e=>ue.value.onlyTemplate=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1})]),x("div",ia,[l[51]||(l[51]=x("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"树形结构设置",-1)),D(h,{gutter:20,align:"middle"},{default:C((()=>[D(v,{span:24},{default:C((()=>[D(m,{label:"树型结构"},{default:C((()=>[x("div",ra,[D(s,{content:"注：会自动创建parentID来进行父子关系关联,仅支持主键为int类型",placement:"top",effect:"light"},{default:C((()=>[D(w,{modelValue:ue.value.isTree,"onUpdate:modelValue":l[24]||(l[24]=e=>ue.value.isTree=e)},null,8,["modelValue"])])),_:1}),D(n,{modelValue:ue.value.treeJson,"onUpdate:modelValue":l[25]||(l[25]=e=>ue.value.treeJson=e),disabled:!ue.value.isTree,placeholder:"前端展示json属性",class:"flex-1"},null,8,["modelValue","disabled"])])])),_:1})])),_:1})])),_:1})])])])),_:1})])),_:1})]),x("div",da,[x("div",ua,[D(u,{type:"primary",onClick:l[26]||(l[26]=e=>ye()),disabled:ue.value.onlyTemplate},{default:C((()=>l[52]||(l[52]=[S(" 新增字段 ")]))),_:1},8,["disabled"])]),x("div",sa,[D(L,{data:ue.value.fields,"row-key":"fieldName"},{default:C((()=>[K.value?E("",!0):(N(),A(R,{key:0,fixed:"left",align:"left",type:"index",width:"60"},{default:C((()=>[D(d,{class:"cursor-grab drag-column"},{default:C((()=>[D(P)])),_:1})])),_:1})),D(R,{fixed:"left",align:"left",type:"index",label:"序列",width:"60"}),D(R,{fixed:"left",align:"left",type:"index",label:"主键",width:"60"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.primaryKey,"onUpdate:modelValue":t=>e.primaryKey=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{fixed:"left",align:"left",prop:"fieldName",label:"字段名称",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.fieldName,"onUpdate:modelValue":t=>e.fieldName=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"fieldDesc",label:"中文名",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.fieldDesc,"onUpdate:modelValue":t=>e.fieldDesc=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"defaultValue",label:"默认值",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.defaultValue,"onUpdate:modelValue":t=>e.defaultValue=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"require",label:"必填"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.require,"onUpdate:modelValue":t=>e.require=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"sort",label:"排序"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.sort,"onUpdate:modelValue":t=>e.sort=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"form",width:"100",label:"新建/编辑"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.form,"onUpdate:modelValue":t=>e.form=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"table",label:"表格"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.table,"onUpdate:modelValue":t=>e.table=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"desc",label:"详情"},{default:C((({row:e})=>[D(w,{disabled:e.disabled,modelValue:e.desc,"onUpdate:modelValue":t=>e.desc=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),K.value?E("",!0):(N(),A(R,{key:1,align:"left",prop:"excel",width:"100",label:"导入/导出"},{default:C((({row:e})=>[D(w,{modelValue:e.excel,"onUpdate:modelValue":t=>e.excel=t},null,8,["modelValue","onUpdate:modelValue"])])),_:1})),D(R,{align:"left",prop:"fieldJson",width:"160px",label:"字段Json"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.fieldJson,"onUpdate:modelValue":t=>e.fieldJson=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"fieldType",label:"字段类型",width:"160"},{default:C((({row:e})=>[D(p,{modelValue:e.fieldType,"onUpdate:modelValue":t=>e.fieldType=t,style:{width:"100%"},placeholder:"请选择字段类型",disabled:e.disabled,clearable:""},{default:C((()=>[(N(!0),T(V,null,k(Z.value,(e=>(N(),A(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),D(R,{align:"left",prop:"fieldIndexType",label:"索引类型",width:"160"},{default:C((({row:e})=>[D(p,{modelValue:e.fieldIndexType,"onUpdate:modelValue":t=>e.fieldIndexType=t,style:{width:"100%"},placeholder:"请选择字段索引类型",disabled:e.disabled,clearable:""},{default:C((()=>[(N(!0),T(V,null,k(ee.value,(e=>(N(),A(f,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),D(R,{align:"left",prop:"dataTypeLong",label:"字段长度/枚举值",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.dataTypeLong,"onUpdate:modelValue":t=>e.dataTypeLong=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"columnName",label:"数据库字段",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.columnName,"onUpdate:modelValue":t=>e.columnName=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"comment",label:"数据库字段描述",width:"160"},{default:C((({row:e})=>[D(n,{disabled:e.disabled,modelValue:e.comment,"onUpdate:modelValue":t=>e.comment=t},null,8,["disabled","modelValue","onUpdate:modelValue"])])),_:1}),D(R,{align:"left",prop:"fieldSearchType",label:"搜索条件",width:"130"},{default:C((({row:e})=>[D(p,{modelValue:e.fieldSearchType,"onUpdate:modelValue":t=>e.fieldSearchType=t,style:{width:"100%"},placeholder:"请选择字段查询条件",clearable:"",disabled:"json"!==e.fieldType||e.disabled},{default:C((()=>[(N(!0),T(V,null,k($.value,(t=>(N(),A(f,{key:t.value,label:t.label,value:t.value,disabled:Pe(e.fieldType,t.value)},null,8,["label","value","disabled"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])])),_:1}),D(R,{align:"left",label:"操作",width:"300",fixed:"right"},{default:C((e=>[e.row.disabled?E("",!0):(N(),A(u,{key:0,type:"primary",link:"",icon:"edit",onClick:t=>ye(e.row)},{default:C((()=>l[53]||(l[53]=[S(" 高级编辑 ")]))),_:2},1032,["onClick"])),e.row.disabled?E("",!0):(N(),A(u,{key:1,type:"primary",link:"",icon:"delete",onClick:t=>{return a=e.$index,void U.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{ue.value.fields.splice(a,1)}));var a}},{default:C((()=>l[54]||(l[54]=[S(" 删除 ")]))),_:2},1032,["onClick"]))])),_:1})])),_:1},8,["data"])]),x("div",ca,[D(u,{type:"primary",disabled:K.value,onClick:l[27]||(l[27]=e=>(()=>{const e=JSON.stringify(ue.value,null,2),t=new Blob([e],{type:"application/json"}),a=URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="form_data.json",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a)})())},{default:C((()=>l[55]||(l[55]=[S(" 导出json ")]))),_:1},8,["disabled"]),D(W,{class:"flex items-center","before-upload":Ue,"show-file-list":!1,headers:{"x-token":I(X)},accept:".json"},{default:C((()=>[D(u,{type:"primary",class:"mx-2",disabled:K.value},{default:C((()=>l[56]||(l[56]=[S("导入json")]))),_:1},8,["disabled"])])),_:1},8,["headers"]),D(u,{type:"primary",disabled:K.value,onClick:l[28]||(l[28]=e=>Be())},{default:C((()=>l[57]||(l[57]=[S(" 清除暂存 ")]))),_:1},8,["disabled"]),D(u,{type:"primary",disabled:K.value,onClick:l[29]||(l[29]=e=>{window.sessionStorage.setItem("autoCode",JSON.stringify(ue.value))})},{default:C((()=>l[58]||(l[58]=[S(" 暂存 ")]))),_:1},8,["disabled"]),D(u,{type:"primary",disabled:K.value,onClick:l[30]||(l[30]=e=>De(!1))},{default:C((()=>l[59]||(l[59]=[S(" 生成代码 ")]))),_:1},8,["disabled"]),D(u,{type:"primary",onClick:l[31]||(l[31]=e=>De(!0))},{default:C((()=>[S(O(K.value?"查看代码":"预览代码"),1)])),_:1})])]),D(q,{modelValue:pe.value,"onUpdate:modelValue":l[32]||(l[32]=e=>pe.value=e),size:"70%","show-close":!1},{header:C((()=>[x("div",fa,[l[62]||(l[62]=x("span",{class:"text-lg"},"组件内容",-1)),x("div",null,[D(u,{onClick:Te},{default:C((()=>l[60]||(l[60]=[S(" 取 消 ")]))),_:1}),D(u,{type:"primary",onClick:_e},{default:C((()=>l[61]||(l[61]=[S(" 确 定 ")]))),_:1})])])])),default:C((()=>[pe.value?(N(),A(e,{key:0,ref_key:"fieldDialogNode",ref:we,"dialog-middle":ce.value,"type-options":Z.value,"type-search-options":$.value,"type-index-options":ee.value},null,8,["dialog-middle","type-options","type-search-options","type-index-options"])):E("",!0)])),_:1},8,["modelValue"]),D(q,{modelValue:me.value,"onUpdate:modelValue":l[33]||(l[33]=e=>me.value=e),size:"80%","show-close":!1},{header:C((()=>[x("div",pa,[l[65]||(l[65]=x("span",{class:"text-lg"},"操作栏",-1)),x("div",null,[D(u,{type:"primary",onClick:ge},{default:C((()=>l[63]||(l[63]=[S(" 全选 ")]))),_:1}),D(u,{type:"primary",onClick:be},{default:C((()=>l[64]||(l[64]=[S(" 复制 ")]))),_:1})])])])),default:C((()=>[me.value?(N(),A(t,{key:0,"is-add":K.value,ref_key:"previewNode",ref:he,"preview-code":oe.value},null,8,["is-add","preview-code"])):E("",!0)])),_:1},8,["modelValue"])])}}});export{ma as default};
