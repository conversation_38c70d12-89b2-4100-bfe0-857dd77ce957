/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},o=Object.prototype,i=o.hasOwnProperty,a=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(r){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,i=Object.create(o.prototype),u=new N(n||[]);return a(i,"_invoke",{value:C(t,r,u)}),i}function d(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var y="suspendedStart",p="suspendedYield",v="executing",m="completed",g={};function w(){}function x(){}function b(){}var E={};f(E,c,(function(){return this}));var _=Object.getPrototypeOf,j=_&&_(_(O([])));j&&j!==o&&i.call(j,c)&&(E=j);var L=b.prototype=w.prototype=Object.create(E);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function B(e,r){function n(o,a,u,c){var l=d(e[o],e,a);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==t(f)&&i.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){s.value=t,u(s)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,e){function i(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(i,i):i()}})}function C(t,e,n){var o=y;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:r,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=I(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===y)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var l=d(t,e,n);if("normal"===l.type){if(o=n.done?m:p,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function I(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,I(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=d(o,t.iterator,e.arg);if("throw"===i.type)return e.method="throw",e.arg=i.arg,e.delegate=null,g;var a=i.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[c];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,a=function t(){for(;++o<e.length;)if(i.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+" is not iterable")}return x.prototype=b,a(L,"constructor",{value:b,configurable:!0}),a(b,"constructor",{value:x,configurable:!0}),x.displayName=f(b,s,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===x||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,f(t,s,"GeneratorFunction")),t.prototype=Object.create(L),t},n.awrap=function(t){return{__await:t}},k(B.prototype),f(B.prototype,l,(function(){return this})),n.AsyncIterator=B,n.async=function(t,e,r,o,i){void 0===i&&(i=Promise);var a=new B(h(t,e,r,o),i);return n.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},k(L),f(L,s,"Generator"),f(L,c,(function(){return this})),f(L,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=O,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(D),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return u.type="throw",u.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;D(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:O(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0tools-legacy.CqJup9rn.js","./087AC4D233B64EB0index-legacy.DaierbDO.js","./087AC4D233B64EB0index-legacy.EWbaRXSJ.js","./087AC4D233B64EB0index-legacy.DxvfuVcx.js","./087AC4D233B64EB0title-legacy.ByWsba41.js","./087AC4D233B64EB0doc-legacy.yzTEdl6U.js","./087AC4D233B64EB0normalMode-legacy.D9acfsIM.js","./087AC4D233B64EB0index-legacy.Dvj98d63.js","./087AC4D233B64EB0menuItem-legacy.fHn_s5TX.js","./087AC4D233B64EB0asyncSubmenu-legacy.BIXEZreJ.js","./087AC4D233B64EB0headMode-legacy.Cdf26N3I.js","./087AC4D233B64EB0combinationMode-legacy.1C56VBRH.js"],(function(t,n){"use strict";var o,i,a,u,c,l,s,f,h,d,y,p,v,m,g,w,x,b,E,_,j,L,k,B,C,I,A;return{setters:[function(t){o=t.k,i=t.u,a=t.aj,u=t.I,c=t.J,l=t.K,s=t.g,f=t.c,h=t.o,d=t.b,y=t.a7,p=t.d,v=t.i,m=t.v,g=t.n,w=t.t,x=t.a8,b=t.w,E=t.F,_=t.D,j=t.h,L=t.al,k=t.f,B=t.am},function(t){C=t.default},function(t){I=t.C},function(t){A=t.default},null,null,null,null,null,null,null,null,null],execute:function(){var n={class:"flex justify-between fixed top-0 left-0 right-0 z-10 h-16 bg-white text-slate-700 dark:text-slate-300 dark:bg-slate-900 shadow dark:shadow-gray-700 items-center px-2"},D={class:"flex items-center cursor-pointer flex-1"},N=["src"],O={class:"ml-2 flex items-center"},S={class:"flex justify-center items-center h-full w-full"},G={class:"cursor-pointer flex justify-center items-center text-black dark:text-gray-100"},P={class:"font-bold"};t("default",{__name:"index",setup:function(t){var T=o(),F=i(),M=a(),U=u(),V=c(U),Y=V.device,$=V.config,q=l((function(){return"mobile"===Y.value})),z=function(){F.push({name:"person"})},H=l((function(){return M.meta.matched})),J=function(){var t,n=(t=e().mark((function t(r){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,B({authorityId:r});case 2:0===t.sent.code&&(window.sessionStorage.setItem("needCloseAll","true"),window.sessionStorage.setItem("needToHome","true"),window.location.reload());case 4:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(o,i){var a=t.apply(e,n);function u(t){r(a,o,i,u,c,"next",t)}function c(t){r(a,o,i,u,c,"throw",t)}u(void 0)}))});return function(t){return n.apply(this,arguments)}}();return function(t,e){var r=s("el-breadcrumb-item"),o=s("el-breadcrumb"),i=s("arrow-down"),a=s("el-icon"),u=s("el-dropdown-item"),c=s("el-dropdown-menu"),l=s("el-dropdown");return h(),f("div",n,[d("div",D,[d("div",{class:g(["flex items-center cursor-pointer",q.value?"":"min-w-48"]),onClick:e[0]||(e[0]=function(t){return m(F).push({path:"/"})})},[d("img",{alt:"",class:"h-12 bg-white rounded-full",src:t.$GIN_VUE_ADMIN.appLogo},null,8,N),q.value?p("",!0):(h(),f("div",{key:0,class:g(["inline-flex font-bold text-2xl ml-2",("head"===m($).side_mode||"combination"===m($).side_mode)&&"min-w-fit"])},w(t.$GIN_VUE_ADMIN.appName),3))],2),"head"!==m($).side_mode&&"combination"!==m($).side_mode?y((h(),v(o,{key:0,class:"ml-4"},{default:b((function(){return[(h(!0),f(E,null,_(H.value.slice(1,H.value.length),(function(t){return h(),v(r,{key:t.path},{default:b((function(){return[j(w(m(L)(t.meta.title,m(M))),1)]})),_:2},1024)})),128))]})),_:1},512)),[[x,!q.value]]):p("",!0),"head"!==m($).side_mode||q.value?p("",!0):(h(),v(A,{key:1,class:"flex-1"})),"combination"!==m($).side_mode||q.value?p("",!0):(h(),v(A,{key:2,mode:"head",class:"flex-1"}))]),d("div",O,[k(C),k(l,null,{dropdown:b((function(){return[k(c,null,{default:b((function(){return[k(u,null,{default:b((function(){return[d("span",P," 当前角色："+w(m(T).userInfo.authority.authorityName),1)]})),_:1}),m(T).userInfo.authorities?(h(!0),f(E,{key:0},_(m(T).userInfo.authorities.filter((function(t){return t.authorityId!==m(T).userInfo.authorityId})),(function(t){return h(),v(u,{key:t.authorityId,onClick:function(e){return J(t.authorityId)}},{default:b((function(){return[d("span",null," 切换为："+w(t.authorityName),1)]})),_:2},1032,["onClick"])})),128)):p("",!0),k(u,{icon:"avatar",onClick:z},{default:b((function(){return e[1]||(e[1]=[j(" 个人信息 ")])})),_:1}),k(u,{icon:"reading-lamp",onClick:m(T).LoginOut},{default:b((function(){return e[2]||(e[2]=[j(" 登 出 ")])})),_:1},8,["onClick"])]})),_:1})]})),default:b((function(){return[d("div",S,[d("span",G,[k(I),y(d("span",{class:"w-16"},w(m(T).userInfo.nickName),513),[[x,!q.value]]),k(a,null,{default:b((function(){return[k(i)]})),_:1})])])]})),_:1})])])}}})}}}))}();
