/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},u="function"==typeof Symbol?Symbol:{},l=u.iterator||"@@iterator",c=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function p(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(r){p=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var o=t&&t.prototype instanceof y?t:y,a=Object.create(o.prototype),u=new z(n||[]);return i(a,"_invoke",{value:O(e,r,u)}),a}function _(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=d;var f="suspendedStart",g="suspendedYield",v="executing",h="completed",m={};function y(){}function b(){}function w(){}var k={};p(k,l,(function(){return this}));var x=Object.getPrototypeOf,j=x&&x(x(P([])));j&&j!==o&&a.call(j,l)&&(k=j);var V=w.prototype=y.prototype=Object.create(k);function L(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function E(t,r){function n(o,i,u,l){var c=_(t[o],t,i);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==e(p)&&a.call(p,"__await")?r.resolve(p.__await).then((function(e){n("next",e,u,l)}),(function(e){n("throw",e,u,l)})):r.resolve(p).then((function(e){s.value=e,u(s)}),(function(e){return n("throw",e,u,l)}))}l(c.arg)}var o;i(this,"_invoke",{value:function(e,t){function a(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(a,a):a()}})}function O(e,t,n){var o=f;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===h){if("throw"===a)throw i;return{value:r,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var l=D(u,n);if(l){if(l===m)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===f)throw o=h,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var c=_(e,t,n);if("normal"===c.type){if(o=n.done?h:g,c.arg===m)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=h,n.method="throw",n.arg=c.arg)}}}function D(e,t){var n=t.method,o=e.iterator[n];if(o===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,D(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;var a=_(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,m;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,m):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,m)}function U(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function z(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(U,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function e(){for(;++o<t.length;)if(a.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=r,e.done=!0,e};return i.next=i}}throw new TypeError(e(t)+" is not iterable")}return b.prototype=w,i(V,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:b,configurable:!0}),b.displayName=p(w,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,p(e,s,"GeneratorFunction")),e.prototype=Object.create(V),e},n.awrap=function(e){return{__await:e}},L(E.prototype),p(E.prototype,c,(function(){return this})),n.AsyncIterator=E,n.async=function(e,t,r,o,a){void 0===a&&(a=Promise);var i=new E(d(e,t,r,o),a);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},L(V),p(V,s,"Generator"),p(V,l,(function(){return this})),p(V,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=P,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,o){return u.type="throw",u.arg=e,t.next=n,o&&(t.method="next",t.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:P(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),m}},n}function r(e,t,r,n,o,a,i){try{var u=e[a](i),l=u.value}catch(e){return void r(e)}u.done?t(l):Promise.resolve(l).then(n,o)}function n(e){return function(){var t=this,n=arguments;return new Promise((function(o,a){var i=e.apply(t,n);function u(e){r(i,o,a,u,l,"next",e)}function l(e){r(i,o,a,u,l,"throw",e)}u(void 0)}))}}System.register(["./087AC4D233B64EB0arrayCtrl-legacy.C4_FsKwQ.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,r){"use strict";var o,a,i,u,l,c,s,p,d,_,f,g,v,h,m,y,b;return{setters:[function(e){o=e._,a=e.f,i=e.c,u=e.u},function(e){l=e.aj,c=e.u,s=e.a,p=e.r,d=e.g,_=e.c,f=e.o,g=e.b,v=e.f,h=e.w,m=e.h,y=e.t,b=e.E}],execute:function(){var r={class:"gva-form-box"};e("default",Object.assign({name:"OzoneOrderDetailForm"},{__name:"ozoneOrderDetailForm",setup:function(e){var w=l(),k=c(),x=s(!1),j=s(""),V=s({addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}),L=p({}),E=s(),O=function(){var e=n(t().mark((function e(){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!w.query.id){e.next=7;break}return e.next=3,a({ID:w.query.id});case 3:0===(r=e.sent).code&&(V.value=r.data,j.value="update"),e.next=8;break;case 7:j.value="create";case 8:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();O();var D=function(){var e=n(t().mark((function e(){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:x.value=!0,null===(r=E.value)||void 0===r||r.validate(function(){var e=n(t().mark((function e(r){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r){e.next=2;break}return e.abrupt("return",x.value=!1);case 2:e.t0=j.value,e.next="create"===e.t0?5:"update"===e.t0?9:13;break;case 5:return e.next=7,i(V.value);case 7:return n=e.sent,e.abrupt("break",17);case 9:return e.next=11,u(V.value);case 11:return n=e.sent,e.abrupt("break",17);case 13:return e.next=15,i(V.value);case 15:return n=e.sent,e.abrupt("break",17);case 17:x.value=!1,0===n.code&&b({type:"success",message:"创建/更改成功"});case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),U=function(){k.go(-1)};return function(e,t){var n=d("el-form-item"),a=d("el-date-picker"),i=d("el-switch"),u=d("el-input"),l=d("el-button"),c=d("el-form");return f(),_("div",null,[g("div",r,[v(c,{model:V.value,ref_key:"elFormRef",ref:E,"label-position":"right",rules:L,"label-width":"80px"},{default:h((function(){return[v(n,{label:"收件人联系方式:",prop:"addressee"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取 "+y(V.value.addressee),1)]})),_:1}),v(n,{label:"分析数据:",prop:"analytics_data"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取 "+y(V.value.analytics_data),1)]})),_:1}),v(n,{label:"买家信息:",prop:"customer"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取 "+y(V.value.customer),1)]})),_:1}),v(n,{label:"货件条码:",prop:"barcodes"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取 "+y(V.value.barcodes),1)]})),_:1}),v(n,{label:"货件交付物流的时间:",prop:"delivering_date"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取 "+y(V.value.delivering_date),1)]})),_:1}),v(n,{label:"快递方式:",prop:"delivery_method"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取 "+y(V.value.delivery_method),1)]})),_:1}),v(n,{label:"有关商品成本、折扣幅度、付款和佣金的信息:",prop:"financial_data"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取 "+y(V.value.financial_data),1)]})),_:1}),v(n,{label:"开始处理货件的日期和时间:",prop:"in_process_at"},{default:h((function(){return[v(a,{modelValue:V.value.in_process_at,"onUpdate:modelValue":t[0]||(t[0]=function(e){return V.value.in_process_at=e}),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),v(n,{label:"如果使用快速物流 Ozon Express —— true:",prop:"is_express"},{default:h((function(){return[v(i,{modelValue:V.value.is_express,"onUpdate:modelValue":t[1]||(t[1]=function(e){return V.value.is_express=e}),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"是","inactive-text":"否",clearable:""},null,8,["modelValue"])]})),_:1}),v(n,{label:"带有附加特征的商品列表:",prop:"optional"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取 "+y(V.value.optional),1)]})),_:1}),v(n,{label:"货件所属订单的ID:",prop:"order_id"},{default:h((function(){return[v(u,{modelValue:V.value.order_id,"onUpdate:modelValue":t[2]||(t[2]=function(e){return V.value.order_id=e}),modelModifiers:{number:!0},clearable:!0,placeholder:"请输入"},null,8,["modelValue"])]})),_:1}),v(n,{label:"货件所属的订单号:",prop:"order_number"},{default:h((function(){return[v(u,{modelValue:V.value.order_number,"onUpdate:modelValue":t[3]||(t[3]=function(e){return V.value.order_number=e}),clearable:!0,placeholder:"请输入货件所属的订单号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"快递母件编号，从该母件中拆分出了当前货件:",prop:"parent_posting_number"},{default:h((function(){return[v(u,{modelValue:V.value.parent_posting_number,"onUpdate:modelValue":t[4]||(t[4]=function(e){return V.value.parent_posting_number=e}),clearable:!0,placeholder:"请输入快递母件编号，从该母件中拆分出了当前货件"},null,8,["modelValue"])]})),_:1}),v(n,{label:"货件号:",prop:"posting_number"},{default:h((function(){return[v(u,{modelValue:V.value.posting_number,"onUpdate:modelValue":t[5]||(t[5]=function(e){return V.value.posting_number=e}),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"货运商品列表:",prop:"products"},{default:h((function(){return[v(o,{modelValue:V.value.products,"onUpdate:modelValue":t[6]||(t[6]=function(e){return V.value.products=e}),editable:""},null,8,["modelValue"])]})),_:1}),v(n,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:",prop:"requirements"},{default:h((function(){return[m(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取 "+y(V.value.requirements),1)]})),_:1}),v(n,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:",prop:"shipment_date"},{default:h((function(){return[v(a,{modelValue:V.value.shipment_date,"onUpdate:modelValue":t[7]||(t[7]=function(e){return V.value.shipment_date=e}),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),v(n,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:",prop:"status"},{default:h((function(){return[v(u,{modelValue:V.value.status,"onUpdate:modelValue":t[8]||(t[8]=function(e){return V.value.status=e}),clearable:!0,placeholder:"请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])]})),_:1}),v(n,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:",prop:"substatus"},{default:h((function(){return[v(u,{modelValue:V.value.substatus,"onUpdate:modelValue":t[9]||(t[9]=function(e){return V.value.substatus=e}),clearable:!0,placeholder:"请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])]})),_:1}),v(n,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:",prop:"tpl_integration_type"},{default:h((function(){return[v(u,{modelValue:V.value.tpl_integration_type,"onUpdate:modelValue":t[10]||(t[10]=function(e){return V.value.tpl_integration_type=e}),clearable:!0,placeholder:"请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},null,8,["modelValue"])]})),_:1}),v(n,{label:"货件跟踪号:",prop:"tracking_number"},{default:h((function(){return[v(u,{modelValue:V.value.tracking_number,"onUpdate:modelValue":t[11]||(t[11]=function(e){return V.value.tracking_number=e}),clearable:!0,placeholder:"请输入货件跟踪号"},null,8,["modelValue"])]})),_:1}),v(n,{label:"发运的计费信息:",prop:"tariffication\t"},{default:h((function(){return[v(o,{modelValue:V.value.tariffication,"onUpdate:modelValue":t[12]||(t[12]=function(e){return V.value.tariffication=e}),editable:""},null,8,["modelValue"])]})),_:1}),v(n,null,{default:h((function(){return[v(l,{loading:x.value,type:"primary",onClick:D},{default:h((function(){return t[13]||(t[13]=[m("保存")])})),_:1},8,["loading"]),v(l,{type:"primary",onClick:U},{default:h((function(){return t[14]||(t[14]=[m("返回")])})),_:1})]})),_:1})]})),_:1},8,["model","rules"])])])}}}))}}}))}();
