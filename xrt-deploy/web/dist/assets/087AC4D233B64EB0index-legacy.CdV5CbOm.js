/*! 
 Build based on gin-vue-admin 
 Time : 1753520196000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(t,n,a){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0!==a){var r=a.call(t,n||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[n]=a,t}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function(){return n};var t,n={},a=Object.prototype,o=a.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function f(e,t,n,a){var r=t&&t.prototype instanceof y?t:y,o=Object.create(r.prototype),i=new A(a||[]);return l(o,"_invoke",{value:k(e,n,i)}),o}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var v="suspendedStart",m="suspendedYield",h="executing",g="completed",b={};function y(){}function w(){}function _(){}var x={};d(x,u,(function(){return this}));var E=Object.getPrototypeOf,T=E&&E(E(I([])));T&&T!==a&&o.call(T,u)&&(x=T);var N=_.prototype=y.prototype=Object.create(x);function S(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function D(t,n){function a(r,l,i,u){var c=p(t[r],t,l);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==e(d)&&o.call(d,"__await")?n.resolve(d.__await).then((function(e){a("next",e,i,u)}),(function(e){a("throw",e,i,u)})):n.resolve(d).then((function(e){s.value=e,i(s)}),(function(e){return a("throw",e,i,u)}))}u(c.arg)}var r;l(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,r){a(e,t,n,r)}))}return r=r?r.then(o,o):o()}})}function k(e,n,a){var r=v;return function(o,l){if(r===h)throw Error("Generator is already running");if(r===g){if("throw"===o)throw l;return{value:t,done:!0}}for(a.method=o,a.arg=l;;){var i=a.delegate;if(i){var u=C(i,a);if(u){if(u===b)continue;return u}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(r===v)throw r=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);r=h;var c=p(e,n,a);if("normal"===c.type){if(r=a.done?g:m,c.arg===b)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(r=g,a.method="throw",a.arg=c.arg)}}}function C(e,n){var a=n.method,r=e.iterator[a];if(r===t)return n.delegate=null,"throw"===a&&e.iterator.return&&(n.method="return",n.arg=t,C(e,n),"throw"===n.method)||"return"!==a&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+a+"' method")),b;var o=p(r,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,b;var l=o.arg;return l?l.done?(n[e.resultName]=l.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,b):l:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function V(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(V,this),this.reset(!0)}function I(n){if(n||""===n){var a=n[u];if(a)return a.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var r=-1,l=function e(){for(;++r<n.length;)if(o.call(n,r))return e.value=n[r],e.done=!1,e;return e.value=t,e.done=!0,e};return l.next=l}}throw new TypeError(e(n)+" is not iterable")}return w.prototype=_,l(N,"constructor",{value:_,configurable:!0}),l(_,"constructor",{value:w,configurable:!0}),w.displayName=d(_,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,d(e,s,"GeneratorFunction")),e.prototype=Object.create(N),e},n.awrap=function(e){return{__await:e}},S(D.prototype),d(D.prototype,c,(function(){return this})),n.AsyncIterator=D,n.async=function(e,t,a,r,o){void 0===o&&(o=Promise);var l=new D(f(e,t,a,r),o);return n.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},S(N),d(N,s,"Generator"),d(N,u,(function(){return this})),d(N,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},n.values=I,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function a(a,r){return i.type="throw",i.arg=e,n.next=a,r&&(n.method="next",n.arg=t),!!r}for(var r=this.tryEntries.length-1;r>=0;--r){var l=this.tryEntries[r],i=l.completion;if("root"===l.tryLoc)return a("end");if(l.tryLoc<=this.prev){var u=o.call(l,"catchLoc"),c=o.call(l,"finallyLoc");if(u&&c){if(this.prev<l.catchLoc)return a(l.catchLoc,!0);if(this.prev<l.finallyLoc)return a(l.finallyLoc)}else if(u){if(this.prev<l.catchLoc)return a(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return a(l.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&o.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var r=a;break}}r&&("break"===e||"continue"===e)&&r.tryLoc<=t&&t<=r.finallyLoc&&(r=null);var l=r?r.completion:{};return l.type=e,l.arg=t,r?(this.method="next",this.next=r.finallyLoc,b):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var r=a.arg;O(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(e,n,a){return this.delegate={iterator:I(e),resultName:n,nextLoc:a},"next"===this.method&&(this.arg=t),b}},n}function o(e,t,n,a,r,o,l){try{var i=e[o](l),u=i.value}catch(e){return void n(e)}i.done?t(u):Promise.resolve(u).then(a,r)}function l(e){return function(){var t=this,n=arguments;return new Promise((function(a,r){var l=e.apply(t,n);function i(e){o(l,a,r,i,u,"next",e)}function u(e){o(l,a,r,i,u,"throw",e)}i(void 0)}))}}System.register(["./087AC4D233B64EB0fieldDialog-legacy.CFA0eJ5t.js","./087AC4D233B64EB0previewCodeDialog-legacy.BwEOuo3M.js","./087AC4D233B64EB0stringFun-legacy.2vIcgB7Q.js","./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js"],(function(e,t){"use strict";var a,o,i,u,c,s,d,f,p,v,m,h,g,b,y,w,_,x,E,T,N,S,D,k,C,V,O,A,I,P,M,B,U,j,L,R,F,X,Y,W;return{setters:[function(e){a=e.default},function(e){o=e.default},function(e){i=e.b,u=e.c,c=e.a,s=e.t},function(e){d=e.e,f=e.l,p=e.g,v=e.c,m=e.a,h=e.d,g=e.p,b=e.f,y=e.h},function(e){w=e.k,_=e.a,x=e.p,E=e.aj,T=e.u,N=e.Q,S=e.g,D=e.c,k=e.o,C=e.f,V=e.d,O=e.b,A=e.h,I=e.w,P=e.F,M=e.D,B=e.i,U=e.t,j=e.v,L=e.T,R=e.E,F=e.ab,X=e.b7,Y=e.b8},function(e){W=e._}],execute:function(){var t=document.createElement("style");
/**!
       * Sortable 1.15.6
       * <AUTHOR>   <<EMAIL>>
       * <AUTHOR>    <<EMAIL>>
       * @license MIT
       */
function q(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function J(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q(Object(n),!0).forEach((function(t){G(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function z(e){return z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},z(e)}function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function H(){return H=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},H.apply(this,arguments)}function K(e,t){if(null==e)return{};var n,a,r=function(e,t){if(null==e)return{};var n,a,r={},o=Object.keys(e);for(a=0;a<o.length;a++)n=o[a],t.indexOf(n)>=0||(r[n]=e[n]);return r}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(a=0;a<o.length;a++)n=o[a],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}t.textContent=".no-border-collapse,.no-border-collapse .el-collapse-item__header,.no-border-collapse .el-collapse-item__wrap{border-style:none}.no-border-collapse .el-collapse-item__content{padding-bottom:0}\n/*$vite$:1*/",document.head.appendChild(t);function $(e){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(e)}var Q=$(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),Z=$(/Edge/i),ee=$(/firefox/i),te=$(/safari/i)&&!$(/chrome/i)&&!$(/android/i),ne=$(/iP(ad|od|hone)/i),ae=$(/chrome/i)&&$(/android/i),re={capture:!1,passive:!1};function oe(e,t,n){e.addEventListener(t,n,!Q&&re)}function le(e,t,n){e.removeEventListener(t,n,!Q&&re)}function ie(e,t){if(t){if(">"===t[0]&&(t=t.substring(1)),e)try{if(e.matches)return e.matches(t);if(e.msMatchesSelector)return e.msMatchesSelector(t);if(e.webkitMatchesSelector)return e.webkitMatchesSelector(t)}catch(n){return!1}return!1}}function ue(e){return e.host&&e!==document&&e.host.nodeType?e.host:e.parentNode}function ce(e,t,n,a){if(e){n=n||document;do{if(null!=t&&(">"===t[0]?e.parentNode===n&&ie(e,t):ie(e,t))||a&&e===n)return e;if(e===n)break}while(e=ue(e))}return null}var se,de=/\s+/g;function fe(e,t,n){if(e&&t)if(e.classList)e.classList[n?"add":"remove"](t);else{var a=(" "+e.className+" ").replace(de," ").replace(" "+t+" "," ");e.className=(a+(n?" "+t:"")).replace(de," ")}}function pe(e,t,n){var a=e&&e.style;if(a){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(e,""):e.currentStyle&&(n=e.currentStyle),void 0===t?n:n[t];t in a||-1!==t.indexOf("webkit")||(t="-webkit-"+t),a[t]=n+("string"==typeof n?"":"px")}}function ve(e,t){var n="";if("string"==typeof e)n=e;else do{var a=pe(e,"transform");a&&"none"!==a&&(n=a+" "+n)}while(!t&&(e=e.parentNode));var r=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return r&&new r(n)}function me(e,t,n){if(e){var a=e.getElementsByTagName(t),r=0,o=a.length;if(n)for(;r<o;r++)n(a[r],r);return a}return[]}function he(){var e=document.scrollingElement;return e||document.documentElement}function ge(e,t,n,a,r){if(e.getBoundingClientRect||e===window){var o,l,i,u,c,s,d;if(e!==window&&e.parentNode&&e!==he()?(l=(o=e.getBoundingClientRect()).top,i=o.left,u=o.bottom,c=o.right,s=o.height,d=o.width):(l=0,i=0,u=window.innerHeight,c=window.innerWidth,s=window.innerHeight,d=window.innerWidth),(t||n)&&e!==window&&(r=r||e.parentNode,!Q))do{if(r&&r.getBoundingClientRect&&("none"!==pe(r,"transform")||n&&"static"!==pe(r,"position"))){var f=r.getBoundingClientRect();l-=f.top+parseInt(pe(r,"border-top-width")),i-=f.left+parseInt(pe(r,"border-left-width")),u=l+o.height,c=i+o.width;break}}while(r=r.parentNode);if(a&&e!==window){var p=ve(r||e),v=p&&p.a,m=p&&p.d;p&&(u=(l/=m)+(s/=m),c=(i/=v)+(d/=v))}return{top:l,left:i,bottom:u,right:c,width:d,height:s}}}function be(e,t,n){for(var a=Ee(e,!0),r=ge(e)[t];a;){if(!(r>=ge(a)[n]))return a;if(a===he())break;a=Ee(a,!1)}return!1}function ye(e,t,n,a){for(var r=0,o=0,l=e.children;o<l.length;){if("none"!==l[o].style.display&&l[o]!==Dt.ghost&&(a||l[o]!==Dt.dragged)&&ce(l[o],n.draggable,e,!1)){if(r===t)return l[o];r++}o++}return null}function we(e,t){for(var n=e.lastElementChild;n&&(n===Dt.ghost||"none"===pe(n,"display")||t&&!ie(n,t));)n=n.previousElementSibling;return n||null}function _e(e,t){var n=0;if(!e||!e.parentNode)return-1;for(;e=e.previousElementSibling;)"TEMPLATE"===e.nodeName.toUpperCase()||e===Dt.clone||t&&!ie(e,t)||n++;return n}function xe(e){var t=0,n=0,a=he();if(e)do{var r=ve(e),o=r.a,l=r.d;t+=e.scrollLeft*o,n+=e.scrollTop*l}while(e!==a&&(e=e.parentNode));return[t,n]}function Ee(e,t){if(!e||!e.getBoundingClientRect)return he();var n=e,a=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var r=pe(n);if(n.clientWidth<n.scrollWidth&&("auto"==r.overflowX||"scroll"==r.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==r.overflowY||"scroll"==r.overflowY)){if(!n.getBoundingClientRect||n===document.body)return he();if(a||t)return n;a=!0}}}while(n=n.parentNode);return he()}function Te(e,t){return Math.round(e.top)===Math.round(t.top)&&Math.round(e.left)===Math.round(t.left)&&Math.round(e.height)===Math.round(t.height)&&Math.round(e.width)===Math.round(t.width)}function Ne(e,t){return function(){if(!se){var n=arguments;1===n.length?e.call(this,n[0]):e.apply(this,n),se=setTimeout((function(){se=void 0}),t)}}}function Se(e,t,n){e.scrollLeft+=t,e.scrollTop+=n}function De(e){var t=window.Polymer,n=window.jQuery||window.Zepto;return t&&t.dom?t.dom(e).cloneNode(!0):n?n(e).clone(!0)[0]:e.cloneNode(!0)}function ke(e,t,n){var a={};return Array.from(e.children).forEach((function(r){var o,l,i,u;if(ce(r,t.draggable,e,!1)&&!r.animated&&r!==n){var c=ge(r);a.left=Math.min(null!==(o=a.left)&&void 0!==o?o:1/0,c.left),a.top=Math.min(null!==(l=a.top)&&void 0!==l?l:1/0,c.top),a.right=Math.max(null!==(i=a.right)&&void 0!==i?i:-1/0,c.right),a.bottom=Math.max(null!==(u=a.bottom)&&void 0!==u?u:-1/0,c.bottom)}})),a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}var Ce="Sortable"+(new Date).getTime();function Ve(){var e,t=[];return{captureAnimationState:function(){(t=[],this.options.animation)&&[].slice.call(this.el.children).forEach((function(e){if("none"!==pe(e,"display")&&e!==Dt.ghost){t.push({target:e,rect:ge(e)});var n=J({},t[t.length-1].rect);if(e.thisAnimationDuration){var a=ve(e,!0);a&&(n.top-=a.f,n.left-=a.e)}e.fromRect=n}}))},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(e,t){for(var n in e)if(e.hasOwnProperty(n))for(var a in t)if(t.hasOwnProperty(a)&&t[a]===e[n][a])return Number(n);return-1}(t,{target:e}),1)},animateAll:function(n){var a=this;if(!this.options.animation)return clearTimeout(e),void("function"==typeof n&&n());var r=!1,o=0;t.forEach((function(e){var t=0,n=e.target,l=n.fromRect,i=ge(n),u=n.prevFromRect,c=n.prevToRect,s=e.rect,d=ve(n,!0);d&&(i.top-=d.f,i.left-=d.e),n.toRect=i,n.thisAnimationDuration&&Te(u,i)&&!Te(l,i)&&(s.top-i.top)/(s.left-i.left)==(l.top-i.top)/(l.left-i.left)&&(t=function(e,t,n,a){return Math.sqrt(Math.pow(t.top-e.top,2)+Math.pow(t.left-e.left,2))/Math.sqrt(Math.pow(t.top-n.top,2)+Math.pow(t.left-n.left,2))*a.animation}(s,u,c,a.options)),Te(i,l)||(n.prevFromRect=l,n.prevToRect=i,t||(t=a.options.animation),a.animate(n,s,i,t)),t&&(r=!0,o=Math.max(o,t),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout((function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null}),t),n.thisAnimationDuration=t)})),clearTimeout(e),r?e=setTimeout((function(){"function"==typeof n&&n()}),o):"function"==typeof n&&n(),t=[]},animate:function(e,t,n,a){if(a){pe(e,"transition",""),pe(e,"transform","");var r=ve(this.el),o=r&&r.a,l=r&&r.d,i=(t.left-n.left)/(o||1),u=(t.top-n.top)/(l||1);e.animatingX=!!i,e.animatingY=!!u,pe(e,"transform","translate3d("+i+"px,"+u+"px,0)"),this.forRepaintDummy=function(e){return e.offsetWidth}(e),pe(e,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),pe(e,"transform","translate3d(0,0,0)"),"number"==typeof e.animated&&clearTimeout(e.animated),e.animated=setTimeout((function(){pe(e,"transition",""),pe(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1}),a)}}}}var Oe=[],Ae={initializeByDefault:!0},Ie={mount:function(e){for(var t in Ae)Ae.hasOwnProperty(t)&&!(t in e)&&(e[t]=Ae[t]);Oe.forEach((function(t){if(t.pluginName===e.pluginName)throw"Sortable: Cannot mount plugin ".concat(e.pluginName," more than once")})),Oe.push(e)},pluginEvent:function(e,t,n){var a=this;this.eventCanceled=!1,n.cancel=function(){a.eventCanceled=!0};var r=e+"Global";Oe.forEach((function(a){t[a.pluginName]&&(t[a.pluginName][r]&&t[a.pluginName][r](J({sortable:t},n)),t.options[a.pluginName]&&t[a.pluginName][e]&&t[a.pluginName][e](J({sortable:t},n)))}))},initializePlugins:function(e,t,n,a){for(var r in Oe.forEach((function(a){var r=a.pluginName;if(e.options[r]||a.initializeByDefault){var o=new a(e,t,e.options);o.sortable=e,o.options=e.options,e[r]=o,H(n,o.defaults)}})),e.options)if(e.options.hasOwnProperty(r)){var o=this.modifyOption(e,r,e.options[r]);void 0!==o&&(e.options[r]=o)}},getEventProperties:function(e,t){var n={};return Oe.forEach((function(a){"function"==typeof a.eventProperties&&H(n,a.eventProperties.call(t[a.pluginName],e))})),n},modifyOption:function(e,t,n){var a;return Oe.forEach((function(r){e[r.pluginName]&&r.optionListeners&&"function"==typeof r.optionListeners[t]&&(a=r.optionListeners[t].call(e[r.pluginName],n))})),a}};var Pe=["evt"],Me=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=n.evt,r=K(n,Pe);Ie.pluginEvent.bind(Dt)(e,t,J({dragEl:Ue,parentEl:je,ghostEl:Le,rootEl:Re,nextEl:Fe,lastDownEl:Xe,cloneEl:Ye,cloneHidden:We,dragStarted:at,putSortable:Ke,activeSortable:Dt.active,originalEvent:a,oldIndex:qe,oldDraggableIndex:ze,newIndex:Je,newDraggableIndex:Ge,hideGhostForTarget:Et,unhideGhostForTarget:Tt,cloneNowHidden:function(){We=!0},cloneNowShown:function(){We=!1},dispatchSortableEvent:function(e){Be({sortable:t,name:e,originalEvent:a})}},r))};function Be(e){!function(e){var t=e.sortable,n=e.rootEl,a=e.name,r=e.targetEl,o=e.cloneEl,l=e.toEl,i=e.fromEl,u=e.oldIndex,c=e.newIndex,s=e.oldDraggableIndex,d=e.newDraggableIndex,f=e.originalEvent,p=e.putSortable,v=e.extraEventProperties;if(t=t||n&&n[Ce]){var m,h=t.options,g="on"+a.charAt(0).toUpperCase()+a.substr(1);!window.CustomEvent||Q||Z?(m=document.createEvent("Event")).initEvent(a,!0,!0):m=new CustomEvent(a,{bubbles:!0,cancelable:!0}),m.to=l||n,m.from=i||n,m.item=r||n,m.clone=o,m.oldIndex=u,m.newIndex=c,m.oldDraggableIndex=s,m.newDraggableIndex=d,m.originalEvent=f,m.pullMode=p?p.lastPutMode:void 0;var b=J(J({},v),Ie.getEventProperties(a,t));for(var y in b)m[y]=b[y];n&&n.dispatchEvent(m),h[g]&&h[g].call(t,m)}}(J({putSortable:Ke,cloneEl:Ye,targetEl:Ue,rootEl:Re,oldIndex:qe,oldDraggableIndex:ze,newIndex:Je,newDraggableIndex:Ge},e))}var Ue,je,Le,Re,Fe,Xe,Ye,We,qe,Je,ze,Ge,He,Ke,$e,Qe,Ze,et,tt,nt,at,rt,ot,lt,it,ut=!1,ct=!1,st=[],dt=!1,ft=!1,pt=[],vt=!1,mt=[],ht="undefined"!=typeof document,gt=ne,bt=Z||Q?"cssFloat":"float",yt=ht&&!ae&&!ne&&"draggable"in document.createElement("div"),wt=function(){if(ht){if(Q)return!1;var e=document.createElement("x");return e.style.cssText="pointer-events:auto","auto"===e.style.pointerEvents}}(),_t=function(e,t){var n=pe(e),a=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),r=ye(e,0,t),o=ye(e,1,t),l=r&&pe(r),i=o&&pe(o),u=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+ge(r).width,c=i&&parseInt(i.marginLeft)+parseInt(i.marginRight)+ge(o).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(r&&l.float&&"none"!==l.float){var s="left"===l.float?"left":"right";return!o||"both"!==i.clear&&i.clear!==s?"horizontal":"vertical"}return r&&("block"===l.display||"flex"===l.display||"table"===l.display||"grid"===l.display||u>=a&&"none"===n[bt]||o&&"none"===n[bt]&&u+c>a)?"vertical":"horizontal"},xt=function(e){function t(e,n){return function(a,r,o,l){var i=a.options.group.name&&r.options.group.name&&a.options.group.name===r.options.group.name;if(null==e&&(n||i))return!0;if(null==e||!1===e)return!1;if(n&&"clone"===e)return e;if("function"==typeof e)return t(e(a,r,o,l),n)(a,r,o,l);var u=(n?a:r).options.group.name;return!0===e||"string"==typeof e&&e===u||e.join&&e.indexOf(u)>-1}}var n={},a=e.group;a&&"object"==z(a)||(a={name:a}),n.name=a.name,n.checkPull=t(a.pull,!0),n.checkPut=t(a.put),n.revertClone=a.revertClone,e.group=n},Et=function(){!wt&&Le&&pe(Le,"display","none")},Tt=function(){!wt&&Le&&pe(Le,"display","")};ht&&!ae&&document.addEventListener("click",(function(e){if(ct)return e.preventDefault(),e.stopPropagation&&e.stopPropagation(),e.stopImmediatePropagation&&e.stopImmediatePropagation(),ct=!1,!1}),!0);var Nt=function(e){if(Ue){e=e.touches?e.touches[0]:e;var t=(r=e.clientX,o=e.clientY,st.some((function(e){var t=e[Ce].options.emptyInsertThreshold;if(t&&!we(e)){var n=ge(e),a=r>=n.left-t&&r<=n.right+t,i=o>=n.top-t&&o<=n.bottom+t;return a&&i?l=e:void 0}})),l);if(t){var n={};for(var a in e)e.hasOwnProperty(a)&&(n[a]=e[a]);n.target=n.rootEl=t,n.preventDefault=void 0,n.stopPropagation=void 0,t[Ce]._onDragOver(n)}}var r,o,l},St=function(e){Ue&&Ue.parentNode[Ce]._isOutsideThisEl(e.target)};function Dt(e,t){if(!e||!e.nodeType||1!==e.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));this.el=e,this.options=t=H({},t),e[Ce]=this;var n={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(e.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return _t(e,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(e,t){e.setData("Text",t.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Dt.supportPointer&&"PointerEvent"in window&&(!te||ne),emptyInsertThreshold:5};for(var a in Ie.initializePlugins(this,e,n),n)!(a in t)&&(t[a]=n[a]);for(var r in xt(t),this)"_"===r.charAt(0)&&"function"==typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!t.forceFallback&&yt,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?oe(e,"pointerdown",this._onTapStart):(oe(e,"mousedown",this._onTapStart),oe(e,"touchstart",this._onTapStart)),this.nativeDraggable&&(oe(e,"dragover",this),oe(e,"dragenter",this)),st.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),H(this,Ve())}function kt(e,t,n,a,r,o,l,i){var u,c,s=e[Ce],d=s.options.onMove;return!window.CustomEvent||Q||Z?(u=document.createEvent("Event")).initEvent("move",!0,!0):u=new CustomEvent("move",{bubbles:!0,cancelable:!0}),u.to=t,u.from=e,u.dragged=n,u.draggedRect=a,u.related=r||t,u.relatedRect=o||ge(t),u.willInsertAfter=i,u.originalEvent=l,e.dispatchEvent(u),d&&(c=d.call(s,u,l)),c}function Ct(e){e.draggable=!1}function Vt(){vt=!1}function Ot(e){for(var t=e.tagName+e.className+e.src+e.href+e.textContent,n=t.length,a=0;n--;)a+=t.charCodeAt(n);return a.toString(36)}function At(e){return setTimeout(e,0)}function It(e){return clearTimeout(e)}Dt.prototype={constructor:Dt,_isOutsideThisEl:function(e){this.el.contains(e)||e===this.el||(rt=null)},_getDirection:function(e,t){return"function"==typeof this.options.direction?this.options.direction.call(this,e,t,Ue):this.options.direction},_onTapStart:function(e){if(e.cancelable){var t=this,n=this.el,a=this.options,r=a.preventOnFilter,o=e.type,l=e.touches&&e.touches[0]||e.pointerType&&"touch"===e.pointerType&&e,i=(l||e).target,u=e.target.shadowRoot&&(e.path&&e.path[0]||e.composedPath&&e.composedPath()[0])||i,c=a.filter;if(function(e){mt.length=0;var t=e.getElementsByTagName("input"),n=t.length;for(;n--;){var a=t[n];a.checked&&mt.push(a)}}(n),!Ue&&!(/mousedown|pointerdown/.test(o)&&0!==e.button||a.disabled)&&!u.isContentEditable&&(this.nativeDraggable||!te||!i||"SELECT"!==i.tagName.toUpperCase())&&!((i=ce(i,a.draggable,n,!1))&&i.animated||Xe===i)){if(qe=_e(i),ze=_e(i,a.draggable),"function"==typeof c){if(c.call(this,e,i,this))return Be({sortable:t,rootEl:u,name:"filter",targetEl:i,toEl:n,fromEl:n}),Me("filter",t,{evt:e}),void(r&&e.preventDefault())}else if(c&&(c=c.split(",").some((function(a){if(a=ce(u,a.trim(),n,!1))return Be({sortable:t,rootEl:a,name:"filter",targetEl:i,fromEl:n,toEl:n}),Me("filter",t,{evt:e}),!0}))))return void(r&&e.preventDefault());a.handle&&!ce(u,a.handle,n,!1)||this._prepareDragStart(e,l,i)}}},_prepareDragStart:function(e,t,n){var a,r=this,o=r.el,l=r.options,i=o.ownerDocument;if(n&&!Ue&&n.parentNode===o){var u=ge(n);if(Re=o,je=(Ue=n).parentNode,Fe=Ue.nextSibling,Xe=n,He=l.group,Dt.dragged=Ue,$e={target:Ue,clientX:(t||e).clientX,clientY:(t||e).clientY},tt=$e.clientX-u.left,nt=$e.clientY-u.top,this._lastX=(t||e).clientX,this._lastY=(t||e).clientY,Ue.style["will-change"]="all",a=function(){Me("delayEnded",r,{evt:e}),Dt.eventCanceled?r._onDrop():(r._disableDelayedDragEvents(),!ee&&r.nativeDraggable&&(Ue.draggable=!0),r._triggerDragStart(e,t),Be({sortable:r,name:"choose",originalEvent:e}),fe(Ue,l.chosenClass,!0))},l.ignore.split(",").forEach((function(e){me(Ue,e.trim(),Ct)})),oe(i,"dragover",Nt),oe(i,"mousemove",Nt),oe(i,"touchmove",Nt),l.supportPointer?(oe(i,"pointerup",r._onDrop),!this.nativeDraggable&&oe(i,"pointercancel",r._onDrop)):(oe(i,"mouseup",r._onDrop),oe(i,"touchend",r._onDrop),oe(i,"touchcancel",r._onDrop)),ee&&this.nativeDraggable&&(this.options.touchStartThreshold=4,Ue.draggable=!0),Me("delayStart",this,{evt:e}),!l.delay||l.delayOnTouchOnly&&!t||this.nativeDraggable&&(Z||Q))a();else{if(Dt.eventCanceled)return void this._onDrop();l.supportPointer?(oe(i,"pointerup",r._disableDelayedDrag),oe(i,"pointercancel",r._disableDelayedDrag)):(oe(i,"mouseup",r._disableDelayedDrag),oe(i,"touchend",r._disableDelayedDrag),oe(i,"touchcancel",r._disableDelayedDrag)),oe(i,"mousemove",r._delayedDragTouchMoveHandler),oe(i,"touchmove",r._delayedDragTouchMoveHandler),l.supportPointer&&oe(i,"pointermove",r._delayedDragTouchMoveHandler),r._dragStartTimer=setTimeout(a,l.delay)}}},_delayedDragTouchMoveHandler:function(e){var t=e.touches?e.touches[0]:e;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){Ue&&Ct(Ue),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var e=this.el.ownerDocument;le(e,"mouseup",this._disableDelayedDrag),le(e,"touchend",this._disableDelayedDrag),le(e,"touchcancel",this._disableDelayedDrag),le(e,"pointerup",this._disableDelayedDrag),le(e,"pointercancel",this._disableDelayedDrag),le(e,"mousemove",this._delayedDragTouchMoveHandler),le(e,"touchmove",this._delayedDragTouchMoveHandler),le(e,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(e,t){t=t||"touch"==e.pointerType&&e,!this.nativeDraggable||t?this.options.supportPointer?oe(document,"pointermove",this._onTouchMove):oe(document,t?"touchmove":"mousemove",this._onTouchMove):(oe(Ue,"dragend",this),oe(Re,"dragstart",this._onDragStart));try{document.selection?At((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(n){}},_dragStarted:function(e,t){if(ut=!1,Re&&Ue){Me("dragStarted",this,{evt:t}),this.nativeDraggable&&oe(document,"dragover",St);var n=this.options;!e&&fe(Ue,n.dragClass,!1),fe(Ue,n.ghostClass,!0),Dt.active=this,e&&this._appendGhost(),Be({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(Qe){this._lastX=Qe.clientX,this._lastY=Qe.clientY,Et();for(var e=document.elementFromPoint(Qe.clientX,Qe.clientY),t=e;e&&e.shadowRoot&&(e=e.shadowRoot.elementFromPoint(Qe.clientX,Qe.clientY))!==t;)t=e;if(Ue.parentNode[Ce]._isOutsideThisEl(e),t)do{if(t[Ce]){if(t[Ce]._onDragOver({clientX:Qe.clientX,clientY:Qe.clientY,target:e,rootEl:t})&&!this.options.dragoverBubble)break}e=t}while(t=ue(t));Tt()}},_onTouchMove:function(e){if($e){var t=this.options,n=t.fallbackTolerance,a=t.fallbackOffset,r=e.touches?e.touches[0]:e,o=Le&&ve(Le,!0),l=Le&&o&&o.a,i=Le&&o&&o.d,u=gt&&it&&xe(it),c=(r.clientX-$e.clientX+a.x)/(l||1)+(u?u[0]-pt[0]:0)/(l||1),s=(r.clientY-$e.clientY+a.y)/(i||1)+(u?u[1]-pt[1]:0)/(i||1);if(!Dt.active&&!ut){if(n&&Math.max(Math.abs(r.clientX-this._lastX),Math.abs(r.clientY-this._lastY))<n)return;this._onDragStart(e,!0)}if(Le){o?(o.e+=c-(Ze||0),o.f+=s-(et||0)):o={a:1,b:0,c:0,d:1,e:c,f:s};var d="matrix(".concat(o.a,",").concat(o.b,",").concat(o.c,",").concat(o.d,",").concat(o.e,",").concat(o.f,")");pe(Le,"webkitTransform",d),pe(Le,"mozTransform",d),pe(Le,"msTransform",d),pe(Le,"transform",d),Ze=c,et=s,Qe=r}e.cancelable&&e.preventDefault()}},_appendGhost:function(){if(!Le){var e=this.options.fallbackOnBody?document.body:Re,t=ge(Ue,!0,gt,!0,e),n=this.options;if(gt){for(it=e;"static"===pe(it,"position")&&"none"===pe(it,"transform")&&it!==document;)it=it.parentNode;it!==document.body&&it!==document.documentElement?(it===document&&(it=he()),t.top+=it.scrollTop,t.left+=it.scrollLeft):it=he(),pt=xe(it)}fe(Le=Ue.cloneNode(!0),n.ghostClass,!1),fe(Le,n.fallbackClass,!0),fe(Le,n.dragClass,!0),pe(Le,"transition",""),pe(Le,"transform",""),pe(Le,"box-sizing","border-box"),pe(Le,"margin",0),pe(Le,"top",t.top),pe(Le,"left",t.left),pe(Le,"width",t.width),pe(Le,"height",t.height),pe(Le,"opacity","0.8"),pe(Le,"position",gt?"absolute":"fixed"),pe(Le,"zIndex","100000"),pe(Le,"pointerEvents","none"),Dt.ghost=Le,e.appendChild(Le),pe(Le,"transform-origin",tt/parseInt(Le.style.width)*100+"% "+nt/parseInt(Le.style.height)*100+"%")}},_onDragStart:function(e,t){var n=this,a=e.dataTransfer,r=n.options;Me("dragStart",this,{evt:e}),Dt.eventCanceled?this._onDrop():(Me("setupClone",this),Dt.eventCanceled||((Ye=De(Ue)).removeAttribute("id"),Ye.draggable=!1,Ye.style["will-change"]="",this._hideClone(),fe(Ye,this.options.chosenClass,!1),Dt.clone=Ye),n.cloneId=At((function(){Me("clone",n),Dt.eventCanceled||(n.options.removeCloneOnHide||Re.insertBefore(Ye,Ue),n._hideClone(),Be({sortable:n,name:"clone"}))})),!t&&fe(Ue,r.dragClass,!0),t?(ct=!0,n._loopId=setInterval(n._emulateDragOver,50)):(le(document,"mouseup",n._onDrop),le(document,"touchend",n._onDrop),le(document,"touchcancel",n._onDrop),a&&(a.effectAllowed="move",r.setData&&r.setData.call(n,a,Ue)),oe(document,"drop",n),pe(Ue,"transform","translateZ(0)")),ut=!0,n._dragStartId=At(n._dragStarted.bind(n,t,e)),oe(document,"selectstart",n),at=!0,window.getSelection().removeAllRanges(),te&&pe(document.body,"user-select","none"))},_onDragOver:function(e){var t,n,a,r,o=this.el,l=e.target,i=this.options,u=i.group,c=Dt.active,s=He===u,d=i.sort,f=Ke||c,p=this,v=!1;if(!vt){if(void 0!==e.preventDefault&&e.cancelable&&e.preventDefault(),l=ce(l,i.draggable,o,!0),C("dragOver"),Dt.eventCanceled)return v;if(Ue.contains(e.target)||l.animated&&l.animatingX&&l.animatingY||p._ignoreWhileAnimating===l)return O(!1);if(ct=!1,c&&!i.disabled&&(s?d||(a=je!==Re):Ke===this||(this.lastPutMode=He.checkPull(this,c,Ue,e))&&u.checkPut(this,c,Ue,e))){if(r="vertical"===this._getDirection(e,l),t=ge(Ue),C("dragOverValid"),Dt.eventCanceled)return v;if(a)return je=Re,V(),this._hideClone(),C("revert"),Dt.eventCanceled||(Fe?Re.insertBefore(Ue,Fe):Re.appendChild(Ue)),O(!0);var m=we(o,i.draggable);if(!m||function(e,t,n){var a=ge(we(n.el,n.options.draggable)),r=ke(n.el,n.options,Le),o=10;return t?e.clientX>r.right+o||e.clientY>a.bottom&&e.clientX>a.left:e.clientY>r.bottom+o||e.clientX>a.right&&e.clientY>a.top}(e,r,this)&&!m.animated){if(m===Ue)return O(!1);if(m&&o===e.target&&(l=m),l&&(n=ge(l)),!1!==kt(Re,o,Ue,t,l,n,e,!!l))return V(),m&&m.nextSibling?o.insertBefore(Ue,m.nextSibling):o.appendChild(Ue),je=o,A(),O(!0)}else if(m&&function(e,t,n){var a=ge(ye(n.el,0,n.options,!0)),r=ke(n.el,n.options,Le),o=10;return t?e.clientX<r.left-o||e.clientY<a.top&&e.clientX<a.right:e.clientY<r.top-o||e.clientY<a.bottom&&e.clientX<a.left}(e,r,this)){var h=ye(o,0,i,!0);if(h===Ue)return O(!1);if(n=ge(l=h),!1!==kt(Re,o,Ue,t,l,n,e,!1))return V(),o.insertBefore(Ue,h),je=o,A(),O(!0)}else if(l.parentNode===o){n=ge(l);var g,b,y,w=Ue.parentNode!==o,_=!function(e,t,n){var a=n?e.left:e.top,r=n?e.right:e.bottom,o=n?e.width:e.height,l=n?t.left:t.top,i=n?t.right:t.bottom,u=n?t.width:t.height;return a===l||r===i||a+o/2===l+u/2}(Ue.animated&&Ue.toRect||t,l.animated&&l.toRect||n,r),x=r?"top":"left",E=be(l,"top","top")||be(Ue,"top","top"),T=E?E.scrollTop:void 0;if(rt!==l&&(b=n[x],dt=!1,ft=!_&&i.invertSwap||w),g=function(e,t,n,a,r,o,l,i){var u=a?e.clientY:e.clientX,c=a?n.height:n.width,s=a?n.top:n.left,d=a?n.bottom:n.right,f=!1;if(!l)if(i&&lt<c*r){if(!dt&&(1===ot?u>s+c*o/2:u<d-c*o/2)&&(dt=!0),dt)f=!0;else if(1===ot?u<s+lt:u>d-lt)return-ot}else if(u>s+c*(1-r)/2&&u<d-c*(1-r)/2)return function(e){return _e(Ue)<_e(e)?1:-1}(t);if((f=f||l)&&(u<s+c*o/2||u>d-c*o/2))return u>s+c/2?1:-1;return 0}(e,l,n,r,_?1:i.swapThreshold,null==i.invertedSwapThreshold?i.swapThreshold:i.invertedSwapThreshold,ft,rt===l),0!==g){var N=_e(Ue);do{N-=g,y=je.children[N]}while(y&&("none"===pe(y,"display")||y===Le))}if(0===g||y===l)return O(!1);rt=l,ot=g;var S=l.nextElementSibling,D=!1,k=kt(Re,o,Ue,t,l,n,e,D=1===g);if(!1!==k)return 1!==k&&-1!==k||(D=1===k),vt=!0,setTimeout(Vt,30),V(),D&&!S?o.appendChild(Ue):l.parentNode.insertBefore(Ue,D?S:l),E&&Se(E,0,T-E.scrollTop),je=Ue.parentNode,void 0===b||ft||(lt=Math.abs(b-ge(l)[x])),A(),O(!0)}if(o.contains(Ue))return O(!1)}return!1}function C(i,u){Me(i,p,J({evt:e,isOwner:s,axis:r?"vertical":"horizontal",revert:a,dragRect:t,targetRect:n,canSort:d,fromSortable:f,target:l,completed:O,onMove:function(n,a){return kt(Re,o,Ue,t,n,ge(n),e,a)},changed:A},u))}function V(){C("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function O(t){return C("dragOverCompleted",{insertion:t}),t&&(s?c._hideClone():c._showClone(p),p!==f&&(fe(Ue,Ke?Ke.options.ghostClass:c.options.ghostClass,!1),fe(Ue,i.ghostClass,!0)),Ke!==p&&p!==Dt.active?Ke=p:p===Dt.active&&Ke&&(Ke=null),f===p&&(p._ignoreWhileAnimating=l),p.animateAll((function(){C("dragOverAnimationComplete"),p._ignoreWhileAnimating=null})),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(l===Ue&&!Ue.animated||l===o&&!l.animated)&&(rt=null),i.dragoverBubble||e.rootEl||l===document||(Ue.parentNode[Ce]._isOutsideThisEl(e.target),!t&&Nt(e)),!i.dragoverBubble&&e.stopPropagation&&e.stopPropagation(),v=!0}function A(){Je=_e(Ue),Ge=_e(Ue,i.draggable),Be({sortable:p,name:"change",toEl:o,newIndex:Je,newDraggableIndex:Ge,originalEvent:e})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){le(document,"mousemove",this._onTouchMove),le(document,"touchmove",this._onTouchMove),le(document,"pointermove",this._onTouchMove),le(document,"dragover",Nt),le(document,"mousemove",Nt),le(document,"touchmove",Nt)},_offUpEvents:function(){var e=this.el.ownerDocument;le(e,"mouseup",this._onDrop),le(e,"touchend",this._onDrop),le(e,"pointerup",this._onDrop),le(e,"pointercancel",this._onDrop),le(e,"touchcancel",this._onDrop),le(document,"selectstart",this)},_onDrop:function(e){var t=this.el,n=this.options;Je=_e(Ue),Ge=_e(Ue,n.draggable),Me("drop",this,{evt:e}),je=Ue&&Ue.parentNode,Je=_e(Ue),Ge=_e(Ue,n.draggable),Dt.eventCanceled||(ut=!1,ft=!1,dt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),It(this.cloneId),It(this._dragStartId),this.nativeDraggable&&(le(document,"drop",this),le(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),te&&pe(document.body,"user-select",""),pe(Ue,"transform",""),e&&(at&&(e.cancelable&&e.preventDefault(),!n.dropBubble&&e.stopPropagation()),Le&&Le.parentNode&&Le.parentNode.removeChild(Le),(Re===je||Ke&&"clone"!==Ke.lastPutMode)&&Ye&&Ye.parentNode&&Ye.parentNode.removeChild(Ye),Ue&&(this.nativeDraggable&&le(Ue,"dragend",this),Ct(Ue),Ue.style["will-change"]="",at&&!ut&&fe(Ue,Ke?Ke.options.ghostClass:this.options.ghostClass,!1),fe(Ue,this.options.chosenClass,!1),Be({sortable:this,name:"unchoose",toEl:je,newIndex:null,newDraggableIndex:null,originalEvent:e}),Re!==je?(Je>=0&&(Be({rootEl:je,name:"add",toEl:je,fromEl:Re,originalEvent:e}),Be({sortable:this,name:"remove",toEl:je,originalEvent:e}),Be({rootEl:je,name:"sort",toEl:je,fromEl:Re,originalEvent:e}),Be({sortable:this,name:"sort",toEl:je,originalEvent:e})),Ke&&Ke.save()):Je!==qe&&Je>=0&&(Be({sortable:this,name:"update",toEl:je,originalEvent:e}),Be({sortable:this,name:"sort",toEl:je,originalEvent:e})),Dt.active&&(null!=Je&&-1!==Je||(Je=qe,Ge=ze),Be({sortable:this,name:"end",toEl:je,originalEvent:e}),this.save())))),this._nulling()},_nulling:function(){Me("nulling",this),Re=Ue=je=Le=Fe=Ye=Xe=We=$e=Qe=at=Je=Ge=qe=ze=rt=ot=Ke=He=Dt.dragged=Dt.ghost=Dt.clone=Dt.active=null,mt.forEach((function(e){e.checked=!0})),mt.length=Ze=et=0},handleEvent:function(e){switch(e.type){case"drop":case"dragend":this._onDrop(e);break;case"dragenter":case"dragover":Ue&&(this._onDragOver(e),function(e){e.dataTransfer&&(e.dataTransfer.dropEffect="move");e.cancelable&&e.preventDefault()}(e));break;case"selectstart":e.preventDefault()}},toArray:function(){for(var e,t=[],n=this.el.children,a=0,r=n.length,o=this.options;a<r;a++)ce(e=n[a],o.draggable,this.el,!1)&&t.push(e.getAttribute(o.dataIdAttr)||Ot(e));return t},sort:function(e,t){var n={},a=this.el;this.toArray().forEach((function(e,t){var r=a.children[t];ce(r,this.options.draggable,a,!1)&&(n[e]=r)}),this),t&&this.captureAnimationState(),e.forEach((function(e){n[e]&&(a.removeChild(n[e]),a.appendChild(n[e]))})),t&&this.animateAll()},save:function(){var e=this.options.store;e&&e.set&&e.set(this)},closest:function(e,t){return ce(e,t||this.options.draggable,this.el,!1)},option:function(e,t){var n=this.options;if(void 0===t)return n[e];var a=Ie.modifyOption(this,e,t);n[e]=void 0!==a?a:t,"group"===e&&xt(n)},destroy:function(){Me("destroy",this);var e=this.el;e[Ce]=null,le(e,"mousedown",this._onTapStart),le(e,"touchstart",this._onTapStart),le(e,"pointerdown",this._onTapStart),this.nativeDraggable&&(le(e,"dragover",this),le(e,"dragenter",this)),Array.prototype.forEach.call(e.querySelectorAll("[draggable]"),(function(e){e.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),st.splice(st.indexOf(this.el),1),this.el=e=null},_hideClone:function(){if(!We){if(Me("hideClone",this),Dt.eventCanceled)return;pe(Ye,"display","none"),this.options.removeCloneOnHide&&Ye.parentNode&&Ye.parentNode.removeChild(Ye),We=!0}},_showClone:function(e){if("clone"===e.lastPutMode){if(We){if(Me("showClone",this),Dt.eventCanceled)return;Ue.parentNode!=Re||this.options.group.revertClone?Fe?Re.insertBefore(Ye,Fe):Re.appendChild(Ye):Re.insertBefore(Ye,Ue),this.options.group.revertClone&&this.animate(Ue,Ye),pe(Ye,"display",""),We=!1}}else this._hideClone()}},ht&&oe(document,"touchmove",(function(e){(Dt.active||ut)&&e.cancelable&&e.preventDefault()})),Dt.utils={on:oe,off:le,css:pe,find:me,is:function(e,t){return!!ce(e,t,e,!1)},extend:function(e,t){if(e&&t)for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e},throttle:Ne,closest:ce,toggleClass:fe,clone:De,index:_e,nextTick:At,cancelNextTick:It,detectDirection:_t,getChild:ye,expando:Ce},Dt.get=function(e){return e[Ce]},Dt.mount=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];t[0].constructor===Array&&(t=t[0]),t.forEach((function(e){if(!e.prototype||!e.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(e));e.utils&&(Dt.utils=J(J({},Dt.utils),e.utils)),Ie.mount(e)}))},Dt.create=function(e,t){return new Dt(e,t)},Dt.version="1.15.6";var Pt,Mt,Bt,Ut,jt,Lt,Rt=[],Ft=!1;function Xt(){Rt.forEach((function(e){clearInterval(e.pid)})),Rt=[]}function Yt(){clearInterval(Lt)}var Wt=Ne((function(e,t,n,a){if(t.scroll){var r,o=(e.touches?e.touches[0]:e).clientX,l=(e.touches?e.touches[0]:e).clientY,i=t.scrollSensitivity,u=t.scrollSpeed,c=he(),s=!1;Mt!==n&&(Mt=n,Xt(),Pt=t.scroll,r=t.scrollFn,!0===Pt&&(Pt=Ee(n,!0)));var d=0,f=Pt;do{var p=f,v=ge(p),m=v.top,h=v.bottom,g=v.left,b=v.right,y=v.width,w=v.height,_=void 0,x=void 0,E=p.scrollWidth,T=p.scrollHeight,N=pe(p),S=p.scrollLeft,D=p.scrollTop;p===c?(_=y<E&&("auto"===N.overflowX||"scroll"===N.overflowX||"visible"===N.overflowX),x=w<T&&("auto"===N.overflowY||"scroll"===N.overflowY||"visible"===N.overflowY)):(_=y<E&&("auto"===N.overflowX||"scroll"===N.overflowX),x=w<T&&("auto"===N.overflowY||"scroll"===N.overflowY));var k=_&&(Math.abs(b-o)<=i&&S+y<E)-(Math.abs(g-o)<=i&&!!S),C=x&&(Math.abs(h-l)<=i&&D+w<T)-(Math.abs(m-l)<=i&&!!D);if(!Rt[d])for(var V=0;V<=d;V++)Rt[V]||(Rt[V]={});Rt[d].vx==k&&Rt[d].vy==C&&Rt[d].el===p||(Rt[d].el=p,Rt[d].vx=k,Rt[d].vy=C,clearInterval(Rt[d].pid),0==k&&0==C||(s=!0,Rt[d].pid=setInterval(function(){a&&0===this.layer&&Dt.active._onTouchMove(jt);var t=Rt[this.layer].vy?Rt[this.layer].vy*u:0,n=Rt[this.layer].vx?Rt[this.layer].vx*u:0;"function"==typeof r&&"continue"!==r.call(Dt.dragged.parentNode[Ce],n,t,e,jt,Rt[this.layer].el)||Se(Rt[this.layer].el,n,t)}.bind({layer:d}),24))),d++}while(t.bubbleScroll&&f!==c&&(f=Ee(f,!1)));Ft=s}}),30),qt=function(e){var t=e.originalEvent,n=e.putSortable,a=e.dragEl,r=e.activeSortable,o=e.dispatchSortableEvent,l=e.hideGhostForTarget,i=e.unhideGhostForTarget;if(t){var u=n||r;l();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,s=document.elementFromPoint(c.clientX,c.clientY);i(),u&&!u.el.contains(s)&&(o("spill"),this.onSpill({dragEl:a,putSortable:n}))}};function Jt(){}function zt(){}Jt.prototype={startIndex:null,dragStart:function(e){var t=e.oldDraggableIndex;this.startIndex=t},onSpill:function(e){var t=e.dragEl,n=e.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState();var a=ye(this.sortable.el,this.startIndex,this.options);a?this.sortable.el.insertBefore(t,a):this.sortable.el.appendChild(t),this.sortable.animateAll(),n&&n.animateAll()},drop:qt},H(Jt,{pluginName:"revertOnSpill"}),zt.prototype={onSpill:function(e){var t=e.dragEl,n=e.putSortable||this.sortable;n.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),n.animateAll()},drop:qt},H(zt,{pluginName:"removeOnSpill"}),Dt.mount(new function(){function e(){for(var e in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this))}return e.prototype={dragStarted:function(e){var t=e.originalEvent;this.sortable.nativeDraggable?oe(document,"dragover",this._handleAutoScroll):this.options.supportPointer?oe(document,"pointermove",this._handleFallbackAutoScroll):t.touches?oe(document,"touchmove",this._handleFallbackAutoScroll):oe(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(e){var t=e.originalEvent;this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?le(document,"dragover",this._handleAutoScroll):(le(document,"pointermove",this._handleFallbackAutoScroll),le(document,"touchmove",this._handleFallbackAutoScroll),le(document,"mousemove",this._handleFallbackAutoScroll)),Yt(),Xt(),clearTimeout(se),se=void 0},nulling:function(){jt=Mt=Pt=Ft=Lt=Bt=Ut=null,Rt.length=0},_handleFallbackAutoScroll:function(e){this._handleAutoScroll(e,!0)},_handleAutoScroll:function(e,t){var n=this,a=(e.touches?e.touches[0]:e).clientX,r=(e.touches?e.touches[0]:e).clientY,o=document.elementFromPoint(a,r);if(jt=e,t||this.options.forceAutoScrollFallback||Z||Q||te){Wt(e,this.options,o,t);var l=Ee(o,!0);!Ft||Lt&&a===Bt&&r===Ut||(Lt&&Yt(),Lt=setInterval((function(){var o=Ee(document.elementFromPoint(a,r),!0);o!==l&&(l=o,Xt()),Wt(e,n.options,o,t)}),10),Bt=a,Ut=r)}else{if(!this.options.bubbleScroll||Ee(o,!0)===he())return void Xt();Wt(e,this.options,Ee(o,!1),!1)}}},H(e,{pluginName:"scroll",initializeByDefault:!0})}),Dt.mount(zt,Jt);var Gt={key:0,class:"gva-search-box"},Ht={class:"relative"},Kt={class:"flex absolute right-28 bottom-2"},$t={class:"flex absolute right-2 bottom-2"},Qt={key:1,class:"gva-search-box"},Zt={style:{float:"right",color:"#8492a6","font-size":"13px"}},en={class:"flex justify-end w-full"},tn={class:"gva-search-box"},nn={class:"flex gap-2 w-full"},an={class:"absolute right-0"},rn={style:{float:"right",color:"#8492a6","font-size":"13px"}},on={class:"gva-search-box"},ln={class:"text-lg ml-auto mr-4 font-normal"},un={class:"p-4"},cn={class:"border-b border-gray-200 last:border-0"},sn={class:"border-b border-gray-200 last:border-0"},dn={class:"border-b border-gray-200 last:border-0"},fn={class:"last:pb-0"},pn={class:"flex items-center gap-4"},vn={class:"gva-table-box"},mn={class:"gva-btn-list"},hn={class:"draggable"},gn={class:"gva-btn-list justify-end mt-4"},bn={class:"flex justify-between items-center"},yn={class:"flex justify-between items-center"};e("default",Object.assign({name:"AutoCode"},{__name:"index",setup:function(e){var t=w().token,q=function(){document.addEventListener("keydown",z),document.addEventListener("paste",G)},J=function(){document.removeEventListener("keydown",z),document.removeEventListener("paste",G)},z=function(e){(e.ctrlKey||e.metaKey)&&"Enter"===e.key&&Q()},G=function(e){for(var t=e.clipboardData.items,n=0;n<t.length;n++)if(-1!==t[n].type.indexOf("image")){var a=t[n].getAsFile(),o=new FileReader;o.onload=function(){var e=l(r().mark((function e(t){var n,a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.target.result,e.next=3,d({picture:n,command:"eye"});case 3:0===(a=e.sent).code&&(K.value=a.data,Q());case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),o.readAsDataURL(a)}},H=function(){for(var e="";e.length<16;)e+=Math.random().toString(16).substring(2);return e.substring(0,16)},K=_(""),$=function(){var e=l(r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:(t=document.createElement("input")).type="file",t.accept="image/*",t.onchange=function(e){var t=e.target.files[0];if(t){var n=new FileReader;n.onload=function(){var e=l(r().mark((function e(t){var n,a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.target.result,e.next=3,d({picture:n,command:"eye"});case 3:0===(a=e.sent).code&&(K.value=a.data,Q());case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),n.readAsDataURL(t)}},t.click();case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Q=function(){var e=l(r().mark((function e(t){var n,a,o,l;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t||pe.value.structName){e.next=3;break}return R.error("请输入结构体名称"),e.abrupt("return");case 3:if(t||K.value){e.next=6;break}return R.error("请输入描述"),e.abrupt("return");case 6:if(!(pe.value.fields.length>0)){e.next=12;break}return e.next=9,F.confirm("AI生成会清空当前数据，是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});case 9:if("confirm"===e.sent){e.next=12;break}return e.abrupt("return");case 12:return e.next=14,f({prompt:t?"结构体名称为"+pe.value.structName:K.value});case 14:if(0===(n=e.sent).code){for(l in pe.value.fields=[],o=JSON.parse(n.data),null===(a=o.fields)||void 0===a||a.forEach((function(e){e.fieldName=i(e.fieldName)})),o)pe.value[l]=o[l];pe.value.generateServer=!0,pe.value.generateWeb=!0}case 16:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Z=_(!1);x((function(){var e,t;t=document.querySelector(".draggable .el-table__body-wrapper tbody"),Dt.create(t,{draggable:".draggable .el-table__row",handle:".drag-column",onEnd:(e=l(r().mark((function e(t){var n,a,o;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.newIndex,a=t.oldIndex,e.next=3,L();case 3:o=pe.value.fields.splice(a,1)[0],pe.value.fields.splice(n,0,o);case 5:case"end":return e.stop()}}),e)}))),function(t){return e.apply(this,arguments)})})}));var ee=["id","created_at","updated_at","deleted_at"],te=["created_by","updated_by","deleted_by"],ne=_([{label:"字符串",value:"string"},{label:"富文本",value:"richtext"},{label:"整型",value:"int"},{label:"布尔值",value:"bool"},{label:"浮点型",value:"float64"},{label:"时间",value:"time.Time"},{label:"枚举",value:"enum"},{label:"单图片（字符串）",value:"picture"},{label:"多图片（json字符串）",value:"pictures"},{label:"视频（字符串）",value:"video"},{label:"文件（json字符串）",value:"file"},{label:"JSON",value:"json"},{label:"数组",value:"array"}]),ae=_([{label:"=",value:"="},{label:"<>",value:"<>"},{label:">",value:">"},{label:"<",value:"<"},{label:"LIKE",value:"LIKE"},{label:"BETWEEN",value:"BETWEEN"},{label:"NOT BETWEEN",value:"NOT BETWEEN"}]),re=_([{label:"index",value:"index"},{label:"uniqueIndex",value:"uniqueIndex"}]),oe={fieldName:"",fieldDesc:"",fieldType:"",dataType:"",fieldJson:"",columnName:"",dataTypeLong:"",comment:"",defaultValue:"",require:!1,sort:!1,form:!0,desc:!0,table:!0,excel:!1,errorText:"",primaryKey:!1,clearable:!0,fieldSearchType:"",fieldIndexType:"",dictType:"",dataSource:{dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}},le=E(),ie=T(),ue=_({}),ce=_({businessDB:"",dbName:"",tableName:""}),se=_([]),de=_(""),fe=_({}),pe=_({structName:"",tableName:"",packageName:"",package:"",abbreviation:"",description:"",businessDB:"",autoCreateApiToSql:!0,autoCreateMenuToSql:!0,autoCreateBtnAuth:!1,autoMigrate:!0,gvaModel:!0,autoCreateResource:!1,onlyTemplate:!1,isTree:!1,generateWeb:!0,generateServer:!0,treeJson:"",fields:[]}),ve=_({structName:[{required:!0,message:"请输入结构体名称",trigger:"blur"}],abbreviation:[{required:!0,message:"请输入结构体简称",trigger:"blur"}],description:[{required:!0,message:"请输入结构体描述",trigger:"blur"}],packageName:[{required:!0,message:"文件名称：sysXxxxXxxx",trigger:"blur"}],package:[{required:!0,message:"请选择package",trigger:"blur"}]}),me=_({}),he=_({}),ge=_(!1),be=_(!1),ye=function(e){e&&pe.value.fields.length&&F.confirm("如果您开启GVA默认结构，会自动添加ID,CreatedAt,UpdatedAt,DeletedAt字段，此行为将自动清除您目前在下方创建的重名字段，是否继续？","注意",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((function(){pe.value.fields=pe.value.fields.filter((function(e){return!ee.some((function(t){return t===e.columnName}))}))})).catch((function(){pe.value.gvaModel=!1}))},we=_(null),_e=function(){we.value.selectText()},xe=function(){we.value.copy()},Ee=function(e){ge.value=!0,e?(de.value="edit",e.dataSource||(e.dataSource={dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}),he.value=JSON.parse(JSON.stringify(e)),me.value=e):(de.value="add",oe.onlyNumber=H(),me.value=JSON.parse(JSON.stringify(oe)))},Te=_(null),Ne=function(){Te.value.fieldDialogForm.validate((function(e){if(!e)return!1;me.value.fieldName=i(me.value.fieldName),"add"===de.value&&pe.value.fields.push(me.value),ge.value=!1}))},Se=function(){"edit"===de.value&&(me.value=he.value),ge.value=!1},De=_(null),ke=function(){var e=l(r().mark((function e(t){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!pe.value.isTree||pe.value.treeJson){e.next=3;break}return R({type:"error",message:"请填写树型结构的前端展示json属性"}),e.abrupt("return",!1);case 3:if(pe.value.generateWeb||pe.value.generateServer){e.next=6;break}return R({type:"error",message:"请至少选择一个生成项"}),e.abrupt("return",!1);case 6:if(pe.value.onlyTemplate){e.next=25;break}if(!(pe.value.fields.length<=0)){e.next=10;break}return R({type:"error",message:"请填写至少一个field"}),e.abrupt("return",!1);case 10:if(pe.value.gvaModel||!pe.value.fields.every((function(e){return!e.primaryKey}))){e.next=13;break}return R({type:"error",message:"您至少需要创建一个主键才能保证自动化代码的可行性"}),e.abrupt("return",!1);case 13:if(!pe.value.fields.some((function(e){return e.fieldName===pe.value.structName}))){e.next=16;break}return R({type:"error",message:"存在与结构体同名的字段"}),e.abrupt("return",!1);case 16:if(!pe.value.fields.some((function(e){return e.fieldJson===pe.value.package}))){e.next=19;break}return R({type:"error",message:"存在与模板同名的的字段JSON"}),e.abrupt("return",!1);case 19:if(!pe.value.fields.some((function(e){return!e.fieldType}))){e.next=22;break}return R({type:"error",message:"请填写所有字段类型后进行提交"}),e.abrupt("return",!1);case 22:if(pe.value.package!==pe.value.abbreviation){e.next=25;break}return R({type:"error",message:"package和结构体简称不可同名"}),e.abrupt("return",!1);case 25:De.value.validate(function(){var e=l(r().mark((function e(a){var o,l,u;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!a){e.next=28;break}for(l in pe.value)"string"==typeof pe.value[l]&&(pe.value[l]=pe.value[l].trim());if(pe.value.structName=i(pe.value.structName),pe.value.tableName=pe.value.tableName.replace(" ",""),pe.value.tableName||(pe.value.tableName=s(c(pe.value.structName))),pe.value.structName!==pe.value.abbreviation){e.next=8;break}return R({type:"error",message:"structName和struct简称不能相同"}),e.abrupt("return",!1);case 8:if(pe.value.humpPackageName=s(pe.value.packageName),null===(o=pe.value.fields)||void 0===o||o.forEach((function(e){if(e.fieldName=i(e.fieldName),"enum"===e.fieldType){e.dataTypeLong=e.dataTypeLong.replace(/[\[\]{}()]/g,"");var t=e.dataTypeLong.split(",");t.forEach((function(e,n){-1===e.indexOf("'")&&(t[n]="'".concat(e,"'"))})),e.dataTypeLong=t.join(",")}})),delete pe.value.primaryField,!t){e.next=21;break}return e.next=14,g(n(n({},pe.value),{},{isAdd:!!Z.value,fields:pe.value.fields.filter((function(e){return!e.disabled}))}));case 14:if(0===(u=e.sent).code){e.next=17;break}return e.abrupt("return");case 17:ue.value=u.data.autoCode,be.value=!0,e.next=28;break;case 21:return e.next=23,b(pe.value);case 23:if(0===e.sent.code){e.next=26;break}return e.abrupt("return");case 26:R({type:"success",message:"自动化代码创建成功，自动移动成功"}),Xe();case 28:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 26:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ce=_([]),Ve=_([]),Oe=function(){var e=l(r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ce.value.dbName="",ce.value.tableName="",e.next=4,p({businessDB:ce.value.businessDB});case 4:0===(t=e.sent).code&&(Ve.value=t.data.dbs,Ce.value=t.data.dbList);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ae=function(){var e=l(r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,v({businessDB:ce.value.businessDB,dbName:ce.value.dbName});case 2:0===(t=e.sent).code&&(se.value=t.data.tables),ce.value.tableName="";case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Ie=function(){var e=l(r().mark((function e(){var t,n,a,o,l;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m(ce.value);case 2:0===(t=e.sent).code&&(n="",""!==ce.value.businessDB&&(a=Ce.value.find((function(e){return e.aliasName===ce.value.businessDB})),o=X(a),n=o.dbtype),pe.value.gvaModel=!1,l=u(ce.value.tableName),pe.value.structName=i(l),pe.value.tableName=ce.value.tableName,pe.value.packageName=c(l),pe.value.abbreviation=c(l),pe.value.description=l+"表",pe.value.autoCreateApiToSql=!0,pe.value.generateServer=!0,pe.value.generateWeb=!0,pe.value.fields=[],t.data.columns&&t.data.columns.forEach((function(e){if(Pe(e)){var t=u(e.columnName);pe.value.fields.push({onlyNumber:H(),fieldName:i(t),fieldDesc:e.columnComment||t+"字段",fieldType:fe.value[e.dataType],dataType:e.dataType,fieldJson:t,primaryKey:e.primaryKey,dataTypeLong:e.dataTypeLong&&e.dataTypeLong.split(",")[0],columnName:"oracle"===n?e.columnName.toUpperCase():e.columnName,comment:e.columnComment,require:!1,errorText:"",clearable:!0,fieldSearchType:"",fieldIndexType:"",dictType:"",form:!0,table:!0,excel:!1,desc:!0,dataSource:{dbName:"",association:1,table:"",label:"",value:"",hasDeletedAt:!1}})}})));case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Pe=function(e){var t=!0;return pe.value.gvaModel&&ee.some((function(t){return t===e.columnName}))&&(t=!1),pe.value.autoCreateResource&&te.some((function(t){return t===e.columnName}))&&(t=!1),t},Me=function(){var e=l(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:["string","int","bool","float64","time.Time"].forEach(function(){var e=l(r().mark((function e(t){var n;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Y(t);case 2:(n=e.sent)&&n.forEach((function(e){fe.value[e.label]=t}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Be=function(){var e=l(r().mark((function e(t){var n,a;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,y({id:Number(t)});case 2:0===(n=e.sent).code&&(a=le.query.isAdd,Z.value=a,pe.value=JSON.parse(n.data.meta),Z.value&&pe.value.fields.forEach((function(e){e.disabled=!0})));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),Ue=_([]),je=function(){var e=l(r().mark((function e(){var t;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,h();case 2:0===(t=e.sent).code&&(Ue.value=t.data.pkgs);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),Le=function(){ie.push({name:"autoPkg"})},Re=function(){Oe(),Me(),je();var e=le.params.id;e&&Be(e)};Re(),N((function(){return le.params.id}),(function(){"autoCodeEdit"===le.name&&Re()})),N((function(){return pe.value.generateServer}),(function(){pe.value.generateServer||(pe.value.autoCreateApiToSql=!1,pe.value.autoMigrate=!1)})),N((function(){return pe.value.generateWeb}),(function(){pe.value.generateWeb||(pe.value.autoCreateMenuToSql=!1,pe.value.autoCreateBtnAuth=!1)}));var Fe,Xe=function(){var e=l(r().mark((function e(){return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return pe.value={structName:"",tableName:"",packageName:"",package:"",abbreviation:"",description:"",businessDB:"",autoCreateApiToSql:!0,autoCreateMenuToSql:!0,autoCreateBtnAuth:!1,autoMigrate:!0,gvaModel:!0,autoCreateResource:!1,onlyTemplate:!1,isTree:!1,treeJson:"",fields:[]},e.next=3,L();case 3:window.sessionStorage.removeItem("autoCode");case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();(Fe=window.sessionStorage.getItem("autoCode"))&&(pe.value=JSON.parse(Fe));var Ye=function(e){var t=new FileReader;return t.onload=function(e){try{pe.value=JSON.parse(e.target.result),R.success("JSON 文件导入成功")}catch(t){R.error("无效的 JSON 文件")}},t.readAsText(e),!1};N((function(){return pe.value.onlyTemplate}),(function(e){e&&F.confirm("使用基础模板将不会生成任何结构体和CURD,仅仅配置enter等属性方便自行开发非CURD逻辑","注意",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((function(){pe.value.fields=[]})).catch((function(){pe.value.onlyTemplate=!1}))}));var We=function(e,t){if("richtext"===e)return"LIKE"!==t;if("string"!==e&&"LIKE"===t)return!0;return!(["int","time.Time","float64"].includes(e)||!["BETWEEN","NOT BETWEEN"].includes(t))};return function(e,n){var i=S("el-input"),u=S("ai-gva"),s=S("el-icon"),d=S("el-button"),f=S("el-tooltip"),p=S("QuestionFilled"),v=S("el-option"),m=S("el-select"),h=S("el-form-item"),g=S("el-col"),b=S("el-row"),y=S("el-form"),w=S("refresh"),_=S("document-add"),x=S("el-checkbox"),E=S("el-collapse-item"),T=S("el-collapse"),N=S("MoreFilled"),L=S("el-table-column"),R=S("el-table"),X=S("el-upload"),Y=S("el-drawer");return k(),D("div",null,[C(W,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请点我观看。"}),Z.value?V("",!0):(k(),D("div",Gt,[n[38]||(n[38]=O("div",{class:"text-lg mb-2 text-gray-600"},[A(" 使用AI创建"),O("a",{class:"text-blue-600 text-sm ml-4",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"获取AiPath")],-1)),O("div",Ht,[C(i,{modelValue:K.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return K.value=e}),type:"textarea",rows:5,maxlength:2e3,placeholder:"现已完全免费\n试试复制一张图片然后按下ctrl+v或者commend+v\n试试描述你的表，让AI帮你完成。\n此功能需要到插件市场个人中心获取自己的AI-Path，把AI-Path填入config.yaml下的autocode--\x3eai-path，重启项目即可使用。\n按下 Ctrl+Enter 或 Cmd+Enter 直接生成",resize:"none",onFocus:q,onBlur:J},null,8,["modelValue"]),O("div",Kt,[C(f,{effect:"light"},{content:I((function(){return n[34]||(n[34]=[O("div",null,[A(" 【完全免费】前往"),O("a",{class:"text-blue-600",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"插件市场个人中心"),A("申请AIPath，填入config.yaml的ai-path属性即可使用。 ")],-1)])})),default:I((function(){return[C(d,{disabled:pe.value.onlyTemplate,type:"primary",onClick:n[1]||(n[1]=function(e){return $()})},{default:I((function(){return[C(s,{size:"18"},{default:I((function(){return[C(u)]})),_:1}),n[35]||(n[35]=A(" 识图 "))]})),_:1},8,["disabled"])]})),_:1})]),O("div",$t,[C(f,{effect:"light"},{content:I((function(){return n[36]||(n[36]=[O("div",null,[A(" 【完全免费】前往"),O("a",{class:"text-blue-600",href:"https://plugin.gin-vue-admin.com/#/layout/userInfo/center",target:"_blank"},"插件市场个人中心"),A("申请AIPath，填入config.yaml的ai-path属性即可使用。 ")],-1)])})),default:I((function(){return[C(d,{disabled:pe.value.onlyTemplate,type:"primary",onClick:n[2]||(n[2]=function(e){return Q()})},{default:I((function(){return[C(s,{size:"18"},{default:I((function(){return[C(u)]})),_:1}),n[37]||(n[37]=A(" 生成 "))]})),_:1},8,["disabled"])]})),_:1})])])])),Z.value?V("",!0):(k(),D("div",Qt,[n[41]||(n[41]=O("div",{class:"text-lg mb-2 text-gray-600"},"从数据库创建",-1)),C(y,{ref:"getTableForm",inline:!0,model:ce.value,"label-width":"120px"},{default:I((function(){return[C(b,{class:"w-full"},{default:I((function(){return[C(g,{span:6},{default:I((function(){return[C(h,{label:"业务库",prop:"selectDBtype",class:"w-full"},{label:I((function(){return[C(f,{content:"注：需要提前到db-list自行配置多数据库，如未配置需配置后重启服务方可使用。（此处可选择对应库表，可理解为从哪个库选择表）",placement:"bottom",effect:"light"},{default:I((function(){return[O("div",null,[n[39]||(n[39]=A(" 业务库 ")),C(s,null,{default:I((function(){return[C(p)]})),_:1})])]})),_:1})]})),default:I((function(){return[C(m,{modelValue:ce.value.businessDB,"onUpdate:modelValue":n[3]||(n[3]=function(e){return ce.value.businessDB=e}),clearable:"",placeholder:"选择业务库",onChange:Oe,class:"w-full"},{default:I((function(){return[(k(!0),D(P,null,M(Ce.value,(function(e){return k(),B(v,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:I((function(){return[O("div",null,[O("span",null,U(e.aliasName),1),O("span",Zt,U(e.dbName),1)])]})),_:2},1032,["value","label","disabled"])})),128))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"数据库名",prop:"structName",class:"w-full"},{default:I((function(){return[C(m,{modelValue:ce.value.dbName,"onUpdate:modelValue":n[4]||(n[4]=function(e){return ce.value.dbName=e}),clearable:"",filterable:"",placeholder:"请选择数据库",class:"w-full",onChange:Ae},{default:I((function(){return[(k(!0),D(P,null,M(Ve.value,(function(e){return k(),B(v,{key:e.database,label:e.database,value:e.database},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"表名",prop:"structName",class:"w-full"},{default:I((function(){return[C(m,{modelValue:ce.value.tableName,"onUpdate:modelValue":n[5]||(n[5]=function(e){return ce.value.tableName=e}),disabled:!ce.value.dbName,class:"w-full",filterable:"",placeholder:"请选择表"},{default:I((function(){return[(k(!0),D(P,null,M(se.value,(function(e){return k(),B(v,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","disabled"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{class:"w-full"},{default:I((function(){return[O("div",en,[C(d,{type:"primary",onClick:Ie},{default:I((function(){return n[40]||(n[40]=[A(" 使用此表 ")])})),_:1})])]})),_:1})]})),_:1})]})),_:1})]})),_:1},8,["model"])])),O("div",tn,[n[46]||(n[46]=O("div",{class:"text-lg mb-2 text-gray-600"},"自动化结构",-1)),C(y,{disabled:Z.value,ref_key:"autoCodeForm",ref:De,rules:ve.value,model:pe.value,"label-width":"120px",inline:!0},{default:I((function(){return[C(b,{class:"w-full"},{default:I((function(){return[C(g,{span:6},{default:I((function(){return[C(h,{label:"结构名称",prop:"structName",class:"w-full"},{default:I((function(){return[O("div",nn,[C(i,{modelValue:pe.value.structName,"onUpdate:modelValue":n[6]||(n[6]=function(e){return pe.value.structName=e}),placeholder:"首字母自动转换大写"},null,8,["modelValue"]),C(d,{disabled:pe.value.onlyTemplate,type:"primary",onClick:n[7]||(n[7]=function(e){return Q(!0)})},{default:I((function(){return[C(s,{size:"18"},{default:I((function(){return[C(u)]})),_:1}),n[42]||(n[42]=A(" 生成 "))]})),_:1},8,["disabled"])])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"TableName",class:"w-full"},{label:I((function(){return[C(f,{content:"简称会作为入参对象名和路由group",placement:"bottom",effect:"light"},{default:I((function(){return[O("div",null,[n[43]||(n[43]=A(" 结构简称 ")),C(s,null,{default:I((function(){return[C(p)]})),_:1})])]})),_:1})]})),default:I((function(){return[C(i,{modelValue:pe.value.abbreviation,"onUpdate:modelValue":n[8]||(n[8]=function(e){return pe.value.abbreviation=e}),placeholder:"请输入Struct简称"},null,8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"中文名称",prop:"description",class:"w-full"},{default:I((function(){return[C(i,{modelValue:pe.value.description,"onUpdate:modelValue":n[9]||(n[9]=function(e){return pe.value.description=e}),placeholder:"中文描述作为自动api描述"},null,8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"表名",prop:"tableName",class:"w-full"},{default:I((function(){return[C(i,{modelValue:pe.value.tableName,"onUpdate:modelValue":n[10]||(n[10]=function(e){return pe.value.tableName=e}),placeholder:"指定表名（非必填）"},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(b,{class:"w-full"},{default:I((function(){return[C(g,{span:6},{default:I((function(){return[C(h,{prop:"packageName",class:"w-full"},{label:I((function(){return[C(f,{content:"生成文件的默认名称(建议为驼峰格式,首字母小写,如sysXxxXxxx)",placement:"bottom",effect:"light"},{default:I((function(){return[O("div",null,[n[44]||(n[44]=A(" 文件名称 ")),C(s,null,{default:I((function(){return[C(p)]})),_:1})])]})),_:1})]})),default:I((function(){return[C(i,{modelValue:pe.value.packageName,"onUpdate:modelValue":n[11]||(n[11]=function(e){return pe.value.packageName=e}),placeholder:"请输入文件名称",onBlur:n[12]||(n[12]=function(e){return function(e,t){e[t]=c(e[t])}(pe.value,"packageName")})},null,8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"选择模板",prop:"package",class:"w-full relative"},{default:I((function(){return[C(m,{modelValue:pe.value.package,"onUpdate:modelValue":n[13]||(n[13]=function(e){return pe.value.package=e}),class:"w-full pr-12"},{default:I((function(){return[(k(!0),D(P,null,M(Ue.value,(function(e){return k(),B(v,{key:e.ID,value:e.packageName,label:e.packageName},null,8,["value","label"])})),128))]})),_:1},8,["modelValue"]),O("span",an,[C(s,{class:"cursor-pointer ml-2 text-gray-600",onClick:je},{default:I((function(){return[C(w)]})),_:1}),C(s,{class:"cursor-pointer ml-2 text-gray-600",onClick:Le},{default:I((function(){return[C(_)]})),_:1})])]})),_:1})]})),_:1}),C(g,{span:6},{default:I((function(){return[C(h,{label:"业务库",prop:"businessDB",class:"w-full"},{label:I((function(){return[C(f,{content:"注：需要提前到db-list自行配置多数据库，此项为空则会使用gva本库创建自动化代码(global.GVA_DB),填写后则会创建指定库的代码(global.MustGetGlobalDBByDBName(dbname))",placement:"bottom",effect:"light"},{default:I((function(){return[O("div",null,[n[45]||(n[45]=A(" 业务库 ")),C(s,null,{default:I((function(){return[C(p)]})),_:1})])]})),_:1})]})),default:I((function(){return[C(m,{modelValue:pe.value.businessDB,"onUpdate:modelValue":n[14]||(n[14]=function(e){return pe.value.businessDB=e}),placeholder:"选择业务库",class:"w-full"},{default:I((function(){return[(k(!0),D(P,null,M(Ce.value,(function(e){return k(),B(v,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:I((function(){return[O("div",null,[O("span",null,U(e.aliasName),1),O("span",rn,U(e.dbName),1)])]})),_:2},1032,["value","label","disabled"])})),128))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1})]})),_:1})]})),_:1},8,["disabled","rules","model"])]),O("div",on,[C(T,{class:"no-border-collapse"},{default:I((function(){return[C(E,null,{title:I((function(){return n[47]||(n[47]=[O("div",{class:"text-lg text-gray-600 font-normal"}," 专家模式 ",-1)])})),icon:I((function(e){var t=e.isActive;return[O("span",ln,U(t?"收起":"展开"),1)]})),default:I((function(){return[O("div",un,[O("div",cn,[n[48]||(n[48]=O("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"基础设置",-1)),C(b,{gutter:20},{default:I((function(){return[C(g,{span:3},{default:I((function(){return[C(f,{content:"注：会自动在结构体global.Model其中包含主键和软删除相关操作配置",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"使用GVA结构"},{default:I((function(){return[C(x,{modelValue:pe.value.gvaModel,"onUpdate:modelValue":n[15]||(n[15]=function(e){return pe.value.gvaModel=e}),onChange:ye},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(f,{content:"注：会自动产生页面内的按钮权限配置，若不在角色管理中进行按钮分配则按钮不可见",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"创建按钮权限"},{default:I((function(){return[C(x,{disabled:!pe.value.generateWeb,modelValue:pe.value.autoCreateBtnAuth,"onUpdate:modelValue":n[16]||(n[16]=function(e){return pe.value.autoCreateBtnAuth=e})},null,8,["disabled","modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(h,{label:"生成前端"},{default:I((function(){return[C(x,{modelValue:pe.value.generateWeb,"onUpdate:modelValue":n[17]||(n[17]=function(e){return pe.value.generateWeb=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(h,{label:"生成后端"},{default:I((function(){return[C(x,{disabled:"",modelValue:pe.value.generateServer,"onUpdate:modelValue":n[18]||(n[18]=function(e){return pe.value.generateServer=e})},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1})]),O("div",sn,[n[49]||(n[49]=O("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"自动化设置",-1)),C(b,{gutter:20},{default:I((function(){return[C(g,{span:3},{default:I((function(){return[C(f,{content:"注：把自动生成的API注册进数据库",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"自动创建API"},{default:I((function(){return[C(x,{disabled:!pe.value.generateServer,modelValue:pe.value.autoCreateApiToSql,"onUpdate:modelValue":n[19]||(n[19]=function(e){return pe.value.autoCreateApiToSql=e})},null,8,["disabled","modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(f,{content:"注：把自动生成的菜单注册进数据库",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"自动创建菜单"},{default:I((function(){return[C(x,{disabled:!pe.value.generateWeb,modelValue:pe.value.autoCreateMenuToSql,"onUpdate:modelValue":n[20]||(n[20]=function(e){return pe.value.autoCreateMenuToSql=e})},null,8,["disabled","modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(f,{content:"注：自动同步数据库表结构，如果不需要可以选择关闭",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"同步表结构"},{default:I((function(){return[C(x,{disabled:!pe.value.generateServer,modelValue:pe.value.autoMigrate,"onUpdate:modelValue":n[21]||(n[21]=function(e){return pe.value.autoMigrate=e})},null,8,["disabled","modelValue"])]})),_:1})]})),_:1})]})),_:1})]})),_:1})]),O("div",dn,[n[50]||(n[50]=O("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"高级设置",-1)),C(b,{gutter:20},{default:I((function(){return[C(g,{span:3},{default:I((function(){return[C(f,{content:"注：会自动在结构体添加 created_by updated_by deleted_by，方便用户进行资源权限控制",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"创建资源标识"},{default:I((function(){return[C(x,{modelValue:pe.value.autoCreateResource,"onUpdate:modelValue":n[22]||(n[22]=function(e){return pe.value.autoCreateResource=e})},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),C(g,{span:3},{default:I((function(){return[C(f,{content:"注：使用基础模板将不会生成任何结构体和CURD,仅仅配置enter等属性方便自行开发非CURD逻辑",placement:"top",effect:"light"},{default:I((function(){return[C(h,{label:"基础模板"},{default:I((function(){return[C(x,{modelValue:pe.value.onlyTemplate,"onUpdate:modelValue":n[23]||(n[23]=function(e){return pe.value.onlyTemplate=e})},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1})]})),_:1})]),O("div",fn,[n[51]||(n[51]=O("h3",{class:"text-lg font-medium mb-4 text-gray-700"},"树形结构设置",-1)),C(b,{gutter:20,align:"middle"},{default:I((function(){return[C(g,{span:24},{default:I((function(){return[C(h,{label:"树型结构"},{default:I((function(){return[O("div",pn,[C(f,{content:"注：会自动创建parentID来进行父子关系关联,仅支持主键为int类型",placement:"top",effect:"light"},{default:I((function(){return[C(x,{modelValue:pe.value.isTree,"onUpdate:modelValue":n[24]||(n[24]=function(e){return pe.value.isTree=e})},null,8,["modelValue"])]})),_:1}),C(i,{modelValue:pe.value.treeJson,"onUpdate:modelValue":n[25]||(n[25]=function(e){return pe.value.treeJson=e}),disabled:!pe.value.isTree,placeholder:"前端展示json属性",class:"flex-1"},null,8,["modelValue","disabled"])])]})),_:1})]})),_:1})]})),_:1})])])]})),_:1})]})),_:1})]),O("div",vn,[O("div",mn,[C(d,{type:"primary",onClick:n[26]||(n[26]=function(e){return Ee()}),disabled:pe.value.onlyTemplate},{default:I((function(){return n[52]||(n[52]=[A(" 新增字段 ")])})),_:1},8,["disabled"])]),O("div",hn,[C(R,{data:pe.value.fields,"row-key":"fieldName"},{default:I((function(){return[Z.value?V("",!0):(k(),B(L,{key:0,fixed:"left",align:"left",type:"index",width:"60"},{default:I((function(){return[C(s,{class:"cursor-grab drag-column"},{default:I((function(){return[C(N)]})),_:1})]})),_:1})),C(L,{fixed:"left",align:"left",type:"index",label:"序列",width:"60"}),C(L,{fixed:"left",align:"left",type:"index",label:"主键",width:"60"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.primaryKey,"onUpdate:modelValue":function(e){return t.primaryKey=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{fixed:"left",align:"left",prop:"fieldName",label:"字段名称",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.fieldName,"onUpdate:modelValue":function(e){return t.fieldName=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"fieldDesc",label:"中文名",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.fieldDesc,"onUpdate:modelValue":function(e){return t.fieldDesc=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"defaultValue",label:"默认值",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.defaultValue,"onUpdate:modelValue":function(e){return t.defaultValue=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"require",label:"必填"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.require,"onUpdate:modelValue":function(e){return t.require=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"sort",label:"排序"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.sort,"onUpdate:modelValue":function(e){return t.sort=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"form",width:"100",label:"新建/编辑"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.form,"onUpdate:modelValue":function(e){return t.form=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"table",label:"表格"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.table,"onUpdate:modelValue":function(e){return t.table=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"desc",label:"详情"},{default:I((function(e){var t=e.row;return[C(x,{disabled:t.disabled,modelValue:t.desc,"onUpdate:modelValue":function(e){return t.desc=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),Z.value?V("",!0):(k(),B(L,{key:1,align:"left",prop:"excel",width:"100",label:"导入/导出"},{default:I((function(e){var t=e.row;return[C(x,{modelValue:t.excel,"onUpdate:modelValue":function(e){return t.excel=e}},null,8,["modelValue","onUpdate:modelValue"])]})),_:1})),C(L,{align:"left",prop:"fieldJson",width:"160px",label:"字段Json"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.fieldJson,"onUpdate:modelValue":function(e){return t.fieldJson=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"fieldType",label:"字段类型",width:"160"},{default:I((function(e){var t=e.row;return[C(m,{modelValue:t.fieldType,"onUpdate:modelValue":function(e){return t.fieldType=e},style:{width:"100%"},placeholder:"请选择字段类型",disabled:t.disabled,clearable:""},{default:I((function(){return[(k(!0),D(P,null,M(ne.value,(function(e){return k(),B(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]})),_:1}),C(L,{align:"left",prop:"fieldIndexType",label:"索引类型",width:"160"},{default:I((function(e){var t=e.row;return[C(m,{modelValue:t.fieldIndexType,"onUpdate:modelValue":function(e){return t.fieldIndexType=e},style:{width:"100%"},placeholder:"请选择字段索引类型",disabled:t.disabled,clearable:""},{default:I((function(){return[(k(!0),D(P,null,M(re.value,(function(e){return k(),B(v,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]})),_:1}),C(L,{align:"left",prop:"dataTypeLong",label:"字段长度/枚举值",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.dataTypeLong,"onUpdate:modelValue":function(e){return t.dataTypeLong=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"columnName",label:"数据库字段",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.columnName,"onUpdate:modelValue":function(e){return t.columnName=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"comment",label:"数据库字段描述",width:"160"},{default:I((function(e){var t=e.row;return[C(i,{disabled:t.disabled,modelValue:t.comment,"onUpdate:modelValue":function(e){return t.comment=e}},null,8,["disabled","modelValue","onUpdate:modelValue"])]})),_:1}),C(L,{align:"left",prop:"fieldSearchType",label:"搜索条件",width:"130"},{default:I((function(e){var t=e.row;return[C(m,{modelValue:t.fieldSearchType,"onUpdate:modelValue":function(e){return t.fieldSearchType=e},style:{width:"100%"},placeholder:"请选择字段查询条件",clearable:"",disabled:"json"!==t.fieldType||t.disabled},{default:I((function(){return[(k(!0),D(P,null,M(ae.value,(function(e){return k(),B(v,{key:e.value,label:e.label,value:e.value,disabled:We(t.fieldType,e.value)},null,8,["label","value","disabled"])})),128))]})),_:2},1032,["modelValue","onUpdate:modelValue","disabled"])]})),_:1}),C(L,{align:"left",label:"操作",width:"300",fixed:"right"},{default:I((function(e){return[e.row.disabled?V("",!0):(k(),B(d,{key:0,type:"primary",link:"",icon:"edit",onClick:function(t){return Ee(e.row)}},{default:I((function(){return n[53]||(n[53]=[A(" 高级编辑 ")])})),_:2},1032,["onClick"])),e.row.disabled?V("",!0):(k(),B(d,{key:1,type:"primary",link:"",icon:"delete",onClick:function(t){return function(e){F.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(l(r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:pe.value.fields.splice(e,1);case 1:case"end":return t.stop()}}),t)}))))}(e.$index)}},{default:I((function(){return n[54]||(n[54]=[A(" 删除 ")])})),_:2},1032,["onClick"]))]})),_:1})]})),_:1},8,["data"])]),O("div",gn,[C(d,{type:"primary",disabled:Z.value,onClick:n[27]||(n[27]=function(e){return t=JSON.stringify(pe.value,null,2),n=new Blob([t],{type:"application/json"}),a=URL.createObjectURL(n),(r=document.createElement("a")).href=a,r.download="form_data.json",document.body.appendChild(r),r.click(),document.body.removeChild(r),void URL.revokeObjectURL(a);var t,n,a,r})},{default:I((function(){return n[55]||(n[55]=[A(" 导出json ")])})),_:1},8,["disabled"]),C(X,{class:"flex items-center","before-upload":Ye,"show-file-list":!1,headers:{"x-token":j(t)},accept:".json"},{default:I((function(){return[C(d,{type:"primary",class:"mx-2",disabled:Z.value},{default:I((function(){return n[56]||(n[56]=[A("导入json")])})),_:1},8,["disabled"])]})),_:1},8,["headers"]),C(d,{type:"primary",disabled:Z.value,onClick:n[28]||(n[28]=function(e){return Xe()})},{default:I((function(){return n[57]||(n[57]=[A(" 清除暂存 ")])})),_:1},8,["disabled"]),C(d,{type:"primary",disabled:Z.value,onClick:n[29]||(n[29]=function(e){window.sessionStorage.setItem("autoCode",JSON.stringify(pe.value))})},{default:I((function(){return n[58]||(n[58]=[A(" 暂存 ")])})),_:1},8,["disabled"]),C(d,{type:"primary",disabled:Z.value,onClick:n[30]||(n[30]=function(e){return ke(!1)})},{default:I((function(){return n[59]||(n[59]=[A(" 生成代码 ")])})),_:1},8,["disabled"]),C(d,{type:"primary",onClick:n[31]||(n[31]=function(e){return ke(!0)})},{default:I((function(){return[A(U(Z.value?"查看代码":"预览代码"),1)]})),_:1})])]),C(Y,{modelValue:ge.value,"onUpdate:modelValue":n[32]||(n[32]=function(e){return ge.value=e}),size:"70%","show-close":!1},{header:I((function(){return[O("div",bn,[n[62]||(n[62]=O("span",{class:"text-lg"},"组件内容",-1)),O("div",null,[C(d,{onClick:Se},{default:I((function(){return n[60]||(n[60]=[A(" 取 消 ")])})),_:1}),C(d,{type:"primary",onClick:Ne},{default:I((function(){return n[61]||(n[61]=[A(" 确 定 ")])})),_:1})])])]})),default:I((function(){return[ge.value?(k(),B(a,{key:0,ref_key:"fieldDialogNode",ref:Te,"dialog-middle":me.value,"type-options":ne.value,"type-search-options":ae.value,"type-index-options":re.value},null,8,["dialog-middle","type-options","type-search-options","type-index-options"])):V("",!0)]})),_:1},8,["modelValue"]),C(Y,{modelValue:be.value,"onUpdate:modelValue":n[33]||(n[33]=function(e){return be.value=e}),size:"80%","show-close":!1},{header:I((function(){return[O("div",yn,[n[65]||(n[65]=O("span",{class:"text-lg"},"操作栏",-1)),O("div",null,[C(d,{type:"primary",onClick:_e},{default:I((function(){return n[63]||(n[63]=[A(" 全选 ")])})),_:1}),C(d,{type:"primary",onClick:xe},{default:I((function(){return n[64]||(n[64]=[A(" 复制 ")])})),_:1})])])]})),default:I((function(){return[be.value?(k(),B(o,{key:0,"is-add":Z.value,ref_key:"previewNode",ref:we,"preview-code":ue.value},null,8,["is-add","preview-code"])):V("",!0)]})),_:1},8,["modelValue"])])}}}))}}}))}();
