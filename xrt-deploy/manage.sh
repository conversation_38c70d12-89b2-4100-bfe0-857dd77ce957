#!/bin/bash

case "$1" in
    start)
        echo "启动服务..."
        docker-compose up -d
        ;;
    stop)
        echo "停止服务..."
        docker-compose down
        ;;
    restart)
        echo "重启服务..."
        docker-compose restart
        ;;
    rebuild)
        echo "重新构建并启动..."
        docker-compose down
        docker-compose up -d --build
        ;;
    logs)
        echo "查看日志..."
        docker-compose logs -f
        ;;
    status)
        echo "查看状态..."
        docker-compose ps
        ;;
    *)
        echo "用法: $0 {start|stop|restart|rebuild|logs|status}"
        ;;
esac
