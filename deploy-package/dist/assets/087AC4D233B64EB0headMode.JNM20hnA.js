/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import e from"./087AC4D233B64EB0index.DAKhbPae.js";import{_ as a,I as s,J as t,aj as o,u as r,ac as l,a as n,R as u,g as d,c as i,o as m,f as c,w as p,F as v,D as f,v as x,i as h,d as b,ak as y}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import"./087AC4D233B64EB0menuItem.h4t5Q0OS.js";import"./087AC4D233B64EB0asyncSubmenu.BQ2WRSj6.js";const I={class:"bg-white h-[calc(100%-4px)] text-slate-700 dark:text-slate-300 mx-2 dark:bg-slate-900 flex items-center w-[calc(100vw-600px)] overflow-auto"},g=a(Object.assign({name:"GvaAside"},{__name:"headMode",setup(a){const g=s(),{device:C}=t(g),w=o(),B=r(),k=l(),_=localStorage.getItem("sidebarCollapsed"),j=n(!_||JSON.parse(_)),q=n("");u((()=>{"Iframe"!==w.name?q.value=w.meta.activeName||w.name:q.value=decodeURIComponent(w.query.url)})),u((()=>{"mobile"===C.value&&(j.value=!0)})),u((()=>{localStorage.setItem("sidebarCollapsed",JSON.stringify(j.value))})),y("isCollapse",j);const S=e=>{var a,s;const t={},o={};if((null==(a=k.routeMap[e])?void 0:a.parameters)&&(null==(s=k.routeMap[e])||s.parameters.forEach((e=>{"query"===e.type?t[e.key]=e.value:o[e.key]=e.value}))),e!==w.name)return e.indexOf("http://")>-1||e.indexOf("https://")>-1?"Iframe"===e?(t.url=decodeURIComponent(e),void B.push({name:"Iframe",query:t,params:o})):void window.open(e,"_blank"):void B.push({name:e,query:t,params:o})};return(a,s)=>{const t=d("el-menu");return m(),i("div",I,[c(t,{"default-active":q.value,mode:"horizontal",class:"border-r-0 w-full flex gap-1 items-center box-border h-[calc(100%-1px)]","unique-opened":"",onSelect:S},{default:p((()=>[(m(!0),i(v,null,f(x(k).asyncRouters[0].children,(a=>(m(),i(v,null,[a.hidden?b("",!0):(m(),h(e,{key:a.name,"router-info":a,mode:"horizontal"},null,8,["router-info"]))],64)))),256))])),_:1},8,["default-active"])])}}}),[["__scopeId","data-v-66534c43"]]);export{g as default};
