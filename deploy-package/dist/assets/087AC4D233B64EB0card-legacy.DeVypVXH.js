/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,e){"use strict";var s,a,i,n,o,r,d;return{setters:[function(t){s=t.c,a=t.o,i=t.d,n=t.b,o=t.t,r=t.H,d=t.n}],execute:function(){var e={key:0,class:"flex justify-between items-center"},u={class:"text-base font-bold"},l={key:0,class:"text-sm text-active cursor-pointer"},c={class:"mt-2"};t("default",{__name:"card",props:{title:{type:String,default:""},showAction:{type:Boolean,default:!1},customClass:{type:String,default:""},withoutPadding:{type:Boolean,default:!1}},setup:function(t){return function(f,y){return a(),s("div",{class:d(["bg-white dark:bg-slate-900 text-gray-800 dark:text-gray-400 rounded shadow",[t.customClass||"",t.withoutPadding?"p-0":"p-4"]])},[t.title?(a(),s("div",e,[n("div",u,o(t.title),1),t.showAction?(a(),s("div",l," 查看更多 ")):i("",!0)])):i("",!0),n("div",c,[r(f.$slots,"default")])],2)}}})}}}));
