/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return r};var n,r={},a=Object.prototype,l=a.hasOwnProperty,u=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",f=o.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(n){d=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof w?t:w,l=Object.create(a.prototype),o=new j(r||[]);return u(l,"_invoke",{value:C(e,n,o)}),l}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}r.wrap=s;var m="suspendedStart",v="suspendedYield",h="executing",y="completed",g={};function w(){}function b(){}function _(){}var x={};d(x,i,(function(){return this}));var V=Object.getPrototypeOf,k=V&&V(V(N([])));k&&k!==a&&l.call(k,i)&&(x=k);var I=_.prototype=w.prototype=Object.create(x);function D(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function E(t,n){function r(a,u,o,i){var c=p(t[a],t,u);if("throw"!==c.type){var f=c.arg,d=f.value;return d&&"object"==e(d)&&l.call(d,"__await")?n.resolve(d.__await).then((function(e){r("next",e,o,i)}),(function(e){r("throw",e,o,i)})):n.resolve(d).then((function(e){f.value=e,o(f)}),(function(e){return r("throw",e,o,i)}))}i(c.arg)}var a;u(this,"_invoke",{value:function(e,t){function l(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(l,l):l()}})}function C(e,t,r){var a=m;return function(l,u){if(a===h)throw Error("Generator is already running");if(a===y){if("throw"===l)throw u;return{value:n,done:!0}}for(r.method=l,r.arg=u;;){var o=r.delegate;if(o){var i=L(o,r);if(i){if(i===g)continue;return i}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===m)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=h;var c=p(e,t,r);if("normal"===c.type){if(a=r.done?y:v,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=y,r.method="throw",r.arg=c.arg)}}}function L(e,t){var r=t.method,a=e.iterator[r];if(a===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=n,L(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var l=p(a,e.iterator,t.arg);if("throw"===l.type)return t.method="throw",t.arg=l.arg,t.delegate=null,g;var u=l.arg;return u?u.done?(t[e.resultName]=u.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,g):u:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function U(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(U,this),this.reset(!0)}function N(t){if(t||""===t){var r=t[i];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,u=function e(){for(;++a<t.length;)if(l.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=n,e.done=!0,e};return u.next=u}}throw new TypeError(e(t)+" is not iterable")}return b.prototype=_,u(I,"constructor",{value:_,configurable:!0}),u(_,"constructor",{value:b,configurable:!0}),b.displayName=d(_,f,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,d(e,f,"GeneratorFunction")),e.prototype=Object.create(I),e},r.awrap=function(e){return{__await:e}},D(E.prototype),d(E.prototype,c,(function(){return this})),r.AsyncIterator=E,r.async=function(e,t,n,a,l){void 0===l&&(l=Promise);var u=new E(s(e,t,n,a),l);return r.isGeneratorFunction(t)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},D(I),d(I,f,"Generator"),d(I,i,(function(){return this})),d(I,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},r.values=N,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(B),!e)for(var t in this)"t"===t.charAt(0)&&l.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,a){return o.type="throw",o.arg=e,t.next=r,a&&(t.method="next",t.arg=n),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a],o=u.completion;if("root"===u.tryLoc)return r("end");if(u.tryLoc<=this.prev){var i=l.call(u,"catchLoc"),c=l.call(u,"finallyLoc");if(i&&c){if(this.prev<u.catchLoc)return r(u.catchLoc,!0);if(this.prev<u.finallyLoc)return r(u.finallyLoc)}else if(i){if(this.prev<u.catchLoc)return r(u.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return r(u.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&l.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;B(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:N(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),g}},r}function n(e,t,n,r,a,l,u){try{var o=e[l](u),i=o.value}catch(e){return void n(e)}o.done?t(i):Promise.resolve(i).then(r,a)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(a,l){var u=e.apply(t,r);function o(e){n(u,a,l,o,i,"next",e)}function i(e){n(u,a,l,o,i,"throw",e)}o(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0icon-legacy.uwqFpZ44.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0authorityBtn-legacy.cvbwrcdH.js","./087AC4D233B64EB0doc-legacy.yzTEdl6U.js","./087AC4D233B64EB0stringFun-legacy.2vIcgB7Q.js","./087AC4D233B64EB0components-cascader-legacy.CsckEUvV.js"],(function(e,n){"use strict";var a,l,u,o,i,c,f,d,s,p,m,v,h,y,g,w,b,_,x,V,k,I,D,E,C,L,U,B,j,N,A;return{setters:[function(e){a=e._,l=e.I,u=e.r,o=e.a,i=e.g,c=e.c,f=e.o,d=e.b,s=e.f,p=e.w,m=e.h,v=e.t,h=e.d,y=e.i,g=e.Y,w=e.v,b=e.aW,_=e.aX,x=e.aY,V=e.ab,k=e.aZ,I=e.E,D=e.a_,E=e.a$,C=e.aU},function(e){L=e.default},function(e){U=e._},function(e){B=e.c},function(e){j=e.t},function(e){N=e.a},function(e){A=e.default}],execute:function(){var n=document.createElement("style");n.textContent=".warning[data-v-63e1540b]{color:#dc143c}.icon-column[data-v-63e1540b]{display:flex;align-items:center}.icon-column .el-icon[data-v-63e1540b]{margin-right:8px}\n/*$vite$:1*/",document.head.appendChild(n);var T={class:"gva-table-box"},O={class:"gva-btn-list"},S={key:0,class:"icon-column"},P={class:"flex justify-between items-center"},F={class:"text-lg"},G={style:{display:"inline-flex","align-items":"center"}},q={class:"flex items-center gap-2"},M={class:"flex items-center gap-2 mt-3"},$=Object.assign({name:"Menus"},{__name:"menu",setup:function(e){var n=l(),a=u({path:[{required:!0,message:"请输入菜单name",trigger:"blur"}],component:[{required:!0,message:"请输入文件路径",trigger:"blur"}],"meta.title":[{required:!0,message:"请输入菜单展示名称",trigger:"blur"}]}),$=o([]),z=function(){var e=r(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,_();case 2:0===(n=e.sent).code&&($.value=n.data);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();z();var Y=function(e){W.value.component=e.replace(/\\/g,"/"),W.value.name=N(C["/src/"+e]),W.value.path=W.value.name},H=function(){var e=r(t().mark((function e(n,r){var a;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==(a=n[r]).ID){e.next=4;break}return n.splice(r,1),e.abrupt("return");case 4:return e.next=6,B({id:a.ID});case 6:0===e.sent.code&&n.splice(r,1);case 8:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),W=o({ID:0,path:"",name:"",hidden:!1,parentId:0,component:"",meta:{activeName:"",title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1},parameters:[],menuBtn:[]}),K=function(){W.value.path=W.value.name},X=function(e){Q(),e()},Z=o(null),J=o(!1),Q=function(){J.value=!1,Z.value.resetFields(),W.value={ID:0,path:"",name:"",hidden:!1,parentId:0,component:"",meta:{title:"",icon:"",defaultMenu:!1,closeTab:!1,keepAlive:!1}}},R=o(!1),ee=function(){Q(),R.value=!1},te=function(){var e=r(t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Z.value.validate(function(){var e=r(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!n){e.next=13;break}if(!le.value){e.next=7;break}return e.next=4,D(W.value);case 4:r=e.sent,e.next=10;break;case 7:return e.next=9,E(W.value);case 9:r=e.sent;case 10:0===r.code&&(I({type:"success",message:le.value?"编辑成功":"添加成功!"}),z()),Q(),R.value=!1;case 13:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ne=o([{ID:"0",title:"根菜单"}]),re=function(){ne.value=[{ID:0,title:"根目录"}],ae($.value,ne.value,!1)},ae=function(e,t,n){e&&e.forEach((function(e){if(e.children&&e.children.length){var r={title:e.meta.title,ID:e.ID,disabled:n||e.ID===W.value.ID,children:[]};ae(e.children,r.children,n||e.ID===W.value.ID),t.push(r)}else{var a={title:e.meta.title,ID:e.ID,disabled:n||e.ID===W.value.ID};t.push(a)}}))},le=o(!1),ue=o("新增菜单"),oe=function(e){ue.value="新增菜单",W.value.parentId=e,le.value=!1,re(),R.value=!0},ie=function(){var e=r(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ue.value="编辑菜单",e.next=3,x({id:n});case 3:r=e.sent,W.value=r.data.menu,le.value=!0,re(),R.value=!0;case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}();return function(e,l){var u=i("el-button"),o=i("el-table-column"),_=i("el-icon"),x=i("el-table"),D=i("el-form-item"),E=i("el-col"),C=i("el-input"),B=i("el-row"),N=i("el-checkbox"),Q=i("el-option"),re=i("el-select"),ae=i("el-cascader"),ce=i("el-tooltip"),fe=i("el-form"),de=i("el-drawer");return f(),c("div",null,[d("div",T,[d("div",O,[s(u,{type:"primary",icon:"plus",onClick:l[0]||(l[0]=function(e){return oe(0)})},{default:p((function(){return l[19]||(l[19]=[m(" 新增根菜单 ")])})),_:1})]),s(x,{data:$.value,"row-key":"ID"},{default:p((function(){return[s(o,{align:"left",label:"ID","min-width":"100",prop:"ID"}),s(o,{align:"left",label:"展示名称","min-width":"120",prop:"authorityName"},{default:p((function(e){return[d("span",null,v(e.row.meta.title),1)]})),_:1}),s(o,{align:"left",label:"图标","min-width":"140",prop:"authorityName"},{default:p((function(e){return[e.row.meta.icon?(f(),c("div",S,[s(_,null,{default:p((function(){return[(f(),y(g(e.row.meta.icon)))]})),_:2},1024),d("span",null,v(e.row.meta.icon),1)])):h("",!0)]})),_:1}),s(o,{align:"left",label:"路由Name","show-overflow-tooltip":"","min-width":"160",prop:"name"}),s(o,{align:"left",label:"路由Path","show-overflow-tooltip":"","min-width":"160",prop:"path"}),s(o,{align:"left",label:"是否隐藏","min-width":"100",prop:"hidden"},{default:p((function(e){return[d("span",null,v(e.row.hidden?"隐藏":"显示"),1)]})),_:1}),s(o,{align:"left",label:"父节点","min-width":"90",prop:"parentId"}),s(o,{align:"left",label:"排序","min-width":"70",prop:"sort"}),s(o,{align:"left",label:"文件路径","min-width":"360",prop:"component"}),s(o,{align:"left",fixed:"right",label:"操作","min-width":w(n).operateMinWith},{default:p((function(e){return[s(u,{type:"primary",link:"",icon:"plus",onClick:function(t){return oe(e.row.ID)}},{default:p((function(){return l[20]||(l[20]=[m(" 添加子菜单 ")])})),_:2},1032,["onClick"]),s(u,{type:"primary",link:"",icon:"edit",onClick:function(t){return ie(e.row.ID)}},{default:p((function(){return l[21]||(l[21]=[m(" 编辑 ")])})),_:2},1032,["onClick"]),s(u,{type:"primary",link:"",icon:"delete",onClick:function(n){return a=e.row.ID,void V.confirm("此操作将永久删除所有角色下该菜单, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(r(t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,k({ID:a});case 2:0===e.sent.code&&(I({type:"success",message:"删除成功!"}),z());case 4:case"end":return e.stop()}}),e)})))).catch((function(){I({type:"info",message:"已取消删除"})}));var a}},{default:p((function(){return l[22]||(l[22]=[m(" 删除 ")])})),_:2},1032,["onClick"])]})),_:1},8,["min-width"])]})),_:1},8,["data"])]),s(de,{modelValue:R.value,"onUpdate:modelValue":l[18]||(l[18]=function(e){return R.value=e}),size:w(n).drawerSize,"before-close":X,"show-close":!1},{header:p((function(){return[d("div",P,[d("span",F,v(ue.value),1),d("div",null,[s(u,{onClick:ee},{default:p((function(){return l[23]||(l[23]=[m(" 取 消 ")])})),_:1}),s(u,{type:"primary",onClick:te},{default:p((function(){return l[24]||(l[24]=[m(" 确 定 ")])})),_:1})])])]})),default:p((function(){return[s(U,{title:"新增菜单，需要在角色管理内配置权限才可使用"}),R.value?(f(),y(fe,{key:0,ref_key:"menuForm",ref:Z,inline:!0,model:W.value,rules:a,"label-position":"top"},{default:p((function(){return[s(B,{class:"w-full"},{default:p((function(){return[s(E,{span:16},{default:p((function(){return[s(D,{label:"文件路径",prop:"component"},{default:p((function(){return[s(A,{component:W.value.component,onChange:Y},null,8,["component"]),l[26]||(l[26]=d("span",{style:{"font-size":"12px","margin-right":"12px"}},"如果菜单包含子菜单，请创建router-view二级路由页面或者",-1)),s(u,{style:{"margin-top":"4px"},onClick:l[1]||(l[1]=function(e){return W.value.component="view/routerHolder.vue"})},{default:p((function(){return l[25]||(l[25]=[m(" 点我设置 ")])})),_:1})]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"展示名称",prop:"meta.title"},{default:p((function(){return[s(C,{modelValue:W.value.meta.title,"onUpdate:modelValue":l[2]||(l[2]=function(e){return W.value.meta.title=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),s(B,{class:"w-full"},{default:p((function(){return[s(E,{span:8},{default:p((function(){return[s(D,{label:"路由Name",prop:"path"},{default:p((function(){return[s(C,{modelValue:W.value.name,"onUpdate:modelValue":l[3]||(l[3]=function(e){return W.value.name=e}),autocomplete:"off",placeholder:"唯一英文字符串",onChange:K},null,8,["modelValue"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{prop:"path"},{label:p((function(){return[d("span",G,[l[28]||(l[28]=d("span",null,"路由Path",-1)),s(N,{modelValue:J.value,"onUpdate:modelValue":l[4]||(l[4]=function(e){return J.value=e}),style:{"margin-left":"12px",height:"auto"}},{default:p((function(){return l[27]||(l[27]=[m("添加参数")])})),_:1},8,["modelValue"])])]})),default:p((function(){return[s(C,{modelValue:W.value.path,"onUpdate:modelValue":l[5]||(l[5]=function(e){return W.value.path=e}),disabled:!J.value,autocomplete:"off",placeholder:"建议只在后方拼接参数"},null,8,["modelValue","disabled"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"是否隐藏"},{default:p((function(){return[s(re,{modelValue:W.value.hidden,"onUpdate:modelValue":l[6]||(l[6]=function(e){return W.value.hidden=e}),style:{width:"100%"},placeholder:"是否在列表隐藏"},{default:p((function(){return[s(Q,{value:!1,label:"否"}),s(Q,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),s(B,{class:"w-full"},{default:p((function(){return[s(E,{span:8},{default:p((function(){return[s(D,{label:"父节点ID"},{default:p((function(){return[s(ae,{modelValue:W.value.parentId,"onUpdate:modelValue":l[7]||(l[7]=function(e){return W.value.parentId=e}),style:{width:"100%"},disabled:!le.value,options:ne.value,props:{checkStrictly:!0,label:"title",value:"ID",disabled:"disabled",emitPath:!1},"show-all-levels":!1,filterable:""},null,8,["modelValue","disabled","options"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"图标",prop:"meta.icon"},{default:p((function(){return[s(L,{modelValue:W.value.meta.icon,"onUpdate:modelValue":l[8]||(l[8]=function(e){return W.value.meta.icon=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"排序标记",prop:"sort"},{default:p((function(){return[s(C,{modelValue:W.value.sort,"onUpdate:modelValue":l[9]||(l[9]=function(e){return W.value.sort=e}),modelModifiers:{number:!0},autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),s(B,{class:"w-full"},{default:p((function(){return[s(E,{span:8},{default:p((function(){return[s(D,{prop:"meta.activeName"},{label:p((function(){return[d("div",null,[l[29]||(l[29]=d("span",null," 高亮菜单 ",-1)),s(ce,{content:"注：当到达此路由时候，指定左侧菜单指定name会处于活跃状态（亮起），可为空，为空则为本路由Name。",placement:"top",effect:"light"},{default:p((function(){return[s(_,null,{default:p((function(){return[s(w(b))]})),_:1})]})),_:1})])]})),default:p((function(){return[s(C,{modelValue:W.value.meta.activeName,"onUpdate:modelValue":l[10]||(l[10]=function(e){return W.value.meta.activeName=e}),placeholder:W.value.name,autocomplete:"off"},null,8,["modelValue","placeholder"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"KeepAlive",prop:"meta.keepAlive"},{default:p((function(){return[s(re,{modelValue:W.value.meta.keepAlive,"onUpdate:modelValue":l[11]||(l[11]=function(e){return W.value.meta.keepAlive=e}),style:{width:"100%"},placeholder:"是否keepAlive缓存页面"},{default:p((function(){return[s(Q,{value:!1,label:"否"}),s(Q,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,{label:"CloseTab",prop:"meta.closeTab"},{default:p((function(){return[s(re,{modelValue:W.value.meta.closeTab,"onUpdate:modelValue":l[12]||(l[12]=function(e){return W.value.meta.closeTab=e}),style:{width:"100%"},placeholder:"是否自动关闭tab"},{default:p((function(){return[s(Q,{value:!1,label:"否"}),s(Q,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),s(B,{class:"w-full"},{default:p((function(){return[s(E,{span:8},{default:p((function(){return[s(D,null,{label:p((function(){return[d("div",null,[l[30]||(l[30]=d("span",null," 是否为基础页面 ",-1)),s(ce,{content:"此项选择为是，则不会展示左侧菜单以及顶部信息。",placement:"top",effect:"light"},{default:p((function(){return[s(_,null,{default:p((function(){return[s(w(b))]})),_:1})]})),_:1})])]})),default:p((function(){return[s(re,{modelValue:W.value.meta.defaultMenu,"onUpdate:modelValue":l[13]||(l[13]=function(e){return W.value.meta.defaultMenu=e}),style:{width:"100%"},placeholder:"是否为基础页面"},{default:p((function(){return[s(Q,{value:!1,label:"否"}),s(Q,{value:!0,label:"是"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1}),s(E,{span:8},{default:p((function(){return[s(D,null,{label:p((function(){return[d("div",null,[l[31]||(l[31]=d("span",null," 路由切换动画 ",-1)),s(ce,{content:"如果设置了路由切换动画，在本路由下的动画优先级高于全局动画切换优先级。",placement:"top",effect:"light"},{default:p((function(){return[s(_,null,{default:p((function(){return[s(w(b))]})),_:1})]})),_:1})])]})),default:p((function(){return[s(re,{modelValue:W.value.meta.transitionType,"onUpdate:modelValue":l[14]||(l[14]=function(e){return W.value.meta.transitionType=e}),style:{width:"100%"},placeholder:"跟随全局",clearable:""},{default:p((function(){return[s(Q,{value:"fade",label:"淡入淡出"}),s(Q,{value:"slide",label:"滑动"}),s(Q,{value:"zoom",label:"缩放"}),s(Q,{value:"none",label:"无动画"})]})),_:1},8,["modelValue"])]})),_:1})]})),_:1})]})),_:1})]})),_:1},8,["model","rules"])):h("",!0),d("div",null,[d("div",q,[s(u,{type:"primary",icon:"edit",onClick:l[15]||(l[15]=function(e){return function(e){e.parameters||(e.parameters=[]),e.parameters.push({type:"query",key:"",value:""})}(W.value)})},{default:p((function(){return l[32]||(l[32]=[m(" 新增菜单参数 ")])})),_:1})]),s(x,{data:W.value.parameters,style:{width:"100%","margin-top":"12px"}},{default:p((function(){return[s(o,{align:"left",prop:"type",label:"参数类型",width:"180"},{default:p((function(e){return[s(re,{modelValue:e.row.type,"onUpdate:modelValue":function(t){return e.row.type=t},placeholder:"请选择"},{default:p((function(){return[s(Q,{key:"query",value:"query",label:"query"}),s(Q,{key:"params",value:"params",label:"params"})]})),_:2},1032,["modelValue","onUpdate:modelValue"])]})),_:1}),s(o,{align:"left",prop:"key",label:"参数key",width:"180"},{default:p((function(e){return[d("div",null,[s(C,{modelValue:e.row.key,"onUpdate:modelValue":function(t){return e.row.key=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),s(o,{align:"left",prop:"value",label:"参数值"},{default:p((function(e){return[d("div",null,[s(C,{modelValue:e.row.value,"onUpdate:modelValue":function(t){return e.row.value=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),s(o,{align:"left"},{default:p((function(e){return[d("div",null,[s(u,{type:"danger",icon:"delete",onClick:function(t){return n=W.value.parameters,r=e.$index,void n.splice(r,1);var n,r}},{default:p((function(){return l[33]||(l[33]=[m(" 删除 ")])})),_:2},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"]),d("div",M,[s(u,{type:"primary",icon:"edit",onClick:l[16]||(l[16]=function(e){return function(e){e.menuBtn||(e.menuBtn=[]),e.menuBtn.push({name:"",desc:""})}(W.value)})},{default:p((function(){return l[34]||(l[34]=[m(" 新增可控按钮 ")])})),_:1}),s(_,{class:"cursor-pointer",onClick:l[17]||(l[17]=function(e){return w(j)("https://www.gin-vue-admin.com/guide/web/button-auth.html")})},{default:p((function(){return[s(w(b))]})),_:1})]),s(x,{data:W.value.menuBtn,style:{width:"100%","margin-top":"12px"}},{default:p((function(){return[s(o,{align:"left",prop:"name",label:"按钮名称",width:"180"},{default:p((function(e){return[d("div",null,[s(C,{modelValue:e.row.name,"onUpdate:modelValue":function(t){return e.row.name=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),s(o,{align:"left",prop:"name",label:"备注",width:"180"},{default:p((function(e){return[d("div",null,[s(C,{modelValue:e.row.desc,"onUpdate:modelValue":function(t){return e.row.desc=t}},null,8,["modelValue","onUpdate:modelValue"])])]})),_:1}),s(o,{align:"left"},{default:p((function(e){return[d("div",null,[s(u,{type:"danger",icon:"delete",onClick:function(t){return H(W.value.menuBtn,e.$index)}},{default:p((function(){return l[35]||(l[35]=[m(" 删除 ")])})),_:2},1032,["onClick"])])]})),_:1})]})),_:1},8,["data"])])]})),_:1},8,["modelValue","size"])])}}});e("default",a($,[["__scopeId","data-v-63e1540b"]]))}}}))}();
