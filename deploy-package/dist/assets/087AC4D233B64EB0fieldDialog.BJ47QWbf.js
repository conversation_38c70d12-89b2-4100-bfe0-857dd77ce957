/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{a as e,t as l}from"./087AC4D233B64EB0stringFun.BxqK0MAg.js";import{a,p as u,g as d,c as t,o,f as n,w as m,b as r,F as i,D as s,i as p,t as c,h as f,d as v,aO as b,ab as y}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as V}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{g,a as _,c as h}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";const N={style:{float:"right",color:"#8492a6","font-size":"13px"}},T={style:{"font-weight":"bold"}},S={style:{float:"left"}},U={style:{float:"right","margin-left":"5px",color:"var(--el-text-color-secondary)","font-size":"13px"}},x={style:{"font-weight":"bold"}},w={style:{float:"left"}},D={style:{float:"right","margin-left":"5px",color:"var(--el-text-color-secondary)","font-size":"13px"}},B={key:0},k=Object.assign({name:"FieldDialog"},{__name:"fieldDialog",props:{dialogMiddle:{type:Object,default:function(){return{}}},typeOptions:{type:Array,default:function(){return[]}},typeSearchOptions:{type:Array,default:function(){return[]}},typeIndexOptions:{type:Array,default:function(){return[]}}},setup(k,{expose:E}){const C=k,O=a([]),j=a({}),A=a([]),I=a([]),L=a({fieldName:[{required:!0,message:"请输入字段英文名",trigger:"blur"}],fieldDesc:[{required:!0,message:"请输入字段中文名",trigger:"blur"}],fieldJson:[{required:!0,message:"请输入字段格式化json",trigger:"blur"}],columnName:[{required:!0,message:"请输入数据库字段",trigger:"blur"}],fieldType:[{required:!0,message:"请选择字段类型",trigger:"blur"}],dataTypeLong:[{validator:(e,l,a)=>{"enum"!=j.value.fieldType||/^('([^']*)'(?:,'([^']+)'*)*)$/.test(l)?a():a(new Error("枚举值校验错误"))},trigger:"blur"}]});(async()=>{j.value=C.dialogMiddle;const e=await b({page:1,pageSize:999999});A.value=e.data})();const q=()=>{j.value.fieldJson=e(j.value.fieldName),j.value.columnName=l(j.value.fieldJson)},F=e=>{const l=j.value.fieldType;if("richtext"===l)return"LIKE"!==e;if("string"!==l&&"LIKE"===e)return!0;return!(["int","time.Time","float64"].includes(l)||!["BETWEEN","NOT BETWEEN"].includes(e))},J=()=>{j.value.fieldSearchType="",j.value.dictType=""},K=e=>{2===e&&y.confirm("一对多关联模式下，数据类型会改变为数组，后端表现为json，具体表现为数组模式，是否继续？","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((()=>{j.value.fieldType="array"})).catch((()=>{j.value.dataSource.association=1}))},z=()=>{j.value.dataSource.value="",j.value.dataSource.label=""},P=()=>{M(),j.value.dataSource.table="",z()},H=a([]),M=async()=>{const e=await h({businessDB:j.value.dataSource.dbName});if(0===e.code){let l=e.data.tables;H.value=l.map((e=>({tableName:e.tableName,value:e.tableName})))}z()},W=a([]),G=async(e,l)=>{j.value.dataSource.hasDeletedAt=!1,j.value.dataSource.table=e;const a=await _({businessDB:j.value.dataSource.dbName,tableName:e});if(0===a.code){let e=a.data.columns;W.value=e.map((e=>("deleted_at"===e.columnName&&(j.value.dataSource.hasDeletedAt=!0),{columnName:e.columnName,value:e.columnName,type:e.dataType,isPrimary:e.primaryKey,comment:e.columnComment}))),W.value.length>0&&!l&&(j.value.dataSource.label=W.value[0].columnName,j.value.dataSource.value=W.value[0].columnName)}},$=a(null);return E({fieldDialogForm:$}),u((()=>{(async()=>{const e=await g();0===e.code&&(I.value=e.data.dbList)})(),j.value.dataSource.table&&G(j.value.dataSource.table,!0)})),(e,l)=>{const a=d("el-input"),u=d("el-button"),b=d("el-form-item"),y=d("el-option"),g=d("el-select"),_=d("el-checkbox"),h=d("el-switch"),E=d("el-form"),C=d("el-col"),Q=d("el-tag"),R=d("block"),X=d("el-row"),Y=d("el-collapse-item"),Z=d("el-collapse");return o(),t("div",null,[n(V,{title:"id , created_at , updated_at , deleted_at 会自动生成请勿重复创建。搜索时如果条件为LIKE只支持字符串"}),n(E,{ref_key:"fieldDialogForm",ref:$,model:j.value,"label-width":"120px","label-position":"right",rules:L.value,class:"grid grid-cols-2"},{default:m((()=>[n(b,{label:"字段名称",prop:"fieldName"},{default:m((()=>[n(a,{modelValue:j.value.fieldName,"onUpdate:modelValue":l[0]||(l[0]=e=>j.value.fieldName=e),autocomplete:"off",style:{width:"80%"}},null,8,["modelValue"]),n(u,{style:{width:"18%","margin-left":"2%"},onClick:q},{default:m((()=>l[27]||(l[27]=[r("span",{style:{"font-size":"12px"}},"自动填充",-1)]))),_:1})])),_:1}),n(b,{label:"字段中文名",prop:"fieldDesc"},{default:m((()=>[n(a,{modelValue:j.value.fieldDesc,"onUpdate:modelValue":l[1]||(l[1]=e=>j.value.fieldDesc=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(b,{label:"字段JSON",prop:"fieldJson"},{default:m((()=>[n(a,{modelValue:j.value.fieldJson,"onUpdate:modelValue":l[2]||(l[2]=e=>j.value.fieldJson=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(b,{label:"数据库字段名",prop:"columnName"},{default:m((()=>[n(a,{modelValue:j.value.columnName,"onUpdate:modelValue":l[3]||(l[3]=e=>j.value.columnName=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(b,{label:"数据库字段描述",prop:"comment"},{default:m((()=>[n(a,{modelValue:j.value.comment,"onUpdate:modelValue":l[4]||(l[4]=e=>j.value.comment=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),n(b,{label:"字段类型",prop:"fieldType"},{default:m((()=>[n(g,{modelValue:j.value.fieldType,"onUpdate:modelValue":l[5]||(l[5]=e=>j.value.fieldType=e),style:{width:"100%"},placeholder:"请选择字段类型",clearable:"",onChange:J},{default:m((()=>[(o(!0),t(i,null,s(k.typeOptions,(e=>(o(),p(y,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(b,{label:"enum"===j.value.fieldType?"枚举值":"类型长度",prop:"dataTypeLong"},{default:m((()=>[n(a,{modelValue:j.value.dataTypeLong,"onUpdate:modelValue":l[6]||(l[6]=e=>j.value.dataTypeLong=e),placeholder:"enum"===j.value.fieldType?"例:'北京','天津'":"数据库类型长度"},null,8,["modelValue","placeholder"])])),_:1},8,["label"]),n(b,{label:"字段查询条件",prop:"fieldSearchType"},{default:m((()=>[n(g,{modelValue:j.value.fieldSearchType,"onUpdate:modelValue":l[7]||(l[7]=e=>j.value.fieldSearchType=e),disabled:"json"===j.value.fieldType,style:{width:"100%"},placeholder:"请选择字段查询条件",clearable:""},{default:m((()=>[(o(!0),t(i,null,s(k.typeSearchOptions,(e=>(o(),p(y,{key:e.value,label:e.label,value:e.value,disabled:F(e.value)},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),n(b,{label:"关联字典",prop:"dictType"},{default:m((()=>[n(g,{modelValue:j.value.dictType,"onUpdate:modelValue":l[8]||(l[8]=e=>j.value.dictType=e),style:{width:"100%"},disabled:"string"!==j.value.fieldType&&"array"!==j.value.fieldType,placeholder:"请选择字典",clearable:""},{default:m((()=>[(o(!0),t(i,null,s(A.value,(e=>(o(),p(y,{key:e.type,label:"".concat(e.type,"(").concat(e.name,")"),value:e.type},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),n(b,{label:"默认值"},{default:m((()=>[n(a,{modelValue:j.value.defaultValue,"onUpdate:modelValue":l[9]||(l[9]=e=>j.value.defaultValue=e),placeholder:"请输入默认值"},null,8,["modelValue"])])),_:1}),n(b,{label:"主键"},{default:m((()=>[n(_,{modelValue:j.value.primaryKey,"onUpdate:modelValue":l[10]||(l[10]=e=>j.value.primaryKey=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"索引类型",prop:"fieldIndexType"},{default:m((()=>[n(g,{modelValue:j.value.fieldIndexType,"onUpdate:modelValue":l[11]||(l[11]=e=>j.value.fieldIndexType=e),disabled:"json"===j.value.fieldType,style:{width:"100%"},placeholder:"请选择字段索引类型",clearable:""},{default:m((()=>[(o(!0),t(i,null,s(k.typeIndexOptions,(e=>(o(),p(y,{key:e.value,label:e.label,value:e.value,disabled:F(e.value)},null,8,["label","value","disabled"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),n(b,{label:"前端新建/编辑"},{default:m((()=>[n(h,{modelValue:j.value.form,"onUpdate:modelValue":l[12]||(l[12]=e=>j.value.form=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"前端表格列"},{default:m((()=>[n(h,{modelValue:j.value.table,"onUpdate:modelValue":l[13]||(l[13]=e=>j.value.table=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"前端详情"},{default:m((()=>[n(h,{modelValue:j.value.desc,"onUpdate:modelValue":l[14]||(l[14]=e=>j.value.desc=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"导入/导出"},{default:m((()=>[n(h,{modelValue:j.value.excel,"onUpdate:modelValue":l[15]||(l[15]=e=>j.value.excel=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"是否排序"},{default:m((()=>[n(h,{modelValue:j.value.sort,"onUpdate:modelValue":l[16]||(l[16]=e=>j.value.sort=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"是否必填"},{default:m((()=>[n(h,{modelValue:j.value.require,"onUpdate:modelValue":l[17]||(l[17]=e=>j.value.require=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"是否可清空"},{default:m((()=>[n(h,{modelValue:j.value.clearable,"onUpdate:modelValue":l[18]||(l[18]=e=>j.value.clearable=e)},null,8,["modelValue"])])),_:1}),n(b,{label:"隐藏查询条件"},{default:m((()=>[n(h,{modelValue:j.value.fieldSearchHide,"onUpdate:modelValue":l[19]||(l[19]=e=>j.value.fieldSearchHide=e),disabled:!j.value.fieldSearchType},null,8,["modelValue","disabled"])])),_:1}),n(b,{label:"校验失败文案"},{default:m((()=>[n(a,{modelValue:j.value.errorText,"onUpdate:modelValue":l[20]||(l[20]=e=>j.value.errorText=e)},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"]),n(Z,{modelValue:O.value,"onUpdate:modelValue":l[26]||(l[26]=e=>O.value=e)},{default:m((()=>[n(Y,{title:"数据源配置（此配置为高级配置，如编程基础不牢，可能导致自动化代码不可用）",name:"1"},{default:m((()=>[n(X,{gutter:8},{default:m((()=>[n(C,{span:4},{default:m((()=>[n(g,{modelValue:j.value.dataSource.dbName,"onUpdate:modelValue":l[21]||(l[21]=e=>j.value.dataSource.dbName=e),placeholder:"数据库【不填则为GVA库】",onChange:P,clearable:""},{default:m((()=>[(o(!0),t(i,null,s(I.value,(e=>(o(),p(y,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:m((()=>[r("div",null,[r("span",null,c(e.aliasName),1),r("span",N,c(e.dbName),1)])])),_:2},1032,["value","label","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(C,{span:4},{default:m((()=>[n(g,{modelValue:j.value.dataSource.association,"onUpdate:modelValue":l[22]||(l[22]=e=>j.value.dataSource.association=e),placeholder:"关联模式",onChange:K},{default:m((()=>[n(y,{label:"一对一",value:1}),n(y,{label:"一对多",value:2})])),_:1},8,["modelValue"])])),_:1}),n(C,{span:5},{default:m((()=>[n(g,{modelValue:j.value.dataSource.table,"onUpdate:modelValue":l[23]||(l[23]=e=>j.value.dataSource.table=e),placeholder:"请选择数据源表",filterable:"","allow-create":"",clearable:"",onFocus:M,onChange:G,onClear:z},{default:m((()=>[(o(!0),t(i,null,s(H.value,(e=>(o(),p(y,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(C,{span:5},{default:m((()=>[n(g,{modelValue:j.value.dataSource.value,"onUpdate:modelValue":l[24]||(l[24]=e=>j.value.dataSource.value=e),placeholder:"请先选择需要存储的数据"},{label:m((({value:e})=>[l[28]||(l[28]=r("span",null,"存储: ",-1)),r("span",T,c(e),1)])),default:m((()=>[(o(!0),t(i,null,s(W.value,(e=>(o(),p(y,{key:e.columnName,value:e.columnName},{default:m((()=>[r("span",S,[n(Q,{type:e.isPrimary?"primary":"info"},{default:m((()=>[f(c(e.isPrimary?"主 键":"非主键"),1)])),_:2},1032,["type"]),f(" "+c(e.columnName),1)]),r("span",U,[f(" 类型："+c(e.type)+" ",1),""!=e.comment?(o(),p(R,{key:0},{default:m((()=>[f("，字段说明："+c(e.comment),1)])),_:2},1024)):v("",!0)])])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])),_:1}),n(C,{span:5},{default:m((()=>[n(g,{modelValue:j.value.dataSource.label,"onUpdate:modelValue":l[25]||(l[25]=e=>j.value.dataSource.label=e),placeholder:"请先选择需要展示的数据"},{label:m((({value:e})=>[l[29]||(l[29]=r("span",null,"展示: ",-1)),r("span",x,c(e),1)])),default:m((()=>[(o(!0),t(i,null,s(W.value,(e=>(o(),p(y,{key:e.columnName,value:e.columnName},{default:m((()=>[r("span",w,[n(Q,{type:e.isPrimary?"primary":"info"},{default:m((()=>[f(c(e.isPrimary?"主 键":"非主键"),1)])),_:2},1032,["type"]),f(" "+c(e.columnName),1)]),r("span",D,[f(" 类型："+c(e.type)+" ",1),""!=e.comment?(o(),t("span",B,"，字段说明："+c(e.comment),1)):v("",!0)])])),_:2},1032,["value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1},8,["modelValue"])])}}});export{k as default};
