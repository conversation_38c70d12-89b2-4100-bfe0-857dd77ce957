/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e,a,p as l,u as o,q as t,c as u,b as s,f as n,w as r,v as c,x as i,g as d,i as p,d as v,F as f,o as m,y as g,z as h,A as x,B as w,h as y,t as k,E as b,j as C}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{M as _}from"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";const R={class:"flex justify-center w-full pt-2"},B={class:"flex flex-col w-full h-auto p-0 pt-4"},j={class:"flex-1 min-h-[60vh]"},z={class:"w-screen h-[calc(100vh-175px)] rounded"},A={key:1,class:"flex justify-center items-center w-full h-[calc(100vh-175px)]"},D={class:"toolbar"},U=e(Object.assign({name:"scanUpload"},{__name:"scanUpload",setup(e){const U=a(0),E=a(""),F=a(!1),I=a(300),S=function(){I.value=window.innerWidth};l((()=>{S(),window.addEventListener("resize",S)}));const $=o();$.isReady().then((()=>{let e=$.currentRoute.value.query;U.value=e.id,E.value=e.token})).catch((e=>{}));const L=a(null),V=a(""),W=a(null),{proxy:q}=t(),H=a({}),M=a(!1),N=e=>{q.$refs.cropperRef.changeScale(e)},O=a([1,1]),T=a(300),G=a(300),J=a(!1),K=e=>{if(!e.raw.type.includes("image"))return void b.error("请选择图片文件");if(e.raw.size/1024/1024>8)return b.error("文件大小不能超过8MB!"),!1;const a=C.service({lock:!0,text:"请稍后",background:"rgba(0, 0, 0, 0.7)"}),l=new FileReader;l.onload=e=>{V.value=e.target.result,a.close()},l.readAsDataURL(e.raw)},P=e=>{-90===e?q.$refs.cropperRef.rotateLeft():q.$refs.cropperRef.rotateRight()},Q=()=>{if(M.value=!0,!1===F.value)return L.value.submit(),!0;q.$refs.cropperRef.getCropBlob((e=>{try{const a=new File([e],"".concat(Date.now(),".jpg"),{type:"image/jpeg"});L.value.clearFiles(),L.value.handleStart(a),L.value.submit()}catch(a){M.value=!1,b.error("上传失败: "+a.message)}}))},X=e=>{const{data:a}=e;a&&(V.value=null,M.value=!1,H.value={},b.success("上传成功"))};return(e,a)=>{const l=d("el-icon"),o=d("el-upload"),t=d("el-image"),b=d("el-button"),C=d("el-tooltip"),S=d("el-button-group"),$=d("el-switch");return m(),u(f,null,[s("div",R,[n(o,{ref_key:"uploadRef",ref:L,class:"h5-uploader",action:"".concat(c(i)(),"/fileUploadAndDownload/upload"),accept:"image/*","show-file-list":!1,"auto-upload":!1,headers:{"x-token":E.value},data:{classId:U.value},"on-success":X,"on-change":K},{default:r((()=>[n(l,{class:"h5-uploader-icon"},{default:r((()=>[n(c(g))])),_:1})])),_:1},8,["action","headers","data"])]),s("div",B,[s("div",j,[s("div",z,[F.value?(m(),p(c(_),{key:0,ref_key:"cropperRef",ref:W,img:V.value,mode:"contain",outputType:"jpeg",autoCrop:!0,autoCropWidth:T.value,autoCropHeight:G.value,fixedBox:!1,fixed:J.value,fixedNumber:O.value,centerBox:!0,canMoveBox:!0,full:!1,maxImgSize:I.value,original:!0},null,8,["img","autoCropWidth","autoCropHeight","fixed","fixedNumber","maxImgSize"])):(m(),u("div",A,[V.value?(m(),p(t,{key:0,src:V.value,class:"max-w-full max-h-full",mode:"cover"},null,8,["src"])):v("",!0)]))])])]),s("div",D,[F.value?(m(),p(S,{key:0},{default:r((()=>[n(C,{content:"向左旋转"},{default:r((()=>[n(b,{onClick:a[0]||(a[0]=e=>P(-90)),icon:c(h)},null,8,["icon"])])),_:1}),n(C,{content:"向右旋转"},{default:r((()=>[n(b,{onClick:a[1]||(a[1]=e=>P(90)),icon:c(x)},null,8,["icon"])])),_:1}),n(b,{icon:c(g),onClick:a[2]||(a[2]=e=>N(1))},null,8,["icon"]),n(b,{icon:c(w),onClick:a[3]||(a[3]=e=>N(-1))},null,8,["icon"])])),_:1})):v("",!0),n($,{size:"large",modelValue:F.value,"onUpdate:modelValue":a[4]||(a[4]=e=>F.value=e),"inline-prompt":"","active-text":"裁剪","inactive-text":"裁剪"},null,8,["modelValue"]),n(b,{type:"primary",onClick:Q,loading:M.value},{default:r((()=>[y(k(M.value?"上传中...":"上 传"),1)])),_:1},8,["loading"])])],64)}}}),[["__scopeId","data-v-71b771ce"]]);export{U as default};
