/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
class t{constructor(t,e,i=1920){this.file=t,this.fileSize=e,this.maxWH=i}compress(){const t=this.file.type;this.file.size;return new Promise((e=>{const i=new FileReader;i.readAsDataURL(this.file),i.onload=()=>{const s=document.createElement("canvas"),o=document.createElement("img");o.src=i.result,o.onload=()=>{const i=s.getContext("2d"),n=this.dWH(o.width,o.height,this.maxWH);s.width=n.width,s.height=n.height,i.clearRect(0,0,s.width,s.height),i.drawImage(o,0,0,s.width,s.height);const a=s.toDataURL(t,.9);this.fileSizeKB(a);this.fileSize;const h=this.dataURLtoBlob(a,t),l=new File([h],this.file.name);e(l)}}}))}dWH(t,e,i){const s={width:t,height:e};return Math.max(t,e)>i?t>e?(s.width=i,s.height=Math.round(e*(i/t)),s):(s.height=i,s.width=Math.round(t*(i/e)),s):s}fileSizeKB(t){let e=0;return e=Math.round(3*t.split(",")[1].length/4/1024),e}dataURLtoBlob(t,e){const i=atob(t.split(",")[1]);let s=t.split(",")[0].split(":")[1].split(";")[0];const o=new ArrayBuffer(i.length),n=new Uint8Array(o);for(let a=0;a<i.length;a++)n[a]=i.charCodeAt(a);return e&&(s=e),new Blob([o],{type:s,lastModifiedDate:new Date})}}const e="/api",i=t=>t&&"http"!==t.slice(0,4)?"/"===t.slice(0,1)?e+t:e+"/"+t:t,s=[".mp4",".mov",".webm",".ogg"],o=["video/mp4","video/webm","video/ogg"],n=["image/jpeg","image/png","image/webp","image/svg+xml"],a=t=>{const e=(null==t?void 0:t.toLowerCase())||"";return""!==e&&s.some((t=>e.endsWith(t)))},h=t=>{const e=(null==t?void 0:t.toLowerCase())||"";return""!==e&&o.includes(e)},l=t=>{const e=(null==t?void 0:t.toLowerCase())||"";return""!==e&&n.includes(e)};export{t as I,l as a,a as b,i as g,h as i};
