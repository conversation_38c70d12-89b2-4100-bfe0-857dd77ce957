/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return r};var n,r={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},l="function"==typeof Symbol?Symbol:{},u=l.iterator||"@@iterator",c=l.asyncIterator||"@@asyncIterator",s=l.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(n){f=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var o=t&&t.prototype instanceof w?t:w,a=Object.create(o.prototype),l=new F(r||[]);return i(a,"_invoke",{value:C(e,n,l)}),a}function h(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}r.wrap=d;var m="suspendedStart",p="suspendedYield",v="executing",y="completed",g={};function w(){}function b(){}function x(){}var _={};f(_,u,(function(){return this}));var V=Object.getPrototypeOf,E=V&&V(V(U([])));E&&E!==o&&a.call(E,u)&&(_=E);var j=x.prototype=w.prototype=Object.create(_);function k(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function L(t,n){function r(o,i,l,u){var c=h(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==e(f)&&a.call(f,"__await")?n.resolve(f.__await).then((function(e){r("next",e,l,u)}),(function(e){r("throw",e,l,u)})):n.resolve(f).then((function(e){s.value=e,l(s)}),(function(e){return r("throw",e,l,u)}))}u(c.arg)}var o;i(this,"_invoke",{value:function(e,t){function a(){return new n((function(n,o){r(e,t,n,o)}))}return o=o?o.then(a,a):a()}})}function C(e,t,r){var o=m;return function(a,i){if(o===v)throw Error("Generator is already running");if(o===y){if("throw"===a)throw i;return{value:n,done:!0}}for(r.method=a,r.arg=i;;){var l=r.delegate;if(l){var u=S(l,r);if(u){if(u===g)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===m)throw o=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var c=h(e,t,r);if("normal"===c.type){if(o=r.done?y:p,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=y,r.method="throw",r.arg=c.arg)}}}function S(e,t){var r=t.method,o=e.iterator[r];if(o===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=n,S(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var a=h(o,e.iterator,t.arg);if("throw"===a.type)return t.method="throw",t.arg=a.arg,t.delegate=null,g;var i=a.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,g):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function F(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function U(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function e(){for(;++o<t.length;)if(a.call(t,o))return e.value=t[o],e.done=!1,e;return e.value=n,e.done=!0,e};return i.next=i}}throw new TypeError(e(t)+" is not iterable")}return b.prototype=x,i(j,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,s,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,f(e,s,"GeneratorFunction")),e.prototype=Object.create(j),e},r.awrap=function(e){return{__await:e}},k(L.prototype),f(L.prototype,c,(function(){return this})),r.AsyncIterator=L,r.async=function(e,t,n,o,a){void 0===a&&(a=Promise);var i=new L(d(e,t,n,o),a);return r.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},k(j),f(j,s,"Generator"),f(j,u,(function(){return this})),f(j,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},r.values=U,F.prototype={constructor:F,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(B),!e)for(var t in this)"t"===t.charAt(0)&&a.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,o){return l.type="throw",l.arg=e,t.next=r,o&&(t.method="next",t.arg=n),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],l=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var u=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(u&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(u){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&a.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;B(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:U(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),g}},r}function n(e,t,n,r,o,a,i){try{var l=e[a](i),u=l.value}catch(e){return void n(e)}l.done?t(u):Promise.resolve(u).then(r,o)}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0title-legacy.ByWsba41.js"],(function(e,r){"use strict";var o,a,i,l,u,c,s,f,d,h,m,p,v,y,g,w,b,x,_,V,E,j;return{setters:[function(e){o=e._,a=e.I,i=e.J,l=e.K,u=e.ap,c=e.a,s=e.g,f=e.i,d=e.o,h=e.w,m=e.b,p=e.f,v=e.v,y=e.c,g=e.F,w=e.D,b=e.X,x=e.d,_=e.h,V=e.aq,E=e.E},function(e){j=e.default}],execute:function(){var r=document.createElement("style");r.textContent="[data-v-eb18a363] .el-drawer__header{--tw-border-opacity: 1;border-color:rgb(156 163 175 / var(--tw-border-opacity, 1))}[data-v-eb18a363] .el-drawer__header:is(.dark *){--tw-border-opacity: 1;border-color:rgb(75 85 99 / var(--tw-border-opacity, 1))}\n/*$vite$:1*/",document.head.appendChild(r);var k={class:"flex justify-between items-center"},L={class:"flex flex-col"},C={class:"mb-8"},S={class:"mt-2 text-sm p-2 flex items-center justify-center gap-2"},O={class:"mb-8"},B={class:"mt-2 text-sm p-2 flex items-center gap-2 justify-center"},F=["onClick"],U={class:"mb-8"},P={class:"mt-2 text-md p-2 flex flex-col gap-2"},G={class:"flex items-center justify-between"},T={class:"flex items-center justify-between"},N={class:"flex items-center justify-between"},I={class:"flex items-center justify-between"},z={class:"flex items-center justify-between"},D={class:"flex items-center justify-between gap-2"},M={class:"mb-8"},A={class:"mt-2 text-md p-2 flex flex-col gap-2"},J={class:"flex items-center justify-between mb-2"},W={class:"flex items-center justify-between mb-2"},Y={class:"flex items-center justify-between mb-2"},$=Object.assign({name:"GvaSetting"},{__name:"index",props:{drawer:{default:!0,type:Boolean},drawerModifiers:{}},emits:["update:drawer"],setup:function(e){var r=a(),o=i(r),$=o.config,q=o.device,K=l((function(){return"mobile"===q.value?"100%":"500px"})),X=["#EB2F96","#3b82f6","#2FEB54","#EBEB2F","#EB2F2F","#2FEBEB"],H=u(e,"drawer"),Q=["dark","light","auto"],R=[{label:"正常模式",value:"normal"},{label:"顶部菜单栏模式",value:"head"},{label:"组合模式",value:"combination"}],Z=function(){var e,r=(e=t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,V($.value);case 2:0===e.sent.code&&(localStorage.setItem("originSetting",JSON.stringify($.value)),E.success("保存成功"),H.value=!1);case 4:case"end":return e.stop()}}),e)})),function(){var t=this,r=arguments;return new Promise((function(o,a){var i=e.apply(t,r);function l(e){n(i,o,a,l,u,"next",e)}function u(e){n(i,o,a,l,u,"throw",e)}l(void 0)}))});return function(){return r.apply(this,arguments)}}(),ee=c("");return function(e,t){var n=s("el-button"),o=s("el-segmented"),a=s("Select"),i=s("el-icon"),l=s("el-color-picker"),u=s("el-switch"),c=s("el-option"),V=s("el-select"),E=s("el-input-number"),q=s("el-drawer");return d(),f(q,{modelValue:H.value,"onUpdate:modelValue":t[11]||(t[11]=function(e){return H.value=e}),title:"系统配置",direction:"rtl",size:K.value,"show-close":!1},{header:h((function(){return[m("div",k,[t[13]||(t[13]=m("span",{class:"text-lg"},"系统配置",-1)),p(n,{type:"primary",onClick:Z},{default:h((function(){return t[12]||(t[12]=[_("保存配置")])})),_:1})])]})),default:h((function(){return[m("div",L,[m("div",C,[p(j,{title:"默认主题"}),m("div",S,[p(o,{modelValue:v($).darkMode,"onUpdate:modelValue":t[0]||(t[0]=function(e){return v($).darkMode=e}),options:Q,size:"default",onChange:v(r).toggleDarkMode},null,8,["modelValue","onChange"])])]),m("div",O,[p(j,{title:"主题色"}),m("div",B,[(d(),y(g,null,w(X,(function(e){return m("div",{key:e,class:"w-5 h-5 rounded cursor-pointer flex items-center justify-center",style:b("background:".concat(e)),onClick:function(t){return v(r).togglePrimaryColor(e)}},[v($).primaryColor===e?(d(),f(i,{key:0},{default:h((function(){return[p(a)]})),_:1})):x("",!0)],12,F)})),64)),p(l,{modelValue:ee.value,"onUpdate:modelValue":t[1]||(t[1]=function(e){return ee.value=e}),onChange:v(r).togglePrimaryColor},null,8,["modelValue","onChange"])])]),m("div",U,[p(j,{title:"主题配置"}),m("div",P,[m("div",G,[t[14]||(t[14]=m("div",null,"展示水印",-1)),p(u,{modelValue:v($).show_watermark,"onUpdate:modelValue":t[2]||(t[2]=function(e){return v($).show_watermark=e}),onChange:v(r).toggleConfigWatermark},null,8,["modelValue","onChange"])]),m("div",T,[t[15]||(t[15]=m("div",null,"灰色模式",-1)),p(u,{modelValue:v($).grey,"onUpdate:modelValue":t[3]||(t[3]=function(e){return v($).grey=e}),onChange:v(r).toggleGrey},null,8,["modelValue","onChange"])]),m("div",N,[t[16]||(t[16]=m("div",null,"色弱模式",-1)),p(u,{modelValue:v($).weakness,"onUpdate:modelValue":t[4]||(t[4]=function(e){return v($).weakness=e}),onChange:v(r).toggleWeakness},null,8,["modelValue","onChange"])]),m("div",I,[t[17]||(t[17]=m("div",null,"菜单模式",-1)),p(o,{modelValue:v($).side_mode,"onUpdate:modelValue":t[5]||(t[5]=function(e){return v($).side_mode=e}),options:R,size:"default",onChange:v(r).toggleSideMode},null,8,["modelValue","onChange"])]),m("div",z,[t[18]||(t[18]=m("div",null,"显示标签页",-1)),p(u,{modelValue:v($).showTabs,"onUpdate:modelValue":t[6]||(t[6]=function(e){return v($).showTabs=e}),onChange:v(r).toggleTabs},null,8,["modelValue","onChange"])]),m("div",D,[t[19]||(t[19]=m("div",{class:"flex-shrink-0"},"页面切换动画",-1)),p(V,{modelValue:v($).transition_type,"onUpdate:modelValue":t[7]||(t[7]=function(e){return v($).transition_type=e}),onChange:v(r).toggleTransition,class:"w-40"},{default:h((function(){return[p(c,{value:"fade",label:"淡入淡出"}),p(c,{value:"slide",label:"滑动"}),p(c,{value:"zoom",label:"缩放"}),p(c,{value:"none",label:"无动画"})]})),_:1},8,["modelValue","onChange"])])])]),m("div",M,[p(j,{title:"layout 大小配置"}),m("div",A,[m("div",J,[t[20]||(t[20]=m("div",null,"侧边栏展开宽度",-1)),p(E,{modelValue:v($).layout_side_width,"onUpdate:modelValue":t[8]||(t[8]=function(e){return v($).layout_side_width=e}),min:150,max:400,step:10},null,8,["modelValue"])]),m("div",W,[t[21]||(t[21]=m("div",null,"侧边栏收缩宽度",-1)),p(E,{modelValue:v($).layout_side_collapsed_width,"onUpdate:modelValue":t[9]||(t[9]=function(e){return v($).layout_side_collapsed_width=e}),min:60,max:100},null,8,["modelValue"])]),m("div",Y,[t[22]||(t[22]=m("div",null,"侧边栏子项高度",-1)),p(E,{modelValue:v($).layout_side_item_height,"onUpdate:modelValue":t[10]||(t[10]=function(e){return v($).layout_side_item_height=e}),min:30,max:50},null,8,["modelValue"])])])])])]})),_:1},8,["modelValue","size"])}}});e("default",o($,[["__scopeId","data-v-eb18a363"]]))}}}))}();
