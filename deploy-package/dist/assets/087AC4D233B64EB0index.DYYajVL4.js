/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e,a,k as r,K as s,g as c,c as i,o as p,d as l,i as t,F as u,v as n}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const v={class:"headerAvatar"},o=["src"],d=["src"],g=e(Object.assign({name:"CustomPic"},{__name:"index",props:{picType:{type:String,required:!1,default:"avatar"},picSrc:{type:String,required:!1,default:""},preview:{type:Boolean,default:!1}},setup(e){const g=e,m=a("/api/"),y=a("/assets/087AC4D233B64EB0noBody.uscRzRXF.png"),f=r(),I=s((()=>""===g.picSrc?""!==f.userInfo.headerImg&&"http"===f.userInfo.headerImg.slice(0,4)?f.userInfo.headerImg:m.value+f.userInfo.headerImg:""!==g.picSrc&&"http"===g.picSrc.slice(0,4)?g.picSrc:m.value+g.picSrc)),S=s((()=>g.picSrc&&"http"!==g.picSrc.slice(0,4)?m.value+g.picSrc:g.picSrc)),h=s((()=>g.preview?[S.value]:[]));return(a,r)=>{const s=c("el-avatar"),g=c("el-image");return p(),i("span",v,["avatar"===e.picType?(p(),i(u,{key:0},[n(f).userInfo.headerImg?(p(),t(s,{key:0,size:30,src:I.value},null,8,["src"])):(p(),t(s,{key:1,size:30,src:y.value},null,8,["src"]))],64)):l("",!0),"img"===e.picType?(p(),i(u,{key:1},[n(f).userInfo.headerImg?(p(),i("img",{key:0,src:I.value,class:"avatar"},null,8,o)):(p(),i("img",{key:1,src:y.value,class:"avatar"},null,8,d))],64)):l("",!0),"file"===e.picType?(p(),t(g,{key:2,src:S.value,class:"file","preview-src-list":h.value,"preview-teleported":!0},null,8,["src","preview-src-list"])):l("",!0)])}}}),[["__scopeId","data-v-fb5f62ec"]]);export{g as C};
