/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{a as e,I as a,r as l,g as t,c as u,o as n,b as r,f as i,w as d,d as o,h as s,F as c,i as p,l as v,v as m,t as g,ab as f,E as y}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as b,f as h,c as I,u as w,d as C,a as _}from"./087AC4D233B64EB0ozonShop.CCOgv6DF.js";const A={class:"gva-search-box"},D={class:"gva-table-box"},k={class:"gva-btn-list"},V={class:"gva-pagination"},z={class:"flex justify-between items-center"},x={class:"text-lg"},K=Object.assign({name:"OzonShop"},{__name:"ozonShop",setup(K){const T=e(!1),P=a(),S=e(!1),B=e({name:"",clientID:"",APIKey:"",accountID:""}),U=l({name:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],clientID:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],APIKey:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],accountID:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}]}),E=l({createdAt:[{validator:(e,a,l)=>{Q.value.startCreatedAt&&!Q.value.endCreatedAt?l(new Error("请填写结束日期")):!Q.value.startCreatedAt&&Q.value.endCreatedAt?l(new Error("请填写开始日期")):Q.value.startCreatedAt&&Q.value.endCreatedAt&&(Q.value.startCreatedAt.getTime()===Q.value.endCreatedAt.getTime()||Q.value.startCreatedAt.getTime()>Q.value.endCreatedAt.getTime())?l(new Error("开始日期应当早于结束日期")):l()},trigger:"change"}]}),j=e(),F=e(),q=e(1),M=e(0),O=e(10),R=e([]),Q=e({}),W=()=>{Q.value={},L()},G=()=>{var e;null==(e=F.value)||e.validate((async e=>{e&&(q.value=1,L())}))},H=e=>{O.value=e,L()},J=e=>{q.value=e,L()},L=async()=>{const e=await b({page:q.value,pageSize:O.value,...Q.value});0===e.code&&(R.value=e.data.list,M.value=e.data.total,q.value=e.data.page,O.value=e.data.pageSize)};L();(async()=>{})();const N=e([]),X=e=>{N.value=e},Y=async()=>{f.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===N.value.length)return void y({type:"warning",message:"请选择要删除的数据"});N.value&&N.value.map((a=>{e.push(a.ID)}));0===(await _({IDs:e})).code&&(y({type:"success",message:"删除成功"}),R.value.length===e.length&&q.value>1&&q.value--,L())}))},Z=e(""),$=async e=>{0===(await C({ID:e.ID})).code&&(y({type:"success",message:"删除成功"}),1===R.value.length&&q.value>1&&q.value--,L())},ee=e(!1),ae=()=>{ee.value=!1,B.value={name:"",clientID:"",APIKey:"",accountID:""}},le=async()=>{var e;T.value=!0,null==(e=j.value)||e.validate((async e=>{if(!e)return T.value=!1;let a;switch(Z.value){case"create":default:a=await I(B.value);break;case"update":a=await w(B.value)}T.value=!1,0===a.code&&(y({type:"success",message:"创建/更改成功"}),ae(),L())}))},te=e({}),ue=e(!1),ne=async e=>{const a=await h({ID:e.ID});0===a.code&&(te.value=a.data,ue.value=!0)},re=()=>{ue.value=!1,te.value={}};return(e,a)=>{const l=t("QuestionFilled"),y=t("el-icon"),b=t("el-tooltip"),I=t("el-date-picker"),w=t("el-form-item"),C=t("el-button"),_=t("el-form"),K=t("el-table-column"),L=t("InfoFilled"),ie=t("el-table"),de=t("el-pagination"),oe=t("el-input"),se=t("el-drawer"),ce=t("el-descriptions-item"),pe=t("el-descriptions");return n(),u("div",null,[r("div",A,[i(_,{ref_key:"elSearchFormRef",ref:F,inline:!0,model:Q.value,class:"demo-form-inline",rules:E,onKeyup:v(G,["enter"])},{default:d((()=>[i(w,{label:"创建日期",prop:"createdAt"},{label:d((()=>[r("span",null,[a[11]||(a[11]=s(" 创建日期 ")),i(b,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:d((()=>[i(y,null,{default:d((()=>[i(l)])),_:1})])),_:1})])])),default:d((()=>[i(I,{modelValue:Q.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!Q.value.endCreatedAt&&e.getTime()>Q.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[12]||(a[12]=s(" — ")),i(I,{modelValue:Q.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!Q.value.startCreatedAt&&e.getTime()<Q.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),S.value?(n(),u(c,{key:0},[],64)):o("",!0),i(w,null,{default:d((()=>[i(C,{type:"primary",icon:"search",onClick:G},{default:d((()=>a[13]||(a[13]=[s("查询")]))),_:1}),i(C,{icon:"refresh",onClick:W},{default:d((()=>a[14]||(a[14]=[s("重置")]))),_:1}),S.value?(n(),p(C,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[3]||(a[3]=e=>S.value=!1)},{default:d((()=>a[16]||(a[16]=[s("收起")]))),_:1})):(n(),p(C,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[2]||(a[2]=e=>S.value=!0)},{default:d((()=>a[15]||(a[15]=[s("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),r("div",D,[r("div",k,[i(C,{type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>(Z.value="create",void(ee.value=!0)))},{default:d((()=>a[17]||(a[17]=[s("新增")]))),_:1}),i(C,{icon:"delete",style:{"margin-left":"10px"},disabled:!N.value.length,onClick:Y},{default:d((()=>a[18]||(a[18]=[s("删除")]))),_:1},8,["disabled"])]),i(ie,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:R.value,"row-key":"ID",onSelectionChange:X},{default:d((()=>[i(K,{type:"selection",width:"55"}),i(K,{align:"left",label:"店铺名称",prop:"name",width:"120"}),i(K,{align:"left",label:"店铺ID",prop:"clientID",width:"120"}),i(K,{align:"left",label:"APIKey",prop:"APIKey",width:"120"}),i(K,{align:"left",label:"关联的账号",prop:"accountID",width:"120"}),i(K,{align:"left",label:"操作",fixed:"right","min-width":m(P).operateMinWith},{default:d((e=>[i(C,{type:"primary",link:"",class:"table-button",onClick:a=>ne(e.row)},{default:d((()=>[i(y,{style:{"margin-right":"5px"}},{default:d((()=>[i(L)])),_:1}),a[19]||(a[19]=s("查看"))])),_:2},1032,["onClick"]),i(C,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await h({ID:e.ID});Z.value="update",0===a.code&&(B.value=a.data,ee.value=!0)})(e.row)},{default:d((()=>a[20]||(a[20]=[s("编辑")]))),_:2},1032,["onClick"]),i(C,{type:"primary",link:"",icon:"delete",onClick:a=>{return l=e.row,void f.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{$(l)}));var l}},{default:d((()=>a[21]||(a[21]=[s("删除")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),r("div",V,[i(de,{layout:"total, sizes, prev, pager, next, jumper","current-page":q.value,"page-size":O.value,"page-sizes":[10,30,50,100],total:M.value,onCurrentChange:J,onSizeChange:H},null,8,["current-page","page-size","total"])])]),i(se,{"destroy-on-close":"",size:m(P).drawerSize,modelValue:ee.value,"onUpdate:modelValue":a[9]||(a[9]=e=>ee.value=e),"show-close":!1,"before-close":ae},{header:d((()=>[r("div",z,[r("span",x,g("create"===Z.value?"新增":"编辑"),1),r("div",null,[i(C,{loading:T.value,type:"primary",onClick:le},{default:d((()=>a[22]||(a[22]=[s("确 定")]))),_:1},8,["loading"]),i(C,{onClick:ae},{default:d((()=>a[23]||(a[23]=[s("取 消")]))),_:1})])])])),default:d((()=>[i(_,{model:B.value,"label-position":"top",ref_key:"elFormRef",ref:j,rules:U,"label-width":"80px"},{default:d((()=>[i(w,{label:"店铺名称:",prop:"name"},{default:d((()=>[i(oe,{modelValue:B.value.name,"onUpdate:modelValue":a[5]||(a[5]=e=>B.value.name=e),clearable:!0,placeholder:"请输入店铺名称"},null,8,["modelValue"])])),_:1}),i(w,{label:"店铺ID:",prop:"clientID"},{default:d((()=>[i(oe,{modelValue:B.value.clientID,"onUpdate:modelValue":a[6]||(a[6]=e=>B.value.clientID=e),clearable:!0,placeholder:"请输入店铺ID"},null,8,["modelValue"])])),_:1}),i(w,{label:"APIKey:",prop:"APIKey"},{default:d((()=>[i(oe,{modelValue:B.value.APIKey,"onUpdate:modelValue":a[7]||(a[7]=e=>B.value.APIKey=e),clearable:!0,placeholder:"请输入APIKey"},null,8,["modelValue"])])),_:1}),i(w,{label:"关联的账号:",prop:"accountID"},{default:d((()=>[i(oe,{modelValue:B.value.accountID,"onUpdate:modelValue":a[8]||(a[8]=e=>B.value.accountID=e),clearable:!0,placeholder:"请输入关联的账号"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["size","modelValue"]),i(se,{"destroy-on-close":"",size:m(P).drawerSize,modelValue:ue.value,"onUpdate:modelValue":a[10]||(a[10]=e=>ue.value=e),"show-close":!0,"before-close":re,title:"查看"},{default:d((()=>[i(pe,{column:1,border:""},{default:d((()=>[i(ce,{label:"店铺名称"},{default:d((()=>[s(g(te.value.name),1)])),_:1}),i(ce,{label:"店铺ID"},{default:d((()=>[s(g(te.value.clientID),1)])),_:1}),i(ce,{label:"APIKey"},{default:d((()=>[s(g(te.value.APIKey),1)])),_:1}),i(ce,{label:"关联的账号"},{default:d((()=>[s(g(te.value.accountID),1)])),_:1})])),_:1})])),_:1},8,["size","modelValue"])])}}});export{K as default};
