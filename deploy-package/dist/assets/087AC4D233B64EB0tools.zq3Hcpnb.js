/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{I as e,k as l,u as t,ac as o,a as i,r as a,Q as r,g as n,i as d,o as s,w as c,c as u,F as m,D as w,d as b,t as p,b as h,f,h as k,a7 as v,an as y,v as g,n as B,ao as _}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import V from"./087AC4D233B64EB0index.TTT0xrWT.js";import{t as C}from"./087AC4D233B64EB0doc.DwE8vRuh.js";import"./087AC4D233B64EB0title.noh5aIFt.js";const x={key:0,class:"quick-title"},D=["onClick"],E={class:"dialog-footer"},j=Object.assign({name:"CommandMenu"},{__name:"index",setup(g,{expose:B}){const _=e(),V=l(),C=t(),j=t(),O=o(),S=i(!1),T=i(""),q=a([]),A=e=>{const l=[];return null==e||e.forEach((e=>{(null==e?void 0:e.children)&&(e.children&&e.children.length>0?l.push(...A(e.children)):e.meta.title&&e.meta.title.indexOf(T.value)>-1&&l.push({label:e.meta.title,func:()=>N(e)}))})),l},M=()=>{var e;const l={label:"跳转",children:[]},t=A((null==(e=O.asyncRouters[0])?void 0:e.children)||[]);l.children.push(...t),q.push(l)},I=()=>{const e={label:"操作",children:[]},l=[{label:"亮色主题",func:()=>U(!1)},{label:"暗色主题",func:()=>U(!0)},{label:"退出登录",func:()=>V.LoginOut()}];e.children.push(...l.filter((e=>e.label.indexOf(T.value)>-1))),q.push(e)};M(),I();const N=e=>{var l,t;const o=e.name,i={},a={};(null==(l=O.routeMap[o])?void 0:l.parameters)&&(null==(t=O.routeMap[o])||t.parameters.forEach((e=>{"query"===e.type?i[e.key]=e.value:a[e.key]=e.value}))),o!==j.name&&(e.name.indexOf("http://")>-1||e.name.indexOf("https://")>-1?window.open(e.name):C.push({name:o,query:i,params:a}),S.value=!1)},U=e=>{_.toggleTheme(e)},K=()=>{S.value=!1};return B({open:()=>{S.value=!0}}),r(T,(()=>{q.length=0,M(),I()})),(e,l)=>{const t=n("el-button"),o=n("el-dialog");return s(),d(o,{modelValue:S.value,"onUpdate:modelValue":l[1]||(l[1]=e=>S.value=e),width:"30%",class:"overlay","show-close":!1},{header:c((()=>[v(h("input",{"onUpdate:modelValue":l[0]||(l[0]=e=>T.value=e),class:"quick-input",placeholder:"请输入你需要快捷到达的功能"},null,512),[[y,T.value]])])),footer:c((()=>[h("span",E,[f(t,{onClick:K},{default:c((()=>l[2]||(l[2]=[k("关闭")]))),_:1})])])),default:c((()=>[(s(!0),u(m,null,w(q,((e,l)=>(s(),u("div",{key:l},[e.children.length?(s(),u("div",x,p(e.label),1)):b("",!0),(s(!0),u(m,null,w(e.children,((e,t)=>(s(),u("div",{key:l+"-"+t,class:"quick-item",onClick:e.func},p(e.label),9,D)))),128))])))),128))])),_:1},8,["modelValue"])}}}),O={class:"flex items-center mx-4 gap-4"},S={__name:"tools",setup(l){const t=e(),o=i(!1),a=i(!1),r=()=>{a.value=!0,_.emit("reload"),setTimeout((()=>{a.value=!1}),1e3)},b=()=>{o.value=!0},h=i(""),v=i(),y=()=>{v.value.open()};(()=>{"WIN"===window.localStorage.getItem("osType")?h.value="Ctrl":h.value="⌘";window.addEventListener("keydown",(e=>{e.ctrlKey&&"k"===e.key&&(e.preventDefault(),y())}))})();const x=[{title:"1.clone项目和安装依赖",link:"https://www.bilibili.com/video/BV1jx4y1s7xx"},{title:"2.初始化项目",link:"https://www.bilibili.com/video/BV1sr421K7sv"},{title:"3.开启调试工具+创建初始化包",link:"https://www.bilibili.com/video/BV1iH4y1c7Na"},{title:"4.手动使用自动化创建功能",link:"https://www.bilibili.com/video/BV1UZ421T7fV"},{title:"5.使用已有表格创建业务",link:"https://www.bilibili.com/video/BV1NE4m1977s"},{title:"6.使用AI创建业务和创建数据源模式的可选项",link:"https://www.bilibili.com/video/BV17i421a7DE"},{title:"7.创建自己的后端方法",link:"https://www.bilibili.com/video/BV1Yw4m1k7fg"},{title:"8.新增一个前端页面",link:"https://www.bilibili.com/video/BV12y411i7oE"},{title:"9.配置一个前端二级页面",link:"https://www.bilibili.com/video/BV1ZM4m1y7i3"},{title:"10.配置一个前端菜单参数",link:"https://www.bilibili.com/video/BV1WS42197DZ"},{title:"11.菜单参数实战+动态菜单标题+菜单高亮配置",link:"https://www.bilibili.com/video/BV1NE4m1979c"},{title:"12.增加菜单可控按钮",link:"https://www.bilibili.com/video/BV1Sw4m1k746"},{title:"14.新增客户角色和其相关配置教学",link:"https://www.bilibili.com/video/BV1Ki421a7X2"},{title:"15.发布项目上线",link:"https://www.bilibili.com/video/BV1Lx4y1s77D"}];return(e,l)=>{const i=n("Film"),h=n("el-icon"),_=n("el-dropdown-item"),D=n("el-dropdown-menu"),E=n("el-dropdown"),S=n("el-tooltip"),T=n("Search"),q=n("Setting"),A=n("Refresh"),M=n("Sunny"),I=n("Moon");return s(),u("div",O,[f(S,{class:"",effect:"dark",content:"视频教程",placement:"bottom"},{default:c((()=>[f(E,{onCommand:g(C)},{dropdown:c((()=>[f(D,null,{default:c((()=>[(s(),u(m,null,w(x,(e=>f(_,{key:e.link,command:e.link},{default:c((()=>[k(p(e.title),1)])),_:2},1032,["command"]))),64))])),_:1})])),default:c((()=>[f(h,{class:"w-8 h-8 shadow rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer border-solid"},{default:c((()=>[f(i)])),_:1})])),_:1},8,["onCommand"])])),_:1}),f(S,{class:"",effect:"dark",content:"搜索",placement:"bottom"},{default:c((()=>[f(h,{onClick:y,class:"w-8 h-8 shadow rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer border-solid"},{default:c((()=>[f(T)])),_:1})])),_:1}),f(S,{class:"",effect:"dark",content:"系统设置",placement:"bottom"},{default:c((()=>[f(h,{class:"w-8 h-8 shadow rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer border-solid",onClick:b},{default:c((()=>[f(q)])),_:1})])),_:1}),f(S,{class:"",effect:"dark",content:"刷新",placement:"bottom"},{default:c((()=>[f(h,{class:B(["w-8 h-8 shadow rounded-full border border-gray-200 dark:border-gray-600 cursor-pointer border-solid",a.value?"animate-spin":""]),onClick:r},{default:c((()=>[f(A)])),_:1},8,["class"])])),_:1}),f(S,{class:"",effect:"dark",content:"切换主题",placement:"bottom"},{default:c((()=>[g(t).isDark?(s(),d(h,{key:0,class:"w-8 h-8 shadow rounded-full border border-gray-600 cursor-pointer border-solid",onClick:l[0]||(l[0]=e=>g(t).toggleTheme(!1))},{default:c((()=>[f(M)])),_:1})):(s(),d(h,{key:1,class:"w-8 h-8 shadow rounded-full border border-gray-200 cursor-pointer border-solid",onClick:l[1]||(l[1]=e=>g(t).toggleTheme(!0))},{default:c((()=>[f(I)])),_:1}))])),_:1}),f(V,{drawer:o.value,"onUpdate:drawer":l[2]||(l[2]=e=>o.value=e)},null,8,["drawer"]),f(j,{ref_key:"command",ref:v},null,512)])}}};export{S as default};
