/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as t,_ as e,k as o,g as r,c as i,o as a,f as n,w as s,v as l,ah as c,h,x as u,E as d,a as g,q as f,b as p,z as m,A as v,y,B as w,F as b,D as C,i as x,X as A,t as B,d as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{I as D,i as P,a as T}from"./087AC4D233B64EB0image.DZXThNBc.js";import{M as E}from"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";import{l as _}from"./087AC4D233B64EB0logo.D8P6F9wK.js";const S=e=>t({url:"/fileUploadAndDownload/getFileList",method:"post",data:e}),R=e=>t({url:"/fileUploadAndDownload/deleteFile",method:"post",data:e}),L=e=>t({url:"/fileUploadAndDownload/editFileName",method:"post",data:e}),I=e=>t({url:"/fileUploadAndDownload/importURL",method:"post",data:e}),M=e(Object.assign({name:"UploadImage"},{__name:"image",props:{imageUrl:{type:String,default:""},fileSize:{type:Number,default:2048},maxWH:{type:Number,default:1920},classId:{type:Number,default:0}},emits:["on-success"],setup(t,{emit:e}){const g=e,f=t,p=o().token,m=t=>{var e,o;const r="image/jpeg"===(null==(e=t.type)?void 0:e.toLowerCase()),i="image/png"===(null==(o=t.type)?void 0:o.toLowerCase());if(!r&&!i)return d.error("上传头像图片只能是 jpg或png 格式!"),!1;const a=t.size/1024<f.fileSize;if(!a){return new D(t,f.fileSize,f.maxWH).compress()}return a},v=t=>{const{data:e}=t;e.file&&g("on-success",e.file.url)};return(t,e)=>{const o=r("el-button"),d=r("el-upload");return a(),i("div",null,[n(d,{action:"".concat(l(u)(),"/fileUploadAndDownload/upload"),"show-file-list":!1,"on-success":v,"before-upload":m,multiple:!1,data:{classId:f.classId},headers:{"x-token":l(p)}},{default:s((()=>[n(o,{type:"primary",icon:l(c)},{default:s((()=>e[0]||(e[0]=[h("压缩上传")]))),_:1},8,["icon"])])),_:1},8,["action","data","headers"])])}}}),[["__scopeId","data-v-f8955cc8"]]),N=Object.assign({name:"UploadCommon"},{__name:"common",props:{classId:{type:Number,default:0}},emits:["on-success"],setup(t,{emit:e}){const f=o().token,p=t,m=e,v=g(!1),y=t=>{v.value=!0;const e=t.size/1024/1024<.5,o=t.size/1024/1024<5,r=P(t.type),i=T(t.type);let a=!0;return r||i||(d.error("上传图片只能是 jpg,png,svg,webp 格式, 上传视频只能是 mp4,webm 格式!"),v.value=!1,a=!1),!o&&r&&(d.error("上传视频大小不能超过 5MB"),v.value=!1,a=!1),!e&&i&&(d.error("未压缩的上传图片大小不能超过 500KB，请使用压缩上传"),v.value=!1,a=!1),a},w=t=>{const{data:e}=t;e.file&&m("on-success",e.file.url)},b=()=>{d({type:"error",message:"上传失败"}),v.value=!1};return(t,e)=>{const o=r("el-button"),d=r("el-upload");return a(),i("div",null,[n(d,{action:"".concat(l(u)(),"/fileUploadAndDownload/upload"),"before-upload":y,"on-error":b,"on-success":w,"show-file-list":!1,data:{classId:p.classId},headers:{"x-token":l(f)},multiple:"",class:"upload-btn"},{default:s((()=>[n(o,{type:"primary",icon:l(c)},{default:s((()=>e[0]||(e[0]=[h("普通上传")]))),_:1},8,["icon"])])),_:1},8,["action","data","headers"])])}}}),O=()=>t({url:"/attachmentCategory/getCategoryList",method:"get"}),U=e=>t({url:"/attachmentCategory/addCategory",method:"post",data:e}),j=e=>t({url:"/attachmentCategory/deleteCategory",method:"post",data:e}),z={class:"flex gap-[30px] h-[600px]"},F={class:"flex flex-col flex-1"},H={class:"flex-1 bg-[#f8f8f8] rounded-lg overflow-hidden"},G={class:"mt-[20px] flex items-center p-[10px] bg-white rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]"},q={class:"w-[340px]"},Q={class:"bg-white p-5 rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]"},K={class:"w-full h-full relative overflow-hidden"},X=["src"],V={class:"dialog-footer"},$=e(Object.assign({name:"CropperImage"},{__name:"cropper",props:{classId:{type:Number,default:0}},emits:["on-success"],setup(t,{emit:e}){const o=e,c=t,k=g(null),D=g(!1),P=g(""),T=g(null),{proxy:_}=f(),S=g({}),R=g(!1),L=t=>{_.$refs.cropperRef.changeScale(t)},I=g([{label:"1:1",value:[1,1]},{label:"16:9",value:[16,9]},{label:"9:16",value:[9,16]},{label:"4:3",value:[4,3]},{label:"自由比例",value:[]}]),M=g([1,1]),N=g(300),O=g(300),U=g(!1),j=g(4),$=()=>{switch(M.value=I.value[j.value].value,j.value){case 0:N.value=300,O.value=300,U.value=!0;break;case 1:N.value=300,O.value=168.75,U.value=!0;break;case 2:N.value=168.75,O.value=300,U.value=!0;break;case 3:N.value=300,O.value=225,U.value=!0;break;default:N.value=300,O.value=300,U.value=!1}},W=t=>{if(!t.raw.type.includes("image"))return void d.error("请选择图片文件");if(t.raw.size/1024/1024>8)return d.error("文件大小不能超过8MB!"),!1;const e=new FileReader;e.onload=t=>{P.value=t.target.result,D.value=!0},e.readAsDataURL(t.raw)},Y=t=>{-90===t?_.$refs.cropperRef.rotateLeft():_.$refs.cropperRef.rotateRight()},J=t=>{S.value=t},Z=()=>{R.value=!0,_.$refs.cropperRef.getCropBlob((t=>{try{const e=new File([t],"".concat(Date.now(),".jpg"),{type:"image/jpeg"});k.value.clearFiles(),k.value.handleStart(e),k.value.submit()}catch(e){R.value=!1,d.error("上传失败: "+e.message)}}))},tt=t=>{const{data:e}=t;e&&setTimeout((()=>{R.value=!1,D.value=!1,S.value={},d.success("上传成功"),o("on-success",e.url)}),1e3)};return(t,e)=>{const o=r("el-button"),d=r("el-upload"),g=r("el-tooltip"),f=r("el-button-group"),_=r("el-option"),et=r("el-select"),ot=r("el-dialog");return a(),i(b,null,[n(d,{ref_key:"uploadRef",ref:k,action:"".concat(l(u)(),"/fileUploadAndDownload/upload"),accept:"image/*","show-file-list":!1,"auto-upload":!1,data:{classId:c.classId},"on-success":tt,"on-change":W,headers:{"x-token":t.token}},{default:s((()=>[n(o,{type:"primary",icon:"crop"},{default:s((()=>e[8]||(e[8]=[h(" 裁剪上传")]))),_:1})])),_:1},8,["action","data","headers"]),n(ot,{modelValue:D.value,"onUpdate:modelValue":e[6]||(e[6]=t=>D.value=t),title:"图片裁剪",width:"1200px","append-to-body":"",onClose:e[7]||(e[7]=t=>D.value=!1),"close-on-click-modal":!1,draggable:""},{footer:s((()=>[p("div",V,[n(o,{onClick:e[5]||(e[5]=t=>D.value=!1)},{default:s((()=>e[10]||(e[10]=[h("取 消")]))),_:1}),n(o,{type:"primary",onClick:Z,loading:R.value},{default:s((()=>[h(B(R.value?"上传中...":"上 传"),1)])),_:1},8,["loading"])])])),default:s((()=>[p("div",z,[p("div",F,[p("div",H,[n(l(E),{ref_key:"cropperRef",ref:T,img:P.value,outputType:"jpeg",autoCrop:!0,autoCropWidth:N.value,autoCropHeight:O.value,fixedBox:!1,fixed:U.value,fixedNumber:M.value,centerBox:!0,canMoveBox:!0,full:!1,maxImgSize:1200,original:!0,onRealTime:J},null,8,["img","autoCropWidth","autoCropHeight","fixed","fixedNumber"])]),p("div",G,[n(f,null,{default:s((()=>[n(g,{content:"向左旋转"},{default:s((()=>[n(o,{onClick:e[0]||(e[0]=t=>Y(-90)),icon:l(m)},null,8,["icon"])])),_:1}),n(g,{content:"向右旋转"},{default:s((()=>[n(o,{onClick:e[1]||(e[1]=t=>Y(90)),icon:l(v)},null,8,["icon"])])),_:1}),n(o,{icon:l(y),onClick:e[2]||(e[2]=t=>L(1))},null,8,["icon"]),n(o,{icon:l(w),onClick:e[3]||(e[3]=t=>L(-1))},null,8,["icon"])])),_:1}),n(et,{modelValue:j.value,"onUpdate:modelValue":e[4]||(e[4]=t=>j.value=t),placeholder:"选择比例",class:"w-32 ml-4",onChange:$},{default:s((()=>[(a(!0),i(b,null,C(I.value,((t,e)=>(a(),x(_,{key:e,label:t.label,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])]),p("div",q,[p("div",Q,[e[9]||(e[9]=p("div",{class:"mb-[15px] text-gray-600"},"裁剪预览",-1)),p("div",{class:"bg-white p-5 rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]",style:A({width:S.value.w+"px",height:S.value.h+"px"})},[p("div",K,[p("img",{src:S.value.url,style:A(S.value.img),alt:"",class:"max-w-none absolute transition-all duration-300 ease-in-out image-render-pixelated origin-[0_0]"},null,12,X)])],4)])])])])),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-7f698837"]]);function W(t){return""===t?t:"true"===t||"1"==t}function Y(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function J(t,e){for(var o,r="",i=0,a=-1,n=0,s=0;s<=t.length;++s){if(s<t.length)o=t.charCodeAt(s);else{if(47===o)break;o=47}if(47===o){if(a===s-1||1===n);else if(a!==s-1&&2===n){if(r.length<2||2!==i||46!==r.charCodeAt(r.length-1)||46!==r.charCodeAt(r.length-2))if(r.length>2){var l=r.lastIndexOf("/");if(l!==r.length-1){-1===l?(r="",i=0):i=(r=r.slice(0,l)).length-1-r.lastIndexOf("/"),a=s,n=0;continue}}else if(2===r.length||1===r.length){r="",i=0,a=s,n=0;continue}e&&(r.length>0?r+="/..":r="..",i=2)}else r.length>0?r+="/"+t.slice(a+1,s):r=t.slice(a+1,s),i=s-a-1;a=s,n=0}else 46===o&&-1!==n?++n:n=-1}return r}var Z={resolve:function(){for(var t,e="",o=!1,r=arguments.length-1;r>=-1&&!o;r--){var i;r>=0?i=arguments[r]:(void 0===t&&(t=process.cwd()),i=t),Y(i),0!==i.length&&(e=i+"/"+e,o=47===i.charCodeAt(0))}return e=J(e,!o),o?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(Y(t),0===t.length)return".";var e=47===t.charCodeAt(0),o=47===t.charCodeAt(t.length-1);return 0!==(t=J(t,!e)).length||e||(t="."),t.length>0&&o&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return Y(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var o=arguments[e];Y(o),o.length>0&&(void 0===t?t=o:t+="/"+o)}return void 0===t?".":Z.normalize(t)},relative:function(t,e){if(Y(t),Y(e),t===e)return"";if((t=Z.resolve(t))===(e=Z.resolve(e)))return"";for(var o=1;o<t.length&&47===t.charCodeAt(o);++o);for(var r=t.length,i=r-o,a=1;a<e.length&&47===e.charCodeAt(a);++a);for(var n=e.length-a,s=i<n?i:n,l=-1,c=0;c<=s;++c){if(c===s){if(n>s){if(47===e.charCodeAt(a+c))return e.slice(a+c+1);if(0===c)return e.slice(a+c)}else i>s&&(47===t.charCodeAt(o+c)?l=c:0===c&&(l=0));break}var h=t.charCodeAt(o+c);if(h!==e.charCodeAt(a+c))break;47===h&&(l=c)}var u="";for(c=o+l+1;c<=r;++c)c!==r&&47!==t.charCodeAt(c)||(0===u.length?u+="..":u+="/..");return u.length>0?u+e.slice(a+l):(a+=l,47===e.charCodeAt(a)&&++a,e.slice(a))},_makeLong:function(t){return t},dirname:function(t){if(Y(t),0===t.length)return".";for(var e=t.charCodeAt(0),o=47===e,r=-1,i=!0,a=t.length-1;a>=1;--a)if(47===(e=t.charCodeAt(a))){if(!i){r=a;break}}else i=!1;return-1===r?o?"/":".":o&&1===r?"//":t.slice(0,r)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');Y(t);var o,r=0,i=-1,a=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var n=e.length-1,s=-1;for(o=t.length-1;o>=0;--o){var l=t.charCodeAt(o);if(47===l){if(!a){r=o+1;break}}else-1===s&&(a=!1,s=o+1),n>=0&&(l===e.charCodeAt(n)?-1==--n&&(i=o):(n=-1,i=s))}return r===i?i=s:-1===i&&(i=t.length),t.slice(r,i)}for(o=t.length-1;o>=0;--o)if(47===t.charCodeAt(o)){if(!a){r=o+1;break}}else-1===i&&(a=!1,i=o+1);return-1===i?"":t.slice(r,i)},extname:function(t){Y(t);for(var e=-1,o=0,r=-1,i=!0,a=0,n=t.length-1;n>=0;--n){var s=t.charCodeAt(n);if(47!==s)-1===r&&(i=!1,r=n+1),46===s?-1===e?e=n:1!==a&&(a=1):-1!==e&&(a=-1);else if(!i){o=n+1;break}}return-1===e||-1===r||0===a||1===a&&e===r-1&&e===o+1?"":t.slice(e,r)},format:function(t){if(null===t||"object"!=typeof t)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof t);return function(t,e){var o=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||"");return o?o===e.root?o+r:o+t+r:r}("/",t)},parse:function(t){Y(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var o,r=t.charCodeAt(0),i=47===r;i?(e.root="/",o=1):o=0;for(var a=-1,n=0,s=-1,l=!0,c=t.length-1,h=0;c>=o;--c)if(47!==(r=t.charCodeAt(c)))-1===s&&(l=!1,s=c+1),46===r?-1===a?a=c:1!==h&&(h=1):-1!==a&&(h=-1);else if(!l){n=c+1;break}return-1===a||-1===s||0===h||1===h&&a===s-1&&a===n+1?-1!==s&&(e.base=e.name=0===n&&i?t.slice(1,s):t.slice(n,s)):(0===n&&i?(e.name=t.slice(1,a),e.base=t.slice(1,s)):(e.name=t.slice(n,a),e.base=t.slice(n,s)),e.ext=t.slice(a,s)),n>0?e.dir=t.slice(0,n-1):i&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};Z.posix=Z;const tt=Z.extname,et=Z.basename;class ot{constructor(){let t=(()=>"undefined"==typeof global)(),e="image/png",o="image/jpeg",r="image/jpeg",i="image/webp",a="application/pdf",n="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:t?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:t?{png:e,jpg:o,jpeg:r,webp:i}:{png:e,jpg:o,jpeg:r,pdf:a,svg:n},mimes:t?{[e]:"png",[o]:"jpg",[i]:"webp"}:{[e]:"png",[o]:"jpg",[a]:"pdf",[n]:"svg"}})}toMime(t){return this.formats[(t||"").replace(/^\./,"").toLowerCase()]}fromMime(t){return this.mimes[t]}}class rt{static for(t){return(new rt).append(t).get()}constructor(){this.crc=-1}get(){return~this.crc}append(t){for(var e=0|this.crc,o=this.table,r=0,i=0|t.length;r<i;r++)e=e>>>8^o[255&(e^t[r])];return this.crc=e,this}}function it(t){let e=new Uint8Array(t),o=new DataView(e.buffer),r={array:e,view:o,size:t,set8:(t,e)=>(o.setUint8(t,e),r),set16:(t,e)=>(o.setUint16(t,e,!0),r),set32:(t,e)=>(o.setUint32(t,e,!0),r),bytes:(t,o)=>(e.set(o,t),r)};return r}rt.prototype.table=(()=>{var t,e,o,r=[];for(t=0;t<256;t++){for(o=t,e=0;e<8;e++)o=1&o?o>>>1^3988292384:o>>>1;r[t]=o}return r})();class at{constructor(t){let e=new Date;Object.assign(this,{directory:t,offset:0,files:[],time:(e.getHours()<<6|e.getMinutes())<<5|e.getSeconds()/2,date:(e.getFullYear()-1980<<4|e.getMonth()+1)<<5|e.getDate()}),this.add(t)}async add(t,e){let o=!e,r=at.encoder.encode("".concat(this.directory,"/").concat(o?"":t)),i=new Uint8Array(o?0:await e.arrayBuffer()),a=30+r.length,n=a+i.length,{offset:s}=this,l=it(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,rt.for(i)).set32(14,i.length).set32(18,i.length).set16(22,r.length);s+=a;let c=it(a+i.length+16).set32(0,67324752).bytes(4,l.array).bytes(30,r).bytes(a,i);s+=i.length,c.set32(n,134695760).bytes(n+4,l.array.slice(10,22)),s+=16,this.files.push({offset:s,folder:o,name:r,header:l,payload:c}),this.offset=s}toBuffer(){let t=this.files.reduce(((t,{name:e})=>46+e.length+t),0),e=it(t+22),o=0;for(var{offset:r,name:i,header:a,folder:n}of this.files)e.set32(o,33639248).set16(o+4,20).bytes(o+6,a.array).set8(o+38,n?16:0).set32(o+42,r).bytes(o+46,i),o+=46+i.length;e.set32(o,101010256).set16(o+8,this.files.length).set16(o+10,this.files.length).set32(o+12,t).set32(o+16,this.offset);let s=new Uint8Array(this.offset+e.size),l=0;for(var{payload:c}of this.files)s.set(c.array,l),l+=c.size;return s.set(e.array,l),s}get blob(){return new Blob([this.toBuffer()],{type:"application/zip"})}}at.encoder=new TextEncoder;const nt=(t,e,o,r)=>{if(r){let{width:e,height:o}=t,i=Object.assign(document.createElement("canvas"),{width:e,height:o}),a=i.getContext("2d");a.fillStyle=r,a.fillRect(0,0,e,o),a.drawImage(t,0,0),t=i}return new Promise(((r,i)=>t.toBlob(r,e,o)))},st=(t,e)=>{const o=window.URL.createObjectURL(e),r=document.createElement("a");r.style.display="none",r.href=o,r.setAttribute("download",t),void 0===r.download&&r.setAttribute("target","_blank"),document.body.appendChild(r),r.click(),document.body.removeChild(r),setTimeout((()=>window.URL.revokeObjectURL(o)),100)},lt={asBuffer:(...t)=>nt(...t).then((t=>t.arrayBuffer())),asDownload:async(t,e,o,r,i)=>{st(i,await nt(t,e,o,r))},asZipDownload:async(t,e,o,r,i,a,n)=>{let s=et(i,".zip")||"archive",l=new at(s);await Promise.all(t.map((async(t,i)=>{let s=(t=>a.replace("{}",String(t+1).padStart(n,"0")))(i);await l.add(s,await nt(t,e,o,r))}))),st("".concat(s,".zip"),l.blob)},atScale:(t,e,o)=>t.map((t=>{if(1==e&&!o)return t.canvas;let r=document.createElement("canvas"),i=r.getContext("2d"),a=t.canvas?t.canvas:t;return r.width=a.width*e,r.height=a.height*e,o&&(i.fillStyle=o,i.fillRect(0,0,r.width,r.height)),i.scale(e,e),i.drawImage(a,0,0),r})),options:function(t,{filename:e="",extension:o="",format:r,page:i,quality:a,matte:n,density:s,outline:l,archive:c}={}){var{fromMime:h,toMime:u,expected:d}=new ot,g=(c=c||"canvas",r||o.replace(/@\d+x$/i,"")||tt(e)),f=(r=h(u(g)||g),u(r)),p=t.length;if(!g)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!r)throw new Error('Unsupported file format "'.concat(g,'" (expected ').concat(d,")"));if(!p)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");let m,v,y=e.replace(/{(\d*)}/g,((t,e)=>(v=!0,e=parseInt(e,10),m=isFinite(e)?e:isFinite(m)?m:-1,"{}"))),w=i>0?i-1:i<0?p+i:void 0;if(isFinite(w)&&w<0||w>=p)throw new RangeError(1==p?"Canvas only has a ‘page 1’ (".concat(w," is out of bounds)"):"Canvas has pages 1–".concat(p," (").concat(w," is out of bounds)"));if(t=isFinite(w)?[t[w]]:v||"pdf"==r?t:t.slice(-1),void 0===a)a=.92;else if("number"!=typeof a||!isFinite(a)||a<0||a>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===s){let t=(o||et(e,g)).match(/@(\d+)x$/i);s=t?parseInt(t[1],10):1}else if("number"!=typeof s||!Number.isInteger(s)||s<1)throw new TypeError("The density option must be a non-negative integer");return void 0===l?l=!0:"svg"==r&&(l=!!l),{filename:e,pattern:y,format:r,mime:f,pages:t,padding:m,quality:a,matte:n,density:s,outline:l,archive:c}}},{asBuffer:ct,asDownload:ht,asZipDownload:ut,atScale:dt,options:gt}=lt,ft=Symbol.for("toDataURL");const pt={Canvas:class{constructor(t,e){let o=document.createElement("canvas"),r=[];for(var[i,a]of(Object.defineProperty(o,"async",{value:!0,writable:!1,enumerable:!0}),Object.entries({png:()=>ct(o,"image/png"),jpg:()=>ct(o,"image/jpeg"),pages:()=>r.concat(o).map((t=>t.getContext("2d")))})))Object.defineProperty(o,i,{get:a});return Object.assign(o,{width:t,height:e,newPage(...t){var{width:e,height:i}=o,a=Object.assign(document.createElement("canvas"),{width:e,height:i});a.getContext("2d").drawImage(o,0,0),r.push(a);var[e,i]=t.length?t:[e,i];return Object.assign(o,{width:e,height:i}).getContext("2d")},saveAs(t,e){e="number"==typeof e?{quality:e}:e;let o=gt(this.pages,{filename:t,...e}),{pattern:r,padding:i,mime:a,quality:n,matte:s,density:l,archive:c}=o,h=dt(o.pages,l);return null==i?ht(h[0],a,n,s,t):ut(h,a,n,s,c,r,i)},toBuffer(t="png",e={}){e="number"==typeof e?{quality:e}:e;let o=gt(this.pages,{extension:t,...e}),{mime:r,quality:i,matte:a,pages:n,density:s}=o,l=dt(n,s,a)[0];return ct(l,r,i,a)},[ft]:o.toDataURL.bind(o),toDataURL(t="png",e={}){e="number"==typeof e?{quality:e}:e;let r=gt(this.pages,{extension:t,...e}),{mime:i,quality:a,matte:n,pages:s,density:l}=r,c=dt(s,l,n)[0],h=c[c===o?ft:"toDataURL"](i,a);return Promise.resolve(h)}})}}},mt=(t,e,o={},r=o)=>{if(Array.isArray(e))e.forEach((e=>mt(t,e,o,r)));else if("function"==typeof e)e(t,o,r,mt);else{const i=Object.keys(e)[0];Array.isArray(e[i])?(r[i]={},mt(t,e[i],o,r[i])):r[i]=e[i](t,o,r,mt)}return o},vt=(t,e)=>(o,r,i,a)=>{e(o,r,i)&&a(o,t,r,i)},yt=(t=0)=>e=>e.data[e.pos+t],wt=t=>e=>e.data.subarray(e.pos,e.pos+=t),bt=t=>e=>e.data.subarray(e.pos,e.pos+t),Ct=t=>e=>Array.from(wt(t)(e)).map((t=>String.fromCharCode(t))).join(""),xt=t=>e=>{const o=wt(2)(e);return t?(o[1]<<8)+o[0]:(o[0]<<8)+o[1]},At=(t,e)=>(o,r,i)=>{const a="function"==typeof e?e(o,r,i):e,n=wt(t),s=new Array(a);for(var l=0;l<a;l++)s[l]=n(o);return s},Bt=t=>e=>{const o=(t=>t.data[t.pos++])(e),r=new Array(8);for(var i=0;i<8;i++)r[7-i]=!!(o&1<<i);return Object.keys(t).reduce(((e,o)=>{const i=t[o];return i.length?e[o]=((t,e,o)=>{for(var r=0,i=0;i<o;i++)r+=t[e+i]&&2**(o-i-1);return r})(r,i.index,i.length):e[o]=r[i.index],e}),{})};var kt={blocks:t=>{const e=[],o=t.data.length;for(var r=0,i=(t=>t.data[t.pos++])(t);0!==i&&i;i=(t=>t.data[t.pos++])(t)){if(t.pos+i>=o){const i=o-t.pos;e.push(wt(i)(t)),r+=i;break}e.push(wt(i)(t)),r+=i}const a=new Uint8Array(r);for(var n=0,s=0;s<e.length;s++)a.set(e[s],n),n+=e[s].length;return a}};const Dt=vt({gce:[{codes:wt(2)},{byteSize:t=>t.data[t.pos++]},{extras:Bt({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:xt(!0)},{transparentColorIndex:t=>t.data[t.pos++]},{terminator:t=>t.data[t.pos++]}]},(t=>{var e=bt(2)(t);return 33===e[0]&&249===e[1]})),Pt=vt({image:[{code:t=>t.data[t.pos++]},{descriptor:[{left:xt(!0)},{top:xt(!0)},{width:xt(!0)},{height:xt(!0)},{lct:Bt({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},vt({lct:At(3,((t,e,o)=>Math.pow(2,o.descriptor.lct.size+1)))},((t,e,o)=>o.descriptor.lct.exists)),{data:[{minCodeSize:t=>t.data[t.pos++]},kt]}]},(t=>44===yt()(t))),Tt=vt({text:[{codes:wt(2)},{blockSize:t=>t.data[t.pos++]},{preData:(t,e,o)=>wt(o.text.blockSize)(t)},kt]},(t=>{var e=bt(2)(t);return 33===e[0]&&1===e[1]})),Et=vt({application:[{codes:wt(2)},{blockSize:t=>t.data[t.pos++]},{id:(t,e,o)=>Ct(o.blockSize)(t)},kt]},(t=>{var e=bt(2)(t);return 33===e[0]&&255===e[1]})),_t=vt({comment:[{codes:wt(2)},kt]},(t=>{var e=bt(2)(t);return 33===e[0]&&254===e[1]})),St=[{header:[{signature:Ct(3)},{version:Ct(3)}]},{lsd:[{width:xt(!0)},{height:xt(!0)},{gct:Bt({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:t=>t.data[t.pos++]},{pixelAspectRatio:t=>t.data[t.pos++]}]},vt({gct:At(3,((t,e)=>Math.pow(2,e.lsd.gct.size+1)))},((t,e)=>e.lsd.gct.exists)),{frames:(Rt=[Dt,Et,_t,Pt,Tt],Lt=t=>{var e=yt()(t);return 33===e||44===e},(t,e,o,r)=>{const i=[];let a=t.pos;for(;Lt(t,e,o);){const o={};if(r(t,Rt,e,o),t.pos===a)break;a=t.pos,i.push(o)}return i})}];var Rt,Lt;const It=(t,e,o)=>{if(!t.image)return;const{image:r}=t,i=r.descriptor.width*r.descriptor.height;var a=((t,e,o)=>{const r=4096,i=o;var a,n,s,l,c,h,u,d,g,f;const p=new Array(o),m=new Array(r),v=new Array(r),y=new Array(4097);for(c=1+(n=1<<(f=t)),a=n+2,u=-1,s=(1<<(l=f+1))-1,d=0;d<n;d++)m[d]=0,v[d]=d;var w,b,C,x,A,B;for(w=b=C=x=A=B=0,g=0;g<i;){if(0===x){if(b<l){w+=e[B]<<b,b+=8,B++;continue}if(d=w&s,w>>=l,b-=l,d>a||d==c)break;if(d==n){s=(1<<(l=f+1))-1,a=n+2,u=-1;continue}if(-1==u){y[x++]=v[d],u=d,C=d;continue}for(h=d,d==a&&(y[x++]=C,d=u);d>n;)y[x++]=v[d],d=m[d];C=255&v[d],y[x++]=C,a<r&&(m[a]=u,v[a]=C,!(++a&s)&&a<r&&(l++,s+=a)),u=h}x--,p[A++]=y[x],g++}for(g=A;g<i;g++)p[g]=0;return p})(r.data.minCodeSize,r.data.blocks,i);r.descriptor.lct.interlaced&&(a=((t,e)=>{const o=new Array(t.length),r=t.length/e,i=function(r,i){const a=t.slice(i*e,(i+1)*e);o.splice.apply(o,[r*e,e].concat(a))},a=[0,4,2,1],n=[8,8,4,2];for(var s=0,l=0;l<4;l++)for(var c=a[l];c<r;c+=n[l])i(c,s),s++;return o})(a,r.descriptor.width));const n={pixels:a,dims:{top:t.image.descriptor.top,left:t.image.descriptor.left,width:t.image.descriptor.width,height:t.image.descriptor.height}};return r.descriptor.lct&&r.descriptor.lct.exists?n.colorTable=r.lct:n.colorTable=e,t.gce&&(n.delay=10*(t.gce.delay||10),n.disposalType=t.gce.extras.disposal,t.gce.extras.transparentColorGiven&&(n.transparentIndex=t.gce.transparentColorIndex)),n.patch=(t=>{const e=t.pixels.length,o=new Uint8ClampedArray(4*e);for(var r=0;r<e;r++){const e=4*r,i=t.pixels[r],a=t.colorTable[i];o[e]=a[0],o[e+1]=a[1],o[e+2]=a[2],o[e+3]=i!==t.transparentIndex?255:0}return o})(n),n};function Mt(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=Number(t)?3:0)}class Nt{constructor(t){this.mode=jt.MODE_8BIT_BYTE,this.parsedData=[],this.data=t;const e=[];for(let o=0,r=this.data.length;o<r;o++){const t=[],r=this.data.charCodeAt(o);r>65536?(t[0]=240|(1835008&r)>>>18,t[1]=128|(258048&r)>>>12,t[2]=128|(4032&r)>>>6,t[3]=128|63&r):r>2048?(t[0]=224|(61440&r)>>>12,t[1]=128|(4032&r)>>>6,t[2]=128|63&r):r>128?(t[0]=192|(1984&r)>>>6,t[1]=128|63&r):t[0]=r,e.push(t)}this.parsedData=Array.prototype.concat.apply([],e),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}getLength(){return this.parsedData.length}write(t){for(let e=0,o=this.parsedData.length;e<o;e++)t.put(this.parsedData[e],8)}}class Ot{constructor(t=-1,e=Ut.L){this.moduleCount=0,this.dataList=[],this.typeNumber=t,this.errorCorrectLevel=e,this.moduleCount=0,this.dataList=[]}addData(t){if(this.typeNumber<=0)this.typeNumber=function(t,e){for(var o=1,r=Mt(t),i=0,a=Kt.length;i<a;i++){var n=0;switch(e){case Ut.L:n=Kt[i][0];break;case Ut.M:n=Kt[i][1];break;case Ut.Q:n=Kt[i][2];break;case Ut.H:n=Kt[i][3]}if(r<=n)break;o++}if(o>Kt.length)throw new Error("Too long data");return o}(t,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error("Invalid QR version: ".concat(this.typeNumber));if(!function(t,e,o){const r=Mt(e),i=t-1;let a=0;switch(o){case Ut.L:a=Kt[i][0];break;case Ut.M:a=Kt[i][1];break;case Ut.Q:a=Kt[i][2];break;case Ut.H:a=Kt[i][3]}return r<=a}(this.typeNumber,t,this.errorCorrectLevel))throw new Error("Data is too long for QR version: ".concat(this.typeNumber))}const e=new Nt(t);this.dataList.push(e),this.dataCache=void 0}isDark(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error("".concat(t,",").concat(e));return this.modules[t][e]}getModuleCount(){return this.moduleCount}make(){this.makeImpl(!1,this.getBestMaskPattern())}makeImpl(t,e){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(let o=0;o<this.moduleCount;o++){this.modules[o]=new Array(this.moduleCount);for(let t=0;t<this.moduleCount;t++)this.modules[o][t]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(t,e),this.typeNumber>=7&&this.setupTypeNumber(t),null==this.dataCache&&(this.dataCache=Ot.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,e)}setupPositionProbePattern(t,e){for(let o=-1;o<=7;o++)if(!(t+o<=-1||this.moduleCount<=t+o))for(let r=-1;r<=7;r++)e+r<=-1||this.moduleCount<=e+r||(this.modules[t+o][e+r]=0<=o&&o<=6&&(0==r||6==r)||0<=r&&r<=6&&(0==o||6==o)||2<=o&&o<=4&&2<=r&&r<=4)}getBestMaskPattern(){if(Number.isInteger(this.maskPattern)&&Object.values(zt).includes(this.maskPattern))return this.maskPattern;let t=0,e=0;for(let o=0;o<8;o++){this.makeImpl(!0,o);const r=Ft.getLostPoint(this);(0==o||t>r)&&(t=r,e=o)}return e}setupTimingPattern(){for(let t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(let t=8;t<this.moduleCount-8;t++)null==this.modules[6][t]&&(this.modules[6][t]=t%2==0)}setupPositionAdjustPattern(){const t=Ft.getPatternPosition(this.typeNumber);for(let e=0;e<t.length;e++)for(let o=0;o<t.length;o++){const r=t[e],i=t[o];if(null==this.modules[r][i])for(let t=-2;t<=2;t++)for(let e=-2;e<=2;e++)this.modules[r+t][i+e]=-2==t||2==t||-2==e||2==e||0==t&&0==e}}setupTypeNumber(t){const e=Ft.getBCHTypeNumber(this.typeNumber);for(var o=0;o<18;o++){var r=!t&&1==(e>>o&1);this.modules[Math.floor(o/3)][o%3+this.moduleCount-8-3]=r}for(o=0;o<18;o++){r=!t&&1==(e>>o&1);this.modules[o%3+this.moduleCount-8-3][Math.floor(o/3)]=r}}setupTypeInfo(t,e){const o=this.errorCorrectLevel<<3|e,r=Ft.getBCHTypeInfo(o);for(var i=0;i<15;i++){var a=!t&&1==(r>>i&1);i<6?this.modules[i][8]=a:i<8?this.modules[i+1][8]=a:this.modules[this.moduleCount-15+i][8]=a}for(i=0;i<15;i++){a=!t&&1==(r>>i&1);i<8?this.modules[8][this.moduleCount-i-1]=a:i<9?this.modules[8][15-i-1+1]=a:this.modules[8][15-i-1]=a}this.modules[this.moduleCount-8][8]=!t}mapData(t,e){let o=-1,r=this.moduleCount-1,i=7,a=0;for(let n=this.moduleCount-1;n>0;n-=2)for(6==n&&n--;;){for(let o=0;o<2;o++)if(null==this.modules[r][n-o]){let s=!1;a<t.length&&(s=1==(t[a]>>>i&1));Ft.getMask(e,r,n-o)&&(s=!s),this.modules[r][n-o]=s,i--,-1==i&&(a++,i=7)}if(r+=o,r<0||this.moduleCount<=r){r-=o,o=-o;break}}}static createData(t,e,o){const r=qt.getRSBlocks(t,e),i=new Qt;for(var a=0;a<o.length;a++){const e=o[a];i.put(e.mode,4),i.put(e.getLength(),Ft.getLengthInBits(e.mode,t)),e.write(i)}let n=0;for(a=0;a<r.length;a++)n+=r[a].dataCount;if(i.getLengthInBits()>8*n)throw new Error("code length overflow. (".concat(i.getLengthInBits(),">").concat(8*n,")"));for(i.getLengthInBits()+4<=8*n&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=8*n||(i.put(Ot.PAD0,8),i.getLengthInBits()>=8*n));)i.put(Ot.PAD1,8);return Ot.createBytes(i,r)}static createBytes(t,e){let o=0,r=0,i=0;const a=new Array(e.length),n=new Array(e.length);for(var s=0;s<e.length;s++){const c=e[s].dataCount,h=e[s].totalCount-c;r=Math.max(r,c),i=Math.max(i,h),a[s]=new Array(c);for(var l=0;l<a[s].length;l++)a[s][l]=255&t.buffer[l+o];o+=c;const u=Ft.getErrorCorrectPolynomial(h),d=new Gt(a[s],u.getLength()-1).mod(u);n[s]=new Array(u.getLength()-1);for(l=0;l<n[s].length;l++){const t=l+d.getLength()-n[s].length;n[s][l]=t>=0?d.get(t):0}}let c=0;for(l=0;l<e.length;l++)c+=e[l].totalCount;const h=new Array(c);let u=0;for(l=0;l<r;l++)for(s=0;s<e.length;s++)l<a[s].length&&(h[u++]=a[s][l]);for(l=0;l<i;l++)for(s=0;s<e.length;s++)l<n[s].length&&(h[u++]=n[s][l]);return h}}Ot.PAD0=236,Ot.PAD1=17;const Ut={L:1,M:0,Q:3,H:2},jt={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},zt={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};class Ft{static getBCHTypeInfo(t){let e=t<<10;for(;Ft.getBCHDigit(e)-Ft.getBCHDigit(Ft.G15)>=0;)e^=Ft.G15<<Ft.getBCHDigit(e)-Ft.getBCHDigit(Ft.G15);return(t<<10|e)^Ft.G15_MASK}static getBCHTypeNumber(t){let e=t<<12;for(;Ft.getBCHDigit(e)-Ft.getBCHDigit(Ft.G18)>=0;)e^=Ft.G18<<Ft.getBCHDigit(e)-Ft.getBCHDigit(Ft.G18);return t<<12|e}static getBCHDigit(t){let e=0;for(;0!=t;)e++,t>>>=1;return e}static getPatternPosition(t){return Ft.PATTERN_POSITION_TABLE[t-1]}static getMask(t,e,o){switch(t){case zt.PATTERN000:return(e+o)%2==0;case zt.PATTERN001:return e%2==0;case zt.PATTERN010:return o%3==0;case zt.PATTERN011:return(e+o)%3==0;case zt.PATTERN100:return(Math.floor(e/2)+Math.floor(o/3))%2==0;case zt.PATTERN101:return e*o%2+e*o%3==0;case zt.PATTERN110:return(e*o%2+e*o%3)%2==0;case zt.PATTERN111:return(e*o%3+(e+o)%2)%2==0;default:throw new Error("bad maskPattern:".concat(t))}}static getErrorCorrectPolynomial(t){let e=new Gt([1],0);for(let o=0;o<t;o++)e=e.multiply(new Gt([1,Ht.gexp(o)],0));return e}static getLengthInBits(t,e){if(1<=e&&e<10)switch(t){case jt.MODE_NUMBER:return 10;case jt.MODE_ALPHA_NUM:return 9;case jt.MODE_8BIT_BYTE:case jt.MODE_KANJI:return 8;default:throw new Error("mode:".concat(t))}else if(e<27)switch(t){case jt.MODE_NUMBER:return 12;case jt.MODE_ALPHA_NUM:return 11;case jt.MODE_8BIT_BYTE:return 16;case jt.MODE_KANJI:return 10;default:throw new Error("mode:".concat(t))}else{if(!(e<41))throw new Error("type:".concat(e));switch(t){case jt.MODE_NUMBER:return 14;case jt.MODE_ALPHA_NUM:return 13;case jt.MODE_8BIT_BYTE:return 16;case jt.MODE_KANJI:return 12;default:throw new Error("mode:".concat(t))}}}static getLostPoint(t){const e=t.getModuleCount();let o=0;for(var r=0;r<e;r++)for(var i=0;i<e;i++){let a=0;const n=t.isDark(r,i);for(let o=-1;o<=1;o++)if(!(r+o<0||e<=r+o))for(let s=-1;s<=1;s++)i+s<0||e<=i+s||0==o&&0==s||n==t.isDark(r+o,i+s)&&a++;a>5&&(o+=3+a-5)}for(r=0;r<e-1;r++)for(i=0;i<e-1;i++){let e=0;t.isDark(r,i)&&e++,t.isDark(r+1,i)&&e++,t.isDark(r,i+1)&&e++,t.isDark(r+1,i+1)&&e++,0!=e&&4!=e||(o+=3)}for(r=0;r<e;r++)for(i=0;i<e-6;i++)t.isDark(r,i)&&!t.isDark(r,i+1)&&t.isDark(r,i+2)&&t.isDark(r,i+3)&&t.isDark(r,i+4)&&!t.isDark(r,i+5)&&t.isDark(r,i+6)&&(o+=40);for(i=0;i<e;i++)for(r=0;r<e-6;r++)t.isDark(r,i)&&!t.isDark(r+1,i)&&t.isDark(r+2,i)&&t.isDark(r+3,i)&&t.isDark(r+4,i)&&!t.isDark(r+5,i)&&t.isDark(r+6,i)&&(o+=40);let a=0;for(i=0;i<e;i++)for(r=0;r<e;r++)t.isDark(r,i)&&a++;return o+=10*(Math.abs(100*a/e/e-50)/5),o}}Ft.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],Ft.G15=1335,Ft.G18=7973,Ft.G15_MASK=21522;class Ht{static glog(t){if(t<1)throw new Error("glog(".concat(t,")"));return Ht.LOG_TABLE[t]}static gexp(t){for(;t<0;)t+=255;for(;t>=256;)t-=255;return Ht.EXP_TABLE[t]}}Ht.EXP_TABLE=new Array(256),Ht.LOG_TABLE=new Array(256),Ht._constructor=function(){for(var t=0;t<8;t++)Ht.EXP_TABLE[t]=1<<t;for(t=8;t<256;t++)Ht.EXP_TABLE[t]=Ht.EXP_TABLE[t-4]^Ht.EXP_TABLE[t-5]^Ht.EXP_TABLE[t-6]^Ht.EXP_TABLE[t-8];for(t=0;t<255;t++)Ht.LOG_TABLE[Ht.EXP_TABLE[t]]=t}();class Gt{constructor(t,e){if(null==t.length)throw new Error("".concat(t.length,"/").concat(e));let o=0;for(;o<t.length&&0==t[o];)o++;this.num=new Array(t.length-o+e);for(let r=0;r<t.length-o;r++)this.num[r]=t[r+o]}get(t){return this.num[t]}getLength(){return this.num.length}multiply(t){const e=new Array(this.getLength()+t.getLength()-1);for(let o=0;o<this.getLength();o++)for(let r=0;r<t.getLength();r++)e[o+r]^=Ht.gexp(Ht.glog(this.get(o))+Ht.glog(t.get(r)));return new Gt(e,0)}mod(t){if(this.getLength()-t.getLength()<0)return this;const e=Ht.glog(this.get(0))-Ht.glog(t.get(0)),o=new Array(this.getLength());for(var r=0;r<this.getLength();r++)o[r]=this.get(r);for(r=0;r<t.getLength();r++)o[r]^=Ht.gexp(Ht.glog(t.get(r))+e);return new Gt(o,0).mod(t)}}class qt{constructor(t,e){this.totalCount=t,this.dataCount=e}static getRSBlocks(t,e){const o=qt.getRsBlockTable(t,e);if(null==o)throw new Error("bad rs block @ typeNumber:".concat(t,"/errorCorrectLevel:").concat(e));const r=o.length/3,i=[];for(let a=0;a<r;a++){const t=o[3*a+0],e=o[3*a+1],r=o[3*a+2];for(let o=0;o<t;o++)i.push(new qt(e,r))}return i}static getRsBlockTable(t,e){switch(e){case Ut.L:return qt.RS_BLOCK_TABLE[4*(t-1)+0];case Ut.M:return qt.RS_BLOCK_TABLE[4*(t-1)+1];case Ut.Q:return qt.RS_BLOCK_TABLE[4*(t-1)+2];case Ut.H:return qt.RS_BLOCK_TABLE[4*(t-1)+3];default:return}}}qt.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];class Qt{constructor(){this.buffer=[],this.length=0}get(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)}put(t,e){for(let o=0;o<e;o++)this.putBit(1==(t>>>e-o-1&1))}getLengthInBits(){return this.length}putBit(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}}const Kt=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];var Xt=256,Vt=1024,$t=1<<18;function Wt(t,e){var o,r,i,a,n;function s(t,e,r,i,a){o[e][0]-=t*(o[e][0]-r)/Vt,o[e][1]-=t*(o[e][1]-i)/Vt,o[e][2]-=t*(o[e][2]-a)/Vt}function l(t,e,r,i,a){for(var s,l,c=Math.abs(e-t),h=Math.min(e+t,Xt),u=e+1,d=e-1,g=1;u<h||d>c;)l=n[g++],u<h&&((s=o[u++])[0]-=l*(s[0]-r)/$t,s[1]-=l*(s[1]-i)/$t,s[2]-=l*(s[2]-a)/$t),d>c&&((s=o[d--])[0]-=l*(s[0]-r)/$t,s[1]-=l*(s[1]-i)/$t,s[2]-=l*(s[2]-a)/$t)}function c(t,e,r){var n,s,l,c,h,u=2147483647,d=u,g=-1,f=g;for(n=0;n<Xt;n++)s=o[n],(l=Math.abs(s[0]-t)+Math.abs(s[1]-e)+Math.abs(s[2]-r))<u&&(u=l,g=n),(c=l-(i[n]>>12))<d&&(d=c,f=n),h=a[n]>>10,a[n]-=h,i[n]+=h<<10;return a[g]+=64,i[g]-=65536,f}this.buildColormap=function(){!function(){var t,e;for(o=[],r=new Int32Array(256),i=new Int32Array(Xt),a=new Int32Array(Xt),n=new Int32Array(32),t=0;t<Xt;t++)e=(t<<12)/Xt,o[t]=new Float64Array([e,e,e,0]),a[t]=256,i[t]=0}(),function(){var o,r,i,a,h,u,d=t.length,g=30+(e-1)/3,f=d/(3*e),p=~~(f/100),m=Vt,v=2048,y=v>>6;for(y<=1&&(y=0),o=0;o<y;o++)n[o]=m*(256*(y*y-o*o)/(y*y));d<1509?(e=1,r=3):r=d%499!=0?1497:d%491!=0?1473:d%487!=0?1461:1509;var w=0;for(o=0;o<f;)if(s(m,u=c(i=(255&t[w])<<4,a=(255&t[w+1])<<4,h=(255&t[w+2])<<4),i,a,h),0!==y&&l(y,u,i,a,h),(w+=r)>=d&&(w-=d),0===p&&(p=1),++o%p==0)for(m-=m/g,(y=(v-=v/30)>>6)<=1&&(y=0),u=0;u<y;u++)n[u]=m*(256*(y*y-u*u)/(y*y))}(),function(){for(var t=0;t<Xt;t++)o[t][0]>>=4,o[t][1]>>=4,o[t][2]>>=4,o[t][3]=t}(),function(){var t,e,i,a,n,s,l=0,c=0;for(t=0;t<Xt;t++){for(n=t,s=(i=o[t])[1],e=t+1;e<Xt;e++)(a=o[e])[1]<s&&(n=e,s=a[1]);if(a=o[n],t!=n&&(e=a[0],a[0]=i[0],i[0]=e,e=a[1],a[1]=i[1],i[1]=e,e=a[2],a[2]=i[2],i[2]=e,e=a[3],a[3]=i[3],i[3]=e),s!=l){for(r[l]=c+t>>1,e=l+1;e<s;e++)r[e]=t;l=s,c=t}}for(r[l]=c+255>>1,e=l+1;e<256;e++)r[e]=255}()},this.getColormap=function(){for(var t=[],e=[],r=0;r<Xt;r++)e[o[r][3]]=r;for(var i=0,a=0;a<Xt;a++){var n=e[a];t[i++]=o[n][0],t[i++]=o[n][1],t[i++]=o[n][2]}return t},this.lookupRGB=function(t,e,i){for(var a,n,s,l=1e3,c=-1,h=r[e],u=h-1;h<Xt||u>=0;)h<Xt&&((s=(n=o[h])[1]-e)>=l?h=Xt:(h++,s<0&&(s=-s),(a=n[0]-t)<0&&(a=-a),(s+=a)<l&&((a=n[2]-i)<0&&(a=-a),(s+=a)<l&&(l=s,c=n[3])))),u>=0&&((s=e-(n=o[u])[1])>=l?u=-1:(u--,s<0&&(s=-s),(a=n[0]-t)<0&&(a=-a),(s+=a)<l&&((a=n[2]-i)<0&&(a=-a),(s+=a)<l&&(l=s,c=n[3]))));return c}}var Yt=5003,Jt=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function Zt(t,e,o,r){var i,a,n,s,l,c,h,u,d,g=Math.max(2,r),f=new Uint8Array(256),p=new Int32Array(Yt),m=new Int32Array(Yt),v=0,y=0,w=!1;function b(t,e){f[a++]=t,a>=254&&A(e)}function C(t){x(Yt),y=l+2,w=!0,D(l,t)}function x(t){for(var e=0;e<t;++e)p[e]=-1}function A(t){a>0&&(t.writeByte(a),t.writeBytes(f,0,a),a=0)}function B(t){return(1<<t)-1}function k(){return 0===h?-1:(--h,255&o[u++])}function D(t,e){for(i&=Jt[v],v>0?i|=t<<v:i=t,v+=d;v>=8;)b(255&i,e),i>>=8,v-=8;if((y>n||w)&&(w?(n=B(d=s),w=!1):(++d,n=12==d?4096:B(d))),t==c){for(;v>0;)b(255&i,e),i>>=8,v-=8;A(e)}}this.encode=function(o){o.writeByte(g),h=t*e,u=0,function(t,e){var o,r,i,h,u,g,f;for(w=!1,n=B(d=s=t),c=1+(l=1<<t-1),y=l+2,a=0,h=k(),f=0,o=Yt;o<65536;o*=2)++f;f=8-f,x(g=Yt),D(l,e);t:for(;-1!=(r=k());)if(o=(r<<12)+h,p[i=r<<f^h]!==o){if(p[i]>=0){u=g-i,0===i&&(u=1);do{if((i-=u)<0&&(i+=g),p[i]===o){h=m[i];continue t}}while(p[i]>=0)}D(h,e),h=r,y<4096?(m[i]=y++,p[i]=o):C(e)}else h=m[i];D(h,e),D(c,e)}(g+1,o),o.writeByte(0)}}function te(){this.page=-1,this.pages=[],this.newPage()}te.pageSize=4096,te.charMap={};for(var ee=0;ee<256;ee++)te.charMap[ee]=String.fromCharCode(ee);function oe(t,e){this.width=~~t,this.height=~~e,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new te}te.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(te.pageSize),this.cursor=0},te.prototype.getData=function(){for(var t="",e=0;e<this.pages.length;e++)for(var o=0;o<te.pageSize;o++)t+=te.charMap[this.pages[e][o]];return t},te.prototype.toFlattenUint8Array=function(){const t=[];for(var e=0;e<this.pages.length;e++)if(e===this.pages.length-1){const o=Uint8Array.from(this.pages[e].slice(0,this.cursor));t.push(o)}else t.push(this.pages[e]);const o=new Uint8Array(t.reduce(((t,e)=>t+e.length),0));return t.reduce(((t,e)=>(o.set(e,t),t+e.length)),0),o},te.prototype.writeByte=function(t){this.cursor>=te.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=t},te.prototype.writeUTFBytes=function(t){for(var e=t.length,o=0;o<e;o++)this.writeByte(t.charCodeAt(o))},te.prototype.writeBytes=function(t,e,o){for(var r=o||t.length,i=e||0;i<r;i++)this.writeByte(t[i])},oe.prototype.setDelay=function(t){this.delay=Math.round(t/10)},oe.prototype.setFrameRate=function(t){this.delay=Math.round(100/t)},oe.prototype.setDispose=function(t){t>=0&&(this.dispose=t)},oe.prototype.setRepeat=function(t){this.repeat=t},oe.prototype.setTransparent=function(t){this.transparent=t},oe.prototype.addFrame=function(t){this.image=t,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},oe.prototype.finish=function(){this.out.writeByte(59)},oe.prototype.setQuality=function(t){t<1&&(t=1),this.sample=t},oe.prototype.setDither=function(t){!0===t&&(t="FloydSteinberg"),this.dither=t},oe.prototype.setGlobalPalette=function(t){this.globalPalette=t},oe.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},oe.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},oe.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new Wt(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},oe.prototype.indexPixels=function(t){var e=this.pixels.length/3;this.indexedPixels=new Uint8Array(e);for(var o=0,r=0;r<e;r++){var i=this.findClosestRGB(255&this.pixels[o++],255&this.pixels[o++],255&this.pixels[o++]);this.usedEntry[i]=!0,this.indexedPixels[r]=i}},oe.prototype.ditherPixels=function(t,e){var o={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!t||!o[t])throw"Unknown dithering kernel: "+t;var r=o[t],i=0,a=this.height,n=this.width,s=this.pixels,l=e?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var c=0;c<a;c++){e&&(l*=-1);for(var h=1==l?0:n-1,u=1==l?n:0;h!==u;h+=l){var d=3*(i=c*n+h),g=s[d],f=s[d+1],p=s[d+2];d=this.findClosestRGB(g,f,p),this.usedEntry[d]=!0,this.indexedPixels[i]=d,d*=3;for(var m=g-this.colorTab[d],v=f-this.colorTab[d+1],y=p-this.colorTab[d+2],w=1==l?0:r.length-1,b=1==l?r.length:0;w!==b;w+=l){var C=r[w][1],x=r[w][2];if(C+h>=0&&C+h<n&&x+c>=0&&x+c<a){var A=r[w][0];d=i+C+x*n,s[d*=3]=Math.max(0,Math.min(255,s[d]+m*A)),s[d+1]=Math.max(0,Math.min(255,s[d+1]+v*A)),s[d+2]=Math.max(0,Math.min(255,s[d+2]+y*A))}}}}},oe.prototype.findClosest=function(t,e){return this.findClosestRGB((16711680&t)>>16,(65280&t)>>8,255&t,e)},oe.prototype.findClosestRGB=function(t,e,o,r){if(null===this.colorTab)return-1;if(this.neuQuant&&!r)return this.neuQuant.lookupRGB(t,e,o);for(var i=0,a=16777216,n=this.colorTab.length,s=0,l=0;s<n;l++){var c=t-(255&this.colorTab[s++]),h=e-(255&this.colorTab[s++]),u=o-(255&this.colorTab[s++]),d=c*c+h*h+u*u;(!r||this.usedEntry[l])&&d<a&&(a=d,i=l)}return i},oe.prototype.getImagePixels=function(){var t=this.width,e=this.height;this.pixels=new Uint8Array(t*e*3);for(var o=this.image,r=0,i=0,a=0;a<e;a++)for(var n=0;n<t;n++)this.pixels[i++]=o[r++],this.pixels[i++]=o[r++],this.pixels[i++]=o[r++],r++},oe.prototype.writeGraphicCtrlExt=function(){var t,e;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(t=0,e=0):(t=1,e=2),this.dispose>=0&&(e=7&this.dispose),e<<=2,this.out.writeByte(e|t),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},oe.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},oe.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},oe.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},oe.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var t=768-this.colorTab.length,e=0;e<t;e++)this.out.writeByte(0)},oe.prototype.writeShort=function(t){this.out.writeByte(255&t),this.out.writeByte(t>>8&255)},oe.prototype.writePixels=function(){new Zt(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},oe.prototype.stream=function(){return this.out};var re=function(t,e,o,r){return new(o||(o=Promise))((function(i,a){function n(t){try{l(r.next(t))}catch(e){a(e)}}function s(t){try{l(r.throw(t))}catch(e){a(e)}}function l(t){var e;t.done?i(t.value):(e=t.value,e instanceof o?e:new o((function(t){t(e)}))).then(n,s)}l((r=r.apply(t,e||[])).next())}))};const{Canvas:ie}=pt,ae=.4;function ne(t){if(t)return new Promise((function(o,r){if("data"==t.slice(0,4)){let i=new Image;return i.onload=function(){o(i),e(i)},i.onerror=function(){r("Image load error"),e(i)},void(i.src=t)}let i=new Image;i.setAttribute("crossOrigin","Anonymous"),i.onload=function(){o(i)},i.onerror=function(){r("Image load error")},i.src=t}));function e(t){t.onload=null,t.onerror=null}}class se{constructor(t){const e=Object.assign({},t);if(Object.keys(se.defaultOptions).forEach((t=>{t in e||Object.defineProperty(e,t,{value:se.defaultOptions[t],enumerable:!0,writable:!0})})),e.components?"object"==typeof e.components&&Object.keys(se.defaultComponentOptions).forEach((t=>{t in e.components?Object.defineProperty(e.components,t,{value:Object.assign(Object.assign({},se.defaultComponentOptions[t]),e.components[t]),enumerable:!0,writable:!0}):Object.defineProperty(e.components,t,{value:se.defaultComponentOptions[t],enumerable:!0,writable:!0})})):e.components=se.defaultComponentOptions,null!==e.dotScale&&void 0!==e.dotScale){if(e.dotScale<=0||e.dotScale>1)throw new Error("dotScale should be in range (0, 1].");e.components.data.scale=e.dotScale,e.components.timing.scale=e.dotScale,e.components.alignment.scale=e.dotScale}this.options=e,this.canvas=new ie(t.size,t.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new Ot(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}draw(){return new Promise((t=>this._draw().then(t)))}_clear(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}static _prepareRoundedCornerClip(t,e,o,r,i,a){t.beginPath(),t.moveTo(e,o),t.arcTo(e+r,o,e+r,o+i,a),t.arcTo(e+r,o+i,e,o+i,a),t.arcTo(e,o+i,e,o,a),t.arcTo(e,o,e+r,o,a),t.closePath()}static _getAverageRGB(t){const e={r:0,g:0,b:0};let o,r,i=-4;const a={r:0,g:0,b:0};let n=0;r=t.naturalHeight||t.height,o=t.naturalWidth||t.width;const s=new ie(o,r).getContext("2d");if(!s)return e;let l;s.drawImage(t,0,0);try{l=s.getImageData(0,0,o,r)}catch(c){return e}for(;(i+=20)<l.data.length;)l.data[i]>200||l.data[i+1]>200||l.data[i+2]>200||(++n,a.r+=l.data[i],a.g+=l.data[i+1],a.b+=l.data[i+2]);return a.r=~~(a.r/n),a.g=~~(a.g/n),a.b=~~(a.b/n),a}static _drawDot(t,e,o,r,i=0,a=1){t.fillRect((e+i)*r,(o+i)*r,a*r,a*r)}static _drawAlignProtector(t,e,o,r){t.clearRect((e-2)*r,(o-2)*r,5*r,5*r),t.fillRect((e-2)*r,(o-2)*r,5*r,5*r)}static _drawAlign(t,e,o,r,i=0,a=1,n,s){const l=t.fillStyle;t.fillStyle=n,new Array(4).fill(0).map(((n,s)=>{se._drawDot(t,e-2+s,o-2,r,i,a),se._drawDot(t,e+2,o-2+s,r,i,a),se._drawDot(t,e+2-s,o+2,r,i,a),se._drawDot(t,e-2,o+2-s,r,i,a)})),se._drawDot(t,e,o,r,i,a),s||(t.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map(((n,s)=>{se._drawDot(t,e-1+s,o-1,r,i,a),se._drawDot(t,e+1,o-1+s,r,i,a),se._drawDot(t,e+1-s,o+1,r,i,a),se._drawDot(t,e-1,o+1-s,r,i,a)}))),t.fillStyle=l}_draw(){var t,e,o,r,i,a,n,s,l,c,h,u,d,g,f,p,m,v,y;return re(this,void 0,void 0,(function*(){const w=null===(t=this.qrCode)||void 0===t?void 0:t.moduleCount,b=this.options.size;let C=this.options.margin;(C<0||2*C>=b)&&(C=0);const x=Math.ceil(C),A=b-2*C,B=this.options.whiteMargin,k=this.options.backgroundDimming,D=Math.ceil(A/w),P=D*w,T=P+2*x,E=new ie(T,T),_=E.getContext("2d");this._clear(),_.save(),_.translate(x,x);const S=new ie(T,T),R=S.getContext("2d");let L=null,I=[];if(this.options.gifBackground){const t=(t=>{const e=new Uint8Array(t);return mt({data:e,pos:0},St)})(this.options.gifBackground);if(L=t,I=(M=t).frames.filter((t=>t.image)).map((t=>It(t,M.gct))),this.options.autoColor){let t=0,e=0,o=0,r=0;for(let i=0;i<I[0].colorTable.length;i++){const a=I[0].colorTable[i];a[0]>200||a[1]>200||a[2]>200||(0===a[0]&&0===a[1]&&0===a[2]||(r++,t+=a[0],e+=a[1],o+=a[2]))}t=~~(t/r),e=~~(e/r),o=~~(o/r),this.options.colorDark="rgb(".concat(t,",").concat(e,",").concat(o,")")}}else if(this.options.backgroundImage){const t=yield ne(this.options.backgroundImage);if(this.options.autoColor){const e=se._getAverageRGB(t);this.options.colorDark="rgb(".concat(e.r,",").concat(e.g,",").concat(e.b,")")}R.drawImage(t,0,0,t.width,t.height,0,0,T,T),R.rect(0,0,T,T),R.fillStyle=k,R.fill()}else R.rect(0,0,T,T),R.fillStyle=this.options.colorLight,R.fill();var M;const N=Ft.getPatternPosition(this.qrCode.typeNumber),O=(null===(o=null===(e=this.options.components)||void 0===e?void 0:e.data)||void 0===o?void 0:o.scale)||ae,U=.5*(1-O);for(let t=0;t<w;t++)for(let e=0;e<w;e++){const o=this.qrCode.isDark(t,e),r=e<8&&(t<8||t>=w-8)||e>=w-8&&t<8;let i=r||(6==t&&e>=8&&e<=w-8||6==e&&t>=8&&t<=w-8);for(let s=1;s<N.length-1;s++)i=i||t>=N[s]-2&&t<=N[s]+2&&e>=N[s]-2&&e<=N[s]+2;const a=e*D+(i?0:U*D),n=t*D+(i?0:U*D);if(_.strokeStyle=o?this.options.colorDark:this.options.colorLight,_.lineWidth=.5,_.fillStyle=o?this.options.colorDark:this.options.colorLight,0===N.length)i||_.fillRect(a,n,(i?1:O)*D,(i?1:O)*D);else{i||e<w-4&&e>=w-4-5&&t<w-4&&t>=w-4-5||_.fillRect(a,n,(i?1:O)*D,(i?1:O)*D)}}const j=N[N.length-1],z=this.options.colorLight;if(_.fillStyle=z,_.fillRect(0,0,8*D,8*D),_.fillRect(0,(w-8)*D,8*D,8*D),_.fillRect((w-8)*D,0,8*D,8*D),(null===(i=null===(r=this.options.components)||void 0===r?void 0:r.timing)||void 0===i?void 0:i.protectors)&&(_.fillRect(8*D,6*D,(w-8-8)*D,D),_.fillRect(6*D,8*D,D,(w-8-8)*D)),(null===(n=null===(a=this.options.components)||void 0===a?void 0:a.cornerAlignment)||void 0===n?void 0:n.protectors)&&se._drawAlignProtector(_,j,j,D),null===(l=null===(s=this.options.components)||void 0===s?void 0:s.alignment)||void 0===l?void 0:l.protectors)for(let t=0;t<N.length;t++)for(let e=0;e<N.length;e++){const o=N[e],r=N[t];(6!==o||6!==r&&r!==j)&&((6!==r||6!==o&&o!==j)&&(o===j&&r===j||se._drawAlignProtector(_,o,r,D)))}_.fillStyle=this.options.colorDark,_.fillRect(0,0,7*D,D),_.fillRect((w-7)*D,0,7*D,D),_.fillRect(0,6*D,7*D,D),_.fillRect((w-7)*D,6*D,7*D,D),_.fillRect(0,(w-7)*D,7*D,D),_.fillRect(0,(w-7+6)*D,7*D,D),_.fillRect(0,0,D,7*D),_.fillRect(6*D,0,D,7*D),_.fillRect((w-7)*D,0,D,7*D),_.fillRect((w-7+6)*D,0,D,7*D),_.fillRect(0,(w-7)*D,D,7*D),_.fillRect(6*D,(w-7)*D,D,7*D),_.fillRect(2*D,2*D,3*D,3*D),_.fillRect((w-7+2)*D,2*D,3*D,3*D),_.fillRect(2*D,(w-7+2)*D,3*D,3*D);const F=(null===(h=null===(c=this.options.components)||void 0===c?void 0:c.timing)||void 0===h?void 0:h.scale)||ae,H=.5*(1-F);for(let t=0;t<w-8;t+=2)se._drawDot(_,8+t,6,D,H,F),se._drawDot(_,6,8+t,D,H,F);const G=(null===(d=null===(u=this.options.components)||void 0===u?void 0:u.cornerAlignment)||void 0===d?void 0:d.scale)||ae,q=.5*(1-G);se._drawAlign(_,j,j,D,q,G,this.options.colorDark,(null===(f=null===(g=this.options.components)||void 0===g?void 0:g.cornerAlignment)||void 0===f?void 0:f.protectors)||!1);const Q=(null===(m=null===(p=this.options.components)||void 0===p?void 0:p.alignment)||void 0===m?void 0:m.scale)||ae,K=.5*(1-Q);for(let t=0;t<N.length;t++)for(let e=0;e<N.length;e++){const o=N[e],r=N[t];(6!==o||6!==r&&r!==j)&&((6!==r||6!==o&&o!==j)&&(o===j&&r===j||se._drawAlign(_,o,r,D,K,Q,this.options.colorDark,(null===(y=null===(v=this.options.components)||void 0===v?void 0:v.alignment)||void 0===y?void 0:y.protectors)||!1)))}if(B&&(_.fillStyle=this.options.backgroundColor,_.fillRect(-x,-x,T,x),_.fillRect(-x,P,T,x),_.fillRect(P,-x,x,T),_.fillRect(-x,-x,x,T)),this.options.logoImage){const t=yield ne(this.options.logoImage);let e=this.options.logoScale,o=this.options.logoMargin,r=this.options.logoCornerRadius;(e<=0||e>=1)&&(e=.2),o<0&&(o=0),r<0&&(r=0);const i=P*e,a=.5*(T-i),n=a;_.restore(),_.fillStyle=this.options.logoBackgroundColor,_.save(),se._prepareRoundedCornerClip(_,a-o,n-o,i+2*o,i+2*o,r+o),_.clip();const s=_.globalCompositeOperation;_.globalCompositeOperation="destination-out",_.fill(),_.globalCompositeOperation=s,_.restore(),_.save(),se._prepareRoundedCornerClip(_,a,n,i,i,r),_.clip(),_.drawImage(t,a,n,i,i),_.restore(),_.save(),_.translate(x,x)}if(L){let t,e,o,r,i,a;if(I.forEach((function(n){t||(t=new oe(b,b),t.setDelay(n.delay),t.setRepeat(0));const{width:s,height:l}=n.dims;e||(e=new ie(s,l),o=e.getContext("2d"),o.rect(0,0,e.width,e.height),o.fillStyle="#ffffff",o.fill()),r&&a&&s===r.width&&l===r.height||(r=new ie(s,l),i=r.getContext("2d"),a=i.createImageData(s,l)),a.data.set(n.patch),i.putImageData(a,0,0),o.drawImage(r.getContext("2d").canvas,n.dims.left,n.dims.top);const c=new ie(T,T),h=c.getContext("2d");h.drawImage(e.getContext("2d").canvas,0,0,T,T),h.rect(0,0,T,T),h.fillStyle=k,h.fill(),h.drawImage(E.getContext("2d").canvas,0,0,T,T);const u=new ie(b,b),d=u.getContext("2d");d.drawImage(c.getContext("2d").canvas,0,0,b,b),t.addFrame(d.getImageData(0,0,u.width,u.height).data)})),!t)throw new Error("No frames.");if(t.finish(),le(this.canvas)){const e=t.stream().toFlattenUint8Array().reduce(((t,e)=>t+String.fromCharCode(e)),"");return Promise.resolve("data:image/gif;base64,".concat(window.btoa(e)))}return Promise.resolve(Buffer.from(t.stream().toFlattenUint8Array()))}{R.drawImage(E.getContext("2d").canvas,0,0,T,T),_.drawImage(S.getContext("2d").canvas,-x,-x,T,T);const t=new ie(b,b);t.getContext("2d").drawImage(E.getContext("2d").canvas,0,0,b,b),this.canvas=t;const e=this.options.gifBackground?"gif":"png";return le(this.canvas)?Promise.resolve(this.canvas.toDataURL(e)):Promise.resolve(this.canvas.toBuffer(e))}}))}}function le(t){try{return t instanceof HTMLElement}catch(e){return"object"==typeof t&&1===t.nodeType&&"object"==typeof t.style&&"object"==typeof t.ownerDocument}}se.CorrectLevel=Ut,se.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},se.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:Ut.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:se.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};const ce={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:()=>({imgUrl:""}),watch:{$props:{deep:!0,handler(){this.main()}}},mounted(){this.main()},methods:{async main(){if(this.gifBgSrc){const e=await(t=this.gifBgSrc,new Promise(((e,o)=>{var r=new XMLHttpRequest;r.responseType="blob",r.onload=function(){var t=new FileReader;t.onloadend=function(){e(t.result)},t.readAsArrayBuffer(r.response)},r.open("GET",t),r.send()}))),o=this.logoSrc;return void this.render(void 0,o,e)}var t;const e=this.bgSrc,o=this.logoSrc;this.render(e,o)},async render(t,e,o){const r=this;new se({gifBackground:o,text:r.text,size:r.size,margin:r.margin,colorDark:r.colorDark,colorLight:r.colorLight,backgroundColor:r.backgroundColor,backgroundImage:t,backgroundDimming:r.backgroundDimming,logoImage:e,logoScale:r.logoScale,logoBackgroundColor:r.logoBackgroundColor,correctLevel:r.correctLevel,logoMargin:r.logoMargin,logoCornerRadius:r.logoCornerRadius,whiteMargin:W(r.whiteMargin),dotScale:r.dotScale,autoColor:W(r.autoColor),binarize:W(r.binarize),binarizeThreshold:r.binarizeThreshold,components:r.components}).draw().then((t=>{this.imgUrl=t,r.callback&&r.callback(t,r.qid)}))}}},he=["src"];const ue=e(ce,[["render",function(t,e,o,r,n,s){return o.bindElement?(a(),i("img",{key:0,style:{display:"inline-block"},src:n.imgUrl},null,8,he)):k("",!0)}]]),de={class:"m-2"},ge={class:"dialog-footer"},fe=Object.assign({name:"QRCodeUpload"},{__name:"QR-code",props:{classId:{type:Number,default:0}},emits:["on-success"],setup(t,{emit:e}){const c=e,u=t,d=g(!1),f=o(),m=g(""),v=()=>{const t=window.location;m.value=t.protocol+"//"+t.host+"/#/scanUpload?id="+u.classId+"&token="+f.token+"&t="+Date.now(),d.value=!0},y=()=>{d.value=!1,m.value="",c("on-success","")};return(t,e)=>{const o=r("el-button"),c=r("el-dialog");return a(),i(b,null,[p("div",null,[n(o,{type:"primary",icon:"iphone",onClick:v},{default:s((()=>e[2]||(e[2]=[h(" 扫码上传")]))),_:1})]),n(c,{modelValue:d.value,"onUpdate:modelValue":e[1]||(e[1]=t=>d.value=t),title:"扫码上传",width:"320px","show-close":!1,"append-to-body":"","close-on-click-modal":!1,draggable:""},{footer:s((()=>[p("div",ge,[n(o,{onClick:e[0]||(e[0]=t=>d.value=!1)},{default:s((()=>e[3]||(e[3]=[h("取 消")]))),_:1}),n(o,{type:"primary",onClick:y},{default:s((()=>e[4]||(e[4]=[h("完成上传")]))),_:1})])])),default:s((()=>[p("div",de,[n(ue,{logoSrc:l(_),size:291,margin:0,autoColor:!0,dotScale:1,text:m.value,colorDark:"green",colorLight:"white",ref:"qrcode"},null,8,["logoSrc","text"])])])),_:1},8,["modelValue"])],64)}}});export{$ as C,M as U,N as _,fe as a,O as b,R as c,j as d,L as e,U as f,S as g,I as i};
