/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{f as e,c as a,u as l}from"./087AC4D233B64EB0shop.DuFD14Dz.js";import{aj as o,u as s,a as u,r as t,g as r,c as p,o as d,b as m,f as n,w as c,h as i,E as v}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const h={class:"gva-form-box"},f=Object.assign({name:"ShopForm"},{__name:"shopForm",setup(f){const y=o(),b=s(),I=u(!1),_=u(""),D=u({shopName:"",shopID:"",shopAPIKey:""}),V=t({}),g=u();(async()=>{if(y.query.id){const a=await e({ID:y.query.id});0===a.code&&(D.value=a.data,_.value="update")}else _.value="create"})();const A=async()=>{var e;I.value=!0,null==(e=g.value)||e.validate((async e=>{if(!e)return I.value=!1;let o;switch(_.value){case"create":default:o=await a(D.value);break;case"update":o=await l(D.value)}I.value=!1,0===o.code&&v({type:"success",message:"创建/更改成功"})}))},w=()=>{b.go(-1)};return(e,a)=>{const l=r("el-input"),o=r("el-form-item"),s=r("el-button"),u=r("el-form");return d(),p("div",null,[m("div",h,[n(u,{model:D.value,ref_key:"elFormRef",ref:g,"label-position":"right",rules:V,"label-width":"80px"},{default:c((()=>[n(o,{label:"商店名字:",prop:"shopName"},{default:c((()=>[n(l,{modelValue:D.value.shopName,"onUpdate:modelValue":a[0]||(a[0]=e=>D.value.shopName=e),clearable:!0,placeholder:"请输入商店名字"},null,8,["modelValue"])])),_:1}),n(o,{label:"商店ID:",prop:"shopID"},{default:c((()=>[n(l,{modelValue:D.value.shopID,"onUpdate:modelValue":a[1]||(a[1]=e=>D.value.shopID=e),clearable:!0,placeholder:"请输入商店ID"},null,8,["modelValue"])])),_:1}),n(o,{label:"商店API Key:",prop:"shopAPIKey"},{default:c((()=>[n(l,{modelValue:D.value.shopAPIKey,"onUpdate:modelValue":a[2]||(a[2]=e=>D.value.shopAPIKey=e),clearable:!0,placeholder:"请输入商店API Key"},null,8,["modelValue"])])),_:1}),n(o,null,{default:c((()=>[n(s,{loading:I.value,type:"primary",onClick:A},{default:c((()=>a[3]||(a[3]=[i("保存")]))),_:1},8,["loading"]),n(s,{type:"primary",onClick:w},{default:c((()=>a[4]||(a[4]=[i("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])}}});export{f as default};
