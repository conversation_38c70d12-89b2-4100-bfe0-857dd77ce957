/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return n};var r,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},l=u.iterator||"@@iterator",c=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(r){s=function(t,e,r){return t[e]=r}}function p(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),u=new B(n||[]);return i(a,"_invoke",{value:N(t,r,u)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=p;var d="suspendedStart",v="suspendedYield",y="executing",m="completed",g={};function w(){}function b(){}function x(){}var k={};s(k,l,(function(){return this}));var _=Object.getPrototypeOf,E=_&&_(_(C([])));E&&E!==o&&a.call(E,l)&&(k=E);var L=x.prototype=w.prototype=Object.create(k);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function V(e,r){function n(o,i,u,l){var c=h(e[o],e,i);if("throw"!==c.type){var f=c.arg,s=f.value;return s&&"object"==t(s)&&a.call(s,"__await")?r.resolve(s.__await).then((function(t){n("next",t,u,l)}),(function(t){n("throw",t,u,l)})):r.resolve(s).then((function(t){f.value=t,u(f)}),(function(t){return n("throw",t,u,l)}))}l(c.arg)}var o;i(this,"_invoke",{value:function(t,e){function a(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(a,a):a()}})}function N(t,e,n){var o=d;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:r,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var l=O(u,n);if(l){if(l===g)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var c=h(t,e,n);if("normal"===c.type){if(o=n.done?m:v,c.arg===g)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=m,n.method="throw",n.arg=c.arg)}}}function O(t,e){var n=e.method,o=t.iterator[n];if(o===r)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=r,O(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=h(o,t.iterator,e.arg);if("throw"===a.type)return e.method="throw",e.arg=a.arg,e.delegate=null,g;var i=a.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=r),e.delegate=null,g):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,g)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function S(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function B(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function C(e){if(e||""===e){var n=e[l];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function t(){for(;++o<e.length;)if(a.call(e,o))return t.value=e[o],t.done=!1,t;return t.value=r,t.done=!0,t};return i.next=i}}throw new TypeError(t(e)+" is not iterable")}return b.prototype=x,i(L,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=s(x,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,s(t,f,"GeneratorFunction")),t.prototype=Object.create(L),t},n.awrap=function(t){return{__await:t}},j(V.prototype),s(V.prototype,c,(function(){return this})),n.AsyncIterator=V,n.async=function(t,e,r,o,a){void 0===a&&(a=Promise);var i=new V(p(t,e,r,o),a);return n.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},j(L),s(L,f,"Generator"),s(L,l,(function(){return this})),s(L,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=C,B.prototype={constructor:B,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(S),!t)for(var e in this)"t"===e.charAt(0)&&a.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=r)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return u.type="throw",u.arg=t,e.next=n,o&&(e.method="next",e.arg=r),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),c=a.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),S(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:C(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=r),g}},n}function r(t,e,r,n,o,a,i){try{var u=t[a](i),l=u.value}catch(t){return void r(t)}u.done?e(l):Promise.resolve(l).then(n,o)}function n(t){return function(){var e=this,n=arguments;return new Promise((function(o,a){var i=t.apply(e,n);function u(t){r(i,o,a,u,l,"next",t)}function l(t){r(i,o,a,u,l,"throw",t)}u(void 0)}))}}System.register(["./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,r){"use strict";var o,a,i,u,l,c,f,s,p,h,d,v,y,m,g,w,b,x;return{setters:[function(t){o=t.m,a=t.d,i=t.n,u=t.o},function(t){l=t._},function(t){c=t.a,f=t.g,s=t.c,p=t.o,h=t.f,d=t.b,v=t.w,y=t.h,m=t.F,g=t.D,w=t.i,b=t.ab,x=t.E}],execute:function(){var r={class:"gva-table-box"},k={class:"gva-btn-list gap-3 flex items-center"},_={class:"flex justify-between items-center"};t("default",Object.assign({name:"AutoPkg"},{__name:"autoPkg",setup:function(t){var E=c({packageName:"",template:"",label:"",desc:""}),L=c([]),j=function(){var t=n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,o();case 2:0===(r=t.sent).code&&(L.value=r.data);case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}();j();var V=function(t,e,r){/[\u4E00-\u9FA5]/g.test(e)?r(new Error("不能为中文")):/^\d+$/.test(e[0])?r(new Error("不能够以数字开头")):r()},N=c({packageName:[{required:!0,message:"请输入包名",trigger:"blur"},{validator:V,trigger:"blur"}],template:[{required:!0,message:"请选择模板",trigger:"change"},{validator:V,trigger:"blur"}]}),O=c(!1),P=function(){O.value=!1,E.value={packageName:"",template:"",label:"",desc:""}},S=c(null),B=function(){var t=n(e().mark((function t(){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:S.value.validate(function(){var t=n(e().mark((function t(r){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!r){t.next=7;break}return t.next=3,u(E.value);case 3:0===t.sent.code&&x({type:"success",message:"添加成功",showClose:!0}),T(),P();case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 1:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),C=c([]),T=function(){var t=n(e().mark((function t(){var r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a();case 2:0===(r=t.sent).code&&(C.value=r.data.pkgs);case 4:case"end":return t.stop()}}),t)})));return function(){return t.apply(this,arguments)}}(),F=function(){var t=n(e().mark((function t(r){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:b.confirm("此操作仅删除数据库中的pkg存储，后端相应目录结构请自行删除与数据库保持一致！","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(n(e().mark((function t(){return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i(r);case 2:0===t.sent.code&&(x({type:"success",message:"删除成功!"}),T());case 4:case"end":return t.stop()}}),t)}))));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();return T(),function(t,e){var n=f("el-button"),o=f("el-table-column"),a=f("el-table"),i=f("el-input"),u=f("el-form-item"),c=f("el-option"),b=f("el-select"),x=f("el-form"),j=f("el-drawer");return p(),s("div",null,[h(l,{href:"https://www.bilibili.com/video/BV1kv4y1g7nT?p=3",title:"此功能为开发环境使用，不建议发布到生产，具体使用效果请看视频https://www.bilibili.com/video/BV1kv4y1g7nT?p=3"}),d("div",r,[d("div",k,[h(n,{type:"primary",icon:"plus",onClick:e[0]||(e[0]=function(t){O.value=!0})},{default:v((function(){return e[6]||(e[6]=[y(" 新增 ")])})),_:1})]),h(a,{data:C.value},{default:v((function(){return[h(o,{align:"left",label:"id",width:"120",prop:"ID"}),h(o,{align:"left",label:"包名",width:"150",prop:"packageName"}),h(o,{align:"left",label:"模板",width:"150",prop:"template"}),h(o,{align:"left",label:"展示名",width:"150",prop:"label"}),h(o,{align:"left",label:"描述","min-width":"150",prop:"desc"}),h(o,{align:"left",label:"操作",width:"200"},{default:v((function(t){return[h(n,{icon:"delete",type:"primary",link:"",onClick:function(e){return F(t.row)}},{default:v((function(){return e[7]||(e[7]=[y(" 删除 ")])})),_:2},1032,["onClick"])]})),_:1})]})),_:1},8,["data"])]),h(j,{modelValue:O.value,"onUpdate:modelValue":e[5]||(e[5]=function(t){return O.value=t}),size:"40%","show-close":!1},{header:v((function(){return[d("div",_,[e[10]||(e[10]=d("span",{class:"text-lg"},"创建Package",-1)),d("div",null,[h(n,{onClick:P},{default:v((function(){return e[8]||(e[8]=[y(" 取 消 ")])})),_:1}),h(n,{type:"primary",onClick:B},{default:v((function(){return e[9]||(e[9]=[y(" 确 定 ")])})),_:1})])])]})),default:v((function(){return[h(l,{title:"模板package会创建集成于项目本体中的代码包，模板plugin会创建插件包"}),h(x,{ref_key:"pkgForm",ref:S,model:E.value,rules:N.value,"label-width":"80px"},{default:v((function(){return[h(u,{label:"包名",prop:"packageName"},{default:v((function(){return[h(i,{modelValue:E.value.packageName,"onUpdate:modelValue":e[1]||(e[1]=function(t){return E.value.packageName=t}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),h(u,{label:"模板",prop:"template"},{default:v((function(){return[h(b,{modelValue:E.value.template,"onUpdate:modelValue":e[2]||(e[2]=function(t){return E.value.template=t})},{default:v((function(){return[(p(!0),s(m,null,g(L.value,(function(t){return p(),w(c,{label:t,value:t,key:t},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),h(u,{label:"展示名",prop:"label"},{default:v((function(){return[h(i,{modelValue:E.value.label,"onUpdate:modelValue":e[3]||(e[3]=function(t){return E.value.label=t}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),h(u,{label:"描述",prop:"desc"},{default:v((function(){return[h(i,{modelValue:E.value.desc,"onUpdate:modelValue":e[4]||(e[4]=function(t){return E.value.desc=t}),autocomplete:"off"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])])}}}))}}}))}();
