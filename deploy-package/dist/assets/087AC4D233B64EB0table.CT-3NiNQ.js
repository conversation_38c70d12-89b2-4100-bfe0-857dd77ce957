/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{g as t,c as l,o as n,f as e,w as i}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const a={__name:"table",setup(a){const o=[{ranking:1,title:"更简洁的使用界面，更快速的操作体验",click_num:523,hot:263},{ranking:2,title:"更优质的服务，更便捷的使用体验",click_num:416,hot:223},{ranking:3,title:"更快速的创意实现，更高效的工作效率",click_num:337,hot:176},{ranking:4,title:"更多的创意资源，更多的创意灵感",click_num:292,hot:145},{ranking:5,title:"更合理的代码结构，更清晰的代码逻辑",click_num:173,hot:110}];return(a,r)=>{const c=t("el-table-column"),s=t("el-table");return n(),l("div",null,[e(s,{data:o,stripe:"",style:{width:"100%"}},{default:i((()=>[e(c,{prop:"ranking",label:"排名",width:"80",align:"center"}),e(c,{prop:"title",label:"内容标题","show-overflow-tooltip":""}),e(c,{prop:"click_num",label:"关注度",width:"100"}),e(c,{prop:"hot",label:"热度值",width:"100"})])),_:1})])}}};export{a as default};
