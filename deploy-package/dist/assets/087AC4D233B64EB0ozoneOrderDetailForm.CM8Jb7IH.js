/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e,f as a,c as t,u as l}from"./087AC4D233B64EB0arrayCtrl.BNyxBeGJ.js";import{aj as o,u as n,a as i,r,g as s,c as _,o as d,b as p,f as u,w as c,h as g,t as m,E as v}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const b={class:"gva-form-box"},f=Object.assign({name:"OzoneOrderDetailForm"},{__name:"ozoneOrderDetailForm",setup(f){const y=o(),V=n(),w=i(!1),j=i(""),k=i({addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}),h=r({}),D=i();(async()=>{if(y.query.id){const e=await a({ID:y.query.id});0===e.code&&(k.value=e.data,j.value="update")}else j.value="create"})();const U=async()=>{var e;w.value=!0,null==(e=D.value)||e.validate((async e=>{if(!e)return w.value=!1;let a;switch(j.value){case"create":default:a=await t(k.value);break;case"update":a=await l(k.value)}w.value=!1,0===a.code&&v({type:"success",message:"创建/更改成功"})}))},x=()=>{V.go(-1)};return(a,t)=>{const l=s("el-form-item"),o=s("el-date-picker"),n=s("el-switch"),i=s("el-input"),r=s("el-button"),v=s("el-form");return d(),_("div",null,[p("div",b,[u(v,{model:k.value,ref_key:"elFormRef",ref:D,"label-position":"right",rules:h,"label-width":"80px"},{default:c((()=>[u(l,{label:"收件人联系方式:",prop:"addressee"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取 "+m(k.value.addressee),1)])),_:1}),u(l,{label:"分析数据:",prop:"analytics_data"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取 "+m(k.value.analytics_data),1)])),_:1}),u(l,{label:"买家信息:",prop:"customer"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取 "+m(k.value.customer),1)])),_:1}),u(l,{label:"货件条码:",prop:"barcodes"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取 "+m(k.value.barcodes),1)])),_:1}),u(l,{label:"货件交付物流的时间:",prop:"delivering_date"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取 "+m(k.value.delivering_date),1)])),_:1}),u(l,{label:"快递方式:",prop:"delivery_method"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取 "+m(k.value.delivery_method),1)])),_:1}),u(l,{label:"有关商品成本、折扣幅度、付款和佣金的信息:",prop:"financial_data"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取 "+m(k.value.financial_data),1)])),_:1}),u(l,{label:"开始处理货件的日期和时间:",prop:"in_process_at"},{default:c((()=>[u(o,{modelValue:k.value.in_process_at,"onUpdate:modelValue":t[0]||(t[0]=e=>k.value.in_process_at=e),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1}),u(l,{label:"如果使用快速物流 Ozon Express —— true:",prop:"is_express"},{default:c((()=>[u(n,{modelValue:k.value.is_express,"onUpdate:modelValue":t[1]||(t[1]=e=>k.value.is_express=e),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"是","inactive-text":"否",clearable:""},null,8,["modelValue"])])),_:1}),u(l,{label:"带有附加特征的商品列表:",prop:"optional"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取 "+m(k.value.optional),1)])),_:1}),u(l,{label:"货件所属订单的ID:",prop:"order_id"},{default:c((()=>[u(i,{modelValue:k.value.order_id,"onUpdate:modelValue":t[2]||(t[2]=e=>k.value.order_id=e),modelModifiers:{number:!0},clearable:!0,placeholder:"请输入"},null,8,["modelValue"])])),_:1}),u(l,{label:"货件所属的订单号:",prop:"order_number"},{default:c((()=>[u(i,{modelValue:k.value.order_number,"onUpdate:modelValue":t[3]||(t[3]=e=>k.value.order_number=e),clearable:!0,placeholder:"请输入货件所属的订单号"},null,8,["modelValue"])])),_:1}),u(l,{label:"快递母件编号，从该母件中拆分出了当前货件:",prop:"parent_posting_number"},{default:c((()=>[u(i,{modelValue:k.value.parent_posting_number,"onUpdate:modelValue":t[4]||(t[4]=e=>k.value.parent_posting_number=e),clearable:!0,placeholder:"请输入快递母件编号，从该母件中拆分出了当前货件"},null,8,["modelValue"])])),_:1}),u(l,{label:"货件号:",prop:"posting_number"},{default:c((()=>[u(i,{modelValue:k.value.posting_number,"onUpdate:modelValue":t[5]||(t[5]=e=>k.value.posting_number=e),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])])),_:1}),u(l,{label:"货运商品列表:",prop:"products"},{default:c((()=>[u(e,{modelValue:k.value.products,"onUpdate:modelValue":t[6]||(t[6]=e=>k.value.products=e),editable:""},null,8,["modelValue"])])),_:1}),u(l,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:",prop:"requirements"},{default:c((()=>[g(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取 "+m(k.value.requirements),1)])),_:1}),u(l,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:",prop:"shipment_date"},{default:c((()=>[u(o,{modelValue:k.value.shipment_date,"onUpdate:modelValue":t[7]||(t[7]=e=>k.value.shipment_date=e),type:"date",placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1}),u(l,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:",prop:"status"},{default:c((()=>[u(i,{modelValue:k.value.status,"onUpdate:modelValue":t[8]||(t[8]=e=>k.value.status=e),clearable:!0,placeholder:"请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])])),_:1}),u(l,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:",prop:"substatus"},{default:c((()=>[u(i,{modelValue:k.value.substatus,"onUpdate:modelValue":t[9]||(t[9]=e=>k.value.substatus=e),clearable:!0,placeholder:"请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])])),_:1}),u(l,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:",prop:"tpl_integration_type"},{default:c((()=>[u(i,{modelValue:k.value.tpl_integration_type,"onUpdate:modelValue":t[10]||(t[10]=e=>k.value.tpl_integration_type=e),clearable:!0,placeholder:"请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},null,8,["modelValue"])])),_:1}),u(l,{label:"货件跟踪号:",prop:"tracking_number"},{default:c((()=>[u(i,{modelValue:k.value.tracking_number,"onUpdate:modelValue":t[11]||(t[11]=e=>k.value.tracking_number=e),clearable:!0,placeholder:"请输入货件跟踪号"},null,8,["modelValue"])])),_:1}),u(l,{label:"发运的计费信息:",prop:"tariffication\t"},{default:c((()=>[u(e,{modelValue:k.value.tariffication,"onUpdate:modelValue":t[12]||(t[12]=e=>k.value.tariffication=e),editable:""},null,8,["modelValue"])])),_:1}),u(l,null,{default:c((()=>[u(r,{loading:w.value,type:"primary",onClick:U},{default:c((()=>t[13]||(t[13]=[g("保存")]))),_:1},8,["loading"]),u(r,{type:"primary",onClick:x},{default:c((()=>t[14]||(t[14]=[g("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])}}});export{f as default};
