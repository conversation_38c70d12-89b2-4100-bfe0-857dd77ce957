/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register([],(function(e,r){"use strict";return{execute:function(){e("b",(function(e){return e[0]?e.replace(e[0],e[0].toUpperCase()):""})),e("a",(function(e){return e[0]?e.replace(e[0],e[0].toLowerCase()):""})),e("t",(function(e){return"ID"===e?"ID":e.replace(/([A-Z])/g,"_$1").toLowerCase()})),e("c",(function(e){return e.replace(/\_(\w)/g,(function(e,r){return r.toUpperCase()}))}))}}}));
