/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,i(r.key),r)}}function i(t){var i=function(t,i){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,i||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===i?String:Number)(t)}(t,"string");return"symbol"==e(i)?i:i+""}System.register([],(function(e,i){"use strict";return{execute:function(){var i=function(){return e=function e(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1920;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.file=t,this.fileSize=i,this.maxWH=n},(i=[{key:"compress",value:function(){var e=this,t=this.file.type;return this.file.size,new Promise((function(i){var n=new FileReader;n.readAsDataURL(e.file),n.onload=function(){var r=document.createElement("canvas"),o=document.createElement("img");o.src=n.result,o.onload=function(){var n=r.getContext("2d"),a=e.dWH(o.width,o.height,e.maxWH);r.width=a.width,r.height=a.height,n.clearRect(0,0,r.width,r.height),n.drawImage(o,0,0,r.width,r.height);var u=r.toDataURL(t,.9);e.fileSizeKB(u),e.fileSize;var l=e.dataURLtoBlob(u,t),f=new File([l],e.file.name);i(f)}}}))}},{key:"dWH",value:function(e,t,i){var n={width:e,height:t};return Math.max(e,t)>i?e>t?(n.width=i,n.height=Math.round(t*(i/e)),n):(n.height=i,n.width=Math.round(e*(i/t)),n):n}},{key:"fileSizeKB",value:function(e){return Math.round(3*e.split(",")[1].length/4/1024)}},{key:"dataURLtoBlob",value:function(e,t){for(var i=atob(e.split(",")[1]),n=e.split(",")[0].split(":")[1].split(";")[0],r=new ArrayBuffer(i.length),o=new Uint8Array(r),a=0;a<i.length;a++)o[a]=i.charCodeAt(a);return t&&(n=t),new Blob([r],{type:n,lastModifiedDate:new Date})}}])&&t(e.prototype,i),n&&t(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,i,n}();e("I",i);var n="/api",r=(e("g",(function(e){return e&&"http"!==e.slice(0,4)?"/"===e.slice(0,1)?n+e:n+"/"+e:e})),[".mp4",".mov",".webm",".ogg"]),o=["video/mp4","video/webm","video/ogg"],a=["image/jpeg","image/png","image/webp","image/svg+xml"];e("b",(function(e){var t=(null==e?void 0:e.toLowerCase())||"";return""!==t&&r.some((function(e){return t.endsWith(e)}))})),e("i",(function(e){var t=(null==e?void 0:e.toLowerCase())||"";return""!==t&&o.includes(t)})),e("a",(function(e){var t=(null==e?void 0:e.toLowerCase())||"";return""!==t&&a.includes(t)}))}}}))}();
