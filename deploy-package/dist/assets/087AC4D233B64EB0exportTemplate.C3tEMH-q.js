/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{a as e,r as t,g as n,ae as a,c as o,o as r,f as i,b as l,w as s,h as u,l as c,t as g,v as d,aa as m,a7 as p,i as h,F as x,D as f,ab as b,E as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{g as v,f as y,c as w,u as _,d as C,b as T}from"./087AC4D233B64EB0exportTemplate.CLX4L_9E.js";import{_ as R}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{g as $,c as A,b as I,a as z}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";import{V as q}from"./087AC4D233B64EB0theme-github_dark.JrEfhomR.js";var E,S,j={exports:{}};E||(E=1,S=j,ace.define("ace/mode/folding/mixed",["require","exports","module","ace/lib/oop","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("./fold_mode").FoldMode,r=t.FoldMode=function(e,t){this.defaultMode=e,this.subModes=t};a.inherits(r,o),function(){this.$getMode=function(e){for(var t in"string"!=typeof e&&(e=e[0]),this.subModes)if(0===e.indexOf(t))return this.subModes[t];return null},this.$tryMode=function(e,t,n,a){var o=this.$getMode(e);return o?o.getFoldWidget(t,n,a):""},this.getFoldWidget=function(e,t,n){return this.$tryMode(e.getState(n-1),e,t,n)||this.$tryMode(e.getState(n),e,t,n)||this.defaultMode.getFoldWidget(e,t,n)},this.getFoldWidgetRange=function(e,t,n){var a=this.$getMode(e.getState(n-1));return a&&a.getFoldWidget(e,t,n)||(a=this.$getMode(e.getState(n))),a&&a.getFoldWidget(e,t,n)||(a=this.defaultMode),a.getFoldWidgetRange(e,t,n)}}.call(r.prototype)})),ace.define("ace/mode/folding/xml",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,i=t.FoldMode=function(e,t){r.call(this),this.voidElements=e||{},this.optionalEndTags=a.mixin({},this.voidElements),t&&a.mixin(this.optionalEndTags,t)};a.inherits(i,r);var l=function(){this.tagName="",this.closing=!1,this.selfClosing=!1,this.start={row:0,column:0},this.end={row:0,column:0}};function s(e,t){return e&&e.type&&e.type.lastIndexOf(t+".xml")>-1}(function(){this.getFoldWidget=function(e,t,n){var a=this._getFirstTagInLine(e,n);return a?a.closing||!a.tagName&&a.selfClosing?"markbeginend"===t?"end":"":!a.tagName||a.selfClosing||this.voidElements.hasOwnProperty(a.tagName.toLowerCase())||this._findEndTagInLine(e,n,a.tagName,a.end.column)?"":"start":this.getCommentFoldWidget(e,n)},this.getCommentFoldWidget=function(e,t){return/comment/.test(e.getState(t))&&/<!-/.test(e.getLine(t))?"start":""},this._getFirstTagInLine=function(e,t){for(var n=e.getTokens(t),a=new l,o=0;o<n.length;o++){var r=n[o];if(s(r,"tag-open")){if(a.end.column=a.start.column+r.value.length,a.closing=s(r,"end-tag-open"),!(r=n[++o]))return null;if(a.tagName=r.value,""===r.value){if(!(r=n[++o]))return null;a.tagName=r.value}for(a.end.column+=r.value.length,o++;o<n.length;o++)if(r=n[o],a.end.column+=r.value.length,s(r,"tag-close")){a.selfClosing="/>"==r.value;break}return a}if(s(r,"tag-close"))return a.selfClosing="/>"==r.value,a;a.start.column+=r.value.length}return null},this._findEndTagInLine=function(e,t,n,a){for(var o=e.getTokens(t),r=0,i=0;i<o.length;i++){var l=o[i];if(!((r+=l.value.length)<a-1)&&s(l,"end-tag-open")&&(s(l=o[i+1],"tag-name")&&""===l.value&&(l=o[i+2]),l&&l.value==n))return!0}return!1},this.getFoldWidgetRange=function(e,t,n){if(!this._getFirstTagInLine(e,n))return this.getCommentFoldWidget(e,n)&&e.getCommentFoldRange(n,e.getLine(n).length);var a=e.getMatchingTags({row:n,column:0});return a?new o(a.openTag.end.row,a.openTag.end.column,a.closeTag.start.row,a.closeTag.start.column):void 0}}).call(i.prototype)})),ace.define("ace/mode/folding/cstyle",["require","exports","module","ace/lib/oop","ace/range","ace/mode/folding/fold_mode"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../range").Range,r=e("./fold_mode").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end)))};a.inherits(i,r),function(){this.foldingStartMarker=/([\{\[\(])[^\}\]\)]*$|^\s*(\/\*)/,this.foldingStopMarker=/^[^\[\{\(]*([\}\]\)])|^[\s\*]*(\*\/)/,this.singleLineBlockCommentRe=/^\s*(\/\*).*\*\/\s*$/,this.tripleStarBlockCommentRe=/^\s*(\/\*\*\*).*\*\/\s*$/,this.startRegionRe=/^\s*(\/\*|\/\/)#?region\b/,this._getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var a=e.getLine(n);if(this.singleLineBlockCommentRe.test(a)&&!this.startRegionRe.test(a)&&!this.tripleStarBlockCommentRe.test(a))return"";var o=this._getFoldWidgetBase(e,t,n);return!o&&this.startRegionRe.test(a)?"start":o},this.getFoldWidgetRange=function(e,t,n,a){var o,r=e.getLine(n);if(this.startRegionRe.test(r))return this.getCommentRegionBlock(e,r,n);if(o=r.match(this.foldingStartMarker)){var i=o.index;if(o[1])return this.openingBracketBlock(e,o[1],n,i);var l=e.getCommentFoldRange(n,i+o[0].length,1);return l&&!l.isMultiLine()&&(a?l=this.getSectionRange(e,n):"all"!=t&&(l=null)),l}return"markbegin"!==t&&(o=r.match(this.foldingStopMarker))?(i=o.index+o[0].length,o[1]?this.closingBracketBlock(e,o[1],n,i):e.getCommentFoldRange(n,i,-1)):void 0},this.getSectionRange=function(e,t){for(var n=e.getLine(t),a=n.search(/\S/),r=t,i=n.length,l=t+=1,s=e.getLength();++t<s;){var u=(n=e.getLine(t)).search(/\S/);if(-1!==u){if(a>u)break;var c=this.getFoldWidgetRange(e,"all",t);if(c){if(c.start.row<=r)break;if(c.isMultiLine())t=c.end.row;else if(a==u)break}l=t}}return new o(r,i,l,e.getLine(l).length)},this.getCommentRegionBlock=function(e,t,n){for(var a=t.search(/\s*$/),r=e.getLength(),i=n,l=/^\s*(?:\/\*|\/\/|--)#?(end)?region\b/,s=1;++n<r;){t=e.getLine(n);var u=l.exec(t);if(u&&(u[1]?s--:s++,!s))break}if(n>i)return new o(i,a,n,t.length)}}.call(i.prototype)})),ace.define("ace/mode/folding/html",["require","exports","module","ace/lib/oop","ace/mode/folding/mixed","ace/mode/folding/xml","ace/mode/folding/cstyle"],(function(e,t,n){var a=e("../../lib/oop"),o=e("./mixed").FoldMode,r=e("./xml").FoldMode,i=e("./cstyle").FoldMode,l=t.FoldMode=function(e,t){o.call(this,new r(e,t),{"js-":new i,"css-":new i})};a.inherits(l,o)})),ace.define("ace/mode/behaviour/xml",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/token_iterator"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../behaviour").Behaviour,r=e("../../token_iterator").TokenIterator;function i(e,t){return e&&e.type.lastIndexOf(t+".xml")>-1}var l=function(){this.add("string_dquotes","insertion",(function(e,t,n,a,o){if('"'==o||"'"==o){var l=o,s=a.doc.getTextRange(n.getSelectionRange());if(""!==s&&"'"!==s&&'"'!=s&&n.getWrapBehavioursEnabled())return{text:l+s+l,selection:!1};var u=n.getCursorPosition(),c=a.doc.getLine(u.row).substring(u.column,u.column+1),g=new r(a,u.row,u.column),d=g.getCurrentToken();if(c==l&&(i(d,"attribute-value")||i(d,"string")))return{text:"",selection:[1,1]};if(d||(d=g.stepBackward()),!d)return;for(;i(d,"tag-whitespace")||i(d,"whitespace");)d=g.stepBackward();var m=!c||c.match(/\s/);if(i(d,"attribute-equals")&&(m||">"==c)||i(d,"decl-attribute-equals")&&(m||"?"==c))return{text:l+l,selection:[1,1]}}})),this.add("string_dquotes","deletion",(function(e,t,n,a,o){var r=a.doc.getTextRange(o);if(!o.isMultiLine()&&('"'==r||"'"==r)&&a.doc.getLine(o.start.row).substring(o.start.column+1,o.start.column+2)==r)return o.end.column++,o})),this.add("autoclosing","insertion",(function(e,t,n,a,o){if(">"==o){var l=n.getSelectionRange().start,s=new r(a,l.row,l.column),u=s.getCurrentToken()||s.stepBackward();if(!u||!(i(u,"tag-name")||i(u,"tag-whitespace")||i(u,"attribute-name")||i(u,"attribute-equals")||i(u,"attribute-value")))return;if(i(u,"reference.attribute-value"))return;if(i(u,"attribute-value")){var c=s.getCurrentTokenColumn()+u.value.length;if(l.column<c)return;if(l.column==c){var g=s.stepForward();if(g&&i(g,"attribute-value"))return;s.stepBackward()}}if(/^\s*>/.test(a.getLine(l.row).slice(l.column)))return;for(;!i(u,"tag-name");)if("<"==(u=s.stepBackward()).value){u=s.stepForward();break}var d=s.getCurrentTokenRow(),m=s.getCurrentTokenColumn();if(i(s.stepBackward(),"end-tag-open"))return;var p=u.value;if(d==l.row&&(p=p.substring(0,l.column-m)),this.voidElements&&this.voidElements.hasOwnProperty(p.toLowerCase()))return;return{text:"></"+p+">",selection:[1,1]}}})),this.add("autoindent","insertion",(function(e,t,n,a,o){if("\n"==o){var l=n.getCursorPosition(),s=a.getLine(l.row),u=new r(a,l.row,l.column),c=u.getCurrentToken();if(i(c,"")&&-1!==c.type.indexOf("tag-close")){if("/>"==c.value)return;for(;c&&-1===c.type.indexOf("tag-name");)c=u.stepBackward();if(!c)return;var g=c.value,d=u.getCurrentTokenRow();if(!(c=u.stepBackward())||-1!==c.type.indexOf("end-tag"))return;if(this.voidElements&&!this.voidElements[g]||!this.voidElements){var m=a.getTokenAt(l.row,l.column+1),p=(s=a.getLine(d),this.$getIndent(s)),h=p+a.getTabString();return m&&"</"===m.value?{text:"\n"+h+"\n"+p,selection:[1,h.length,1,h.length]}:{text:"\n"+h}}}}}))};a.inherits(l,o),t.XmlBehaviour=l})),ace.define("ace/mode/html_completions",["require","exports","module","ace/token_iterator"],(function(e,t,n){var a=e("../token_iterator").TokenIterator,o=["accesskey","class","contenteditable","contextmenu","dir","draggable","dropzone","hidden","id","inert","itemid","itemprop","itemref","itemscope","itemtype","lang","spellcheck","style","tabindex","title","translate"].concat(["onabort","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncontextmenu","oncuechange","ondblclick","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onload","onloadeddata","onloadedmetadata","onloadstart","onmousedown","onmousemove","onmouseout","onmouseover","onmouseup","onmousewheel","onpause","onplay","onplaying","onprogress","onratechange","onreset","onscroll","onseeked","onseeking","onselect","onshow","onstalled","onsubmit","onsuspend","ontimeupdate","onvolumechange","onwaiting"]),r={a:{href:1,target:{_blank:1,top:1},ping:1,rel:{nofollow:1,alternate:1,author:1,bookmark:1,help:1,license:1,next:1,noreferrer:1,prefetch:1,prev:1,search:1,tag:1},media:1,hreflang:1,type:1},abbr:{},address:{},area:{shape:1,coords:1,href:1,hreflang:1,alt:1,target:1,media:1,rel:1,ping:1,type:1},article:{pubdate:1},aside:{},audio:{src:1,autobuffer:1,autoplay:{autoplay:1},loop:{loop:1},controls:{controls:1},muted:{muted:1},preload:{auto:1,metadata:1,none:1}},b:{},base:{href:1,target:1},bdi:{},bdo:{},blockquote:{cite:1},body:{onafterprint:1,onbeforeprint:1,onbeforeunload:1,onhashchange:1,onmessage:1,onoffline:1,onpopstate:1,onredo:1,onresize:1,onstorage:1,onundo:1,onunload:1},br:{},button:{autofocus:1,disabled:{disabled:1},form:1,formaction:1,formenctype:1,formmethod:1,formnovalidate:1,formtarget:1,name:1,value:1,type:{button:1,submit:1}},canvas:{width:1,height:1},caption:{},cite:{},code:{},col:{span:1},colgroup:{span:1},command:{type:1,label:1,icon:1,disabled:1,checked:1,radiogroup:1,command:1},data:{},datalist:{},dd:{},del:{cite:1,datetime:1},details:{open:1},dfn:{},dialog:{open:1},div:{},dl:{},dt:{},em:{},embed:{src:1,height:1,width:1,type:1},fieldset:{disabled:1,form:1,name:1},figcaption:{},figure:{},footer:{},form:{"accept-charset":1,action:1,autocomplete:1,enctype:{"multipart/form-data":1,"application/x-www-form-urlencoded":1},method:{get:1,post:1},name:1,novalidate:1,target:{_blank:1,top:1}},h1:{},h2:{},h3:{},h4:{},h5:{},h6:{},head:{},header:{},hr:{},html:{manifest:1},i:{},iframe:{name:1,src:1,height:1,width:1,sandbox:{"allow-same-origin":1,"allow-top-navigation":1,"allow-forms":1,"allow-scripts":1},seamless:{seamless:1}},img:{alt:1,src:1,height:1,width:1,usemap:1,ismap:1},input:{type:{text:1,password:1,hidden:1,checkbox:1,submit:1,radio:1,file:1,button:1,reset:1,image:31,color:1,date:1,datetime:1,"datetime-local":1,email:1,month:1,number:1,range:1,search:1,tel:1,time:1,url:1,week:1},accept:1,alt:1,autocomplete:{on:1,off:1},autofocus:{autofocus:1},checked:{checked:1},disabled:{disabled:1},form:1,formaction:1,formenctype:{"application/x-www-form-urlencoded":1,"multipart/form-data":1,"text/plain":1},formmethod:{get:1,post:1},formnovalidate:{formnovalidate:1},formtarget:{_blank:1,_self:1,_parent:1,_top:1},height:1,list:1,max:1,maxlength:1,min:1,multiple:{multiple:1},name:1,pattern:1,placeholder:1,readonly:{readonly:1},required:{required:1},size:1,src:1,step:1,width:1,files:1,value:1},ins:{cite:1,datetime:1},kbd:{},keygen:{autofocus:1,challenge:{challenge:1},disabled:{disabled:1},form:1,keytype:{rsa:1,dsa:1,ec:1},name:1},label:{form:1,for:1},legend:{},li:{value:1},link:{href:1,hreflang:1,rel:{stylesheet:1,icon:1},media:{all:1,screen:1,print:1},type:{"text/css":1,"image/png":1,"image/jpeg":1,"image/gif":1},sizes:1},main:{},map:{name:1},mark:{},math:{},menu:{type:1,label:1},meta:{"http-equiv":{"content-type":1},name:{description:1,keywords:1},content:{"text/html; charset=UTF-8":1},charset:1},meter:{value:1,min:1,max:1,low:1,high:1,optimum:1},nav:{},noscript:{href:1},object:{param:1,data:1,type:1,height:1,width:1,usemap:1,name:1,form:1,classid:1},ol:{start:1,reversed:1},optgroup:{disabled:1,label:1},option:{disabled:1,selected:1,label:1,value:1},output:{for:1,form:1,name:1},p:{},param:{name:1,value:1},pre:{},progress:{value:1,max:1},q:{cite:1},rp:{},rt:{},ruby:{},s:{},samp:{},script:{charset:1,type:{"text/javascript":1},src:1,defer:1,async:1},select:{autofocus:1,disabled:1,form:1,multiple:{multiple:1},name:1,size:1,readonly:{readonly:1}},small:{},source:{src:1,type:1,media:1},span:{},strong:{},style:{type:1,media:{all:1,screen:1,print:1},scoped:1},sub:{},sup:{},svg:{},table:{summary:1},tbody:{},td:{headers:1,rowspan:1,colspan:1},textarea:{autofocus:{autofocus:1},disabled:{disabled:1},form:1,maxlength:1,name:1,placeholder:1,readonly:{readonly:1},required:{required:1},rows:1,cols:1,wrap:{on:1,off:1,hard:1,soft:1}},tfoot:{},th:{headers:1,rowspan:1,colspan:1,scope:1},thead:{},time:{datetime:1},title:{},tr:{},track:{kind:1,src:1,srclang:1,label:1,default:1},section:{},summary:{},u:{},ul:{},var:{},video:{src:1,autobuffer:1,autoplay:{autoplay:1},loop:{loop:1},controls:{controls:1},width:1,height:1,poster:1,muted:{muted:1},preload:{auto:1,metadata:1,none:1}},wbr:{}},i=Object.keys(r);function l(e,t){return e.type.lastIndexOf(t+".xml")>-1}function s(e,t){for(var n=new a(e,t.row,t.column),o=n.getCurrentToken();o&&!l(o,"tag-name");)o=n.stepBackward();if(o)return o.value}var u=function(){};(function(){this.getCompletions=function(e,t,n,a){var o=t.getTokenAt(n.row,n.column);if(!o)return[];if(l(o,"tag-name")||l(o,"tag-open")||l(o,"end-tag-open"))return this.getTagCompletions(e,t,n,a);if(l(o,"tag-whitespace")||l(o,"attribute-name"))return this.getAttributeCompletions(e,t,n,a);if(l(o,"attribute-value"))return this.getAttributeValueCompletions(e,t,n,a);var r=t.getLine(n.row).substr(0,n.column);return/&[a-z]*$/i.test(r)?this.getHTMLEntityCompletions(e,t,n,a):[]},this.getTagCompletions=function(e,t,n,a){return i.map((function(e){return{value:e,meta:"tag",score:1e6}}))},this.getAttributeCompletions=function(e,t,n,a){var i=s(t,n);if(!i)return[];var l=o;return i in r&&(l=l.concat(Object.keys(r[i]))),l.map((function(e){return{caption:e,snippet:e+'="$0"',meta:"attribute",score:1e6}}))},this.getAttributeValueCompletions=function(e,t,n,o){var i=s(t,n),u=function(e,t){for(var n=new a(e,t.row,t.column),o=n.getCurrentToken();o&&!l(o,"attribute-name");)o=n.stepBackward();if(o)return o.value}(t,n);if(!i)return[];var c=[];return i in r&&u in r[i]&&"object"==typeof r[i][u]&&(c=Object.keys(r[i][u])),c.map((function(e){return{caption:e,snippet:e,meta:"attribute value",score:1e6}}))},this.getHTMLEntityCompletions=function(e,t,n,a){return["Aacute;","aacute;","Acirc;","acirc;","acute;","AElig;","aelig;","Agrave;","agrave;","alefsym;","Alpha;","alpha;","amp;","and;","ang;","Aring;","aring;","asymp;","Atilde;","atilde;","Auml;","auml;","bdquo;","Beta;","beta;","brvbar;","bull;","cap;","Ccedil;","ccedil;","cedil;","cent;","Chi;","chi;","circ;","clubs;","cong;","copy;","crarr;","cup;","curren;","Dagger;","dagger;","dArr;","darr;","deg;","Delta;","delta;","diams;","divide;","Eacute;","eacute;","Ecirc;","ecirc;","Egrave;","egrave;","empty;","emsp;","ensp;","Epsilon;","epsilon;","equiv;","Eta;","eta;","ETH;","eth;","Euml;","euml;","euro;","exist;","fnof;","forall;","frac12;","frac14;","frac34;","frasl;","Gamma;","gamma;","ge;","gt;","hArr;","harr;","hearts;","hellip;","Iacute;","iacute;","Icirc;","icirc;","iexcl;","Igrave;","igrave;","image;","infin;","int;","Iota;","iota;","iquest;","isin;","Iuml;","iuml;","Kappa;","kappa;","Lambda;","lambda;","lang;","laquo;","lArr;","larr;","lceil;","ldquo;","le;","lfloor;","lowast;","loz;","lrm;","lsaquo;","lsquo;","lt;","macr;","mdash;","micro;","middot;","minus;","Mu;","mu;","nabla;","nbsp;","ndash;","ne;","ni;","not;","notin;","nsub;","Ntilde;","ntilde;","Nu;","nu;","Oacute;","oacute;","Ocirc;","ocirc;","OElig;","oelig;","Ograve;","ograve;","oline;","Omega;","omega;","Omicron;","omicron;","oplus;","or;","ordf;","ordm;","Oslash;","oslash;","Otilde;","otilde;","otimes;","Ouml;","ouml;","para;","part;","permil;","perp;","Phi;","phi;","Pi;","pi;","piv;","plusmn;","pound;","Prime;","prime;","prod;","prop;","Psi;","psi;","quot;","radic;","rang;","raquo;","rArr;","rarr;","rceil;","rdquo;","real;","reg;","rfloor;","Rho;","rho;","rlm;","rsaquo;","rsquo;","sbquo;","Scaron;","scaron;","sdot;","sect;","shy;","Sigma;","sigma;","sigmaf;","sim;","spades;","sub;","sube;","sum;","sup;","sup1;","sup2;","sup3;","supe;","szlig;","Tau;","tau;","there4;","Theta;","theta;","thetasym;","thinsp;","THORN;","thorn;","tilde;","times;","trade;","Uacute;","uacute;","uArr;","uarr;","Ucirc;","ucirc;","Ugrave;","ugrave;","uml;","upsih;","Upsilon;","upsilon;","Uuml;","uuml;","weierp;","Xi;","xi;","Yacute;","yacute;","yen;","Yuml;","yuml;","Zeta;","zeta;","zwj;","zwnj;"].map((function(e){return{caption:e,snippet:e,meta:"html entity",score:1e6}}))}}).call(u.prototype),t.HtmlCompletions=u})),ace.define("ace/mode/jsdoc_comment_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(){this.$rules={start:[{token:["comment.doc.tag","comment.doc.text","lparen.doc"],regex:"(@(?:param|member|typedef|property|namespace|var|const|callback))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:["rparen.doc","text.doc","variable.parameter.doc","lparen.doc","variable.parameter.doc","rparen.doc"],regex:/(})(\s*)(?:([\w=:\/\.]+)|(?:(\[)([\w=:\/\.\-\'\" ]+)(\])))/,next:"pop"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","lparen.doc"],regex:"(@(?:returns?|yields|type|this|suppress|public|protected|private|package|modifies|implements|external|exception|throws|enum|define|extends))(\\s*)({)",push:[{token:"lparen.doc",regex:"{",push:[{include:"doc-syntax"},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"}]},{token:"rparen.doc",regex:"}|(?=$)",next:"pop"},{include:"doc-syntax"},{defaultToken:"text.doc"}]},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:'(@(?:alias|memberof|instance|module|name|lends|namespace|external|this|template|requires|param|implements|function|extends|typedef|mixes|constructor|var|memberof\\!|event|listens|exports|class|constructs|interface|emits|fires|throws|const|callback|borrows|augments))(\\s+)(\\w[\\w#.:/~"\\-]*)?'},{token:["comment.doc.tag","text.doc","variable.parameter.doc"],regex:"(@method)(\\s+)(\\w[\\w.\\(\\)]*)"},{token:"comment.doc.tag",regex:"@access\\s+(?:private|public|protected)"},{token:"comment.doc.tag",regex:"@kind\\s+(?:class|constant|event|external|file|function|member|mixin|module|namespace|typedef)"},{token:"comment.doc.tag",regex:"@\\w+(?=\\s|$)"},r.getTagRule(),{defaultToken:"comment.doc.body",caseInsensitive:!0}],"doc-syntax":[{token:"operator.doc",regex:/[|:]/},{token:"paren.doc",regex:/[\[\]]/}]},this.normalizeRules()};a.inherits(r,o),r.getTagRule=function(e){return{token:"comment.doc.tag.storage.type",regex:"\\b(?:TODO|FIXME|XXX|HACK)\\b"}},r.getStartRule=function(e){return{token:"comment.doc",regex:/\/\*\*(?!\/)/,next:e}},r.getEndRule=function(e){return{token:"comment.doc",regex:"\\*\\/",next:e}},t.JsDocCommentHighlightRules=r})),ace.define("ace/mode/javascript_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/jsdoc_comment_highlight_rules","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./jsdoc_comment_highlight_rules").JsDocCommentHighlightRules,r=e("./text_highlight_rules").TextHighlightRules,i="[a-zA-Z\\$_¡-￿][a-zA-Z\\d\\$_¡-￿]*",l=function(e){var t={"variable.language":"Array|Boolean|Date|Function|Iterator|Number|Object|RegExp|String|Proxy|Symbol|Namespace|QName|XML|XMLList|ArrayBuffer|Float32Array|Float64Array|Int16Array|Int32Array|Int8Array|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray|Error|EvalError|InternalError|RangeError|ReferenceError|StopIteration|SyntaxError|TypeError|URIError|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|eval|isFinite|isNaN|parseFloat|parseInt|JSON|Math|this|arguments|prototype|window|document",keyword:"const|yield|import|get|set|async|await|break|case|catch|continue|default|delete|do|else|finally|for|if|in|of|instanceof|new|return|switch|throw|try|typeof|let|var|while|with|debugger|__parent__|__count__|escape|unescape|with|__proto__|class|enum|extends|super|export|implements|private|public|interface|package|protected|static|constructor","storage.type":"const|let|var|function","constant.language":"null|Infinity|NaN|undefined","support.function":"alert","constant.language.boolean":"true|false"},n=this.createKeywordMapper(t,"identifier"),a="\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|u{[0-9a-fA-F]{1,6}}|[0-2][0-7]{0,2}|3[0-7][0-7]?|[4-7][0-7]?|.)",r="(function)(\\s*)(\\*?)",l={token:["identifier","text","paren.lparen"],regex:"(\\b(?!"+Object.values(t).join("|")+"\\b)"+i+")(\\s*)(\\()"};this.$rules={no_regex:[o.getStartRule("doc-start"),u("no_regex"),l,{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:["entity.name.function","text","keyword.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(=)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))("+i+")(\\s*)(\\()",next:"function_arguments"},{token:["entity.name.function","text","punctuation.operator","text","storage.type","text","storage.type","text","paren.lparen"],regex:"("+i+")(\\s*)(:)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:["text","text","storage.type","text","storage.type","text","paren.lparen"],regex:"(:)(\\s*)"+r+"(\\s*)(\\()",next:"function_arguments"},{token:"keyword",regex:"from(?=\\s*('|\"))"},{token:"keyword",regex:"(?:case|do|else|finally|in|instanceof|return|throw|try|typeof|yield|void)\\b",next:"start"},{token:"support.constant",regex:/that\b/},{token:["storage.type","punctuation.operator","support.function.firebug"],regex:/(console)(\.)(warn|info|log|error|debug|time|trace|timeEnd|assert)\b/},{token:n,regex:i},{token:"punctuation.operator",regex:/[.](?![.])/,next:"property"},{token:"storage.type",regex:/=>/,next:"start"},{token:"keyword.operator",regex:/--|\+\+|\.{3}|===|==|=|!=|!==|<+=?|>+=?|!|&&|\|\||\?:|[!$%&*+\-~\/^]=?/,next:"start"},{token:"punctuation.operator",regex:/[?:,;.]/,next:"start"},{token:"paren.lparen",regex:/[\[({]/,next:"start"},{token:"paren.rparen",regex:/[\])}]/},{token:"comment",regex:/^#!.*$/}],property:[{token:"text",regex:"\\s+"},{token:"keyword.operator",regex:/=/},{token:["storage.type","text","storage.type","text","paren.lparen"],regex:r+"(\\s*)(\\()",next:"function_arguments"},{token:["storage.type","text","storage.type","text","text","entity.name.function","text","paren.lparen"],regex:"(function)(?:(?:(\\s*)(\\*)(\\s*))|(\\s+))(\\w+)(\\s*)(\\()",next:"function_arguments"},{token:"punctuation.operator",regex:/[.](?![.])/},{token:"support.function",regex:"prototype"},{token:"support.function",regex:/(s(?:h(?:ift|ow(?:Mod(?:elessDialog|alDialog)|Help))|croll(?:X|By(?:Pages|Lines)?|Y|To)?|t(?:op|rike)|i(?:n|zeToContent|debar|gnText)|ort|u(?:p|b(?:str(?:ing)?)?)|pli(?:ce|t)|e(?:nd|t(?:Re(?:sizable|questHeader)|M(?:i(?:nutes|lliseconds)|onth)|Seconds|Ho(?:tKeys|urs)|Year|Cursor|Time(?:out)?|Interval|ZOptions|Date|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Date|FullYear)|FullYear|Active)|arch)|qrt|lice|avePreferences|mall)|h(?:ome|andleEvent)|navigate|c(?:har(?:CodeAt|At)|o(?:s|n(?:cat|textual|firm)|mpile)|eil|lear(?:Timeout|Interval)?|a(?:ptureEvents|ll)|reate(?:StyleSheet|Popup|EventObject))|t(?:o(?:GMTString|S(?:tring|ource)|U(?:TCString|pperCase)|Lo(?:caleString|werCase))|est|a(?:n|int(?:Enabled)?))|i(?:s(?:NaN|Finite)|ndexOf|talics)|d(?:isableExternalCapture|ump|etachEvent)|u(?:n(?:shift|taint|escape|watch)|pdateCommands)|j(?:oin|avaEnabled)|p(?:o(?:p|w)|ush|lugins.refresh|a(?:ddings|rse(?:Int|Float)?)|r(?:int|ompt|eference))|e(?:scape|nableExternalCapture|val|lementFromPoint|x(?:p|ec(?:Script|Command)?))|valueOf|UTC|queryCommand(?:State|Indeterm|Enabled|Value)|f(?:i(?:nd|lter|le(?:ModifiedDate|Size|CreatedDate|UpdatedDate)|xed)|o(?:nt(?:size|color)|rward|rEach)|loor|romCharCode)|watch|l(?:ink|o(?:ad|g)|astIndexOf)|a(?:sin|nchor|cos|t(?:tachEvent|ob|an(?:2)?)|pply|lert|b(?:s|ort))|r(?:ou(?:nd|teEvents)|e(?:size(?:By|To)|calc|turnValue|place|verse|l(?:oad|ease(?:Capture|Events)))|andom)|g(?:o|et(?:ResponseHeader|M(?:i(?:nutes|lliseconds)|onth)|Se(?:conds|lection)|Hours|Year|Time(?:zoneOffset)?|Da(?:y|te)|UTC(?:M(?:i(?:nutes|lliseconds)|onth)|Seconds|Hours|Da(?:y|te)|FullYear)|FullYear|A(?:ttention|llResponseHeaders)))|m(?:in|ove(?:B(?:y|elow)|To(?:Absolute)?|Above)|ergeAttributes|a(?:tch|rgins|x))|b(?:toa|ig|o(?:ld|rderWidths)|link|ack))\b(?=\()/},{token:"support.function.dom",regex:/(s(?:ub(?:stringData|mit)|plitText|e(?:t(?:NamedItem|Attribute(?:Node)?)|lect))|has(?:ChildNodes|Feature)|namedItem|c(?:l(?:ick|o(?:se|neNode))|reate(?:C(?:omment|DATASection|aption)|T(?:Head|extNode|Foot)|DocumentFragment|ProcessingInstruction|E(?:ntityReference|lement)|Attribute))|tabIndex|i(?:nsert(?:Row|Before|Cell|Data)|tem)|open|delete(?:Row|C(?:ell|aption)|T(?:Head|Foot)|Data)|focus|write(?:ln)?|a(?:dd|ppend(?:Child|Data))|re(?:set|place(?:Child|Data)|move(?:NamedItem|Child|Attribute(?:Node)?)?)|get(?:NamedItem|Element(?:sBy(?:Name|TagName|ClassName)|ById)|Attribute(?:Node)?)|blur)\b(?=\()/},{token:"support.constant",regex:/(s(?:ystemLanguage|cr(?:ipts|ollbars|een(?:X|Y|Top|Left))|t(?:yle(?:Sheets)?|atus(?:Text|bar)?)|ibling(?:Below|Above)|ource|uffixes|e(?:curity(?:Policy)?|l(?:ection|f)))|h(?:istory|ost(?:name)?|as(?:h|Focus))|y|X(?:MLDocument|SLDocument)|n(?:ext|ame(?:space(?:s|URI)|Prop))|M(?:IN_VALUE|AX_VALUE)|c(?:haracterSet|o(?:n(?:structor|trollers)|okieEnabled|lorDepth|mp(?:onents|lete))|urrent|puClass|l(?:i(?:p(?:boardData)?|entInformation)|osed|asses)|alle(?:e|r)|rypto)|t(?:o(?:olbar|p)|ext(?:Transform|Indent|Decoration|Align)|ags)|SQRT(?:1_2|2)|i(?:n(?:ner(?:Height|Width)|put)|ds|gnoreCase)|zIndex|o(?:scpu|n(?:readystatechange|Line)|uter(?:Height|Width)|p(?:sProfile|ener)|ffscreenBuffering)|NEGATIVE_INFINITY|d(?:i(?:splay|alog(?:Height|Top|Width|Left|Arguments)|rectories)|e(?:scription|fault(?:Status|Ch(?:ecked|arset)|View)))|u(?:ser(?:Profile|Language|Agent)|n(?:iqueID|defined)|pdateInterval)|_content|p(?:ixelDepth|ort|ersonalbar|kcs11|l(?:ugins|atform)|a(?:thname|dding(?:Right|Bottom|Top|Left)|rent(?:Window|Layer)?|ge(?:X(?:Offset)?|Y(?:Offset)?))|r(?:o(?:to(?:col|type)|duct(?:Sub)?|mpter)|e(?:vious|fix)))|e(?:n(?:coding|abledPlugin)|x(?:ternal|pando)|mbeds)|v(?:isibility|endor(?:Sub)?|Linkcolor)|URLUnencoded|P(?:I|OSITIVE_INFINITY)|f(?:ilename|o(?:nt(?:Size|Family|Weight)|rmName)|rame(?:s|Element)|gColor)|E|whiteSpace|l(?:i(?:stStyleType|n(?:eHeight|kColor))|o(?:ca(?:tion(?:bar)?|lName)|wsrc)|e(?:ngth|ft(?:Context)?)|a(?:st(?:M(?:odified|atch)|Index|Paren)|yer(?:s|X)|nguage))|a(?:pp(?:MinorVersion|Name|Co(?:deName|re)|Version)|vail(?:Height|Top|Width|Left)|ll|r(?:ity|guments)|Linkcolor|bove)|r(?:ight(?:Context)?|e(?:sponse(?:XML|Text)|adyState))|global|x|m(?:imeTypes|ultiline|enubar|argin(?:Right|Bottom|Top|Left))|L(?:N(?:10|2)|OG(?:10E|2E))|b(?:o(?:ttom|rder(?:Width|RightWidth|BottomWidth|Style|Color|TopWidth|LeftWidth))|ufferDepth|elow|ackground(?:Color|Image)))\b/},{token:"identifier",regex:i},{regex:"",token:"empty",next:"no_regex"}],start:[o.getStartRule("doc-start"),u("start"),{token:"string.regexp",regex:"\\/",next:"regex"},{token:"text",regex:"\\s+|^$",next:"start"},{token:"empty",regex:"",next:"no_regex"}],regex:[{token:"regexp.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"string.regexp",regex:"/[sxngimy]*",next:"no_regex"},{token:"invalid",regex:/\{\d+\b,?\d*\}[+*]|[+*$^?][+*]|[$^][?]|\?{3,}/},{token:"constant.language.escape",regex:/\(\?[:=!]|\)|\{\d+\b,?\d*\}|[+*]\?|[()$^+*?.]/},{token:"constant.language.delimiter",regex:/\|/},{token:"constant.language.escape",regex:/\[\^?/,next:"regex_character_class"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp"}],regex_character_class:[{token:"regexp.charclass.keyword.operator",regex:"\\\\(?:u[\\da-fA-F]{4}|x[\\da-fA-F]{2}|.)"},{token:"constant.language.escape",regex:"]",next:"regex"},{token:"constant.language.escape",regex:"-"},{token:"empty",regex:"$",next:"no_regex"},{defaultToken:"string.regexp.charachterclass"}],default_parameter:[{token:"string",regex:"'(?=.)",push:[{token:"string",regex:"'|$",next:"pop"},{include:"qstring"}]},{token:"string",regex:'"(?=.)',push:[{token:"string",regex:'"|$',next:"pop"},{include:"qqstring"}]},{token:"constant.language",regex:"null|Infinity|NaN|undefined"},{token:"constant.numeric",regex:/0(?:[xX][0-9a-fA-F]+|[oO][0-7]+|[bB][01]+)\b/},{token:"constant.numeric",regex:/(?:\d\d*(?:\.\d*)?|\.\d+)(?:[eE][+-]?\d+\b)?/},{token:"punctuation.operator",regex:",",next:"function_arguments"},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],function_arguments:[u("function_arguments"),{token:"variable.parameter",regex:i},{token:"punctuation.operator",regex:","},{token:"text",regex:"\\s+"},{token:"punctuation.operator",regex:"$"},{token:"empty",regex:"",next:"no_regex"}],qqstring:[{token:"constant.language.escape",regex:a},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:'"|$',next:"no_regex"},{defaultToken:"string"}],qstring:[{token:"constant.language.escape",regex:a},{token:"string",regex:"\\\\$",consumeLineEnd:!0},{token:"string",regex:"'|$",next:"no_regex"},{defaultToken:"string"}]},e&&e.noES6||(this.$rules.no_regex.unshift({regex:"[{}]",onMatch:function(e,t,n){if(this.next="{"==e?this.nextState:"","{"==e&&n.length)n.unshift("start",t);else if("}"==e&&n.length&&(n.shift(),this.next=n.shift(),-1!=this.next.indexOf("string")||-1!=this.next.indexOf("jsx")))return"paren.quasi.end";return"{"==e?"paren.lparen":"paren.rparen"},nextState:"start"},{token:"string.quasi.start",regex:/`/,push:[{token:"constant.language.escape",regex:a},{token:"paren.quasi.start",regex:/\${/,push:"start"},{token:"string.quasi.end",regex:/`/,next:"pop"},{defaultToken:"string.quasi"}]},{token:["variable.parameter","text"],regex:"("+i+")(\\s*)(?=\\=>)"},{token:"paren.lparen",regex:"(\\()(?=[^\\(]+\\s*=>)",next:"function_arguments"},{token:"variable.language",regex:"(?:(?:(?:Weak)?(?:Set|Map))|Promise)\\b"}),this.$rules.function_arguments.unshift({token:"keyword.operator",regex:"=",next:"default_parameter"},{token:"keyword.operator",regex:"\\.{3}"}),this.$rules.property.unshift({token:"support.function",regex:"(findIndex|repeat|startsWith|endsWith|includes|isSafeInteger|trunc|cbrt|log2|log10|sign|then|catch|finally|resolve|reject|race|any|all|allSettled|keys|entries|isInteger)\\b(?=\\()"},{token:"constant.language",regex:"(?:MAX_SAFE_INTEGER|MIN_SAFE_INTEGER|EPSILON)\\b"}),e&&0==e.jsx||s.call(this)),this.embedRules(o,"doc-",[o.getEndRule("no_regex")]),this.normalizeRules()};function s(){var e=i.replace("\\d","\\d\\-"),t={onMatch:function(e,t,n){var a="/"==e.charAt(1)?2:1;return 1==a?(t!=this.nextState?n.unshift(this.next,this.nextState,0):n.unshift(this.next),n[2]++):2==a&&t==this.nextState&&(n[1]--,(!n[1]||n[1]<0)&&(n.shift(),n.shift())),[{type:"meta.tag.punctuation."+(1==a?"":"end-")+"tag-open.xml",value:e.slice(0,a)},{type:"meta.tag.tag-name.xml",value:e.substr(a)}]},regex:"</?(?:"+e+"|(?=>))",next:"jsxAttributes",nextState:"jsx"};this.$rules.start.unshift(t);var n={regex:"{",token:"paren.quasi.start",push:"start"};this.$rules.jsx=[n,t,{include:"reference"},{defaultToken:"string.xml"}],this.$rules.jsxAttributes=[{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",onMatch:function(e,t,n){return t==n[0]&&n.shift(),2==e.length&&(n[0]==this.nextState&&n[1]--,(!n[1]||n[1]<0)&&n.splice(0,2)),this.next=n[0]||"start",[{type:this.token,value:e}]},nextState:"jsx"},n,u("jsxAttributes"),{token:"entity.other.attribute-name.xml",regex:e},{token:"keyword.operator.attribute-equals.xml",regex:"="},{token:"text.tag-whitespace.xml",regex:"\\s+"},{token:"string.attribute-value.xml",regex:"'",stateName:"jsx_attr_q",push:[{token:"string.attribute-value.xml",regex:"'",next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},{token:"string.attribute-value.xml",regex:'"',stateName:"jsx_attr_qq",push:[{token:"string.attribute-value.xml",regex:'"',next:"pop"},{include:"reference"},{defaultToken:"string.attribute-value.xml"}]},t],this.$rules.reference=[{token:"constant.language.escape.reference.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}]}function u(e){return[{token:"comment",regex:/\/\*/,next:[o.getTagRule(),{token:"comment",regex:"\\*\\/",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]},{token:"comment",regex:"\\/\\/",next:[o.getTagRule(),{token:"comment",regex:"$|^",next:e||"pop"},{defaultToken:"comment",caseInsensitive:!0}]}]}a.inherits(l,r),t.JavaScriptHighlightRules=l})),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],(function(e,t,n){var a=e("../range").Range,o=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var o=n[1].length,r=e.findMatchingBracket({row:t,column:o});if(!r||r.row==t)return 0;var i=this.$getIndent(e.getLine(r.row));e.replace(new a(t,0,t,o-1),i)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(o.prototype),t.MatchingBraceOutdent=o})),ace.define("ace/mode/behaviour/javascript",["require","exports","module","ace/lib/oop","ace/token_iterator","ace/mode/behaviour/cstyle","ace/mode/behaviour/xml"],(function(e,t,n){var a=e("../../lib/oop"),o=e("../../token_iterator").TokenIterator,r=e("../behaviour/cstyle").CstyleBehaviour,i=e("../behaviour/xml").XmlBehaviour,l=function(){var e=new i({closeCurlyBraces:!0}).getBehaviours();this.addBehaviours(e),this.inherit(r),this.add("autoclosing-fragment","insertion",(function(e,t,n,a,r){if(">"==r){var i=n.getSelectionRange().start,l=new o(a,i.row,i.column),s=l.getCurrentToken()||l.stepBackward();if(!s)return;if("<"==s.value)return{text:"></>",selection:[1,1]}}}))};a.inherits(l,r),t.JavaScriptBehaviour=l})),ace.define("ace/mode/folding/javascript",["require","exports","module","ace/lib/oop","ace/mode/folding/xml","ace/mode/folding/cstyle"],(function(e,t,n){var a=e("../../lib/oop"),o=e("./xml").FoldMode,r=e("./cstyle").FoldMode,i=t.FoldMode=function(e){e&&(this.foldingStartMarker=new RegExp(this.foldingStartMarker.source.replace(/\|[^|]*?$/,"|"+e.start)),this.foldingStopMarker=new RegExp(this.foldingStopMarker.source.replace(/\|[^|]*?$/,"|"+e.end))),this.xmlFoldMode=new o};a.inherits(i,r),function(){this.getFoldWidgetRangeBase=this.getFoldWidgetRange,this.getFoldWidgetBase=this.getFoldWidget,this.getFoldWidget=function(e,t,n){var a=this.getFoldWidgetBase(e,t,n);return a||this.xmlFoldMode.getFoldWidget(e,t,n)},this.getFoldWidgetRange=function(e,t,n,a){var o=this.getFoldWidgetRangeBase(e,t,n,a);return o||this.xmlFoldMode.getFoldWidgetRange(e,t,n)}}.call(i.prototype)})),ace.define("ace/mode/javascript",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/javascript_highlight_rules","ace/mode/matching_brace_outdent","ace/worker/worker_client","ace/mode/behaviour/javascript","ace/mode/folding/javascript"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text").Mode,r=e("./javascript_highlight_rules").JavaScriptHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,l=e("../worker/worker_client").WorkerClient,s=e("./behaviour/javascript").JavaScriptBehaviour,u=e("./folding/javascript").FoldMode,c=function(){this.HighlightRules=r,this.$outdent=new i,this.$behaviour=new s,this.foldingRules=new u};a.inherits(c,o),function(){this.lineCommentStart="//",this.blockComment={start:"/*",end:"*/"},this.$quotes={'"':'"',"'":"'","`":"`"},this.$pairQuotesAfter={"`":/\w/},this.getNextLineIndent=function(e,t,n){var a=this.$getIndent(t),o=this.getTokenizer().getLineTokens(t,e),r=o.tokens,i=o.state;if(r.length&&"comment"==r[r.length-1].type)return a;if("start"==e||"no_regex"==e)t.match(/^.*(?:\bcase\b.*:|[\{\(\[])\s*$/)&&(a+=n);else if("doc-start"==e&&("start"==i||"no_regex"==i))return"";return a},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.createWorker=function(e){var t=new l(["ace"],"ace/mode/javascript_worker","JavaScriptWorker");return t.attachToDocument(e.getDocument()),t.on("annotate",(function(t){e.setAnnotations(t.data)})),t.on("terminate",(function(){e.clearAnnotations()})),t},this.$id="ace/mode/javascript",this.snippetFileId="ace/snippets/javascript"}.call(c.prototype),t.Mode=c})),ace.define("ace/mode/css_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop");e("../lib/lang");var o=e("./text_highlight_rules").TextHighlightRules,r=t.supportType="align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration|animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image|background-origin|background-position|background-repeat|background-size|border|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius|border-bottom-style|border-bottom-width|border-collapse|border-color|border-image|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-left|border-left-color|border-left-style|border-left-width|border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style|border-top-width|border-width|bottom|box-shadow|box-sizing|caption-side|clear|clip|color|column-count|column-fill|column-gap|column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|content|counter-increment|counter-reset|cursor|direction|display|empty-cells|filter|flex|flex-basis|flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|font|font-family|font-size|font-size-adjust|font-stretch|font-style|font-variant|font-weight|hanging-punctuation|height|justify-content|left|letter-spacing|line-height|list-style|list-style-image|list-style-position|list-style-type|margin|margin-bottom|margin-left|margin-right|margin-top|max-height|max-width|max-zoom|min-height|min-width|min-zoom|nav-down|nav-index|nav-left|nav-right|nav-up|opacity|order|outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-x|overflow-y|padding|padding-bottom|padding-left|padding-right|padding-top|page-break-after|page-break-before|page-break-inside|perspective|perspective-origin|position|quotes|resize|right|tab-size|table-layout|text-align|text-align-last|text-decoration|text-decoration-color|text-decoration-line|text-decoration-style|text-indent|text-justify|text-overflow|text-shadow|text-transform|top|transform|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function|unicode-bidi|user-select|user-zoom|vertical-align|visibility|white-space|width|word-break|word-spacing|word-wrap|z-index",i=t.supportFunction="rgb|rgba|url|attr|counter|counters",l=t.supportConstant="absolute|after-edge|after|all-scroll|all|alphabetic|always|antialiased|armenian|auto|avoid-column|avoid-page|avoid|balance|baseline|before-edge|before|below|bidi-override|block-line-height|block|bold|bolder|border-box|both|bottom|box|break-all|break-word|capitalize|caps-height|caption|center|central|char|circle|cjk-ideographic|clone|close-quote|col-resize|collapse|column|consider-shifts|contain|content-box|cover|crosshair|cubic-bezier|dashed|decimal-leading-zero|decimal|default|disabled|disc|disregard-shifts|distribute-all-lines|distribute-letter|distribute-space|distribute|dotted|double|e-resize|ease-in|ease-in-out|ease-out|ease|ellipsis|end|exclude-ruby|flex-end|flex-start|fill|fixed|georgian|glyphs|grid-height|groove|hand|hanging|hebrew|help|hidden|hiragana-iroha|hiragana|horizontal|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|ideographic|inactive|include-ruby|inherit|initial|inline-block|inline-box|inline-line-height|inline-table|inline|inset|inside|inter-ideograph|inter-word|invert|italic|justify|katakana-iroha|katakana|keep-all|last|left|lighter|line-edge|line-through|line|linear|list-item|local|loose|lower-alpha|lower-greek|lower-latin|lower-roman|lowercase|lr-tb|ltr|mathematical|max-height|max-size|medium|menu|message-box|middle|move|n-resize|ne-resize|newspaper|no-change|no-close-quote|no-drop|no-open-quote|no-repeat|none|normal|not-allowed|nowrap|nw-resize|oblique|open-quote|outset|outside|overline|padding-box|page|pointer|pre-line|pre-wrap|pre|preserve-3d|progress|relative|repeat-x|repeat-y|repeat|replaced|reset-size|ridge|right|round|row-resize|rtl|s-resize|scroll|se-resize|separate|slice|small-caps|small-caption|solid|space|square|start|static|status-bar|step-end|step-start|steps|stretch|strict|sub|super|sw-resize|table-caption|table-cell|table-column-group|table-column|table-footer-group|table-header-group|table-row-group|table-row|table|tb-rl|text-after-edge|text-before-edge|text-bottom|text-size|text-top|text|thick|thin|transparent|underline|upper-alpha|upper-latin|upper-roman|uppercase|use-script|vertical-ideographic|vertical-text|visible|w-resize|wait|whitespace|z-index|zero|zoom",s=t.supportConstantColor="aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen",u=t.supportConstantFonts="arial|century|comic|courier|cursive|fantasy|garamond|georgia|helvetica|impact|lucida|symbol|system|tahoma|times|trebuchet|utopia|verdana|webdings|sans-serif|serif|monospace",c=t.numRe="\\-?(?:(?:[0-9]+(?:\\.[0-9]+)?)|(?:\\.[0-9]+))",g=t.pseudoElements="(\\:+)\\b(after|before|first-letter|first-line|moz-selection|selection)\\b",d=t.pseudoClasses="(:)\\b(active|checked|disabled|empty|enabled|first-child|first-of-type|focus|hover|indeterminate|invalid|last-child|last-of-type|link|not|nth-child|nth-last-child|nth-last-of-type|nth-of-type|only-child|only-of-type|required|root|target|valid|visited)\\b",m=function(){var e=this.createKeywordMapper({"support.function":i,"support.constant":l,"support.type":r,"support.constant.color":s,"support.constant.fonts":u},"text",!0);this.$rules={start:[{include:["strings","url","comments"]},{token:"paren.lparen",regex:"\\{",next:"ruleset"},{token:"paren.rparen",regex:"\\}"},{token:"string",regex:"@(?!viewport)",next:"media"},{token:"keyword",regex:"#[a-z0-9-_]+"},{token:"keyword",regex:"%"},{token:"variable",regex:"\\.[a-z0-9-_]+"},{token:"string",regex:":[a-z0-9-_]+"},{token:"constant.numeric",regex:c},{token:"constant",regex:"[a-z0-9-_]+"},{caseInsensitive:!0}],media:[{include:["strings","url","comments"]},{token:"paren.lparen",regex:"\\{",next:"start"},{token:"paren.rparen",regex:"\\}",next:"start"},{token:"string",regex:";",next:"start"},{token:"keyword",regex:"(?:media|supports|document|charset|import|namespace|media|supports|document|page|font|keyframes|viewport|counter-style|font-feature-values|swash|ornaments|annotation|stylistic|styleset|character-variant)"}],comments:[{token:"comment",regex:"\\/\\*",push:[{token:"comment",regex:"\\*\\/",next:"pop"},{defaultToken:"comment"}]}],ruleset:[{regex:"-(webkit|ms|moz|o)-",token:"text"},{token:"punctuation.operator",regex:"[:;]"},{token:"paren.rparen",regex:"\\}",next:"start"},{include:["strings","url","comments"]},{token:["constant.numeric","keyword"],regex:"("+c+")(ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vm|vw|%)"},{token:"constant.numeric",regex:c},{token:"constant.numeric",regex:"#[a-f0-9]{6}"},{token:"constant.numeric",regex:"#[a-f0-9]{3}"},{token:["punctuation","entity.other.attribute-name.pseudo-element.css"],regex:g},{token:["punctuation","entity.other.attribute-name.pseudo-class.css"],regex:d},{include:"url"},{token:e,regex:"\\-?[a-zA-Z_][a-zA-Z0-9_\\-]*"},{token:"paren.lparen",regex:"\\{"},{caseInsensitive:!0}],url:[{token:"support.function",regex:"(?:url(:?-prefix)?|domain|regexp)\\(",push:[{token:"support.function",regex:"\\)",next:"pop"},{defaultToken:"string"}]}],strings:[{token:"string.start",regex:"'",push:[{token:"string.end",regex:"'|$",next:"pop"},{include:"escapes"},{token:"constant.language.escape",regex:/\\$/,consumeLineEnd:!0},{defaultToken:"string"}]},{token:"string.start",regex:'"',push:[{token:"string.end",regex:'"|$',next:"pop"},{include:"escapes"},{token:"constant.language.escape",regex:/\\$/,consumeLineEnd:!0},{defaultToken:"string"}]}],escapes:[{token:"constant.language.escape",regex:/\\([a-fA-F\d]{1,6}|[^a-fA-F\d])/}]},this.normalizeRules()};a.inherits(m,o),t.CssHighlightRules=m})),ace.define("ace/mode/css_completions",["require","exports","module"],(function(e,t,n){var a={background:{"#$0":1},"background-color":{"#$0":1,transparent:1,fixed:1},"background-image":{"url('/$0')":1},"background-repeat":{repeat:1,"repeat-x":1,"repeat-y":1,"no-repeat":1,inherit:1},"background-position":{bottom:2,center:2,left:2,right:2,top:2,inherit:2},"background-attachment":{scroll:1,fixed:1},"background-size":{cover:1,contain:1},"background-clip":{"border-box":1,"padding-box":1,"content-box":1},"background-origin":{"border-box":1,"padding-box":1,"content-box":1},border:{"solid $0":1,"dashed $0":1,"dotted $0":1,"#$0":1},"border-color":{"#$0":1},"border-style":{solid:2,dashed:2,dotted:2,double:2,groove:2,hidden:2,inherit:2,inset:2,none:2,outset:2,ridged:2},"border-collapse":{collapse:1,separate:1},bottom:{px:1,em:1,"%":1},clear:{left:1,right:1,both:1,none:1},color:{"#$0":1,"rgb(#$00,0,0)":1},cursor:{default:1,pointer:1,move:1,text:1,wait:1,help:1,progress:1,"n-resize":1,"ne-resize":1,"e-resize":1,"se-resize":1,"s-resize":1,"sw-resize":1,"w-resize":1,"nw-resize":1},display:{none:1,block:1,inline:1,"inline-block":1,"table-cell":1},"empty-cells":{show:1,hide:1},float:{left:1,right:1,none:1},"font-family":{Arial:2,"Comic Sans MS":2,Consolas:2,"Courier New":2,Courier:2,Georgia:2,Monospace:2,"Sans-Serif":2,"Segoe UI":2,Tahoma:2,"Times New Roman":2,"Trebuchet MS":2,Verdana:1},"font-size":{px:1,em:1,"%":1},"font-weight":{bold:1,normal:1},"font-style":{italic:1,normal:1},"font-variant":{normal:1,"small-caps":1},height:{px:1,em:1,"%":1},left:{px:1,em:1,"%":1},"letter-spacing":{normal:1},"line-height":{normal:1},"list-style-type":{none:1,disc:1,circle:1,square:1,decimal:1,"decimal-leading-zero":1,"lower-roman":1,"upper-roman":1,"lower-greek":1,"lower-latin":1,"upper-latin":1,georgian:1,"lower-alpha":1,"upper-alpha":1},margin:{px:1,em:1,"%":1},"margin-right":{px:1,em:1,"%":1},"margin-left":{px:1,em:1,"%":1},"margin-top":{px:1,em:1,"%":1},"margin-bottom":{px:1,em:1,"%":1},"max-height":{px:1,em:1,"%":1},"max-width":{px:1,em:1,"%":1},"min-height":{px:1,em:1,"%":1},"min-width":{px:1,em:1,"%":1},overflow:{hidden:1,visible:1,auto:1,scroll:1},"overflow-x":{hidden:1,visible:1,auto:1,scroll:1},"overflow-y":{hidden:1,visible:1,auto:1,scroll:1},padding:{px:1,em:1,"%":1},"padding-top":{px:1,em:1,"%":1},"padding-right":{px:1,em:1,"%":1},"padding-bottom":{px:1,em:1,"%":1},"padding-left":{px:1,em:1,"%":1},"page-break-after":{auto:1,always:1,avoid:1,left:1,right:1},"page-break-before":{auto:1,always:1,avoid:1,left:1,right:1},position:{absolute:1,relative:1,fixed:1,static:1},right:{px:1,em:1,"%":1},"table-layout":{fixed:1,auto:1},"text-decoration":{none:1,underline:1,"line-through":1,blink:1},"text-align":{left:1,right:1,center:1,justify:1},"text-transform":{capitalize:1,uppercase:1,lowercase:1,none:1},top:{px:1,em:1,"%":1},"vertical-align":{top:1,bottom:1},visibility:{hidden:1,visible:1},"white-space":{nowrap:1,normal:1,pre:1,"pre-line":1,"pre-wrap":1},width:{px:1,em:1,"%":1},"word-spacing":{normal:1},filter:{"alpha(opacity=$0100)":1},"text-shadow":{"$02px 2px 2px #777":1},"text-overflow":{"ellipsis-word":1,clip:1,ellipsis:1},"-moz-border-radius":1,"-moz-border-radius-topright":1,"-moz-border-radius-bottomright":1,"-moz-border-radius-topleft":1,"-moz-border-radius-bottomleft":1,"-webkit-border-radius":1,"-webkit-border-top-right-radius":1,"-webkit-border-top-left-radius":1,"-webkit-border-bottom-right-radius":1,"-webkit-border-bottom-left-radius":1,"-moz-box-shadow":1,"-webkit-box-shadow":1,transform:{"rotate($00deg)":1,"skew($00deg)":1},"-moz-transform":{"rotate($00deg)":1,"skew($00deg)":1},"-webkit-transform":{"rotate($00deg)":1,"skew($00deg)":1}},o=function(){};(function(){this.completionsDefined=!1,this.defineCompletions=function(){if(document){var e=document.createElement("c").style;for(var t in e)if("string"==typeof e[t]){var n=t.replace(/[A-Z]/g,(function(e){return"-"+e.toLowerCase()}));a.hasOwnProperty(n)||(a[n]=1)}}this.completionsDefined=!0},this.getCompletions=function(e,t,n,a){if(this.completionsDefined||this.defineCompletions(),"ruleset"===e||"ace/mode/scss"==t.$mode.$id){var o=t.getLine(n.row).substr(0,n.column),r=/\([^)]*$/.test(o);return r&&(o=o.substr(o.lastIndexOf("(")+1)),/:[^;]+$/.test(o)?this.getPropertyValueCompletions(e,t,n,a):this.getPropertyCompletions(e,t,n,a,r)}return[]},this.getPropertyCompletions=function(e,t,n,o,r){return r=r||!1,Object.keys(a).map((function(e){return{caption:e,snippet:e+": $0"+(r?"":";"),meta:"property",score:1e6}}))},this.getPropertyValueCompletions=function(e,t,n,o){var r=t.getLine(n.row).substr(0,n.column),i=(/([\w\-]+):[^:]*$/.exec(r)||{})[1];if(!i)return[];var l=[];return i in a&&"object"==typeof a[i]&&(l=Object.keys(a[i])),l.map((function(e){return{caption:e,snippet:e,meta:"property value",score:1e6}}))}}).call(o.prototype),t.CssCompletions=o})),ace.define("ace/mode/behaviour/css",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/mode/behaviour/cstyle","ace/token_iterator"],(function(e,t,n){var a=e("../../lib/oop");e("../behaviour").Behaviour;var o=e("./cstyle").CstyleBehaviour,r=e("../../token_iterator").TokenIterator,i=function(){this.inherit(o),this.add("colon","insertion",(function(e,t,n,a,o){if(":"===o&&n.selection.isEmpty()){var i=n.getCursorPosition(),l=new r(a,i.row,i.column),s=l.getCurrentToken();if(s&&s.value.match(/\s+/)&&(s=l.stepBackward()),s&&"support.type"===s.type){var u=a.doc.getLine(i.row);if(":"===u.substring(i.column,i.column+1))return{text:"",selection:[1,1]};if(/^(\s+[^;]|\s*$)/.test(u.substring(i.column)))return{text:":;",selection:[1,1]}}}})),this.add("colon","deletion",(function(e,t,n,a,o){var i=a.doc.getTextRange(o);if(!o.isMultiLine()&&":"===i){var l=n.getCursorPosition(),s=new r(a,l.row,l.column),u=s.getCurrentToken();if(u&&u.value.match(/\s+/)&&(u=s.stepBackward()),u&&"support.type"===u.type&&";"===a.doc.getLine(o.start.row).substring(o.end.column,o.end.column+1))return o.end.column++,o}})),this.add("semicolon","insertion",(function(e,t,n,a,o){if(";"===o&&n.selection.isEmpty()){var r=n.getCursorPosition();if(";"===a.doc.getLine(r.row).substring(r.column,r.column+1))return{text:"",selection:[1,1]}}})),this.add("!important","insertion",(function(e,t,n,a,o){if("!"===o&&n.selection.isEmpty()){var r=n.getCursorPosition(),i=a.doc.getLine(r.row);if(/^\s*(;|}|$)/.test(i.substring(r.column)))return{text:"!important",selection:[10,10]}}}))};a.inherits(i,o),t.CssBehaviour=i})),ace.define("ace/mode/css",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/css_highlight_rules","ace/mode/matching_brace_outdent","ace/worker/worker_client","ace/mode/css_completions","ace/mode/behaviour/css","ace/mode/folding/cstyle"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text").Mode,r=e("./css_highlight_rules").CssHighlightRules,i=e("./matching_brace_outdent").MatchingBraceOutdent,l=e("../worker/worker_client").WorkerClient,s=e("./css_completions").CssCompletions,u=e("./behaviour/css").CssBehaviour,c=e("./folding/cstyle").FoldMode,g=function(){this.HighlightRules=r,this.$outdent=new i,this.$behaviour=new u,this.$completer=new s,this.foldingRules=new c};a.inherits(g,o),function(){this.foldingRules="cStyle",this.blockComment={start:"/*",end:"*/"},this.getNextLineIndent=function(e,t,n){var a=this.$getIndent(t),o=this.getTokenizer().getLineTokens(t,e).tokens;return o.length&&"comment"==o[o.length-1].type||t.match(/^.*\{\s*$/)&&(a+=n),a},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.getCompletions=function(e,t,n,a){return this.$completer.getCompletions(e,t,n,a)},this.createWorker=function(e){var t=new l(["ace"],"ace/mode/css_worker","Worker");return t.attachToDocument(e.getDocument()),t.on("annotate",(function(t){e.setAnnotations(t.data)})),t.on("terminate",(function(){e.clearAnnotations()})),t},this.$id="ace/mode/css",this.snippetFileId="ace/snippets/css"}.call(g.prototype),t.Mode=g})),ace.define("ace/mode/xml_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=function(e){var t="[_:a-zA-ZÀ-￿][-_:.a-zA-Z0-9À-￿]*";this.$rules={start:[{token:"string.cdata.xml",regex:"<\\!\\[CDATA\\[",next:"cdata"},{token:["punctuation.instruction.xml","keyword.instruction.xml"],regex:"(<\\?)("+t+")",next:"processing_instruction"},{token:"comment.start.xml",regex:"<\\!--",next:"comment"},{token:["xml-pe.doctype.xml","xml-pe.doctype.xml"],regex:"(<\\!)(DOCTYPE)(?=[\\s])",next:"doctype",caseInsensitive:!0},{include:"tag"},{token:"text.end-tag-open.xml",regex:"</"},{token:"text.tag-open.xml",regex:"<"},{include:"reference"},{defaultToken:"text.xml"}],processing_instruction:[{token:"entity.other.attribute-name.decl-attribute-name.xml",regex:t},{token:"keyword.operator.decl-attribute-equals.xml",regex:"="},{include:"whitespace"},{include:"string"},{token:"punctuation.xml-decl.xml",regex:"\\?>",next:"start"}],doctype:[{include:"whitespace"},{include:"string"},{token:"xml-pe.doctype.xml",regex:">",next:"start"},{token:"xml-pe.xml",regex:"[-_a-zA-Z0-9:]+"},{token:"punctuation.int-subset",regex:"\\[",push:"int_subset"}],int_subset:[{token:"text.xml",regex:"\\s+"},{token:"punctuation.int-subset.xml",regex:"]",next:"pop"},{token:["punctuation.markup-decl.xml","keyword.markup-decl.xml"],regex:"(<\\!)("+t+")",push:[{token:"text",regex:"\\s+"},{token:"punctuation.markup-decl.xml",regex:">",next:"pop"},{include:"string"}]}],cdata:[{token:"string.cdata.xml",regex:"\\]\\]>",next:"start"},{token:"text.xml",regex:"\\s+"},{token:"text.xml",regex:"(?:[^\\]]|\\](?!\\]>))+"}],comment:[{token:"comment.end.xml",regex:"--\x3e",next:"start"},{defaultToken:"comment.xml"}],reference:[{token:"constant.language.escape.reference.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}],attr_reference:[{token:"constant.language.escape.reference.attribute-value.xml",regex:"(?:&#[0-9]+;)|(?:&#x[0-9a-fA-F]+;)|(?:&[a-zA-Z0-9_:\\.-]+;)"}],tag:[{token:["meta.tag.punctuation.tag-open.xml","meta.tag.punctuation.end-tag-open.xml","meta.tag.tag-name.xml"],regex:"(?:(<)|(</))((?:"+t+":)?"+t+")",next:[{include:"attributes"},{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",next:"start"}]}],tag_whitespace:[{token:"text.tag-whitespace.xml",regex:"\\s+"}],whitespace:[{token:"text.whitespace.xml",regex:"\\s+"}],string:[{token:"string.xml",regex:"'",push:[{token:"string.xml",regex:"'",next:"pop"},{defaultToken:"string.xml"}]},{token:"string.xml",regex:'"',push:[{token:"string.xml",regex:'"',next:"pop"},{defaultToken:"string.xml"}]}],attributes:[{token:"entity.other.attribute-name.xml",regex:t},{token:"keyword.operator.attribute-equals.xml",regex:"="},{include:"tag_whitespace"},{include:"attribute_value"}],attribute_value:[{token:"string.attribute-value.xml",regex:"'",push:[{token:"string.attribute-value.xml",regex:"'",next:"pop"},{include:"attr_reference"},{defaultToken:"string.attribute-value.xml"}]},{token:"string.attribute-value.xml",regex:'"',push:[{token:"string.attribute-value.xml",regex:'"',next:"pop"},{include:"attr_reference"},{defaultToken:"string.attribute-value.xml"}]}]},this.constructor===r&&this.normalizeRules()};(function(){this.embedTagRules=function(e,t,n){this.$rules.tag.unshift({token:["meta.tag.punctuation.tag-open.xml","meta.tag."+n+".tag-name.xml"],regex:"(<)("+n+"(?=\\s|>|$))",next:[{include:"attributes"},{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",next:t+"start"}]}),this.$rules[n+"-end"]=[{include:"attributes"},{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",next:"start",onMatch:function(e,t,n){return n.splice(0),this.token}}],this.embedRules(e,t,[{token:["meta.tag.punctuation.end-tag-open.xml","meta.tag."+n+".tag-name.xml"],regex:"(</)("+n+"(?=\\s|>|$))",next:n+"-end"},{token:"string.cdata.xml",regex:"<\\!\\[CDATA\\["},{token:"string.cdata.xml",regex:"\\]\\]>"}])}}).call(o.prototype),a.inherits(r,o),t.XmlHighlightRules=r})),ace.define("ace/mode/html_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/css_highlight_rules","ace/mode/javascript_highlight_rules","ace/mode/xml_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("../lib/lang"),r=e("./css_highlight_rules").CssHighlightRules,i=e("./javascript_highlight_rules").JavaScriptHighlightRules,l=e("./xml_highlight_rules").XmlHighlightRules,s=o.createMap({a:"anchor",button:"form",form:"form",img:"image",input:"form",label:"form",option:"form",script:"script",select:"form",textarea:"form",style:"style",table:"table",tbody:"table",td:"table",tfoot:"table",th:"table",tr:"table"}),u=function(){l.call(this),this.addRules({attributes:[{include:"tag_whitespace"},{token:"entity.other.attribute-name.xml",regex:"[-_a-zA-Z0-9:.]+"},{token:"keyword.operator.attribute-equals.xml",regex:"=",push:[{include:"tag_whitespace"},{token:"string.unquoted.attribute-value.html",regex:"[^<>='\"`\\s]+",next:"pop"},{token:"empty",regex:"",next:"pop"}]},{include:"attribute_value"}],tag:[{token:function(e,t){var n=s[t];return["meta.tag.punctuation."+("<"==e?"":"end-")+"tag-open.xml","meta.tag"+(n?"."+n:"")+".tag-name.xml"]},regex:"(</?)([-_a-zA-Z0-9:.]+)",next:"tag_stuff"}],tag_stuff:[{include:"attributes"},{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",next:"start"}]}),this.embedTagRules(r,"css-","style"),this.embedTagRules(new i({jsx:!1}).getRules(),"js-","script"),this.constructor===u&&this.normalizeRules()};a.inherits(u,l),t.HtmlHighlightRules=u})),ace.define("ace/mode/html",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/text","ace/mode/javascript","ace/mode/css","ace/mode/html_highlight_rules","ace/mode/behaviour/xml","ace/mode/folding/html","ace/mode/html_completions","ace/worker/worker_client"],(function(e,t,n){var a=e("../lib/oop"),o=e("../lib/lang"),r=e("./text").Mode,i=e("./javascript").Mode,l=e("./css").Mode,s=e("./html_highlight_rules").HtmlHighlightRules,u=e("./behaviour/xml").XmlBehaviour,c=e("./folding/html").FoldMode,g=e("./html_completions").HtmlCompletions,d=e("../worker/worker_client").WorkerClient,m=["area","base","br","col","embed","hr","img","input","keygen","link","meta","menuitem","param","source","track","wbr"],p=["li","dt","dd","p","rt","rp","optgroup","option","colgroup","td","th"],h=function(e){this.fragmentContext=e&&e.fragmentContext,this.HighlightRules=s,this.$behaviour=new u,this.$completer=new g,this.createModeDelegates({"js-":i,"css-":l}),this.foldingRules=new c(this.voidElements,o.arrayToMap(p))};a.inherits(h,r),function(){this.blockComment={start:"\x3c!--",end:"--\x3e"},this.voidElements=o.arrayToMap(m),this.getNextLineIndent=function(e,t,n){return this.$getIndent(t)},this.checkOutdent=function(e,t,n){return!1},this.getCompletions=function(e,t,n,a){return this.$completer.getCompletions(e,t,n,a)},this.createWorker=function(e){if(this.constructor==h){var t=new d(["ace"],"ace/mode/html_worker","Worker");return t.attachToDocument(e.getDocument()),this.fragmentContext&&t.call("setOptions",[{context:this.fragmentContext}]),t.on("error",(function(t){e.setAnnotations(t.data)})),t.on("terminate",(function(){e.clearAnnotations()})),t}},this.$id="ace/mode/html",this.snippetFileId="ace/snippets/html"}.call(h.prototype),t.Mode=h})),ace.define("ace/mode/typescript_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/javascript_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./javascript_highlight_rules").JavaScriptHighlightRules,r=function(e){var t=new o({jsx:1==(e&&e.jsx)}).getRules();t.no_regex=[{token:["storage.type","text","entity.name.function.ts"],regex:"(function)(\\s+)([a-zA-Z0-9$_¡-￿][a-zA-Z0-9d$_¡-￿]*)"},{token:"keyword",regex:"(?:\\b(constructor|declare|interface|as|AS|public|private|extends|export|super|readonly|module|namespace|abstract|implements)\\b)"},{token:["keyword","storage.type.variable.ts"],regex:"(class|type)(\\s+[a-zA-Z0-9_?.$][\\w?.$]*)"},{token:"keyword",regex:"\\b(?:super|export|import|keyof|infer)\\b"},{token:["storage.type.variable.ts"],regex:"(?:\\b(this\\.|string\\b|bool\\b|boolean\\b|number\\b|true\\b|false\\b|undefined\\b|any\\b|null\\b|(?:unique )?symbol\\b|object\\b|never\\b|enum\\b))"}].concat(t.no_regex),this.$rules=t};a.inherits(r,o),t.TypeScriptHighlightRules=r})),ace.define("ace/mode/coffee_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules;function r(){var e="[$A-Za-z_\\x7f-\\uffff][$\\w\\x7f-\\uffff]*",t="case|const|function|var|void|with|enum|implements|interface|let|package|private|protected|public|static",n=this.createKeywordMapper({keyword:"this|throw|then|try|typeof|super|switch|return|break|by|continue|catch|class|in|instanceof|is|isnt|if|else|extends|for|own|finally|function|while|when|new|no|not|delete|debugger|do|loop|of|off|or|on|unless|until|and|yes|yield|export|import|default","constant.language":"true|false|null|undefined|NaN|Infinity","invalid.illegal":t,"language.support.class":"Array|Boolean|Date|Function|Number|Object|RegExp|ReferenceError|String|Error|EvalError|InternalError|RangeError|ReferenceError|StopIteration|SyntaxError|TypeError|URIError|ArrayBuffer|Float32Array|Float64Array|Int16Array|Int32Array|Int8Array|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray","language.support.function":"Math|JSON|isNaN|isFinite|parseInt|parseFloat|encodeURI|encodeURIComponent|decodeURI|decodeURIComponent|String|","variable.language":"window|arguments|prototype|document"},"identifier"),a={token:["paren.lparen","variable.parameter","paren.rparen","text","storage.type"],regex:/(?:(\()((?:"[^")]*?"|'[^')]*?'|\/[^\/)]*?\/|[^()"'\/])*?)(\))(\s*))?([\-=]>)/.source},o=/\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.)/;this.$rules={start:[{token:"constant.numeric",regex:"(?:0x[\\da-fA-F]+|(?:\\d+(?:\\.\\d+)?|\\.\\d+)(?:[eE][+-]?\\d+)?)"},{stateName:"qdoc",token:"string",regex:"'''",next:[{token:"string",regex:"'''",next:"start"},{token:"constant.language.escape",regex:o},{defaultToken:"string"}]},{stateName:"qqdoc",token:"string",regex:'"""',next:[{token:"string",regex:'"""',next:"start"},{token:"paren.string",regex:"#{",push:"start"},{token:"constant.language.escape",regex:o},{defaultToken:"string"}]},{stateName:"qstring",token:"string",regex:"'",next:[{token:"string",regex:"'",next:"start"},{token:"constant.language.escape",regex:o},{defaultToken:"string"}]},{stateName:"qqstring",token:"string.start",regex:'"',next:[{token:"string.end",regex:'"',next:"start"},{token:"paren.string",regex:"#{",push:"start"},{token:"constant.language.escape",regex:o},{defaultToken:"string"}]},{stateName:"js",token:"string",regex:"`",next:[{token:"string",regex:"`",next:"start"},{token:"constant.language.escape",regex:o},{defaultToken:"string"}]},{regex:"[{}]",onMatch:function(e,t,n){return this.next="","{"==e&&n.length?(n.unshift("start",t),"paren"):"}"==e&&n.length&&(n.shift(),this.next=n.shift()||"",-1!=this.next.indexOf("string"))?"paren.string":"paren"}},{token:"string.regex",regex:"///",next:"heregex"},{token:"string.regex",regex:/(?:\/(?![\s=])[^[\/\n\\]*(?:(?:\\[\s\S]|\[[^\]\n\\]*(?:\\[\s\S][^\]\n\\]*)*])[^[\/\n\\]*)*\/)(?:[imgy]{0,4})(?!\w)/},{token:"comment",regex:"###(?!#)",next:"comment"},{token:"comment",regex:"#.*"},{token:["punctuation.operator","text","identifier"],regex:"(\\.)(\\s*)("+t+")"},{token:"punctuation.operator",regex:"\\.{1,3}"},{token:["keyword","text","language.support.class","text","keyword","text","language.support.class"],regex:"(class)(\\s+)("+e+")(?:(\\s+)(extends)(\\s+)("+e+"))?"},{token:["entity.name.function","text","keyword.operator","text"].concat(a.token),regex:"("+e+")(\\s*)([=:])(\\s*)"+a.regex},a,{token:"variable",regex:"@(?:"+e+")?"},{token:n,regex:e},{token:"punctuation.operator",regex:"\\,|\\."},{token:"storage.type",regex:"[\\-=]>"},{token:"keyword.operator",regex:"(?:[-+*/%<>&|^!?=]=|>>>=?|\\-\\-|\\+\\+|::|&&=|\\|\\|=|<<=|>>=|\\?\\.|\\.{2,3}|[!*+-=><])"},{token:"paren.lparen",regex:"[({[]"},{token:"paren.rparen",regex:"[\\]})]"},{token:"text",regex:"\\s+"}],heregex:[{token:"string.regex",regex:".*?///[imgy]{0,4}",next:"start"},{token:"comment.regex",regex:"\\s+(?:#.*)?"},{token:"string.regex",regex:"\\S+"}],comment:[{token:"comment",regex:"###",next:"start"},{defaultToken:"comment"}]},this.normalizeRules()}a.inherits(r,o),t.CoffeeHighlightRules=r})),ace.define("ace/mode/stylus_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules","ace/mode/css_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=e("./css_highlight_rules"),i=function(){var e=this.createKeywordMapper({"support.type":r.supportType,"support.function":r.supportFunction,"support.constant":r.supportConstant,"support.constant.color":r.supportConstantColor,"support.constant.fonts":r.supportConstantFonts},"text",!0);this.$rules={start:[{token:"comment",regex:/\/\/.*$/},{token:"comment",regex:/\/\*/,next:"comment"},{token:["entity.name.function.stylus","text"],regex:"^([-a-zA-Z_][-\\w]*)?(\\()"},{token:["entity.other.attribute-name.class.stylus"],regex:"\\.-?[_a-zA-Z]+[_a-zA-Z0-9-]*"},{token:["entity.language.stylus"],regex:"^ *&"},{token:["variable.language.stylus"],regex:"(arguments)"},{token:["keyword.stylus"],regex:"@[-\\w]+"},{token:["punctuation","entity.other.attribute-name.pseudo-element.css"],regex:r.pseudoElements},{token:["punctuation","entity.other.attribute-name.pseudo-class.css"],regex:r.pseudoClasses},{token:["entity.name.tag.stylus"],regex:"(?:\\b)(a|abbr|acronym|address|area|article|aside|audio|b|base|big|blockquote|body|br|button|canvas|caption|cite|code|col|colgroup|datalist|dd|del|details|dfn|dialog|div|dl|dt|em|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|(?:h[1-6])|head|header|hgroup|hr|html|i|iframe|img|input|ins|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|pre|progress|q|samp|script|section|select|small|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video)(?:\\b)"},{token:"constant.numeric",regex:"#[a-fA-F0-9]{6}"},{token:"constant.numeric",regex:"#[a-fA-F0-9]{3}"},{token:["punctuation.definition.entity.stylus","entity.other.attribute-name.id.stylus"],regex:"(#)([a-zA-Z][a-zA-Z0-9_-]*)"},{token:"meta.vendor-prefix.stylus",regex:"-webkit-|-moz\\-|-ms-|-o-"},{token:"keyword.control.stylus",regex:"(?:!important|for|in|return|true|false|null|if|else|unless|return)\\b"},{token:"keyword.operator.stylus",regex:"!|~|\\+|-|(?:\\*)?\\*|\\/|%|(?:\\.)\\.\\.|<|>|(?:=|:|\\?|\\+|-|\\*|\\/|%|<|>)?=|!="},{token:"keyword.operator.stylus",regex:"(?:in|is(?:nt)?|not)\\b"},{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"constant.numeric",regex:r.numRe},{token:"keyword",regex:"(?:ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vm|vw|%)\\b"},{token:e,regex:"\\-?[a-zA-Z_][a-zA-Z0-9_\\-]*"}],comment:[{token:"comment",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}],qqstring:[{token:"string",regex:'[^"\\\\]+'},{token:"string",regex:"\\\\$",next:"qqstring"},{token:"string",regex:'"|$',next:"start"}],qstring:[{token:"string",regex:"[^'\\\\]+"},{token:"string",regex:"\\\\$",next:"qstring"},{token:"string",regex:"'|$",next:"start"}]}};a.inherits(i,o),t.StylusHighlightRules=i})),ace.define("ace/mode/scss_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/text_highlight_rules","ace/mode/css_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("../lib/lang"),r=e("./text_highlight_rules").TextHighlightRules,i=e("./css_highlight_rules"),l=function(){var e=o.arrayToMap(i.supportType.split("|")),t=o.arrayToMap("hsl|hsla|rgb|rgba|url|attr|counter|counters|abs|adjust_color|adjust_hue|alpha|join|blue|ceil|change_color|comparable|complement|darken|desaturate|floor|grayscale|green|hue|if|invert|join|length|lighten|lightness|mix|nth|opacify|opacity|percentage|quote|red|round|saturate|saturation|scale_color|transparentize|type_of|unit|unitless|unquote".split("|")),n=o.arrayToMap(i.supportConstant.split("|")),a=o.arrayToMap(i.supportConstantColor.split("|")),r=o.arrayToMap("@mixin|@extend|@include|@import|@media|@debug|@warn|@if|@for|@each|@while|@else|@font-face|@-webkit-keyframes|if|and|!default|module|def|end|declare".split("|")),l=o.arrayToMap("a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdo|big|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command|datalist|dd|del|details|dfn|dir|div|dl|dt|em|embed|fieldset|figcaption|figure|font|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hgroup|hr|html|i|iframe|img|input|ins|keygen|kbd|label|legend|li|link|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|pre|progress|q|rp|rt|ruby|s|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|u|ul|var|video|wbr|xmp".split("|")),s="\\-?(?:(?:[0-9]+)|(?:[0-9]*\\.[0-9]+))";this.$rules={start:[{token:"comment",regex:"\\/\\/.*$"},{token:"comment",regex:"\\/\\*",next:"comment"},{token:"string",regex:'["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]'},{token:"string",regex:'["].*\\\\$',next:"qqstring"},{token:"string",regex:"['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']"},{token:"string",regex:"['].*\\\\$",next:"qstring"},{token:"constant.numeric",regex:s+"(?:ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vmax|vmin|vm|vw|%)"},{token:"constant.numeric",regex:"#[a-f0-9]{6}"},{token:"constant.numeric",regex:"#[a-f0-9]{3}"},{token:"constant.numeric",regex:s},{token:["support.function","string","support.function"],regex:"(url\\()(.*)(\\))"},{token:function(o){return e.hasOwnProperty(o.toLowerCase())?"support.type":r.hasOwnProperty(o)?"keyword":n.hasOwnProperty(o)?"constant.language":t.hasOwnProperty(o)?"support.function":a.hasOwnProperty(o.toLowerCase())?"support.constant.color":l.hasOwnProperty(o.toLowerCase())?"variable.language":"text"},regex:"\\-?[@a-z_][@a-z0-9_\\-]*"},{token:"variable",regex:"[a-z_\\-$][a-z0-9_\\-$]*\\b"},{token:"variable.language",regex:"#[a-z0-9-_]+"},{token:"variable.language",regex:"\\.[a-z0-9-_]+"},{token:"variable.language",regex:":[a-z0-9-_]+"},{token:"constant",regex:"[a-z0-9-_]+"},{token:"keyword.operator",regex:"<|>|<=|>=|==|!=|-|%|#|\\+|\\$|\\+|\\*"},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:"\\s+"},{caseInsensitive:!0}],comment:[{token:"comment",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}],qqstring:[{token:"string",regex:'(?:(?:\\\\.)|(?:[^"\\\\]))*?"',next:"start"},{token:"string",regex:".+"}],qstring:[{token:"string",regex:"(?:(?:\\\\.)|(?:[^'\\\\]))*?'",next:"start"},{token:"string",regex:".+"}]}};a.inherits(l,r),t.ScssHighlightRules=l})),ace.define("ace/mode/sass_highlight_rules",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/mode/scss_highlight_rules"],(function(e,t,n){var a=e("../lib/oop");e("../lib/lang");var o=e("./scss_highlight_rules").ScssHighlightRules,r=function(){o.call(this);var e=this.$rules.start;"comment"==e[1].token&&(e.splice(1,1,{onMatch:function(e,t,n){return n.unshift(this.next,-1,e.length-2,t),"comment"},regex:/^\s*\/\*/,next:"comment"},{token:"error.invalid",regex:"/\\*|[{;}]"},{token:"support.type",regex:/^\s*:[\w\-]+\s/}),this.$rules.comment=[{regex:/^\s*/,onMatch:function(e,t,n){return-1===n[1]&&(n[1]=Math.max(n[2],e.length-1)),e.length<=n[1]?(n.shift(),n.shift(),n.shift(),this.next=n.shift(),"text"):(this.next="","comment")},next:"start"},{defaultToken:"comment"}])};a.inherits(r,o),t.SassHighlightRules=r})),ace.define("ace/mode/less_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules","ace/mode/css_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules,r=e("./css_highlight_rules"),i=function(){var e="@import|@media|@font-face|@keyframes|@-webkit-keyframes|@supports|@charset|@plugin|@namespace|@document|@page|@viewport|@-ms-viewport|or|and|when|not",t=e.split("|"),n=r.supportType.split("|"),a=this.createKeywordMapper({"support.constant":r.supportConstant,keyword:e,"support.constant.color":r.supportConstantColor,"support.constant.fonts":r.supportConstantFonts},"identifier",!0),o="\\-?(?:(?:[0-9]+)|(?:[0-9]*\\.[0-9]+))";this.$rules={start:[{token:"comment",regex:"\\/\\/.*$"},{token:"comment",regex:"\\/\\*",next:"comment"},{token:"string",regex:'["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]'},{token:"string",regex:"['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']"},{token:["constant.numeric","keyword"],regex:"("+o+")(ch|cm|deg|em|ex|fr|gd|grad|Hz|in|kHz|mm|ms|pc|pt|px|rad|rem|s|turn|vh|vm|vw|%)"},{token:"constant.numeric",regex:"#[a-f0-9]{6}"},{token:"constant.numeric",regex:"#[a-f0-9]{3}"},{token:"constant.numeric",regex:o},{token:["support.function","paren.lparen","string","paren.rparen"],regex:"(url)(\\()(.*)(\\))"},{token:["support.function","paren.lparen"],regex:"(:extend|[a-z0-9_\\-]+)(\\()"},{token:function(e){return t.indexOf(e.toLowerCase())>-1?"keyword":"variable"},regex:"[@\\$][a-z0-9_\\-@\\$]*\\b"},{token:"variable",regex:"[@\\$]\\{[a-z0-9_\\-@\\$]*\\}"},{token:function(e,t){return n.indexOf(e.toLowerCase())>-1?["support.type.property","text"]:["support.type.unknownProperty","text"]},regex:"([a-z0-9-_]+)(\\s*:)"},{token:"keyword",regex:"&"},{token:a,regex:"\\-?[@a-z_][@a-z0-9_\\-]*"},{token:"variable.language",regex:"#[a-z0-9-_]+"},{token:"variable.language",regex:"\\.[a-z0-9-_]+"},{token:"variable.language",regex:":[a-z_][a-z0-9-_]*"},{token:"constant",regex:"[a-z0-9-_]+"},{token:"keyword.operator",regex:"<|>|<=|>=|=|!=|-|%|\\+|\\*"},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:"\\s+"},{caseInsensitive:!0}],comment:[{token:"comment",regex:"\\*\\/",next:"start"},{defaultToken:"comment"}]},this.normalizeRules()};a.inherits(i,o),t.LessHighlightRules=i})),ace.define("ace/mode/slim_highlight_rules",["require","exports","module","ace/config","ace/lib/oop","ace/mode/text_highlight_rules"],(function(e,t,n){var a=e("../config").$modes,o=e("../lib/oop"),r=e("./text_highlight_rules").TextHighlightRules,i=function(){this.$rules={start:[{token:"keyword",regex:/^(\s*)(\w+):\s*/,onMatch:function(e,t,n,a){var o=/^\s*/.exec(a)[0],r=e.match(/^(\s*)(\w+):/)[2];return/^(javascript|ruby|coffee|markdown|css|scss|sass|less)$/.test(r)||(r=""),n.unshift("language-embed",[],[o,r],t),this.token},stateName:"language-embed",next:[{token:"string",regex:/^(\s*)/,onMatch:function(e,t,n,a){var o=n[2][0];return o.length>=e.length?(n.splice(0,3),this.next=n.shift(),this.token):(this.next="",[{type:"text",value:o}])},next:""},{token:"string",regex:/.+/,onMatch:function(e,t,n,o){var r=n[2][0],i=n[2][1],l=n[1];if(a[i]){var s=a[i].getTokenizer().getLineTokens(o.slice(r.length),l.slice(0));return n[1]=s.state,s.tokens}return this.token}}]},{token:"constant.begin.javascript.filter.slim",regex:"^(\\s*)():$"},{token:"constant.begin..filter.slim",regex:"^(\\s*)(ruby):$"},{token:"constant.begin.coffeescript.filter.slim",regex:"^(\\s*)():$"},{token:"constant.begin..filter.slim",regex:"^(\\s*)(markdown):$"},{token:"constant.begin.css.filter.slim",regex:"^(\\s*)():$"},{token:"constant.begin.scss.filter.slim",regex:"^(\\s*)():$"},{token:"constant.begin..filter.slim",regex:"^(\\s*)(sass):$"},{token:"constant.begin..filter.slim",regex:"^(\\s*)(less):$"},{token:"constant.begin..filter.slim",regex:"^(\\s*)(erb):$"},{token:"keyword.html.tags.slim",regex:"^(\\s*)((:?\\*(\\w)+)|doctype html|abbr|acronym|address|applet|area|article|aside|audio|base|basefont|bdo|big|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command|datalist|dd|del|details|dialog|dfn|dir|div|dl|dt|embed|fieldset|figure|font|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hgroup|hr|html|i|iframe|img|input|ins|keygen|kbd|label|legend|link|li|map|mark|menu|meta|meter|nav|noframes|noscript|object|ol|optgroup|option|output|p|param|pre|progress|q|rp|rt|ruby|samp|script|section|select|small|source|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|time|title|tr|tt|ul|var|video|xmp|b|u|s|em|a)(?:([.#](\\w|\\.)+)+\\s?)?\\b"},{token:"keyword.slim",regex:"^(\\s*)(?:([.#](\\w|\\.)+)+\\s?)"},{token:"string",regex:/^(\s*)('|\||\/|(\/!))\s*/,onMatch:function(e,t,n,a){var o=/^\s*/.exec(a)[0];return n.length<1?n.push(this.next):n[0]="mlString",n.length<2?n.push(o.length):n[1]=o.length,this.token},next:"mlString"},{token:"keyword.control.slim",regex:"^(\\s*)(\\-|==|=)",push:[{token:"control.end.slim",regex:"$",next:"pop"},{include:"rubyline"},{include:"misc"}]},{token:"paren",regex:"\\(",push:[{token:"paren",regex:"\\)",next:"pop"},{include:"misc"}]},{token:"paren",regex:"\\[",push:[{token:"paren",regex:"\\]",next:"pop"},{include:"misc"}]},{include:"misc"}],mlString:[{token:"indent",regex:/^\s*/,onMatch:function(e,t,n){return n[1]>=e.length?(this.next="start",n.splice(0)):this.next="mlString",this.token},next:"start"},{defaultToken:"string"}],rubyline:[{token:"keyword.operator.ruby.embedded.slim",regex:"(==|=)(<>|><|<'|'<|<|>)?|-"},{token:"list.ruby.operators.slim",regex:"(\\b)(for|in|do|if|else|elsif|unless|while|yield|not|and|or)\\b"},{token:"string",regex:"['](.)*?[']"},{token:"string",regex:'["](.)*?["]'}],misc:[{token:"class.variable.slim",regex:"\\@([a-zA-Z_][a-zA-Z0-9_]*)\\b"},{token:"list.meta.slim",regex:"(\\b)(true|false|nil)(\\b)"},{token:"keyword.operator.equals.slim",regex:"="},{token:"string",regex:"['](.)*?[']"},{token:"string",regex:'["](.)*?["]'}]},this.normalizeRules()};o.inherits(i,r),t.SlimHighlightRules=i})),ace.define("ace/mode/markdown_highlight_rules",["require","exports","module","ace/config","ace/lib/oop","ace/lib/lang","ace/mode/text_highlight_rules","ace/mode/html_highlight_rules"],(function(e,t,n){var a=e("../config").$modes,o=e("../lib/oop"),r=e("../lib/lang"),i=e("./text_highlight_rules").TextHighlightRules,l=e("./html_highlight_rules").HtmlHighlightRules,s=function(e){return"(?:[^"+r.escapeRegExp(e)+"\\\\]|\\\\.)*"},u=function(){l.call(this);var e={token:"support.function",regex:/^\s*(```+[^`]*|~~~+[^~]*)$/,onMatch:function(e,t,n,o){var r=e.match(/^(\s*)([`~]+)(.*)/),i=/[\w-]+|$/.exec(r[3])[0];return a[i]||(i=""),n.unshift("githubblock",[],[r[1],r[2],i],t),this.token},next:"githubblock"},t=[{token:"support.function",regex:".*",onMatch:function(e,t,n,o){var r=n[1],i=n[2][0],l=n[2][1],s=n[2][2],u=/^(\s*)(`+|~+)\s*$/.exec(e);if(u&&u[1].length<i.length+3&&u[2].length>=l.length&&u[2][0]==l[0])return n.splice(0,3),this.next=n.shift(),this.token;if(this.next="",s&&a[s]){var c=a[s].getTokenizer().getLineTokens(e,r.slice(0));return n[1]=c.state,c.tokens}return this.token}}];this.$rules.start.unshift({token:"empty_line",regex:"^$",next:"allowBlock"},{token:"markup.heading.1",regex:"^=+(?=\\s*$)"},{token:"markup.heading.2",regex:"^\\-+(?=\\s*$)"},{token:function(e){return"markup.heading."+e.length},regex:/^#{1,6}(?=\s|$)/,next:"header"},e,{token:"string.blockquote",regex:"^\\s*>\\s*(?:[*+-]|\\d+\\.)?\\s+",next:"blockquote"},{token:"constant",regex:"^ {0,3}(?:(?:\\* ?){3,}|(?:\\- ?){3,}|(?:\\_ ?){3,})\\s*$",next:"allowBlock"},{token:"markup.list",regex:"^\\s{0,3}(?:[*+-]|\\d+\\.)\\s+",next:"listblock-start"},{include:"basic"}),this.addRules({basic:[{token:"constant.language.escape",regex:/\\[\\`*_{}\[\]()#+\-.!]/},{token:"support.function",regex:"(`+)(.*?[^`])(\\1)"},{token:["text","constant","text","url","string","text"],regex:'^([ ]{0,3}\\[)([^\\]]+)(\\]:\\s*)([^ ]+)(\\s*(?:["][^"]+["])?(\\s*))$'},{token:["text","string","text","constant","text"],regex:"(\\[)("+s("]")+")(\\]\\s*\\[)("+s("]")+")(\\])"},{token:["text","string","text","markup.underline","string","text"],regex:"(\\!?\\[)("+s("]")+')(\\]\\()((?:[^\\)\\s\\\\]|\\\\.|\\s(?=[^"]))*)(\\s*"'+s('"')+'"\\s*)?(\\))'},{token:"string.strong",regex:"([*]{2}|[_]{2}(?=\\S))(.*?\\S[*_]*)(\\1)"},{token:"string.emphasis",regex:"([*]|[_](?=\\S))(.*?\\S[*_]*)(\\1)"},{token:["text","url","text"],regex:"(<)((?:https?|ftp|dict):[^'\">\\s]+|(?:mailto:)?[-.\\w]+\\@[-a-z0-9]+(?:\\.[-a-z0-9]+)*\\.[a-z]+)(>)"}],allowBlock:[{token:"support.function",regex:"^ {4}.+",next:"allowBlock"},{token:"empty_line",regex:"^$",next:"allowBlock"},{token:"empty",regex:"",next:"start"}],header:[{regex:"$",next:"start"},{include:"basic"},{defaultToken:"heading"}],"listblock-start":[{token:"support.variable",regex:/(?:\[[ x]\])?/,next:"listblock"}],listblock:[{token:"empty_line",regex:"^$",next:"start"},{token:"markup.list",regex:"^\\s{0,3}(?:[*+-]|\\d+\\.)\\s+",next:"listblock-start"},{include:"basic",noEscape:!0},e,{defaultToken:"list"}],blockquote:[{token:"empty_line",regex:"^\\s*$",next:"start"},{token:"string.blockquote",regex:"^\\s*>\\s*(?:[*+-]|\\d+\\.)?\\s+",next:"blockquote"},{include:"basic",noEscape:!0},{defaultToken:"string.blockquote"}],githubblock:t}),this.normalizeRules()};o.inherits(u,i),t.MarkdownHighlightRules=u})),ace.define("ace/mode/jade_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules","ace/mode/markdown_highlight_rules","ace/mode/scss_highlight_rules","ace/mode/less_highlight_rules","ace/mode/coffee_highlight_rules","ace/mode/javascript_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./text_highlight_rules").TextHighlightRules;e("./markdown_highlight_rules").MarkdownHighlightRules,e("./scss_highlight_rules").ScssHighlightRules,e("./less_highlight_rules").LessHighlightRules,e("./coffee_highlight_rules").CoffeeHighlightRules;var r=e("./javascript_highlight_rules").JavaScriptHighlightRules;function i(e,t){return{token:"entity.name.function.jade",regex:"^\\s*\\:"+e,next:t+"start"}}var l=function(){var e="\\\\(?:x[0-9a-fA-F]{2}|u[0-9a-fA-F]{4}|[0-2][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.)";this.$rules={start:[{token:"keyword.control.import.include.jade",regex:"\\s*\\binclude\\b"},{token:"keyword.other.doctype.jade",regex:"^!!!\\s*(?:[a-zA-Z0-9-_]+)?"},{onMatch:function(e,t,n){return n.unshift(this.next,e.length-2,t),"comment"},regex:/^\s*\/\//,next:"comment_block"},i("markdown","markdown-"),i("sass","sass-"),i("less","less-"),i("coffee","coffee-"),{token:["storage.type.function.jade","entity.name.function.jade","punctuation.definition.parameters.begin.jade","variable.parameter.function.jade","punctuation.definition.parameters.end.jade"],regex:"^(\\s*mixin)( [\\w\\-]+)(\\s*\\()(.*?)(\\))"},{token:["storage.type.function.jade","entity.name.function.jade"],regex:"^(\\s*mixin)( [\\w\\-]+)"},{token:"source.js.embedded.jade",regex:"^\\s*(?:-|=|!=)",next:"js-start"},{token:"string.interpolated.jade",regex:"[#!]\\{[^\\}]+\\}"},{token:"meta.tag.any.jade",regex:/^\s*(?!\w+:)(?:[\w-]+|(?=\.|#)])/,next:"tag_single"},{token:"suport.type.attribute.id.jade",regex:"#\\w+"},{token:"suport.type.attribute.class.jade",regex:"\\.\\w+"},{token:"punctuation",regex:"\\s*(?:\\()",next:"tag_attributes"}],comment_block:[{regex:/^\s*(?:\/\/)?/,onMatch:function(e,t,n){return e.length<=n[1]?"/"==e.slice(-1)?(n[1]=e.length-2,this.next="","comment"):(n.shift(),n.shift(),this.next=n.shift(),"text"):(this.next="","comment")},next:"start"},{defaultToken:"comment"}],tag_single:[{token:"entity.other.attribute-name.class.jade",regex:"\\.[\\w-]+"},{token:"entity.other.attribute-name.id.jade",regex:"#[\\w-]+"},{token:["text","punctuation"],regex:"($)|((?!\\.|#|=|-))",next:"start"}],tag_attributes:[{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:["entity.other.attribute-name.jade","punctuation"],regex:"([a-zA-Z:\\.-]+)(=)?",next:"attribute_strings"},{token:"punctuation",regex:"\\)",next:"start"}],attribute_strings:[{token:"string",regex:"'(?=.)",next:"qstring"},{token:"string",regex:'"(?=.)',next:"qqstring"},{token:"string",regex:"(?=\\S)",next:"tag_attributes"}],qqstring:[{token:"constant.language.escape",regex:e},{token:"string",regex:'[^"\\\\]+'},{token:"string",regex:"\\\\$",next:"qqstring"},{token:"string",regex:'"|$',next:"tag_attributes"}],qstring:[{token:"constant.language.escape",regex:e},{token:"string",regex:"[^'\\\\]+"},{token:"string",regex:"\\\\$",next:"qstring"},{token:"string",regex:"'|$",next:"tag_attributes"}]},this.embedRules(r,"js-",[{token:"text",regex:".$",next:"start"}])};a.inherits(l,o),t.JadeHighlightRules=l})),ace.define("ace/mode/vue_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/css_highlight_rules","ace/mode/typescript_highlight_rules","ace/mode/coffee_highlight_rules","ace/mode/html_highlight_rules","ace/mode/javascript_highlight_rules","ace/mode/stylus_highlight_rules","ace/mode/sass_highlight_rules","ace/mode/scss_highlight_rules","ace/mode/less_highlight_rules","ace/tokenizer","ace/mode/slim_highlight_rules","ace/mode/jade_highlight_rules","ace/mode/javascript"],(function(e,t,n){var a=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var a,o,r=n.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(l){o={error:l}}finally{try{a&&!a.done&&(n=r.return)&&n.call(r)}finally{if(o)throw o.error}}return i},o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var a,o=0,r=t.length;o<r;o++)!a&&o in t||(a||(a=Array.prototype.slice.call(t,0,o)),a[o]=t[o]);return e.concat(a||Array.prototype.slice.call(t))},r=e("../lib/oop"),i=e("./css_highlight_rules").CssHighlightRules,l=e("./typescript_highlight_rules").TypeScriptHighlightRules,s=e("./coffee_highlight_rules").CoffeeHighlightRules,u=e("./html_highlight_rules").HtmlHighlightRules,c=e("./javascript_highlight_rules").JavaScriptHighlightRules,g=e("./stylus_highlight_rules").StylusHighlightRules,d=e("./sass_highlight_rules").SassHighlightRules,m=e("./scss_highlight_rules").ScssHighlightRules,p=e("./less_highlight_rules").LessHighlightRules,h=e("../tokenizer").Tokenizer,x=e("./slim_highlight_rules").SlimHighlightRules,f=e("./jade_highlight_rules").JadeHighlightRules,b=e("./javascript").Mode,k=function(e){this.embedLangRules=function(e,t,n,a){var o=a?"(?=[^>]*"+a+"\\s*=\\s*['\"]"+n+"['\"]))":"(?=\\s|>|$))";this.$rules.start.unshift({token:["meta.tag.punctuation.tag-open.xml","meta.tag."+t+".tag-name.xml"],regex:"(<)("+t+o,next:[{token:"meta.tag.punctuation.tag-close."+t+".xml",regex:"/?>",next:n+"-start"},{include:"attributes"}]}),this.$rules[t+"-end"]=[{include:"attributes"},{token:"meta.tag.punctuation.tag-close.xml",regex:"/?>",next:"start",onMatch:function(e,t,n){return n.splice(0),this.token}}],this.embedRules(e,n+"-",[{token:["meta.tag.punctuation.end-tag-open.xml","meta.tag."+t+".tag-name.xml"],regex:"(</)("+t+"(?=\\s|>|$))",next:t+"-end"},{token:"string.cdata.xml",regex:"<\\!\\[CDATA\\["},{token:"string.cdata.xml",regex:"\\]\\]>"}])};var t=(new u).getRules();t.start=[{include:"vue-interpolations"}].concat(t.start),t["vue-interpolations"]=[{token:"punctuation",regex:/\{\{\{?/,next:"js-interpolation-start"}];var n=this;t.tag_stuff.unshift({token:"string",regex:/(?:\b(v-)|(:|@))(\[?[a-zA-Z\-.]+\]?)(?:(\:\[?[a-zA-Z\-]+\]?))?((?:\.[a-zA-Z\-]+)*)(\s*)(=)(\s*)(["'])/,onMatch:function(e,t,n){var a=e[e.length-1];n.unshift(a,t);var o=new RegExp(this.regex).exec(e);if(!o)return"text";for(var r=[],i=["entity.other.attribute-name.xml","punctuation.separator.key-value.xml","entity.other.attribute-name.xml","entity.other.attribute-name.xml","entity.other.attribute-name.xml","text","punctuation.separator.key-value.xml","text","string"],l=0,s=i.length;l<s;l++)o[l+1]&&(r[r.length]={type:i[l],value:o[l+1]});return r},next:[{token:"string",regex:/$/,next:"tag_stuff"},{token:"string",regex:/.*/,onMatch:function(e,t,r,i){var l=r[0],s=e.split(l),u=s[0];if(this.next="",s.length>1){r.shift();var c=r.shift(),g=new h(n.$rules).getLineTokens(s.slice(1).join(l),c);g.tokens.unshift({type:"string",value:l}),this.next=Array.isArray(g.state)?g.state[g.state.length-1]:g.state}var d=(new b).getTokenizer().getLineTokens(u,"start").tokens;return g&&d.push.apply(d,o([],a(g.tokens),!1)),d}}]},{token:"string",regex:'"',next:[{token:"string",regex:'"|$',next:"tag_stuff"},{include:"vue-interpolations"},{defaultToken:"string"}]},{token:"string",regex:"'",next:[{token:"string",regex:"'|$",next:"tag_stuff"},{include:"vue-interpolations"},{defaultToken:"string"}]}),this.$rules=t,this.embedRules(c,"js-interpolation-",[{token:"punctuation",regex:/\}\}\}?/,next:"start"}]),this.embedLangRules(i,"style","css"),this.embedLangRules(g,"style","stylus","lang"),this.embedLangRules(d,"style","sass","lang"),this.embedLangRules(m,"style","scss","lang"),this.embedLangRules(p,"style","less","lang"),this.embedLangRules(l,"script","ts","lang"),this.embedLangRules(s,"script","coffee","lang"),this.embedLangRules(x,"template","slm","lang"),this.embedLangRules(f,"template","jade","lang"),this.embedLangRules(g,"template","stylus","lang"),this.normalizeRules()};r.inherits(k,u),t.VueHighlightRules=k})),ace.define("ace/mode/vue",["require","exports","module","ace/lib/oop","ace/mode/folding/html","ace/lib/lang","ace/mode/behaviour/xml","ace/mode/html_completions","ace/mode/html","ace/mode/vue_highlight_rules"],(function(e,t,n){var a=e("../lib/oop"),o=e("./folding/html").FoldMode,r=e("../lib/lang"),i=e("./behaviour/xml").XmlBehaviour,l=e("./html_completions").HtmlCompletions,s=e("./html").Mode,u=e("./vue_highlight_rules").VueHighlightRules,c=["area","base","br","col","embed","hr","img","input","keygen","link","meta","menuitem","param","source","track","wbr"],g=["li","dt","dd","p","rt","rp","optgroup","option","colgroup","td","th"],d=function(){this.HighlightRules=u,this.foldingRules=new o(this.voidElements,r.arrayToMap(g)),this.$behaviour=new i,this.$completer=new l};a.inherits(d,s),function(){this.blockComment={start:"\x3c!--",end:"--\x3e"},this.voidElements=r.arrayToMap(c),this.getCompletions=function(e,t,n,a){return this.$completer.getCompletions(e,t,n,a)},this.$id="ace/mode/vue"}.call(d.prototype),t.Mode=d})),ace.require(["ace/mode/vue"],(function(e){S&&(S.exports=e)})));const N={class:"gva-search-box"},M={class:"gva-table-box"},F={class:"gva-btn-list"},L={class:"gva-pagination"},B={class:"flex justify-between items-center"},O={class:"text-lg"},H={style:{float:"right",color:"#8492a6","font-size":"13px"}},V={class:"relative w-full"},D={class:"w-full flex gap-4"},U={class:"flex justify-end w-full"},W={class:"flex justify-end w-full"},P={class:"flex justify-between items-center"},J=Object.assign({name:"ExportTemplate"},{__name:"exportTemplate",setup(E){const S=e({name:"",tableName:"",dbName:"",templateID:"",templateInfo:"",limit:0,order:"",conditions:[],joinTemplate:[]}),j=e(""),J=e([]),Z=e([{label:"=",value:"="},{label:"<>",value:"<>"},{label:">",value:">"},{label:"<",value:"<"},{label:"LIKE",value:"LIKE"},{label:"BETWEEN",value:"BETWEEN"},{label:"NOT BETWEEN",value:"NOT BETWEEN"}]),X=()=>{S.value.conditions.push({from:"",column:"",operator:""})},Y=()=>{S.value.joinTemplate.push({joins:"LEFT JOIN",table:"",on:""})},K=t({name:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],tableName:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],templateID:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}],templateInfo:[{required:!0,message:"",trigger:["input","blur"]},{whitespace:!0,message:"不能只输入空格",trigger:["input","blur"]}]}),G=t({createdAt:[{validator:(e,t,n)=>{re.value.startCreatedAt&&!re.value.endCreatedAt?n(new Error("请填写结束日期")):!re.value.startCreatedAt&&re.value.endCreatedAt?n(new Error("请填写开始日期")):re.value.startCreatedAt&&re.value.endCreatedAt&&(re.value.startCreatedAt.getTime()===re.value.endCreatedAt.getTime()||re.value.startCreatedAt.getTime()>re.value.endCreatedAt.getTime())?n(new Error("开始日期应当早于结束日期")):n()},trigger:"change"}]}),Q=e(),ee=e(),te=e(1),ne=e(0),ae=e(10),oe=e([]),re=e({}),ie=e([]),le=e([]),se=e(!1),ue=async()=>{if(0===J.value.length)return void k({type:"error",message:"请先选择需要参与导出的表"});se.value=!0;const e=await(async()=>{const e={},t=J.value.map((async t=>{const n=await z({businessDB:S.value.dbName,tableName:t});0===n.code&&(e[t]=n.data.columns)}));return await Promise.all(t),e})(),t=await I({prompt:j.value,businessDB:S.value.dbName||"",tableMap:e,command:"autoExportTemplate"});if(se.value=!1,0===t.code){const e=JSON.parse(t.data);S.value.name=e.name,S.value.tableName=e.tableName,S.value.templateID=e.templateID,S.value.templateInfo=JSON.stringify(e.templateInfo,null,2),S.value.joinTemplate=e.joinTemplate}};(async()=>{const e=await $();0===e.code&&(ie.value=e.data.dbList)})();const ce=()=>{S.value.tableName="",S.value.templateInfo="",J.value=[],ge()},ge=async()=>{const e=await A({businessDB:S.value.dbName});0===e.code&&(le.value=e.data.tables),S.value.tableName=""};ge();const de=async e=>{if(!S.value.tableName)return void k({type:"error",message:"请先选择业务库及选择表后再进行操作"});S.value.templateInfo="",se.value=!0;const t=await z({businessDB:S.value.dbName,tableName:S.value.tableName});if(0===t.code){if(e){const e=await I({data:t.data.columns,command:"exportCompletion"});if(0===e.code){const t=JSON.parse(e.data);return se.value=!1,S.value.templateInfo=JSON.stringify(t.templateInfo,null,2),S.value.name=t.name,void(S.value.templateID=t.templateID)}k.warning("AI自动补全失败，已调整为逻辑填写")}const n={};t.data.columns.forEach((e=>{n[e.columnName]=e.columnComment||e.columnName})),S.value.templateInfo=JSON.stringify(n,null,2)}se.value=!1},me=()=>{re.value={},fe()},pe=()=>{var e;null==(e=ee.value)||e.validate((async e=>{e&&(te.value=1,fe())}))},he=e=>{ae.value=e,fe()},xe=e=>{te.value=e,fe()},fe=async()=>{const e=await v({page:te.value,pageSize:ae.value,...re.value});0===e.code&&(oe.value=e.data.list,ne.value=e.data.total,te.value=e.data.page,ae.value=e.data.pageSize)};fe();(async()=>{})();const be=e([]),ke=e=>{be.value=e},ve=async()=>{b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===be.value.length)return void k({type:"warning",message:"请选择要删除的数据"});be.value&&be.value.map((t=>{e.push(t.ID)}));0===(await T({ids:e})).code&&(k({type:"success",message:"删除成功"}),oe.value.length===e.length&&te.value>1&&te.value--,fe())}))},ye=e(""),we=async e=>{0===(await C({ID:e.ID})).code&&(k({type:"success",message:"删除成功"}),1===oe.value.length&&te.value>1&&te.value--,fe())},_e=e(!1),Ce=e(!1),Te=e(""),Re=()=>{ye.value="create",Ce.value=!0},$e=()=>{_e.value=!1,Ce.value=!1,S.value={name:"",tableName:"",templateID:"",templateInfo:"",limit:0,order:"",conditions:[],joinTemplate:[]}},Ae=async()=>{var e;try{JSON.parse(S.value.templateInfo)}catch(n){return void k({type:"error",message:"模板信息格式不正确，请检查"})}const t=JSON.parse(JSON.stringify(S.value));for(let a=0;a<t.conditions.length;a++){if(!t.conditions[a].from||!t.conditions[a].column||!t.conditions[a].operator)return void k({type:"error",message:"请填写完整的导出条件"});t.conditions[a].templateID=t.templateID}for(let a=0;a<t.joinTemplate.length;a++){if(!t.joinTemplate[a].joins||!t.joinTemplate[a].on)return void k({type:"error",message:"请填写完整的关联"});t.joinTemplate[a].templateID=t.templateID}null==(e=Q.value)||e.validate((async e=>{if(!e)return;let n;switch(ye.value){case"create":default:n=await w(t);break;case"update":n=await _(t)}0===n.code&&(k({type:"success",message:"创建/更改成功"}),$e(),fe())}))};return(e,t)=>{const k=n("QuestionFilled"),v=n("el-icon"),w=n("el-tooltip"),_=n("el-date-picker"),C=n("el-form-item"),T=n("el-input"),$=n("el-button"),A=n("el-form"),I=n("el-table-column"),z=n("el-table"),E=n("el-pagination"),ge=n("el-option"),fe=n("el-select"),Ie=n("ai-gva"),ze=n("el-input-number"),qe=n("el-drawer"),Ee=a("loading");return r(),o("div",null,[i(R,{title:"本功能提供同步的表格导出功能，大数据量的异步表格导出功能，可以选择点我定制",href:"https://flipped-aurora.feishu.cn/docx/KwjxdnvatozgwIxGV0rcpkZSn4d"}),l("div",N,[i(A,{ref_key:"elSearchFormRef",ref:ee,inline:!0,model:re.value,class:"demo-form-inline",rules:G,onKeyup:c(pe,["enter"])},{default:s((()=>[i(C,{label:"创建日期",prop:"createdAt"},{label:s((()=>[l("span",null,[t[19]||(t[19]=u(" 创建日期 ")),i(w,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:s((()=>[i(v,null,{default:s((()=>[i(k)])),_:1})])),_:1})])])),default:s((()=>[i(_,{modelValue:re.value.startCreatedAt,"onUpdate:modelValue":t[0]||(t[0]=e=>re.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!re.value.endCreatedAt&&e.getTime()>re.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),t[20]||(t[20]=u(" — ")),i(_,{modelValue:re.value.endCreatedAt,"onUpdate:modelValue":t[1]||(t[1]=e=>re.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!re.value.startCreatedAt&&e.getTime()<re.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),i(C,{label:"模板名称",prop:"name"},{default:s((()=>[i(T,{modelValue:re.value.name,"onUpdate:modelValue":t[2]||(t[2]=e=>re.value.name=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(C,{label:"表名称",prop:"tableName"},{default:s((()=>[i(T,{modelValue:re.value.tableName,"onUpdate:modelValue":t[3]||(t[3]=e=>re.value.tableName=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(C,{label:"模板标识",prop:"templateID"},{default:s((()=>[i(T,{modelValue:re.value.templateID,"onUpdate:modelValue":t[4]||(t[4]=e=>re.value.templateID=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),i(C,null,{default:s((()=>[i($,{type:"primary",icon:"search",onClick:pe},{default:s((()=>t[21]||(t[21]=[u("查询")]))),_:1}),i($,{icon:"refresh",onClick:me},{default:s((()=>t[22]||(t[22]=[u("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])]),l("div",M,[l("div",F,[i($,{type:"primary",icon:"plus",onClick:Re},{default:s((()=>t[23]||(t[23]=[u("新增")]))),_:1}),i($,{icon:"delete",style:{"margin-left":"10px"},disabled:!be.value.length,onClick:ve},{default:s((()=>t[24]||(t[24]=[u("删除")]))),_:1},8,["disabled"])]),i(z,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:oe.value,"row-key":"ID",onSelectionChange:ke},{default:s((()=>[i(I,{type:"selection",width:"55"}),i(I,{align:"left",label:"日期",width:"180"},{default:s((e=>[u(g(d(m)(e.row.CreatedAt)),1)])),_:1}),i(I,{align:"left",label:"数据库",width:"120"},{default:s((e=>[l("span",null,g(e.row.dbName||"GVA库"),1)])),_:1}),i(I,{align:"left",label:"模板标识",prop:"templateID",width:"120"}),i(I,{align:"left",label:"模板名称",prop:"name",width:"120"}),i(I,{align:"left",label:"表名称",prop:"tableName",width:"120"}),i(I,{align:"left",label:"模板信息",prop:"templateInfo","min-width":"120","show-overflow-tooltip":""}),i(I,{align:"left",label:"操作","min-width":"280"},{default:s((e=>[i($,{type:"primary",link:"",icon:"documentCopy",class:"table-button",onClick:t=>(async e=>{let t;const n=await y({ID:e.ID});0===n.code&&(t=JSON.parse(JSON.stringify(n.data.resysExportTemplate)),t.conditions||(t.conditions=[]),t.joinTemplate||(t.joinTemplate=[]),delete t.ID,delete t.CreatedAt,delete t.UpdatedAt,t.templateID=t.templateID+"_copy",t.name=t.name+"_copy",S.value=t,Ce.value=!0)})(e.row)},{default:s((()=>t[25]||(t[25]=[u("复制")]))),_:2},1032,["onClick"]),i($,{type:"primary",link:"",icon:"edit-pen",class:"table-button",onClick:t=>{return n=e.row,Te.value=(a=n.templateID,'<template>\n  \x3c!-- 导出组件 --\x3e\n  <ExportExcel templateId="'.concat(a,'" :condition="condition" :limit="limit" :offset="offset" :order="order" />\n\n  \x3c!-- 导入组件 handleSuccess为导入成功后的回调函数 --\x3e\n  <ImportExcel templateId="').concat(a,'" @on-success="handleSuccess" />\n\n  \x3c!-- 导出模板 --\x3e\n  <ExportTemplate templateId="').concat(a,"\" />\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n// 导出组件\nimport ExportExcel from '@/components/exportExcel/exportExcel.vue';\n// 导入组件\nimport ImportExcel from '@/components/exportExcel/importExcel.vue';\n// 导出模板组件\nimport ExportTemplate from '@/components/exportExcel/exportTemplate.vue';\n\nconst condition = ref({}); // 查询条件\nconst limit = ref(10); // 最大条数限制\nconst offset = ref(0); // 偏移量\nconst order = ref('id desc'); // 排序条件\n\nconst handleSuccess = (res) => {\n  console.log(res);\n  // 导入成功的回调函数\n};\n<\/script>")),void(_e.value=!0);var n,a}},{default:s((()=>t[26]||(t[26]=[u("代码")]))),_:2},1032,["onClick"]),i($,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:t=>(async e=>{const t=await y({ID:e.ID});ye.value="update",0===t.code&&(S.value=t.data.resysExportTemplate,S.value.conditions||(S.value.conditions=[]),S.value.joinTemplate||(S.value.joinTemplate=[]),Ce.value=!0)})(e.row)},{default:s((()=>t[27]||(t[27]=[u("变更")]))),_:2},1032,["onClick"]),i($,{type:"primary",link:"",icon:"delete",onClick:t=>{return n=e.row,void b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{we(n)}));var n}},{default:s((()=>t[28]||(t[28]=[u("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),l("div",L,[i(E,{layout:"total, sizes, prev, pager, next, jumper","current-page":te.value,"page-size":ae.value,"page-sizes":[10,30,50,100],total:ne.value,onCurrentChange:xe,onSizeChange:he},null,8,["current-page","page-size","total"])])]),i(qe,{modelValue:Ce.value,"onUpdate:modelValue":t[16]||(t[16]=e=>Ce.value=e),size:"60%","before-close":$e,title:"create"===ye.value?"添加":"修改","show-close":!1,"destroy-on-close":""},{header:s((()=>[l("div",B,[l("span",O,g("create"===ye.value?"添加":"修改"),1),l("div",null,[i($,{onClick:$e},{default:s((()=>t[29]||(t[29]=[u("取 消")]))),_:1}),i($,{type:"primary",onClick:Ae},{default:s((()=>t[30]||(t[30]=[u("确 定")]))),_:1})])])])),default:s((()=>[p((r(),h(A,{ref_key:"elFormRef",ref:Q,model:S.value,"label-position":"right",rules:K,"label-width":"100px","element-loading-text":"小淼正在思考..."},{default:s((()=>[i(C,{label:"业务库",prop:"dbName"},{label:s((()=>[i(w,{content:"注：需要提前到db-list自行配置多数据库，如未配置需配置后重启服务方可使用。若无法选择，请到config.yaml中设置disabled:false，选择导入导出的目标库。",placement:"bottom",effect:"light"},{default:s((()=>[l("div",null,[t[31]||(t[31]=u(" 业务库 ")),i(v,null,{default:s((()=>[i(k)])),_:1})])])),_:1})])),default:s((()=>[i(fe,{modelValue:S.value.dbName,"onUpdate:modelValue":t[5]||(t[5]=e=>S.value.dbName=e),clearable:"",onChange:ce,placeholder:"选择业务库"},{default:s((()=>[(r(!0),o(x,null,f(ie.value,(e=>(r(),h(ge,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:s((()=>[l("div",null,[l("span",null,g(e.aliasName),1),l("span",H,g(e.dbName),1)])])),_:2},1032,["value","label","disabled"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(C,{label:"需用到的表",prop:"tables"},{default:s((()=>[i(fe,{multiple:"",modelValue:J.value,"onUpdate:modelValue":t[6]||(t[6]=e=>J.value=e),clearable:"",placeholder:"使用AI的情况下请选择"},{default:s((()=>[(r(!0),o(x,null,f(le.value,(e=>(r(),h(ge,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),i(C,{label:"AI帮写:",prop:"ai"},{default:s((()=>[l("div",V,[i(T,{type:"textarea",modelValue:j.value,"onUpdate:modelValue":t[7]||(t[7]=e=>j.value=e),clearable:!0,rows:5,placeholder:"试试描述你要做的导出功能让AI帮你完成，在此之前请选择你需要导出的表所在的业务库，如不做选择，则默认使用gva库"},null,8,["modelValue"]),i($,{class:"absolute bottom-2 right-2",type:"primary",onClick:ue},{default:s((()=>[i(v,null,{default:s((()=>[i(Ie)])),_:1}),t[32]||(t[32]=u("帮写"))])),_:1})])])),_:1}),i(C,{label:"表名称:",clearable:"",prop:"tableName"},{default:s((()=>[l("div",D,[i(fe,{modelValue:S.value.tableName,"onUpdate:modelValue":t[8]||(t[8]=e=>S.value.tableName=e),class:"flex-1",filterable:"",placeholder:"请选择表"},{default:s((()=>[(r(!0),o(x,null,f(le.value,(e=>(r(),h(ge,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])))),128))])),_:1},8,["modelValue"]),i($,{disabled:!S.value.tableName,type:"primary",onClick:t[9]||(t[9]=e=>de(!0))},{default:s((()=>[i(v,null,{default:s((()=>[i(Ie)])),_:1}),t[33]||(t[33]=u("自动补全"))])),_:1},8,["disabled"]),i($,{disabled:!S.value.tableName,type:"primary",onClick:t[10]||(t[10]=e=>de(!1))},{default:s((()=>t[34]||(t[34]=[u("自动生成模板")]))),_:1},8,["disabled"])])])),_:1}),i(C,{label:"模板名称:",prop:"name"},{default:s((()=>[i(T,{modelValue:S.value.name,"onUpdate:modelValue":t[11]||(t[11]=e=>S.value.name=e),clearable:!0,placeholder:"请输入模板名称"},null,8,["modelValue"])])),_:1}),i(C,{label:"模板标识:",prop:"templateID"},{default:s((()=>[i(T,{modelValue:S.value.templateID,"onUpdate:modelValue":t[12]||(t[12]=e=>S.value.templateID=e),clearable:!0,placeholder:"模板标识为前端组件需要挂在的标识属性"},null,8,["modelValue"])])),_:1}),i(C,{label:"关联条件:"},{default:s((()=>[(r(!0),o(x,null,f(S.value.joinTemplate,((e,n)=>(r(),o("div",{key:n,class:"flex gap-4 w-full mb-2"},[i(fe,{modelValue:e.joins,"onUpdate:modelValue":t=>e.joins=t,placeholder:"请选择关联方式"},{default:s((()=>[i(ge,{label:"LEFT JOIN",value:"LEFT JOIN"}),i(ge,{label:"INNER JOIN",value:"INNER JOIN"}),i(ge,{label:"RIGHT JOIN",value:"RIGHT JOIN"})])),_:2},1032,["modelValue","onUpdate:modelValue"]),i(T,{modelValue:e.table,"onUpdate:modelValue":t=>e.table=t,placeholder:"请输入关联表"},null,8,["modelValue","onUpdate:modelValue"]),i(T,{modelValue:e.on,"onUpdate:modelValue":t=>e.on=t,placeholder:"关联条件 table1.a = table2.b"},null,8,["modelValue","onUpdate:modelValue"]),i($,{type:"danger",icon:"delete",onClick:()=>S.value.joinTemplate.splice(n,1)},{default:s((()=>t[35]||(t[35]=[u("删除")]))),_:2},1032,["onClick"])])))),128)),l("div",U,[i($,{type:"primary",icon:"plus",onClick:Y},{default:s((()=>t[36]||(t[36]=[u("添加条件")]))),_:1})])])),_:1}),i(C,{label:"模板信息:",prop:"templateInfo"},{default:s((()=>[i(T,{modelValue:S.value.templateInfo,"onUpdate:modelValue":t[13]||(t[13]=e=>S.value.templateInfo=e),type:"textarea",rows:12,clearable:!0,placeholder:'模板信息格式：key标识数据库column列名称（在join模式下需要写为 table.column），value标识导出excel列名称，如key为数据库关键字或函数，请按照关键字的处理模式处理，当前以mysql为例，如下：\n{\n  "table_column1":"第一列",\n  "table_column3":"第三列",\n  "table_column4":"第四列",\n  "`rows`":"我属于数据库关键字或函数",\n}\n如果增加了JOINS导出key应该列为 {table_name1.table_column1:"第一列",table_name2.table_column2:"第二列"}\n如果有重复的列名导出格式应为 {table_name1.table_column1 as key:"第一列",table_name2.table_column2 as key2:"第二列"}\nJOINS模式下不支持导入\n'},null,8,["modelValue"])])),_:1}),i(C,{label:"默认导出条数:"},{default:s((()=>[i(ze,{modelValue:S.value.limit,"onUpdate:modelValue":t[14]||(t[14]=e=>S.value.limit=e),step:1,"step-strictly":!0,precision:0},null,8,["modelValue"])])),_:1}),i(C,{label:"默认排序条件:"},{default:s((()=>[i(T,{modelValue:S.value.order,"onUpdate:modelValue":t[15]||(t[15]=e=>S.value.order=e),placeholder:"例:id desc"},null,8,["modelValue"])])),_:1}),i(C,{label:"导出条件:"},{default:s((()=>[(r(!0),o(x,null,f(S.value.conditions,((e,n)=>(r(),o("div",{key:n,class:"flex gap-4 w-full mb-2"},[i(T,{modelValue:e.from,"onUpdate:modelValue":t=>e.from=t,placeholder:"需要从查询条件取的json key"},null,8,["modelValue","onUpdate:modelValue"]),i(T,{modelValue:e.column,"onUpdate:modelValue":t=>e.column=t,placeholder:"表对应的column"},null,8,["modelValue","onUpdate:modelValue"]),i(fe,{modelValue:e.operator,"onUpdate:modelValue":t=>e.operator=t,placeholder:"请选择查询条件"},{default:s((()=>[(r(!0),o(x,null,f(Z.value,(e=>(r(),h(ge,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"]),i($,{type:"danger",icon:"delete",onClick:()=>S.value.conditions.splice(n,1)},{default:s((()=>t[37]||(t[37]=[u("删除")]))),_:2},1032,["onClick"])])))),128)),l("div",W,[i($,{type:"primary",icon:"plus",onClick:X},{default:s((()=>t[38]||(t[38]=[u("添加条件")]))),_:1})])])),_:1})])),_:1},8,["model","rules"])),[[Ee,se.value]])])),_:1},8,["modelValue","title"]),i(qe,{modelValue:_e.value,"onUpdate:modelValue":t[18]||(t[18]=e=>_e.value=e),size:"60%","before-close":$e,title:"create"===ye.value?"添加":"修改","show-close":!1,"destroy-on-close":""},{header:s((()=>[l("div",P,[t[40]||(t[40]=l("span",{class:"text-lg"},"模板",-1)),l("div",null,[i($,{type:"primary",onClick:$e},{default:s((()=>t[39]||(t[39]=[u("确 定")]))),_:1})])])])),default:s((()=>[i(d(q),{value:Te.value,"onUpdate:value":t[17]||(t[17]=e=>Te.value=e),lang:"vue",theme:"github_dark",class:"h-full"},null,8,["value"])])),_:1},8,["modelValue","title"])])}}});export{J as default};
