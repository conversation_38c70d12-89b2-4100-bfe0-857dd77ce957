/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{ai as e,I as t,J as a,O as s,K as n,g as u,i as o,o as l,w as r,H as i,c as f,v as c,X as m,d,b as p,Y as y,t as I,F as b}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const _=Object.assign({name:"AsyncSubmenu"},{__name:"asyncSubmenu",props:{routerInfo:{default:function(){return null},type:Object}},setup(_){e((e=>({e7e064e6:k.value})));const v=t(),{config:x}=a(v),g=s("isCollapse",{default:!1}),k=n((()=>x.value.layout_side_item_height+"px"));return(e,t)=>{const a=u("el-icon"),s=u("el-sub-menu");return l(),o(s,{ref:"subMenu",index:_.routerInfo.name,class:"gva-sub-menu dark:text-slate-300 relative"},{title:r((()=>[c(g)?(l(),f(b,{key:1},[_.routerInfo.meta.icon?(l(),o(a,{key:0},{default:r((()=>[(l(),o(y(_.routerInfo.meta.icon)))])),_:1})):d("",!0),p("span",null,I(_.routerInfo.meta.title),1)],64)):(l(),f("div",{key:0,class:"flex items-center",style:m({height:k.value})},[_.routerInfo.meta.icon?(l(),o(a,{key:0},{default:r((()=>[(l(),o(y(_.routerInfo.meta.icon)))])),_:1})):d("",!0),p("span",null,I(_.routerInfo.meta.title),1)],4))])),default:r((()=>[i(e.$slots,"default")])),_:3},8,["index"])}}});export{_ as default};
