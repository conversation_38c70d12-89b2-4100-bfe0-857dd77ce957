/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{g as e,a,b as l,d as t,c as o,i,e as n,u,f as s,h as p,s as d}from"./087AC4D233B64EB0api.DOS2t6hl.js";import{t as r}from"./087AC4D233B64EB0stringFun.BxqK0MAg.js";import{_ as c}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{e as m,a as v}from"./087AC4D233B64EB0exportTemplate.CLX4L_9E.js";import{g as f,i as h,o as g,w as b,h as w,E as y,k as _,f as A,v as C,_ as k,I,a as x,ae as V,c as G,b as P,F as B,D,t as E,a7 as U,ab as S}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{b as z}from"./087AC4D233B64EB0autoCode.D_vfnCLg.js";const j={__name:"exportExcel",props:{templateId:{type:String,required:!0},condition:{type:Object,default:()=>({})},limit:{type:Number,default:0},offset:{type:Number,default:0},order:{type:String,default:""}},setup(e){const a=e,l=async()=>{if(""===a.templateId)return void y.error("组件未设置模板ID");const e=JSON.parse(JSON.stringify(a.condition));a.limit&&(e.limit=a.limit),a.offset&&(e.offset=a.offset),a.order&&(e.order=a.order);const l=Object.entries(e).map((([e,a])=>"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(a)))).join("&"),t=await m({templateID:a.templateId,params:l});if(0===t.code){y.success("创建导出任务成功，开始下载");const e="".concat("/api").concat(t.data);window.open(e,"_blank")}};return(e,a)=>{const t=f("el-button");return g(),h(t,{type:"primary",icon:"download",onClick:l},{default:b((()=>a[0]||(a[0]=[w("导出")]))),_:1})}}},T={__name:"exportTemplate",props:{templateId:{type:String,required:!0}},setup(e){const a=e,l=async()=>{if(""===a.templateId)return void y.error("组件未设置模板ID");const e=await v({templateID:a.templateId});if(0===e.code){y.success("创建导出任务成功，开始下载");const a="".concat("/api").concat(e.data);window.open(a,"_blank")}};return(e,a)=>{const t=f("el-button");return g(),h(t,{type:"primary",icon:"download",onClick:l},{default:b((()=>a[0]||(a[0]=[w("下载模板")]))),_:1})}}},O={__name:"importExcel",props:{templateId:{type:String,required:!0}},emits:["on-success"],setup(e,{emit:a}){const l=e,t=_().token,o=a,i="".concat("/api","/sysExportTemplate/importExcel?templateID=").concat(l.templateId),n=e=>{0===e.code?(y.success("导入成功"),o("on-success")):y.error(e.msg)};return(e,a)=>{const l=f("el-button"),o=f("el-upload");return g(),h(o,{action:i,"show-file-list":!1,"on-success":n,multiple:!1,headers:{"x-token":C(t)}},{default:b((()=>[A(l,{type:"primary",icon:"upload",class:"ml-3"},{default:b((()=>a[0]||(a[0]=[w(" 导入 ")]))),_:1})])),_:1},8,["headers"])}}},q={class:"gva-search-box"},F={class:"gva-table-box"},N={class:"gva-btn-list"},J={class:"gva-pagination"},R={class:"flex justify-between items-center"},M={class:"flex justify-between items-center"},K={class:"text-lg"},L=k(Object.assign({name:"Api"},{__name:"api",setup(m){const v=I(),_=e=>{const a=W.value.filter((a=>a.value===e))[0];return a&&"".concat(a.label)},k=x([]),L=x({path:"",apiGroup:"",method:"",description:""}),W=x([{value:"POST",label:"创建",type:"success"},{value:"GET",label:"查看",type:""},{value:"PUT",label:"更新",type:"warning"},{value:"DELETE",label:"删除",type:"danger"}]),H=x(""),Q=x({path:[{required:!0,message:"请输入api路径",trigger:"blur"}],apiGroup:[{required:!0,message:"请输入组名称",trigger:"blur"}],method:[{required:!0,message:"请选择请求方式",trigger:"blur"}],description:[{required:!0,message:"请输入api介绍",trigger:"blur"}]}),X=x(1),Y=x(0),Z=x(10),$=x([]),ee=x({}),ae=x([]),le=x({}),te=async()=>{const e=await a();if(0===e.code){const a=e.data.groups;ae.value=a.map((e=>({label:e,value:e}))),le.value=e.data.apiGroupMap}},oe=async(e,a)=>{const l=await i({path:e.path,method:e.method,flag:a});if(0===l.code){if(y({type:"success",message:l.msg}),a)return ge.value.newApis=ge.value.newApis.filter((a=>!(a.path===e.path&&a.method===e.method))),void ge.value.ignoreApis.push(e);ge.value.ignoreApis=ge.value.ignoreApis.filter((a=>!(a.path===e.path&&a.method===e.method))),ge.value.newApis.push(e)}},ie=()=>{be.value=!1},ne=x(!1),ue=async()=>{if(ge.value.newApis.some((e=>!e.apiGroup||!e.description)))return void y({type:"error",message:"存在API未分组或未填写描述"});ne.value=!0;const e=await n(ge.value);ne.value=!1,0===e.code&&(y({type:"success",message:e.msg}),be.value=!1,me())},se=()=>{ee.value={},me()},pe=()=>{X.value=1,me()},de=e=>{Z.value=e,me()},re=e=>{X.value=e,me()},ce=({prop:e,order:a})=>{e&&("ID"===e&&(e="id"),ee.value.orderKey=r(e),ee.value.desc="descending"===a),me()},me=async()=>{const a=await e({page:X.value,pageSize:Z.value,...ee.value});0===a.code&&($.value=a.data.list,Y.value=a.data.total,X.value=a.data.page,Z.value=a.data.pageSize)};me(),te();const ve=e=>{k.value=e},fe=async()=>{S.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=k.value.map((e=>e.ID)),a=await s({ids:e});0===a.code&&(y({type:"success",message:a.msg}),$.value.length===e.length&&X.value>1&&X.value--,me())}))},he=async()=>{S.confirm("确定要刷新缓存吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=await p();0===e.code&&y({type:"success",message:e.msg})}))},ge=x({newApis:[],deleteApis:[],ignoreApis:[]}),be=x(!1),we=async()=>{const e=await d();0===e.code&&(e.data.newApis.forEach((e=>{e.apiGroup=le.value[e.path.split("/")[1]]})),ge.value=e.data,be.value=!0)},ye=x(null),_e=x("新增Api"),Ae=x(!1),Ce=e=>{switch(e){case"addApi":_e.value="新增Api";break;case"edit":_e.value="编辑Api"}H.value=e,Ae.value=!0},ke=()=>{ye.value.resetFields(),L.value={path:"",apiGroup:"",method:"",description:""},Ae.value=!1},Ie=async()=>{ye.value.validate((async e=>{if(e)switch(H.value){case"addApi":0===(await o(L.value)).code&&y({type:"success",message:"添加成功",showClose:!0}),me(),te(),ke();break;case"edit":0===(await u(L.value)).code&&y({type:"success",message:"编辑成功",showClose:!0}),me(),ke();break;default:y({type:"error",message:"未知操作",showClose:!0})}}))},xe=x(!1),Ve=async()=>{xe.value=!0;const e=ge.value.newApis.filter((e=>!e.apiGroup||!e.description)).map((e=>e.path)),a=await z({data:e,command:"apiCompletion"});if(xe.value=!1,0===a.code)try{const e=JSON.parse(a.data);ge.value.newApis.forEach((a=>{const l=e.find((e=>e.path===a.path));l&&(a.apiGroup||(a.apiGroup=l.apiGroup),a.description||(a.description=l.description))}))}catch(l){y({type:"error",message:"AI自动填充失败,请重新生成"})}};return(e,a)=>{const i=f("el-input"),n=f("el-form-item"),u=f("el-option"),s=f("el-select"),p=f("el-button"),d=f("el-form"),r=f("el-table-column"),m=f("el-table"),I=f("el-pagination"),x=f("ai-gva"),z=f("el-icon"),H=f("el-drawer"),le=V("loading");return g(),G("div",null,[P("div",q,[A(d,{ref:"searchForm",inline:!0,model:ee.value},{default:b((()=>[A(n,{label:"路径"},{default:b((()=>[A(i,{modelValue:ee.value.path,"onUpdate:modelValue":a[0]||(a[0]=e=>ee.value.path=e),placeholder:"路径"},null,8,["modelValue"])])),_:1}),A(n,{label:"描述"},{default:b((()=>[A(i,{modelValue:ee.value.description,"onUpdate:modelValue":a[1]||(a[1]=e=>ee.value.description=e),placeholder:"描述"},null,8,["modelValue"])])),_:1}),A(n,{label:"API分组"},{default:b((()=>[A(s,{modelValue:ee.value.apiGroup,"onUpdate:modelValue":a[2]||(a[2]=e=>ee.value.apiGroup=e),clearable:"",placeholder:"请选择"},{default:b((()=>[(g(!0),G(B,null,D(ae.value,(e=>(g(),h(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),A(n,{label:"请求"},{default:b((()=>[A(s,{modelValue:ee.value.method,"onUpdate:modelValue":a[3]||(a[3]=e=>ee.value.method=e),clearable:"",placeholder:"请选择"},{default:b((()=>[(g(!0),G(B,null,D(W.value,(e=>(g(),h(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),A(n,null,{default:b((()=>[A(p,{type:"primary",icon:"search",onClick:pe},{default:b((()=>a[11]||(a[11]=[w(" 查询 ")]))),_:1}),A(p,{icon:"refresh",onClick:se},{default:b((()=>a[12]||(a[12]=[w(" 重置 ")]))),_:1})])),_:1})])),_:1},8,["model"])]),P("div",F,[P("div",N,[A(p,{type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>Ce("addApi"))},{default:b((()=>a[13]||(a[13]=[w(" 新增 ")]))),_:1}),A(p,{icon:"delete",disabled:!k.value.length,onClick:fe},{default:b((()=>a[14]||(a[14]=[w(" 删除 ")]))),_:1},8,["disabled"]),A(p,{icon:"Refresh",onClick:he},{default:b((()=>a[15]||(a[15]=[w(" 刷新缓存 ")]))),_:1}),A(p,{icon:"Compass",onClick:we},{default:b((()=>a[16]||(a[16]=[w(" 同步API ")]))),_:1}),A(T,{"template-id":"api"}),A(j,{"template-id":"api",limit:9999}),A(O,{"template-id":"api",onOnSuccess:me})]),A(m,{data:$.value,onSortChange:ce,onSelectionChange:ve},{default:b((()=>[A(r,{type:"selection",width:"55"}),A(r,{align:"left",label:"id","min-width":"60",prop:"ID",sortable:"custom"}),A(r,{align:"left",label:"API路径","min-width":"150",prop:"path",sortable:"custom"}),A(r,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup",sortable:"custom"}),A(r,{align:"left",label:"API简介","min-width":"150",prop:"description",sortable:"custom"}),A(r,{align:"left",label:"请求","min-width":"150",prop:"method",sortable:"custom"},{default:b((e=>[P("div",null,E(e.row.method)+" / "+E(_(e.row.method)),1)])),_:1}),A(r,{align:"left",fixed:"right",label:"操作","min-width":C(v).operateMinWith},{default:b((e=>[A(p,{icon:"edit",type:"primary",link:"",onClick:a=>(async e=>{const a=await l({id:e.ID});L.value=a.data.api,Ce("edit")})(e.row)},{default:b((()=>a[17]||(a[17]=[w(" 编辑 ")]))),_:2},1032,["onClick"]),A(p,{icon:"delete",type:"primary",link:"",onClick:a=>(async e=>{S.confirm("此操作将永久删除所有角色下该api, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await t(e)).code&&(y({type:"success",message:"删除成功!"}),1===$.value.length&&X.value>1&&X.value--,me(),te())}))})(e.row)},{default:b((()=>a[18]||(a[18]=[w(" 删除 ")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),P("div",J,[A(I,{"current-page":X.value,"page-size":Z.value,"page-sizes":[10,30,50,100],total:Y.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:re,onSizeChange:de},null,8,["current-page","page-size","total"])])]),A(H,{modelValue:be.value,"onUpdate:modelValue":a[5]||(a[5]=e=>be.value=e),size:C(v).drawerSize,"before-close":ie,"show-close":!1},{header:b((()=>[P("div",R,[a[21]||(a[21]=P("span",{class:"text-lg"},"同步路由",-1)),P("div",null,[A(p,{loading:xe.value,onClick:ie},{default:b((()=>a[19]||(a[19]=[w(" 取 消 ")]))),_:1},8,["loading"]),A(p,{type:"primary",loading:ne.value||xe.value,onClick:ue},{default:b((()=>a[20]||(a[20]=[w(" 确 定 ")]))),_:1},8,["loading"])])])])),default:b((()=>[A(c,{title:"同步API，不输入路由分组将不会被自动同步，如果api不需要参与鉴权，可以按忽略按钮进行忽略。"}),P("h4",null,[a[23]||(a[23]=w(" 新增路由 ")),a[24]||(a[24]=P("span",{class:"text-xs text-gray-500 mx-2 font-normal"},"存在于当前路由中，但是不存在于api表",-1)),A(p,{type:"primary",size:"small",onClick:Ve},{default:b((()=>[A(z,{size:"18"},{default:b((()=>[A(x)])),_:1}),a[22]||(a[22]=w(" 自动填充 "))])),_:1})]),U((g(),h(m,{"element-loading-text":"小淼正在思考...",data:ge.value.newApis},{default:b((()=>[A(r,{align:"left",label:"API路径","min-width":"150",prop:"path"}),A(r,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"},{default:b((({row:e})=>[A(s,{modelValue:e.apiGroup,"onUpdate:modelValue":a=>e.apiGroup=a,placeholder:"请选择或新增","allow-create":"",filterable:"","default-first-option":""},{default:b((()=>[(g(!0),G(B,null,D(ae.value,(e=>(g(),h(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:2},1032,["modelValue","onUpdate:modelValue"])])),_:1}),A(r,{align:"left",label:"API简介","min-width":"150",prop:"description"},{default:b((({row:e})=>[A(i,{modelValue:e.description,"onUpdate:modelValue":a=>e.description=a,autocomplete:"off"},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),A(r,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:b((e=>[P("div",null,E(e.row.method)+" / "+E(_(e.row.method)),1)])),_:1}),A(r,{label:"操作","min-width":"150",fixed:"right"},{default:b((({row:e})=>[A(p,{icon:"plus",type:"primary",link:"",onClick:a=>(async e=>{if(!e.apiGroup)return void y({type:"error",message:"请先选择API分组"});if(!e.description)return void y({type:"error",message:"请先填写API描述"});0===(await o(e)).code&&(y({type:"success",message:"添加成功",showClose:!0}),ge.value.newApis=ge.value.newApis.filter((a=>!(a.path===e.path&&a.method===e.method)))),me(),te()})(e)},{default:b((()=>a[25]||(a[25]=[w(" 单条新增 ")]))),_:2},1032,["onClick"]),A(p,{icon:"sunrise",type:"primary",link:"",onClick:a=>oe(e,!0)},{default:b((()=>a[26]||(a[26]=[w(" 忽略 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[le,ne.value||xe.value]]),a[28]||(a[28]=P("h4",null,[w(" 已删除路由 "),P("span",{class:"text-xs text-gray-500 ml-2 font-normal"},"已经不存在于当前项目的路由中，确定同步后会自动从apis表删除")],-1)),A(m,{data:ge.value.deleteApis},{default:b((()=>[A(r,{align:"left",label:"API路径","min-width":"150",prop:"path"}),A(r,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"}),A(r,{align:"left",label:"API简介","min-width":"150",prop:"description"}),A(r,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:b((e=>[P("div",null,E(e.row.method)+" / "+E(_(e.row.method)),1)])),_:1})])),_:1},8,["data"]),a[29]||(a[29]=P("h4",null,[w(" 忽略路由 "),P("span",{class:"text-xs text-gray-500 ml-2 font-normal"},"忽略路由不参与api同步，常见为不需要进行鉴权行为的路由")],-1)),A(m,{data:ge.value.ignoreApis},{default:b((()=>[A(r,{align:"left",label:"API路径","min-width":"150",prop:"path"}),A(r,{align:"left",label:"API分组","min-width":"150",prop:"apiGroup"}),A(r,{align:"left",label:"API简介","min-width":"150",prop:"description"}),A(r,{align:"left",label:"请求","min-width":"150",prop:"method"},{default:b((e=>[P("div",null,E(e.row.method)+" / "+E(_(e.row.method)),1)])),_:1}),A(r,{label:"操作","min-width":"150",fixed:"right"},{default:b((({row:e})=>[A(p,{icon:"sunny",type:"primary",link:"",onClick:a=>oe(e,!1)},{default:b((()=>a[27]||(a[27]=[w(" 取消忽略 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])])),_:1},8,["modelValue","size"]),A(H,{modelValue:Ae.value,"onUpdate:modelValue":a[10]||(a[10]=e=>Ae.value=e),size:C(v).drawerSize,"before-close":ke,"show-close":!1},{header:b((()=>[P("div",M,[P("span",K,E(_e.value),1),P("div",null,[A(p,{onClick:ke},{default:b((()=>a[30]||(a[30]=[w(" 取 消 ")]))),_:1}),A(p,{type:"primary",onClick:Ie},{default:b((()=>a[31]||(a[31]=[w(" 确 定 ")]))),_:1})])])])),default:b((()=>[A(c,{title:"新增API，需要在角色管理内配置权限才可使用"}),A(d,{ref_key:"apiForm",ref:ye,model:L.value,rules:Q.value,"label-width":"80px"},{default:b((()=>[A(n,{label:"路径",prop:"path"},{default:b((()=>[A(i,{modelValue:L.value.path,"onUpdate:modelValue":a[6]||(a[6]=e=>L.value.path=e),autocomplete:"off"},null,8,["modelValue"])])),_:1}),A(n,{label:"请求",prop:"method"},{default:b((()=>[A(s,{modelValue:L.value.method,"onUpdate:modelValue":a[7]||(a[7]=e=>L.value.method=e),placeholder:"请选择",style:{width:"100%"}},{default:b((()=>[(g(!0),G(B,null,D(W.value,(e=>(g(),h(u,{key:e.value,label:"".concat(e.label,"(").concat(e.value,")"),value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),A(n,{label:"api分组",prop:"apiGroup"},{default:b((()=>[A(s,{modelValue:L.value.apiGroup,"onUpdate:modelValue":a[8]||(a[8]=e=>L.value.apiGroup=e),placeholder:"请选择或新增","allow-create":"",filterable:"","default-first-option":""},{default:b((()=>[(g(!0),G(B,null,D(ae.value,(e=>(g(),h(u,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),A(n,{label:"api简介",prop:"description"},{default:b((()=>[A(i,{modelValue:L.value.description,"onUpdate:modelValue":a[9]||(a[9]=e=>L.value.description=e),autocomplete:"off"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","size"])])}}}),[["__scopeId","data-v-3b9f1fec"]]);export{L as default};
