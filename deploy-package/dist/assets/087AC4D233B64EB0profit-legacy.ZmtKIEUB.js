/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function r(e){for(var r=1;r<arguments.length;r++){var o=null!=arguments[r]?arguments[r]:{};r%2?t(Object(o),!0).forEach((function(t){n(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function n(t,r,n){return(r=function(t){var r=function(t,r){if("object"!=e(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var o=n.call(t,r||"default");if("object"!=e(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==e(r)?r:r+""}(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n,t}function o(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */o=function(){return r};var t,r={},n=Object.prototype,a=n.hasOwnProperty,l=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function f(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(t){f=function(e,t,r){return e[t]=r}}function d(e,t,r,n){var o=t&&t.prototype instanceof b?t:b,a=Object.create(o.prototype),i=new j(n||[]);return l(a,"_invoke",{value:O(e,r,i)}),a}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}r.wrap=d;var v="suspendedStart",h="suspendedYield",m="executing",g="completed",y={};function b(){}function w(){}function x(){}var _={};f(_,u,(function(){return this}));var k=Object.getPrototypeOf,V=k&&k(k(E([])));V&&V!==n&&a.call(V,u)&&(_=V);var C=x.prototype=b.prototype=Object.create(_);function P(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function F(t,r){function n(o,l,i,u){var c=p(t[o],t,l);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==e(f)&&a.call(f,"__await")?r.resolve(f.__await).then((function(e){n("next",e,i,u)}),(function(e){n("throw",e,i,u)})):r.resolve(f).then((function(e){s.value=e,i(s)}),(function(e){return n("throw",e,i,u)}))}u(c.arg)}var o;l(this,"_invoke",{value:function(e,t){function a(){return new r((function(r,o){n(e,t,r,o)}))}return o=o?o.then(a,a):a()}})}function O(e,r,n){var o=v;return function(a,l){if(o===m)throw Error("Generator is already running");if(o===g){if("throw"===a)throw l;return{value:t,done:!0}}for(n.method=a,n.arg=l;;){var i=n.delegate;if(i){var u=N(i,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=g,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=m;var c=p(e,r,n);if("normal"===c.type){if(o=n.done?g:h,c.arg===y)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o=g,n.method="throw",n.arg=c.arg)}}}function N(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,N(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var a=p(o,e.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,y;var l=a.arg;return l?l.done?(r[e.resultName]=l.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):l:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function E(r){if(r||""===r){var n=r[u];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,l=function e(){for(;++o<r.length;)if(a.call(r,o))return e.value=r[o],e.done=!1,e;return e.value=t,e.done=!0,e};return l.next=l}}throw new TypeError(e(r)+" is not iterable")}return w.prototype=x,l(C,"constructor",{value:x,configurable:!0}),l(x,"constructor",{value:w,configurable:!0}),w.displayName=f(x,s,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,f(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},r.awrap=function(e){return{__await:e}},P(F.prototype),f(F.prototype,c,(function(){return this})),r.AsyncIterator=F,r.async=function(e,t,n,o,a){void 0===a&&(a=Promise);var l=new F(d(e,t,n,o),a);return r.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},P(C),f(C,s,"Generator"),f(C,u,(function(){return this})),f(C,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},r.values=E,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(n,o){return i.type="throw",i.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var l=this.tryEntries[o],i=l.completion;if("root"===l.tryLoc)return n("end");if(l.tryLoc<=this.prev){var u=a.call(l,"catchLoc"),c=a.call(l,"finallyLoc");if(u&&c){if(this.prev<l.catchLoc)return n(l.catchLoc,!0);if(this.prev<l.finallyLoc)return n(l.finallyLoc)}else if(u){if(this.prev<l.catchLoc)return n(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return n(l.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var l=o?o.completion:{};return l.type=e,l.arg=t,o?(this.method="next",this.next=o.finallyLoc,y):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;S(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:E(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},r}function a(e,t,r,n,o,a,l){try{var i=e[a](l),u=i.value}catch(e){return void r(e)}i.done?t(u):Promise.resolve(u).then(n,o)}function l(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var l=e.apply(t,r);function i(e){a(l,n,o,i,u,"next",e)}function u(e){a(l,n,o,i,u,"throw",e)}i(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,t){"use strict";var n,a,i,u,c,s,f,d,p,v,h,m,g,y,b,w,x,_,k,V;return{setters:[function(e){n=e.s,a=e._,i=e.a,u=e.r,c=e.p,s=e.g,f=e.c,d=e.o,p=e.f,v=e.w,h=e.b,m=e.F,g=e.D,y=e.i,b=e.h,w=e.t,x=e.X,_=e.l,k=e.n,V=e.E}],execute:function(){var t=document.createElement("style");t.textContent=".gva-card-box[data-v-ae9dce50]{margin-bottom:20px}.profit-card[data-v-ae9dce50]{text-align:center}.profit-item[data-v-ae9dce50]{padding:10px}.profit-label[data-v-ae9dce50]{font-size:14px;color:#666;margin-bottom:8px}.profit-value[data-v-ae9dce50]{font-size:24px;font-weight:700;color:#333}.profit-positive[data-v-ae9dce50]{color:#67c23a}.profit-negative[data-v-ae9dce50]{color:#f56c6c}.text-sm[data-v-ae9dce50]{font-size:12px}.text-gray-500[data-v-ae9dce50]{color:#6b7280}\n/*$vite$:1*/",document.head.appendChild(t);var C=function(e){return n({url:"/profit/exportMonthlyOrdersDetail",method:"get",params:{month:e},responseType:"blob"})},P={class:"gva-search-box"},F={class:"gva-table-box"},O={style:{color:"#0369a1","font-weight":"500"}},N={style:{display:"flex","align-items":"center","justify-content":"center"}},L={style:{color:"#dc2626","font-weight":"500","margin-right":"8px"}},S=["title"],j={style:{color:"#a16207","font-weight":"500"}},E={style:{color:"#9333ea","font-weight":"500"}},U={class:"gva-pagination"},z={class:"gva-search-box"},D={class:"gva-card-box"},M={class:"profit-item"},R={class:"profit-value"},T={class:"profit-item"},G={class:"profit-value"},A={class:"profit-item"},I={class:"profit-value",style:{color:"#7c2d12"},title:"仅包含成本价，不含运费佣金等"},Y={class:"profit-item"},q={class:"profit-item"},B={class:"gva-card-box"},K={class:"gva-table-box"},W={class:"gva-btn-list"},$={class:"text-sm text-gray-500"},X={class:"text-sm text-gray-500"},H={style:{color:"#9333ea","font-weight":"500"}},J={style:{color:"#a16207","font-weight":"500"}},Q={class:"gva-pagination"},Z={class:"dialog-footer"},ee={class:"dialog-footer"},te={class:"dialog-footer"},re=Object.assign({name:"OrderProfit"},{__name:"profit",setup:function(e){for(var t=i(),a=i(),re=i(),ne=i(),oe=i("monthly"),ae=i(!1),le=i(!1),ie=i(!1),ue=i(!1),ce=i(1),se=i(0),fe=i(10),de=i([]),pe=i({}),ve=u({}),he=i([]),me=i(0),ge=i({page:1,pageSize:10,year:(new Date).getFullYear(),month:null,shopName:""}),ye=i([]),be=2020;be<=(new Date).getFullYear()+1;be++)ye.value.push(be);var we=i([{label:"1月",value:1},{label:"2月",value:2},{label:"3月",value:3},{label:"4月",value:4},{label:"5月",value:5},{label:"6月",value:6},{label:"7月",value:7},{label:"8月",value:8},{label:"9月",value:9},{label:"10月",value:10},{label:"11月",value:11},{label:"12月",value:12}]),xe=i(!1),_e=i({month:"",advertisingCost:0,notes:""}),ke=u({advertisingCost:[{required:!0,message:"请输入广告费用",trigger:"blur"}]}),Ve=i({totalOrders:0,totalSales:0,totalNetProfit:0,avgProfitMargin:0}),Ce=i(!1),Pe=i({postingNumber:"",costPrice:0,commission:0,shippingCost:0,otherCosts:0}),Fe=u({postingNumber:[{required:!0,message:"请输入货件号",trigger:"blur"}]}),Oe=function(){pe.value={},je()},Ne=function(){var e;null===(e=t.value)||void 0===e||e.validate(function(){var e=l(o().mark((function e(t){return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:ce.value=1,je();case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},Le=function(e){fe.value=e,je()},Se=function(e){ce.value=e,je()},je=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o=r({page:ce.value,pageSize:fe.value},pe.value),n({url:"/profit/getProfitList",method:"get",params:o});case 2:0===(t=e.sent).code&&(de.value=t.data.list,se.value=t.data.total);case 4:case"end":return e.stop()}var o}),e)})));return function(){return e.apply(this,arguments)}}(),Ee=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r=pe.value,n({url:"/profit/getProfitSummary",method:"get",params:r});case 2:0===(t=e.sent).code&&(Ve.value=t.data);case 4:case"end":return e.stop()}var r}),e)})));return function(){return e.apply(this,arguments)}}(),Ue=function(){Pe.value={postingNumber:"",costPrice:0,commission:0,shippingCost:0,otherCosts:0},Ce.value=!0},ze=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:null===(t=re.value)||void 0===t||t.validate(function(){var e=l(o().mark((function e(t){var r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,o=Pe.value,n({url:"/profit/calculateProfit",method:"post",data:o});case 4:0===(r=e.sent).code?(V.success("利润计算成功"),Ce.value=!1,je(),Ee()):V.error(r.msg||"计算失败");case 6:case"end":return e.stop()}var o}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),De=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r=ge.value,n({url:"/profit/getMonthlyProfitList",method:"get",params:r});case 2:0===(t=e.sent).code&&(he.value=t.data.list,me.value=t.data.total);case 4:case"end":return e.stop()}var r}),e)})));return function(){return e.apply(this,arguments)}}(),Me=function(){ge.value.page=1,De()},Re=function(){ge.value={page:1,pageSize:10,year:(new Date).getFullYear(),month:null,shopName:""},De()},Te=function(e){ge.value.page=e,De()},Ge=function(e){ge.value.pageSize=e,De()},Ae=function(e){"monthly"===e.name?De():"detail"===e.name&&(je(),Ee())},Ie=function(){var e=l(o().mark((function e(t,r){var a;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return _e.value={month:t,advertisingCost:r||0,notes:""},e.prev=1,e.next=4,n({url:"/profit/getMonthlyAdvertising",method:"get",params:{month:t}});case 4:0===(a=e.sent).code&&a.data&&(_e.value.advertisingCost=a.data.advertisingCost||0,_e.value.notes=a.data.notes||""),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1);case 11:xe.value=!0;case 12:case"end":return e.stop()}}),e,null,[[1,8]])})));return function(t,r){return e.apply(this,arguments)}}(),Ye=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:null===(t=ne.value)||void 0===t||t.validate(function(){var e=l(o().mark((function e(t){var r;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,o=_e.value,n({url:"/profit/setMonthlyAdvertising",method:"post",data:o});case 5:0===(r=e.sent).code?(V.success("设置成功，净利润和利润率已重新计算"),xe.value=!1,"monthly"===oe.value?De():"detail"===oe.value&&(je(),Ee())):V.error(r.msg||"设置失败"),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(2),V.error("设置失败: "+e.t0.message);case 12:case"end":return e.stop()}var o}),e,null,[[2,9]])})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),qe=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ae.value=!0,e.prev=1,e.next=4,n({url:"/profit/refreshProfitData",method:"post"});case 4:0===(t=e.sent).code?(V.success(t.msg||"刷新成功"),"monthly"===oe.value?De():"detail"===oe.value&&(je(),Ee())):V.error(t.msg||"刷新失败"),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),V.error("刷新失败: "+e.t0.message);case 11:return e.prev=11,ae.value=!1,e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),Be=function(){ie.value=!0},Ke=function(){var e=l(o().mark((function e(){var t;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return le.value=!0,e.prev=1,e.next=4,n({url:"/profit/deleteAllProfitData",method:"delete"});case 4:0===(t=e.sent).code?(V.success(t.msg||"删除成功"),ie.value=!1,"monthly"===oe.value?De():(je(),Ee())):V.error(t.msg||"删除失败"),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),V.error("删除失败: "+e.t0.message);case 11:return e.prev=11,le.value=!1,e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])})));return function(){return e.apply(this,arguments)}}(),We=function(){var e=l(o().mark((function e(t){var r,n,a,l;return o().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return ue.value=!0,e.prev=1,e.next=4,C(t);case 4:r=e.sent,n=new Blob([r.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=window.URL.createObjectURL(n),(l=document.createElement("a")).href=a,l.download="月度订单详细数据_".concat(t,".xlsx"),document.body.appendChild(l),l.click(),document.body.removeChild(l),window.URL.revokeObjectURL(a),V.success("导出成功"),e.next=21;break;case 17:e.prev=17,e.t0=e.catch(1),V.error("导出失败: "+(e.t0.message||"未知错误"));case 21:return e.prev=21,ue.value=!1,e.finish(21);case 24:case"end":return e.stop()}}),e,null,[[1,17,21,24]])})));return function(t){return e.apply(this,arguments)}}();return c((function(){"monthly"===oe.value?De():"detail"===oe.value&&(je(),Ee())})),function(e,r){var n=s("el-option"),o=s("el-select"),l=s("el-form-item"),i=s("el-input"),u=s("el-button"),c=s("el-form"),V=s("el-table-column"),C=s("el-table"),be=s("el-pagination"),je=s("el-tab-pane"),Ee=s("el-date-picker"),De=s("el-card"),$e=s("el-col"),Xe=s("el-row"),He=s("el-alert"),Je=s("el-input-number"),Qe=s("el-dialog"),Ze=s("el-tabs");return d(),f("div",null,[p(Ze,{modelValue:oe.value,"onUpdate:modelValue":r[16]||(r[16]=function(e){return oe.value=e}),onTabClick:Ae},{default:v((function(){return[p(je,{label:"月度统计",name:"monthly"},{default:v((function(){return[h("div",P,[p(c,{ref_key:"elMonthlySearchFormRef",ref:a,inline:!0,model:ge.value,class:"demo-form-inline"},{default:v((function(){return[p(l,{label:"年份"},{default:v((function(){return[p(o,{modelValue:ge.value.year,"onUpdate:modelValue":r[0]||(r[0]=function(e){return ge.value.year=e}),placeholder:"选择年份",style:{width:"120px"}},{default:v((function(){return[(d(!0),f(m,null,g(ye.value,(function(e){return d(),y(n,{key:e,label:e,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),p(l,{label:"月份"},{default:v((function(){return[p(o,{modelValue:ge.value.month,"onUpdate:modelValue":r[1]||(r[1]=function(e){return ge.value.month=e}),placeholder:"选择月份",style:{width:"120px"}},{default:v((function(){return[p(n,{label:"全部",value:null}),(d(!0),f(m,null,g(we.value,(function(e){return d(),y(n,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),p(l,{label:"店铺名称"},{default:v((function(){return[p(i,{modelValue:ge.value.shopName,"onUpdate:modelValue":r[2]||(r[2]=function(e){return ge.value.shopName=e}),placeholder:"搜索条件",style:{width:"200px"}},null,8,["modelValue"])]})),_:1}),p(l,null,{default:v((function(){return[p(u,{type:"primary",icon:"search",onClick:Me},{default:v((function(){return r[24]||(r[24]=[b("查询")])})),_:1}),p(u,{icon:"refresh",onClick:Re},{default:v((function(){return r[25]||(r[25]=[b("重置")])})),_:1})]})),_:1})]})),_:1},8,["model"])]),h("div",F,[p(C,{ref:"monthlyTable",style:{width:"100%"},"tooltip-effect":"dark",data:he.value,"row-key":"month"},{default:v((function(){return[p(V,{align:"center",label:"月份",prop:"month",width:"100"}),p(V,{align:"center",label:"订单数量",prop:"orderCount",width:"100"}),p(V,{align:"center",label:"总销售额",prop:"totalSales",width:"120"},{default:v((function(e){return[h("span",O," ¥"+w(parseFloat(e.row.totalSales||0).toFixed(2)),1)]})),_:1}),p(V,{align:"center",label:"广告费用",prop:"advertisingCost",width:"120"},{default:v((function(e){return[h("div",N,[h("span",L," ¥"+w(parseFloat(e.row.advertisingCost||0).toFixed(2)),1),p(u,{type:"primary",link:"",icon:"edit",size:"small",onClick:function(){return Ie(e.row.month,e.row.advertisingCost)}},{default:v((function(){return r[26]||(r[26]=[b(" 编辑 ")])})),_:2},1032,["onClick"])])]})),_:1}),p(V,{align:"center",label:"总成本",prop:"totalCosts",width:"120"},{default:v((function(e){return[h("span",{style:{color:"#7c2d12","font-weight":"500"},title:"成本价总计: ¥".concat(parseFloat(e.row.totalCosts||0).toFixed(2))}," ¥"+w(parseFloat(e.row.totalCosts||0).toFixed(2)),9,S)]})),_:1}),p(V,{align:"center",label:"物流费用",prop:"totalShipping",width:"120"},{default:v((function(e){return[h("span",j," ¥"+w(parseFloat(e.row.totalShipping||0).toFixed(2)),1)]})),_:1}),p(V,{align:"center",label:"佣金",prop:"totalCommission",width:"120"},{default:v((function(e){return[h("span",E," ¥"+w(parseFloat(e.row.totalCommission||0).toFixed(2)),1)]})),_:1}),p(V,{align:"center",label:"净利润(¥)",prop:"totalProfit",width:"120"},{default:v((function(e){return[h("span",{style:x({color:e.row.totalProfit>=0?"#16a34a":"#dc2626",fontWeight:"600"})}," ¥"+w(parseFloat(e.row.totalProfit||0).toFixed(2)),5)]})),_:1}),p(V,{align:"center",label:"利润率",prop:"profitRate",width:"100"},{default:v((function(e){return[h("span",{style:x({color:e.row.profitRate>=0?"#16a34a":"#dc2626",fontWeight:"600"})},w(parseFloat(e.row.profitRate||0).toFixed(1))+"% ",5)]})),_:1}),p(V,{align:"center",label:"操作",width:"120"},{default:v((function(e){return[p(u,{type:"success",link:"",icon:"download",size:"small",onClick:function(){return We(e.row.month)},loading:ue.value},{default:v((function(){return r[27]||(r[27]=[b(" 导出Excel ")])})),_:2},1032,["onClick","loading"])]})),_:1})]})),_:1},8,["data"]),h("div",U,[p(be,{layout:"total, sizes, prev, pager, next, jumper","current-page":ge.value.page,"page-size":ge.value.pageSize,"page-sizes":[10,25,50,100],total:me.value,onCurrentChange:Te,onSizeChange:Ge},null,8,["current-page","page-size","total"])])])]})),_:1}),p(je,{label:"详细统计",name:"detail"},{default:v((function(){return[h("div",z,[p(c,{ref_key:"elSearchFormRef",ref:t,inline:!0,model:pe.value,class:"demo-form-inline",rules:ve,onKeyup:_(Ne,["enter"])},{default:v((function(){return[p(l,{label:"开始日期",prop:"startDate"},{default:v((function(){return[p(Ee,{modelValue:pe.value.startDate,"onUpdate:modelValue":r[3]||(r[3]=function(e){return pe.value.startDate=e}),type:"date",placeholder:"选择开始日期"},null,8,["modelValue"])]})),_:1}),p(l,{label:"结束日期",prop:"endDate"},{default:v((function(){return[p(Ee,{modelValue:pe.value.endDate,"onUpdate:modelValue":r[4]||(r[4]=function(e){return pe.value.endDate=e}),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])]})),_:1}),p(l,{label:"店铺名称",prop:"shopName"},{default:v((function(){return[p(i,{modelValue:pe.value.shopName,"onUpdate:modelValue":r[5]||(r[5]=function(e){return pe.value.shopName=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(l,{label:"产品名称",prop:"productName"},{default:v((function(){return[p(i,{modelValue:pe.value.productName,"onUpdate:modelValue":r[6]||(r[6]=function(e){return pe.value.productName=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(l,{label:"SKU",prop:"sku"},{default:v((function(){return[p(i,{modelValue:pe.value.sku,"onUpdate:modelValue":r[7]||(r[7]=function(e){return pe.value.sku=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(l,{label:"货件号",prop:"postingNumber"},{default:v((function(){return[p(i,{modelValue:pe.value.postingNumber,"onUpdate:modelValue":r[8]||(r[8]=function(e){return pe.value.postingNumber=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(l,null,{default:v((function(){return[p(u,{type:"primary",icon:"search",onClick:Ne},{default:v((function(){return r[28]||(r[28]=[b("查询")])})),_:1}),p(u,{icon:"refresh",onClick:Oe},{default:v((function(){return r[29]||(r[29]=[b("重置")])})),_:1})]})),_:1})]})),_:1},8,["model","rules"])]),h("div",D,[p(Xe,{gutter:20},{default:v((function(){return[p($e,{span:4},{default:v((function(){return[p(De,{class:"profit-card"},{default:v((function(){return[h("div",M,[r[30]||(r[30]=h("div",{class:"profit-label"},"总订单数",-1)),h("div",R,w(Ve.value.totalOrders),1)])]})),_:1})]})),_:1}),p($e,{span:5},{default:v((function(){return[p(De,{class:"profit-card"},{default:v((function(){var e;return[h("div",T,[r[31]||(r[31]=h("div",{class:"profit-label"},"总销售额",-1)),h("div",G,"¥"+w((null===(e=Ve.value.totalSales)||void 0===e?void 0:e.toFixed(2))||"0.00"),1)])]})),_:1})]})),_:1}),p($e,{span:5},{default:v((function(){return[p(De,{class:"profit-card"},{default:v((function(){var e;return[h("div",A,[r[32]||(r[32]=h("div",{class:"profit-label"},"总成本",-1)),h("div",I," ¥"+w((null===(e=Ve.value.totalCosts)||void 0===e?void 0:e.toFixed(2))||"0.00"),1)])]})),_:1})]})),_:1}),p($e,{span:4},{default:v((function(){return[p(De,{class:"profit-card"},{default:v((function(){var e;return[h("div",Y,[r[33]||(r[33]=h("div",{class:"profit-label"},"总净利润(¥)",-1)),h("div",{class:k(["profit-value",Ve.value.totalNetProfit>=0?"profit-positive":"profit-negative"])}," ¥"+w((null===(e=Ve.value.totalNetProfit)||void 0===e?void 0:e.toFixed(2))||"0.00"),3)])]})),_:1})]})),_:1}),p($e,{span:4},{default:v((function(){return[p(De,{class:"profit-card"},{default:v((function(){var e;return[h("div",q,[r[34]||(r[34]=h("div",{class:"profit-label"},"平均利润率",-1)),h("div",{class:k(["profit-value",Ve.value.avgProfitMargin>=0?"profit-positive":"profit-negative"])},w((null===(e=Ve.value.avgProfitMargin)||void 0===e?void 0:e.toFixed(2))||"0.00")+"% ",3)])]})),_:1})]})),_:1})]})),_:1})]),h("div",B,[p(He,{title:"利润统计说明",type:"info",closable:!1,"show-icon":""},{default:v((function(){return r[35]||(r[35]=[h("p",{style:{margin:"0","font-size":"14px"}},[h("strong",null,"总成本"),b(" = 产品成本价 × 数量"),h("br"),h("strong",null,"订单时间"),b(" = 订单同步到系统的时间（接近实际下单时间）"),h("br"),h("span",{style:{color:"#666","font-size":"12px"}}," 注：总成本不包含运费、佣金等费用，因为这些费用由Ozon平台自动扣除，无需单独支付 ")],-1)])})),_:1})]),h("div",K,[h("div",W,[p(u,{type:"primary",icon:"plus",onClick:Ue},{default:v((function(){return r[36]||(r[36]=[b("计算利润")])})),_:1}),p(u,{type:"success",icon:"refresh",onClick:qe,loading:ae.value},{default:v((function(){return r[37]||(r[37]=[b("刷新汇总")])})),_:1},8,["loading"]),p(u,{type:"danger",icon:"delete",onClick:Be,loading:le.value},{default:v((function(){return r[38]||(r[38]=[b("删除所有数据")])})),_:1},8,["loading"])]),p(C,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:de.value,"row-key":"ID"},{default:v((function(){return[p(V,{align:"left",label:"日期",prop:"orderDate",width:"120"},{default:v((function(e){return[b(w((t=e.row.orderDate,t?new Date(t).toLocaleDateString("zh-CN"):"")),1)];var t})),_:1}),p(V,{align:"left",label:"店铺名称",prop:"shopName",width:"120"}),p(V,{align:"left",label:"货件号",prop:"postingNumber",width:"150"}),p(V,{align:"left",label:"订单号",prop:"orderNumber",width:"150"}),p(V,{align:"left",label:"产品信息",width:"200"},{default:v((function(e){return[h("div",null,[h("div",null,[h("strong",null,w(e.row.productName),1)]),h("div",$,"SKU: "+w(e.row.sku),1),h("div",X,"数量: "+w(e.row.quantity),1)])]})),_:1}),p(V,{align:"left",label:"销售价格",prop:"salePrice",width:"100"},{default:v((function(e){var t;return[b(" ¥"+w((null===(t=e.row.salePrice)||void 0===t?void 0:t.toFixed(2))||"0.00"),1)]})),_:1}),p(V,{align:"left",label:"成本价格",prop:"costPrice",width:"100"},{default:v((function(e){var t;return[b(" ¥"+w((null===(t=e.row.costPrice)||void 0===t?void 0:t.toFixed(2))||"0.00"),1)]})),_:1}),p(V,{align:"left",label:"佣金",prop:"commission",width:"100"},{default:v((function(e){var t;return[h("span",H," ¥"+w((null===(t=e.row.commission)||void 0===t?void 0:t.toFixed(2))||"0.00"),1)]})),_:1}),p(V,{align:"left",label:"物流费用",prop:"shippingCost",width:"100"},{default:v((function(e){var t;return[h("span",J," ¥"+w((null===(t=e.row.shippingCost)||void 0===t?void 0:t.toFixed(2))||"0.00"),1)]})),_:1}),p(V,{align:"left",label:"净利润(¥)",prop:"netProfit",width:"100"},{default:v((function(e){var t;return[h("span",{class:k(e.row.netProfit>=0?"profit-positive":"profit-negative")}," ¥"+w((null===(t=e.row.netProfit)||void 0===t?void 0:t.toFixed(2))||"0.00"),3)]})),_:1}),p(V,{align:"left",label:"利润率",prop:"profitMargin",width:"100"},{default:v((function(e){var t;return[h("span",{class:k(e.row.profitMargin>=0?"profit-positive":"profit-negative")},w((null===(t=e.row.profitMargin)||void 0===t?void 0:t.toFixed(2))||"0.00")+"% ",3)]})),_:1}),p(V,{align:"left",label:"状态",prop:"status",width:"100"})]})),_:1},8,["data"]),h("div",Q,[p(be,{layout:"total, sizes, prev, pager, next, jumper","current-page":ce.value,"page-size":fe.value,"page-sizes":[10,30,50,100],total:se.value,onCurrentChange:Se,onSizeChange:Le},null,8,["current-page","page-size","total"])])]),p(Qe,{modelValue:Ce.value,"onUpdate:modelValue":r[15]||(r[15]=function(e){return Ce.value=e}),title:"计算订单利润",width:"500px"},{footer:v((function(){return[h("div",Z,[p(u,{onClick:r[14]||(r[14]=function(e){return Ce.value=!1})},{default:v((function(){return r[39]||(r[39]=[b("取消")])})),_:1}),p(u,{type:"primary",onClick:ze},{default:v((function(){return r[40]||(r[40]=[b("确定")])})),_:1})])]})),default:v((function(){return[p(He,{title:"利润计算公式：净利润 = 售价 - (成本价 + 运费 + 佣金 + 其他费用)",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),p(c,{ref_key:"calculateFormRef",ref:re,model:Pe.value,rules:Fe,"label-width":"100px"},{default:v((function(){return[p(l,{label:"货件号",prop:"postingNumber"},{default:v((function(){return[p(i,{modelValue:Pe.value.postingNumber,"onUpdate:modelValue":r[9]||(r[9]=function(e){return Pe.value.postingNumber=e}),placeholder:"请输入货件号"},null,8,["modelValue"])]})),_:1}),p(l,{label:"成本价格",prop:"costPrice"},{default:v((function(){return[p(Je,{modelValue:Pe.value.costPrice,"onUpdate:modelValue":r[10]||(r[10]=function(e){return Pe.value.costPrice=e}),precision:2,min:0,placeholder:"请输入成本价格"},null,8,["modelValue"])]})),_:1}),p(l,{label:"佣金",prop:"commission"},{default:v((function(){return[p(Je,{modelValue:Pe.value.commission,"onUpdate:modelValue":r[11]||(r[11]=function(e){return Pe.value.commission=e}),precision:2,min:0,placeholder:"请输入佣金"},null,8,["modelValue"])]})),_:1}),p(l,{label:"运费",prop:"shippingCost"},{default:v((function(){return[p(Je,{modelValue:Pe.value.shippingCost,"onUpdate:modelValue":r[12]||(r[12]=function(e){return Pe.value.shippingCost=e}),precision:2,min:0,placeholder:"请输入运费"},null,8,["modelValue"])]})),_:1}),p(l,{label:"其他费用",prop:"otherCosts"},{default:v((function(){return[p(Je,{modelValue:Pe.value.otherCosts,"onUpdate:modelValue":r[13]||(r[13]=function(e){return Pe.value.otherCosts=e}),precision:2,min:0,placeholder:"请输入其他费用"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"])]})),_:1})]})),_:1},8,["modelValue"]),p(Qe,{modelValue:xe.value,"onUpdate:modelValue":r[21]||(r[21]=function(e){return xe.value=e}),title:"编辑广告费用",width:"500px"},{footer:v((function(){return[h("div",ee,[p(u,{onClick:r[20]||(r[20]=function(e){return xe.value=!1})},{default:v((function(){return r[41]||(r[41]=[b("取消")])})),_:1}),p(u,{type:"primary",onClick:Ye},{default:v((function(){return r[42]||(r[42]=[b("确定")])})),_:1})])]})),default:v((function(){return[p(c,{ref_key:"advertisingFormRef",ref:ne,model:_e.value,rules:ke,"label-width":"100px"},{default:v((function(){return[p(l,{label:"月份",prop:"month"},{default:v((function(){return[p(i,{modelValue:_e.value.month,"onUpdate:modelValue":r[17]||(r[17]=function(e){return _e.value.month=e}),disabled:""},null,8,["modelValue"])]})),_:1}),p(l,{label:"广告费用",prop:"advertisingCost"},{default:v((function(){return[p(Je,{modelValue:_e.value.advertisingCost,"onUpdate:modelValue":r[18]||(r[18]=function(e){return _e.value.advertisingCost=e}),precision:2,min:0,placeholder:"请输入广告费用",style:{width:"100%"}},null,8,["modelValue"])]})),_:1}),p(l,{label:"备注",prop:"notes"},{default:v((function(){return[p(i,{modelValue:_e.value.notes,"onUpdate:modelValue":r[19]||(r[19]=function(e){return _e.value.notes=e}),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["modelValue"]),p(Qe,{modelValue:ie.value,"onUpdate:modelValue":r[23]||(r[23]=function(e){return ie.value=e}),title:"删除确认",width:"500px"},{footer:v((function(){return[h("div",te,[p(u,{onClick:r[22]||(r[22]=function(e){return ie.value=!1})},{default:v((function(){return r[43]||(r[43]=[b("取消")])})),_:1}),p(u,{type:"danger",onClick:Ke,loading:le.value},{default:v((function(){return r[44]||(r[44]=[b("确认删除")])})),_:1},8,["loading"])])]})),default:v((function(){return[p(He,{title:"警告：此操作将永久删除所有利润数据和广告费用记录，且无法恢复！",type:"warning",closable:!1,style:{"margin-bottom":"20px"}}),r[45]||(r[45]=h("p",{style:{"margin-bottom":"20px",color:"#606266"}}," 确定要删除所有利润数据吗？这将包括： ",-1)),r[46]||(r[46]=h("ul",{style:{"margin-bottom":"20px",color:"#606266","padding-left":"20px"}},[h("li",null,"所有订单利润记录"),h("li",null,"所有月度广告费用记录")],-1)),r[47]||(r[47]=h("p",{style:{color:"#F56C6C","font-weight":"bold"}}," 此操作不可撤销，请谨慎操作！ ",-1))]})),_:1},8,["modelValue"])])}}});e("default",a(re,[["__scopeId","data-v-ae9dce50"]]))}}}))}();
