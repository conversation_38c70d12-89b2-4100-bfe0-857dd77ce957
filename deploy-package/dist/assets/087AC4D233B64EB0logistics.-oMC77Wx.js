/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as e,_ as l,a,r as o,p as t,g as i,c as s,o as d,b as u,f as r,w as n,h as c,l as m,F as v,D as p,i as y,d as g,t as f,v as h,ax as b,a7 as V,a8 as _,E as w,ab as k,j as x}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const D=l=>e({url:"/logistics/createLogistics",method:"post",data:l}),C=l=>e({url:"/logistics/findLogistics",method:"get",params:l}),P={class:"gva-search-box"},U={class:"gva-table-box"},z={class:"gva-btn-list"},I={class:"warehouse-container"},A={class:"warehouse-title"},T={class:"warehouse-name"},F={key:0},W={key:1,class:"text-gray-400"},O={key:0},B={key:1,class:"text-gray-400"},K={key:0},R={key:1,class:"text-gray-400"},L={class:"flex flex-col gap-1"},S={key:0,class:"no-logistics"},N={key:0,class:"no-warehouses"},j={class:"dialog-footer"},E=l(Object.assign({name:"Logistics"},{__name:"logistics",setup(l){const E=a([]),q=a(""),G=a([]),H=a({}),J=a([]),M=a({shopName:"",shopClientID:"",ozonDeliveryID:void 0,ozonWarehouseID:void 0,name:"",provider:"",serviceType:"",description:"",isActive:!0,basePrice:void 0,pricePerKg:void 0,pricePerCubic:void 0,minWeight:void 0,maxWeight:void 0,estimatedDays:void 0,commissionRate:void 0,pricingFormula:"",formulaParams:void 0,sortOrder:0,ozonData:void 0});o({});const Q=a(),X=a(),Y=a(!1),Z=a(""),$=async()=>{try{const l=await e({url:"/logistics/getShopWarehouseLogistics",method:"get"});0===l.code&&(E.value=l.data||[],E.value.length>0&&!q.value&&(q.value=E.value[0].name))}catch(l){w.error("获取数据失败")}},ee=e=>{q.value=e.name,G.value=[]},le=()=>{H.value={}},ae=()=>{},oe=e=>{J.value=e};t((()=>{$()}));const te=async()=>{k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const l=[];if(0===J.value.length)return void w({type:"warning",message:"请选择要删除的数据"});J.value&&J.value.map((e=>{l.push(e.ID)}));var a;0===(await(a={IDs:l},e({url:"/logistics/deleteLogisticsByIds",method:"delete",params:a}))).code&&(w({type:"success",message:"删除成功"}),$())}))},ie=async l=>{var a;0===(await(a={ID:l.ID},e({url:"/logistics/deleteLogistics",method:"delete",params:a}))).code&&(w({type:"success",message:"删除成功"}),$())},se=()=>{Z.value="create",Y.value=!0},de=()=>{Y.value=!1,M.value={shopName:"",shopClientID:"",ozonDeliveryID:void 0,ozonWarehouseID:void 0,name:"",provider:"",serviceType:"",description:"",isActive:!0,basePrice:void 0,pricePerKg:void 0,pricePerCubic:void 0,minWeight:void 0,maxWeight:void 0,estimatedDays:void 0,commissionRate:void 0,pricingFormula:"",formulaParams:void 0,sortOrder:0,ozonData:void 0}},ue=async()=>{var l;null==(l=X.value)||l.validate((async l=>{if(!l)return;let a;switch(Z.value){case"create":default:a=await D(M.value);break;case"update":a=await(o=M.value,e({url:"/logistics/updateLogistics",method:"put",data:o}))}var o;0===a.code&&(w({type:"success",message:"创建/更改成功"}),de(),$())}))};a({});const re=async()=>{k.confirm("确定要同步Ozon物流信息吗？这将从所有Ozon店铺拉取最新的物流方式信息。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then((async()=>{const l=x.service({lock:!0,text:"正在同步Ozon物流信息...",background:"rgba(0, 0, 0, 0.7)"});try{0===(await e({url:"/logistics/syncOzonLogistics",method:"post"})).code&&(w({type:"success",message:"同步成功"}),$())}catch(a){w({type:"error",message:"同步失败: "+a.message})}finally{l.close()}}))};return(e,l)=>{const a=i("el-input"),o=i("el-form-item"),t=i("el-option"),x=i("el-select"),D=i("el-button"),$=i("el-form"),ne=i("el-icon"),ce=i("el-tag"),me=i("el-table-column"),ve=i("el-table"),pe=i("el-empty"),ye=i("el-collapse-item"),ge=i("el-collapse"),fe=i("el-tab-pane"),he=i("el-tabs"),be=i("el-switch"),Ve=i("el-input-number"),_e=i("el-dialog");return d(),s("div",null,[u("div",P,[r($,{ref_key:"elSearchFormRef",ref:Q,inline:!0,model:H.value,class:"demo-form-inline",onKeyup:m(ae,["enter"])},{default:n((()=>[r(o,{label:"物流方式名称",prop:"name"},{default:n((()=>[r(a,{modelValue:H.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>H.value.name=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),r(o,{label:"物流服务商",prop:"provider"},{default:n((()=>[r(a,{modelValue:H.value.provider,"onUpdate:modelValue":l[1]||(l[1]=e=>H.value.provider=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),r(o,{label:"是否启用",prop:"isActive"},{default:n((()=>[r(x,{modelValue:H.value.isActive,"onUpdate:modelValue":l[2]||(l[2]=e=>H.value.isActive=e),clearable:"",placeholder:"请选择"},{default:n((()=>[r(t,{label:"启用",value:"true"}),r(t,{label:"禁用",value:"false"})])),_:1},8,["modelValue"])])),_:1}),r(o,null,{default:n((()=>[r(D,{type:"primary",icon:"search",onClick:ae},{default:n((()=>l[21]||(l[21]=[c("查询")]))),_:1}),r(D,{icon:"refresh",onClick:le},{default:n((()=>l[22]||(l[22]=[c("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),u("div",U,[u("div",z,[r(D,{type:"primary",icon:"plus",onClick:se},{default:n((()=>l[23]||(l[23]=[c("新增")]))),_:1}),r(D,{type:"success",icon:"refresh",onClick:re},{default:n((()=>l[24]||(l[24]=[c("同步Ozon物流")]))),_:1}),r(D,{icon:"delete",style:{"margin-left":"10px"},disabled:!J.value.length,onClick:te},{default:n((()=>l[25]||(l[25]=[c("删除")]))),_:1},8,["disabled"])]),r(he,{modelValue:q.value,"onUpdate:modelValue":l[4]||(l[4]=e=>q.value=e),onTabClick:ee,class:"shop-tabs"},{default:n((()=>[(d(!0),s(v,null,p(E.value,(e=>(d(),y(fe,{key:e.name,label:e.name,name:e.name},{default:n((()=>[u("div",I,[r(ge,{modelValue:G.value,"onUpdate:modelValue":l[3]||(l[3]=e=>G.value=e),accordion:""},{default:n((()=>[(d(!0),s(v,null,p(e.warehouses,(e=>(d(),y(ye,{key:e.id,title:"".concat(e.name," (").concat(e.logistics.length,"个物流方式)"),name:e.id.toString()},{title:n((()=>[u("div",A,[r(ne,{class:"warehouse-icon"},{default:n((()=>[r(h(b))])),_:1}),u("span",T,f(e.name),1),r(ce,{size:"small",type:"info",class:"logistics-count"},{default:n((()=>[c(f(e.logistics.length)+"个物流方式 ",1)])),_:2},1024),r(ce,{size:"small",type:"created"===e.status?"success":"warning",class:"warehouse-status"},{default:n((()=>[c(f("created"===e.status?"已创建":e.status),1)])),_:2},1032,["type"])])])),default:n((()=>{return[r(ve,{data:(a=e.logistics,a?a.filter((e=>{var l,a;const o=!H.value.name||(null==(l=e.name)?void 0:l.includes(H.value.name)),t=!H.value.provider||(null==(a=e.provider)?void 0:a.includes(H.value.provider)),i=void 0===H.value.isActive||""===H.value.isActive||e.isActive.toString()===H.value.isActive;return o&&t&&i})):[]),style:{width:"100%"},size:"small",onSelectionChange:oe},{default:n((()=>[r(me,{type:"selection",width:"55"}),r(me,{align:"left",label:"物流方式名称",prop:"name",width:"200"}),r(me,{align:"left",label:"物流服务商",prop:"provider",width:"150"}),r(me,{align:"left",label:"服务类型",prop:"serviceType",width:"120"}),r(me,{align:"left",label:"是否启用",prop:"isActive",width:"100"},{default:n((e=>[r(ce,{type:e.row.isActive?"success":"danger",size:"small"},{default:n((()=>[c(f(e.row.isActive?"启用":"禁用"),1)])),_:2},1032,["type"])])),_:1}),r(me,{align:"left",label:"基础价格",prop:"basePrice",width:"100"},{default:n((e=>[e.row.basePrice?(d(),s("span",F,f(e.row.basePrice.toFixed(2)),1)):(d(),s("span",W,"未设置"))])),_:1}),r(me,{align:"left",label:"每公斤价格",prop:"pricePerKg",width:"100"},{default:n((e=>[e.row.pricePerKg?(d(),s("span",O,f(e.row.pricePerKg.toFixed(2)),1)):(d(),s("span",B,"未设置"))])),_:1}),r(me,{align:"left",label:"预计天数",prop:"estimatedDays",width:"100"}),r(me,{align:"left",label:"佣金比率",prop:"commissionRate",width:"100"},{default:n((e=>[e.row.commissionRate?(d(),s("span",K,f((100*e.row.commissionRate).toFixed(2))+"%",1)):(d(),s("span",R,"使用产品佣金"))])),_:1}),r(me,{align:"left",label:"计价公式",prop:"pricingFormula",width:"200","show-overflow-tooltip":""}),r(me,{align:"left",label:"操作",fixed:"right","min-width":"200"},{default:n((e=>[u("div",L,[r(D,{type:"primary",link:"",icon:"view",size:"small",onClick:l=>(async e=>{const l=await C({ID:e.ID});0===l.code&&(M.value=l.data,Z.value="look",Y.value=!0)})(e.row)},{default:n((()=>l[26]||(l[26]=[c("查看")]))),_:2},1032,["onClick"]),r(D,{type:"primary",link:"",icon:"edit",size:"small",onClick:l=>(async e=>{try{const l=await C({ID:e.ID});Z.value="update",0===l.code?(M.value=l.data,Y.value=!0):w.error("获取物流方式详情失败: "+l.msg)}catch(l){w.error("编辑物流方式失败: "+l.message)}})(e.row)},{default:n((()=>l[27]||(l[27]=[c("编辑")]))),_:2},1032,["onClick"]),r(D,{type:"primary",link:"",icon:"delete",size:"small",onClick:l=>{return a=e.row,void k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ie(a)}));var a}},{default:n((()=>l[28]||(l[28]=[c("删除")]))),_:2},1032,["onClick"])])])),_:1})])),_:2},1032,["data"]),0===e.logistics.length?(d(),s("div",S,[r(pe,{description:"该仓库暂无物流方式","image-size":60})])):g("",!0)];var a})),_:2},1032,["title","name"])))),128))])),_:2},1032,["modelValue"]),0===e.warehouses.length?(d(),s("div",N,[r(pe,{description:"该店铺暂无仓库信息","image-size":80})])):g("",!0)])])),_:2},1032,["label","name"])))),128))])),_:1},8,["modelValue"])]),r(_e,{modelValue:Y.value,"onUpdate:modelValue":l[20]||(l[20]=e=>Y.value=e),"before-close":de,title:"create"===Z.value?"新增物流方式":"update"===Z.value?"修改物流方式":"查看物流方式"},{footer:n((()=>[u("div",j,[r(D,{onClick:de},{default:n((()=>l[29]||(l[29]=[c("取 消")]))),_:1}),V(r(D,{type:"primary",onClick:ue},{default:n((()=>l[30]||(l[30]=[c("确 定")]))),_:1},512),[[_,"look"!==Z.value]])])])),default:n((()=>[r($,{ref_key:"elFormRef",ref:X,model:M.value,"label-position":"right","label-width":"120px",style:{width:"90%"}},{default:n((()=>[r(o,{label:"店铺名称"},{default:n((()=>[r(a,{modelValue:M.value.shopName,"onUpdate:modelValue":l[5]||(l[5]=e=>M.value.shopName=e),clearable:"",readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"物流方式名称"},{default:n((()=>[r(a,{modelValue:M.value.name,"onUpdate:modelValue":l[6]||(l[6]=e=>M.value.name=e),clearable:"",readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"物流服务商"},{default:n((()=>[r(a,{modelValue:M.value.provider,"onUpdate:modelValue":l[7]||(l[7]=e=>M.value.provider=e),clearable:"",readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"服务类型"},{default:n((()=>[r(a,{modelValue:M.value.serviceType,"onUpdate:modelValue":l[8]||(l[8]=e=>M.value.serviceType=e),clearable:"",readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"描述"},{default:n((()=>[r(a,{modelValue:M.value.description,"onUpdate:modelValue":l[9]||(l[9]=e=>M.value.description=e),type:"textarea",readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"是否启用"},{default:n((()=>[r(be,{modelValue:M.value.isActive,"onUpdate:modelValue":l[10]||(l[10]=e=>M.value.isActive=e),disabled:"look"===Z.value},null,8,["modelValue","disabled"])])),_:1}),r(o,{label:"基础价格"},{default:n((()=>[r(Ve,{modelValue:M.value.basePrice,"onUpdate:modelValue":l[11]||(l[11]=e=>M.value.basePrice=e),precision:2,readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"每公斤价格"},{default:n((()=>[r(Ve,{modelValue:M.value.pricePerKg,"onUpdate:modelValue":l[12]||(l[12]=e=>M.value.pricePerKg=e),precision:2,readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"每立方米价格"},{default:n((()=>[r(Ve,{modelValue:M.value.pricePerCubic,"onUpdate:modelValue":l[13]||(l[13]=e=>M.value.pricePerCubic=e),precision:2,readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"最小重量(kg)"},{default:n((()=>[r(Ve,{modelValue:M.value.minWeight,"onUpdate:modelValue":l[14]||(l[14]=e=>M.value.minWeight=e),precision:2,readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"最大重量(kg)"},{default:n((()=>[r(Ve,{modelValue:M.value.maxWeight,"onUpdate:modelValue":l[15]||(l[15]=e=>M.value.maxWeight=e),precision:2,readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"预计送达天数"},{default:n((()=>[r(Ve,{modelValue:M.value.estimatedDays,"onUpdate:modelValue":l[16]||(l[16]=e=>M.value.estimatedDays=e),readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"佣金比率"},{default:n((()=>[r(Ve,{modelValue:M.value.commissionRate,"onUpdate:modelValue":l[17]||(l[17]=e=>M.value.commissionRate=e),min:0,max:1,step:.01,precision:4,readonly:"look"===Z.value,placeholder:"如0.12表示12%，留空则使用产品佣金比率"},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"计价公式"},{default:n((()=>[r(a,{modelValue:M.value.pricingFormula,"onUpdate:modelValue":l[18]||(l[18]=e=>M.value.pricingFormula=e),clearable:"",readonly:"look"===Z.value,placeholder:"例如: basePrice + weight * pricePerKg"},null,8,["modelValue","readonly"])])),_:1}),r(o,{label:"排序"},{default:n((()=>[r(Ve,{modelValue:M.value.sortOrder,"onUpdate:modelValue":l[19]||(l[19]=e=>M.value.sortOrder=e),readonly:"look"===Z.value},null,8,["modelValue","readonly"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-a168163c"]]);export{E as default};
