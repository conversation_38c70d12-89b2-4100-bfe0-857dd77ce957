/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as a}from"./087AC4D233B64EB0authority.YPU6vVUR.js";import{_ as t}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{a as e,g as l,c as o,o as u,f as r,b as i,w as s,h as n,F as d,D as h,i as c,t as y,E as f}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const p={class:"sticky top-0.5 z-10 my-4"},v={class:"clear-both pt-4"},m=Object.assign({name:"Datas"},{__name:"datas",props:{row:{default:function(){return{}},type:Object},authority:{default:function(){return[]},type:Array}},emits:["changeRow"],setup(m,{expose:I,emit:g}){const w=m,A=e([]),b=e(!1),C=a=>{a&&a.forEach((a=>{const t={};t.authorityId=a.authorityId,t.authorityName=a.authorityName,A.value.push(t),a.children&&a.children.length&&C(a.children)}))},_=e([]);C(w.authority),w.row.dataAuthorityId&&w.row.dataAuthorityId.forEach((a=>{const t=A.value&&A.value.filter((t=>t.authorityId===a.authorityId))&&A.value.filter((t=>t.authorityId===a.authorityId))[0];_.value.push(t)}));const k=g,x=()=>{_.value=[...A.value],k("changeRow","dataAuthorityId",_.value),b.value=!0},B=()=>{_.value=A.value.filter((a=>a.authorityId===w.row.authorityId)),k("changeRow","dataAuthorityId",_.value),b.value=!0},E=()=>{const a=[];j(w.row,a),_.value=A.value.filter((t=>a.indexOf(t.authorityId)>-1)),k("changeRow","dataAuthorityId",_.value),b.value=!0},j=(a,t)=>{t.push(a.authorityId),a.children&&a.children.forEach((a=>{j(a,t)}))},D=async()=>{0===(await a(w.row)).code&&f({type:"success",message:"资源设置成功"})},R=()=>{_.value=_.value.filter((a=>a)),k("changeRow","dataAuthorityId",_.value),b.value=!0};return I({enterAndNext:()=>{D()},needConfirm:b}),(a,e)=>{const f=l("el-button"),m=l("el-checkbox"),I=l("el-checkbox-group");return u(),o("div",null,[r(t,{title:"此功能仅用于创建角色和角色的many2many关系表，具体使用还须自己结合表实现业务，详情参考示例代码（客户示例）。此功能不建议使用，建议使用插件市场【组织管理功能（点击前往）】来管理资源权限。",href:"https://plugin.gin-vue-admin.com/#/layout/newPluginInfo?id=36"}),i("div",p,[r(f,{class:"float-left",type:"primary",onClick:x},{default:s((()=>e[1]||(e[1]=[n("全选")]))),_:1}),r(f,{class:"float-left",type:"primary",onClick:B},{default:s((()=>e[2]||(e[2]=[n("本角色")]))),_:1}),r(f,{class:"float-left",type:"primary",onClick:E},{default:s((()=>e[3]||(e[3]=[n("本角色及子角色")]))),_:1}),r(f,{class:"float-right",type:"primary",onClick:D},{default:s((()=>e[4]||(e[4]=[n("确 定")]))),_:1})]),i("div",v,[r(I,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=a=>_.value=a),onChange:R},{default:s((()=>[(u(!0),o(d,null,h(A.value,((a,t)=>(u(),c(m,{key:t,label:a},{default:s((()=>[n(y(a.authorityName),1)])),_:2},1032,["label"])))),128))])),_:1},8,["modelValue"])])])}}});export{m as default};
