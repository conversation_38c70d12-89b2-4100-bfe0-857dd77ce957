/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import{s as l,a,r as t,g as o,c as s,o as d,f as u,b as m,w as n,h as i,E as r}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const c=e=>l({url:"/email/emailTest",method:"post",data:e}),f={class:"gva-form-box"},b=Object.assign({name:"Email"},{__name:"index",setup(l){const b=a(null),p=t({to:"",subject:"",body:""}),_=async()=>{0===(await c()).code&&r.success("发送成功")},V=async()=>{0===(await c()).code&&r.success("发送成功,请查收")};return(l,a)=>{const t=o("el-input"),r=o("el-form-item"),c=o("el-button"),y=o("el-form");return d(),s("div",null,[u(e,{title:"需要提前配置email配置文件，为防止不必要的垃圾邮件，在线体验功能不开放此功能体验。"}),m("div",f,[u(y,{ref_key:"emailForm",ref:b,"label-position":"right","label-width":"80px",model:p},{default:n((()=>[u(r,{label:"目标邮箱"},{default:n((()=>[u(t,{modelValue:p.to,"onUpdate:modelValue":a[0]||(a[0]=e=>p.to=e)},null,8,["modelValue"])])),_:1}),u(r,{label:"邮件"},{default:n((()=>[u(t,{modelValue:p.subject,"onUpdate:modelValue":a[1]||(a[1]=e=>p.subject=e)},null,8,["modelValue"])])),_:1}),u(r,{label:"邮件内容"},{default:n((()=>[u(t,{modelValue:p.body,"onUpdate:modelValue":a[2]||(a[2]=e=>p.body=e),type:"textarea"},null,8,["modelValue"])])),_:1}),u(r,null,{default:n((()=>[u(c,{onClick:_},{default:n((()=>a[3]||(a[3]=[i("发送测试邮件")]))),_:1}),u(c,{onClick:V},{default:n((()=>a[4]||(a[4]=[i("发送邮件")]))),_:1})])),_:1})])),_:1},8,["model"])])])}}});export{b as default};
