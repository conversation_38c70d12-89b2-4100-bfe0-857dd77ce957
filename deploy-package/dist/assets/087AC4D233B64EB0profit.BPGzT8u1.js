/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as e,_ as l,a,r as t,p as o,g as r,c as i,o as u,f as s,w as d,b as n,F as p,D as c,i as v,h as m,t as f,X as g,l as h,n as b,E as y}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const w={class:"gva-search-box"},_={class:"gva-table-box"},V={style:{color:"#0369a1","font-weight":"500"}},C={style:{display:"flex","align-items":"center","justify-content":"center"}},x={style:{color:"#dc2626","font-weight":"500","margin-right":"8px"}},k=["title"],F={style:{color:"#a16207","font-weight":"500"}},P={style:{color:"#9333ea","font-weight":"500"}},N={class:"gva-pagination"},U={class:"gva-search-box"},z={class:"gva-card-box"},D={class:"profit-item"},S={class:"profit-value"},M={class:"profit-item"},R={class:"profit-value"},O={class:"profit-item"},j={class:"profit-value",style:{color:"#7c2d12"},title:"仅包含成本价，不含运费佣金等"},L={class:"profit-item"},A={class:"profit-item"},E={class:"gva-card-box"},T={class:"gva-table-box"},q={class:"gva-btn-list"},B={class:"text-sm text-gray-500"},K={class:"text-sm text-gray-500"},Y={style:{color:"#9333ea","font-weight":"500"}},I={style:{color:"#a16207","font-weight":"500"}},W={class:"gva-pagination"},X={class:"dialog-footer"},G={class:"dialog-footer"},H={class:"dialog-footer"},J=l(Object.assign({name:"OrderProfit"},{__name:"profit",setup(l){const J=a(),Q=a(),Z=a(),$=a(),ee=a("monthly"),le=a(!1),ae=a(!1),te=a(!1),oe=a(!1),re=a(1),ie=a(0),ue=a(10),se=a([]),de=a({}),ne=t({}),pe=a([]),ce=a(0),ve=a({page:1,pageSize:10,year:(new Date).getFullYear(),month:null,shopName:""}),me=a([]);for(let e=2020;e<=(new Date).getFullYear()+1;e++)me.value.push(e);const fe=a([{label:"1月",value:1},{label:"2月",value:2},{label:"3月",value:3},{label:"4月",value:4},{label:"5月",value:5},{label:"6月",value:6},{label:"7月",value:7},{label:"8月",value:8},{label:"9月",value:9},{label:"10月",value:10},{label:"11月",value:11},{label:"12月",value:12}]),ge=a(!1),he=a({month:"",advertisingCost:0,notes:""}),be=t({advertisingCost:[{required:!0,message:"请输入广告费用",trigger:"blur"}]}),ye=a({totalOrders:0,totalSales:0,totalNetProfit:0,avgProfitMargin:0}),we=a(!1),_e=a({postingNumber:"",costPrice:0,commission:0,shippingCost:0,otherCosts:0}),Ve=t({postingNumber:[{required:!0,message:"请输入货件号",trigger:"blur"}]}),Ce=()=>{de.value={},Pe()},xe=()=>{var e;null==(e=J.value)||e.validate((async e=>{e&&(re.value=1,Pe())}))},ke=e=>{ue.value=e,Pe()},Fe=e=>{re.value=e,Pe()},Pe=async()=>{const l=await(a={page:re.value,pageSize:ue.value,...de.value},e({url:"/profit/getProfitList",method:"get",params:a}));var a;0===l.code&&(se.value=l.data.list,ie.value=l.data.total)},Ne=async()=>{const l=await(a=de.value,e({url:"/profit/getProfitSummary",method:"get",params:a}));var a;0===l.code&&(ye.value=l.data)},Ue=()=>{_e.value={postingNumber:"",costPrice:0,commission:0,shippingCost:0,otherCosts:0},we.value=!0},ze=async()=>{var l;null==(l=Z.value)||l.validate((async l=>{if(!l)return;const a=await(t=_e.value,e({url:"/profit/calculateProfit",method:"post",data:t}));var t;0===a.code?(y.success("利润计算成功"),we.value=!1,Pe(),Ne()):y.error(a.msg||"计算失败")}))},De=async()=>{const l=await(a=ve.value,e({url:"/profit/getMonthlyProfitList",method:"get",params:a}));var a;0===l.code&&(pe.value=l.data.list,ce.value=l.data.total)},Se=()=>{ve.value.page=1,De()},Me=()=>{ve.value={page:1,pageSize:10,year:(new Date).getFullYear(),month:null,shopName:""},De()},Re=e=>{ve.value.page=e,De()},Oe=e=>{ve.value.pageSize=e,De()},je=e=>{"monthly"===e.name?De():"detail"===e.name&&(Pe(),Ne())},Le=async(l,a)=>{he.value={month:l,advertisingCost:a||0,notes:""};try{const a=await(t={month:l},e({url:"/profit/getMonthlyAdvertising",method:"get",params:t}));0===a.code&&a.data&&(he.value.advertisingCost=a.data.advertisingCost||0,he.value.notes=a.data.notes||"")}catch(o){}var t;ge.value=!0},Ae=async()=>{var l;null==(l=$.value)||l.validate((async l=>{var a;if(l)try{const l=await(a=he.value,e({url:"/profit/setMonthlyAdvertising",method:"post",data:a}));0===l.code?(y.success("设置成功，净利润和利润率已重新计算"),ge.value=!1,"monthly"===ee.value?De():"detail"===ee.value&&(Pe(),Ne())):y.error(l.msg||"设置失败")}catch(t){y.error("设置失败: "+t.message)}}))},Ee=async()=>{le.value=!0;try{const l=await e({url:"/profit/refreshProfitData",method:"post"});0===l.code?(y.success(l.msg||"刷新成功"),"monthly"===ee.value?De():"detail"===ee.value&&(Pe(),Ne())):y.error(l.msg||"刷新失败")}catch(l){y.error("刷新失败: "+l.message)}finally{le.value=!1}},Te=()=>{te.value=!0},qe=async()=>{ae.value=!0;try{const l=await e({url:"/profit/deleteAllProfitData",method:"delete"});0===l.code?(y.success(l.msg||"删除成功"),te.value=!1,"monthly"===ee.value?De():(Pe(),Ne())):y.error(l.msg||"删除失败")}catch(l){y.error("删除失败: "+l.message)}finally{ae.value=!1}},Be=async l=>{oe.value=!0;try{const a=await(l=>e({url:"/profit/exportMonthlyOrdersDetail",method:"get",params:{month:l},responseType:"blob"}))(l),t=new Blob([a.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=window.URL.createObjectURL(t),r=document.createElement("a");r.href=o,r.download="月度订单详细数据_".concat(l,".xlsx"),document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o),y.success("导出成功")}catch(a){y.error("导出失败: "+(a.message||"未知错误"))}finally{oe.value=!1}};return o((()=>{"monthly"===ee.value?De():"detail"===ee.value&&(Pe(),Ne())})),(e,l)=>{const a=r("el-option"),t=r("el-select"),o=r("el-form-item"),y=r("el-input"),Pe=r("el-button"),Ne=r("el-form"),De=r("el-table-column"),Ke=r("el-table"),Ye=r("el-pagination"),Ie=r("el-tab-pane"),We=r("el-date-picker"),Xe=r("el-card"),Ge=r("el-col"),He=r("el-row"),Je=r("el-alert"),Qe=r("el-input-number"),Ze=r("el-dialog"),$e=r("el-tabs");return u(),i("div",null,[s($e,{modelValue:ee.value,"onUpdate:modelValue":l[16]||(l[16]=e=>ee.value=e),onTabClick:je},{default:d((()=>[s(Ie,{label:"月度统计",name:"monthly"},{default:d((()=>[n("div",w,[s(Ne,{ref_key:"elMonthlySearchFormRef",ref:Q,inline:!0,model:ve.value,class:"demo-form-inline"},{default:d((()=>[s(o,{label:"年份"},{default:d((()=>[s(t,{modelValue:ve.value.year,"onUpdate:modelValue":l[0]||(l[0]=e=>ve.value.year=e),placeholder:"选择年份",style:{width:"120px"}},{default:d((()=>[(u(!0),i(p,null,c(me.value,(e=>(u(),v(a,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(o,{label:"月份"},{default:d((()=>[s(t,{modelValue:ve.value.month,"onUpdate:modelValue":l[1]||(l[1]=e=>ve.value.month=e),placeholder:"选择月份",style:{width:"120px"}},{default:d((()=>[s(a,{label:"全部",value:null}),(u(!0),i(p,null,c(fe.value,(e=>(u(),v(a,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),s(o,{label:"店铺名称"},{default:d((()=>[s(y,{modelValue:ve.value.shopName,"onUpdate:modelValue":l[2]||(l[2]=e=>ve.value.shopName=e),placeholder:"搜索条件",style:{width:"200px"}},null,8,["modelValue"])])),_:1}),s(o,null,{default:d((()=>[s(Pe,{type:"primary",icon:"search",onClick:Se},{default:d((()=>l[24]||(l[24]=[m("查询")]))),_:1}),s(Pe,{icon:"refresh",onClick:Me},{default:d((()=>l[25]||(l[25]=[m("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n("div",_,[s(Ke,{ref:"monthlyTable",style:{width:"100%"},"tooltip-effect":"dark",data:pe.value,"row-key":"month"},{default:d((()=>[s(De,{align:"center",label:"月份",prop:"month",width:"100"}),s(De,{align:"center",label:"订单数量",prop:"orderCount",width:"100"}),s(De,{align:"center",label:"总销售额",prop:"totalSales",width:"120"},{default:d((e=>[n("span",V," ¥"+f(parseFloat(e.row.totalSales||0).toFixed(2)),1)])),_:1}),s(De,{align:"center",label:"广告费用",prop:"advertisingCost",width:"120"},{default:d((e=>[n("div",C,[n("span",x," ¥"+f(parseFloat(e.row.advertisingCost||0).toFixed(2)),1),s(Pe,{type:"primary",link:"",icon:"edit",size:"small",onClick:()=>Le(e.row.month,e.row.advertisingCost)},{default:d((()=>l[26]||(l[26]=[m(" 编辑 ")]))),_:2},1032,["onClick"])])])),_:1}),s(De,{align:"center",label:"总成本",prop:"totalCosts",width:"120"},{default:d((e=>[n("span",{style:{color:"#7c2d12","font-weight":"500"},title:"成本价总计: ¥".concat(parseFloat(e.row.totalCosts||0).toFixed(2))}," ¥"+f(parseFloat(e.row.totalCosts||0).toFixed(2)),9,k)])),_:1}),s(De,{align:"center",label:"物流费用",prop:"totalShipping",width:"120"},{default:d((e=>[n("span",F," ¥"+f(parseFloat(e.row.totalShipping||0).toFixed(2)),1)])),_:1}),s(De,{align:"center",label:"佣金",prop:"totalCommission",width:"120"},{default:d((e=>[n("span",P," ¥"+f(parseFloat(e.row.totalCommission||0).toFixed(2)),1)])),_:1}),s(De,{align:"center",label:"净利润(¥)",prop:"totalProfit",width:"120"},{default:d((e=>[n("span",{style:g({color:e.row.totalProfit>=0?"#16a34a":"#dc2626",fontWeight:"600"})}," ¥"+f(parseFloat(e.row.totalProfit||0).toFixed(2)),5)])),_:1}),s(De,{align:"center",label:"利润率",prop:"profitRate",width:"100"},{default:d((e=>[n("span",{style:g({color:e.row.profitRate>=0?"#16a34a":"#dc2626",fontWeight:"600"})},f(parseFloat(e.row.profitRate||0).toFixed(1))+"% ",5)])),_:1}),s(De,{align:"center",label:"操作",width:"120"},{default:d((e=>[s(Pe,{type:"success",link:"",icon:"download",size:"small",onClick:()=>Be(e.row.month),loading:oe.value},{default:d((()=>l[27]||(l[27]=[m(" 导出Excel ")]))),_:2},1032,["onClick","loading"])])),_:1})])),_:1},8,["data"]),n("div",N,[s(Ye,{layout:"total, sizes, prev, pager, next, jumper","current-page":ve.value.page,"page-size":ve.value.pageSize,"page-sizes":[10,25,50,100],total:ce.value,onCurrentChange:Re,onSizeChange:Oe},null,8,["current-page","page-size","total"])])])])),_:1}),s(Ie,{label:"详细统计",name:"detail"},{default:d((()=>[n("div",U,[s(Ne,{ref_key:"elSearchFormRef",ref:J,inline:!0,model:de.value,class:"demo-form-inline",rules:ne,onKeyup:h(xe,["enter"])},{default:d((()=>[s(o,{label:"开始日期",prop:"startDate"},{default:d((()=>[s(We,{modelValue:de.value.startDate,"onUpdate:modelValue":l[3]||(l[3]=e=>de.value.startDate=e),type:"date",placeholder:"选择开始日期"},null,8,["modelValue"])])),_:1}),s(o,{label:"结束日期",prop:"endDate"},{default:d((()=>[s(We,{modelValue:de.value.endDate,"onUpdate:modelValue":l[4]||(l[4]=e=>de.value.endDate=e),type:"date",placeholder:"选择结束日期"},null,8,["modelValue"])])),_:1}),s(o,{label:"店铺名称",prop:"shopName"},{default:d((()=>[s(y,{modelValue:de.value.shopName,"onUpdate:modelValue":l[5]||(l[5]=e=>de.value.shopName=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(o,{label:"产品名称",prop:"productName"},{default:d((()=>[s(y,{modelValue:de.value.productName,"onUpdate:modelValue":l[6]||(l[6]=e=>de.value.productName=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(o,{label:"SKU",prop:"sku"},{default:d((()=>[s(y,{modelValue:de.value.sku,"onUpdate:modelValue":l[7]||(l[7]=e=>de.value.sku=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(o,{label:"货件号",prop:"postingNumber"},{default:d((()=>[s(y,{modelValue:de.value.postingNumber,"onUpdate:modelValue":l[8]||(l[8]=e=>de.value.postingNumber=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(o,null,{default:d((()=>[s(Pe,{type:"primary",icon:"search",onClick:xe},{default:d((()=>l[28]||(l[28]=[m("查询")]))),_:1}),s(Pe,{icon:"refresh",onClick:Ce},{default:d((()=>l[29]||(l[29]=[m("重置")]))),_:1})])),_:1})])),_:1},8,["model","rules"])]),n("div",z,[s(He,{gutter:20},{default:d((()=>[s(Ge,{span:4},{default:d((()=>[s(Xe,{class:"profit-card"},{default:d((()=>[n("div",D,[l[30]||(l[30]=n("div",{class:"profit-label"},"总订单数",-1)),n("div",S,f(ye.value.totalOrders),1)])])),_:1})])),_:1}),s(Ge,{span:5},{default:d((()=>[s(Xe,{class:"profit-card"},{default:d((()=>{var e;return[n("div",M,[l[31]||(l[31]=n("div",{class:"profit-label"},"总销售额",-1)),n("div",R,"¥"+f((null==(e=ye.value.totalSales)?void 0:e.toFixed(2))||"0.00"),1)])]})),_:1})])),_:1}),s(Ge,{span:5},{default:d((()=>[s(Xe,{class:"profit-card"},{default:d((()=>{var e;return[n("div",O,[l[32]||(l[32]=n("div",{class:"profit-label"},"总成本",-1)),n("div",j," ¥"+f((null==(e=ye.value.totalCosts)?void 0:e.toFixed(2))||"0.00"),1)])]})),_:1})])),_:1}),s(Ge,{span:4},{default:d((()=>[s(Xe,{class:"profit-card"},{default:d((()=>{var e;return[n("div",L,[l[33]||(l[33]=n("div",{class:"profit-label"},"总净利润(¥)",-1)),n("div",{class:b(["profit-value",ye.value.totalNetProfit>=0?"profit-positive":"profit-negative"])}," ¥"+f((null==(e=ye.value.totalNetProfit)?void 0:e.toFixed(2))||"0.00"),3)])]})),_:1})])),_:1}),s(Ge,{span:4},{default:d((()=>[s(Xe,{class:"profit-card"},{default:d((()=>{var e;return[n("div",A,[l[34]||(l[34]=n("div",{class:"profit-label"},"平均利润率",-1)),n("div",{class:b(["profit-value",ye.value.avgProfitMargin>=0?"profit-positive":"profit-negative"])},f((null==(e=ye.value.avgProfitMargin)?void 0:e.toFixed(2))||"0.00")+"% ",3)])]})),_:1})])),_:1})])),_:1})]),n("div",E,[s(Je,{title:"利润统计说明",type:"info",closable:!1,"show-icon":""},{default:d((()=>l[35]||(l[35]=[n("p",{style:{margin:"0","font-size":"14px"}},[n("strong",null,"总成本"),m(" = 产品成本价 × 数量"),n("br"),n("strong",null,"订单时间"),m(" = 订单同步到系统的时间（接近实际下单时间）"),n("br"),n("span",{style:{color:"#666","font-size":"12px"}}," 注：总成本不包含运费、佣金等费用，因为这些费用由Ozon平台自动扣除，无需单独支付 ")],-1)]))),_:1})]),n("div",T,[n("div",q,[s(Pe,{type:"primary",icon:"plus",onClick:Ue},{default:d((()=>l[36]||(l[36]=[m("计算利润")]))),_:1}),s(Pe,{type:"success",icon:"refresh",onClick:Ee,loading:le.value},{default:d((()=>l[37]||(l[37]=[m("刷新汇总")]))),_:1},8,["loading"]),s(Pe,{type:"danger",icon:"delete",onClick:Te,loading:ae.value},{default:d((()=>l[38]||(l[38]=[m("删除所有数据")]))),_:1},8,["loading"])]),s(Ke,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:se.value,"row-key":"ID"},{default:d((()=>[s(De,{align:"left",label:"日期",prop:"orderDate",width:"120"},{default:d((e=>{return[m(f((l=e.row.orderDate,l?new Date(l).toLocaleDateString("zh-CN"):"")),1)];var l})),_:1}),s(De,{align:"left",label:"店铺名称",prop:"shopName",width:"120"}),s(De,{align:"left",label:"货件号",prop:"postingNumber",width:"150"}),s(De,{align:"left",label:"订单号",prop:"orderNumber",width:"150"}),s(De,{align:"left",label:"产品信息",width:"200"},{default:d((e=>[n("div",null,[n("div",null,[n("strong",null,f(e.row.productName),1)]),n("div",B,"SKU: "+f(e.row.sku),1),n("div",K,"数量: "+f(e.row.quantity),1)])])),_:1}),s(De,{align:"left",label:"销售价格",prop:"salePrice",width:"100"},{default:d((e=>{var l;return[m(" ¥"+f((null==(l=e.row.salePrice)?void 0:l.toFixed(2))||"0.00"),1)]})),_:1}),s(De,{align:"left",label:"成本价格",prop:"costPrice",width:"100"},{default:d((e=>{var l;return[m(" ¥"+f((null==(l=e.row.costPrice)?void 0:l.toFixed(2))||"0.00"),1)]})),_:1}),s(De,{align:"left",label:"佣金",prop:"commission",width:"100"},{default:d((e=>{var l;return[n("span",Y," ¥"+f((null==(l=e.row.commission)?void 0:l.toFixed(2))||"0.00"),1)]})),_:1}),s(De,{align:"left",label:"物流费用",prop:"shippingCost",width:"100"},{default:d((e=>{var l;return[n("span",I," ¥"+f((null==(l=e.row.shippingCost)?void 0:l.toFixed(2))||"0.00"),1)]})),_:1}),s(De,{align:"left",label:"净利润(¥)",prop:"netProfit",width:"100"},{default:d((e=>{var l;return[n("span",{class:b(e.row.netProfit>=0?"profit-positive":"profit-negative")}," ¥"+f((null==(l=e.row.netProfit)?void 0:l.toFixed(2))||"0.00"),3)]})),_:1}),s(De,{align:"left",label:"利润率",prop:"profitMargin",width:"100"},{default:d((e=>{var l;return[n("span",{class:b(e.row.profitMargin>=0?"profit-positive":"profit-negative")},f((null==(l=e.row.profitMargin)?void 0:l.toFixed(2))||"0.00")+"% ",3)]})),_:1}),s(De,{align:"left",label:"状态",prop:"status",width:"100"})])),_:1},8,["data"]),n("div",W,[s(Ye,{layout:"total, sizes, prev, pager, next, jumper","current-page":re.value,"page-size":ue.value,"page-sizes":[10,30,50,100],total:ie.value,onCurrentChange:Fe,onSizeChange:ke},null,8,["current-page","page-size","total"])])]),s(Ze,{modelValue:we.value,"onUpdate:modelValue":l[15]||(l[15]=e=>we.value=e),title:"计算订单利润",width:"500px"},{footer:d((()=>[n("div",X,[s(Pe,{onClick:l[14]||(l[14]=e=>we.value=!1)},{default:d((()=>l[39]||(l[39]=[m("取消")]))),_:1}),s(Pe,{type:"primary",onClick:ze},{default:d((()=>l[40]||(l[40]=[m("确定")]))),_:1})])])),default:d((()=>[s(Je,{title:"利润计算公式：净利润 = 售价 - (成本价 + 运费 + 佣金 + 其他费用)",type:"info",closable:!1,style:{"margin-bottom":"20px"}}),s(Ne,{ref_key:"calculateFormRef",ref:Z,model:_e.value,rules:Ve,"label-width":"100px"},{default:d((()=>[s(o,{label:"货件号",prop:"postingNumber"},{default:d((()=>[s(y,{modelValue:_e.value.postingNumber,"onUpdate:modelValue":l[9]||(l[9]=e=>_e.value.postingNumber=e),placeholder:"请输入货件号"},null,8,["modelValue"])])),_:1}),s(o,{label:"成本价格",prop:"costPrice"},{default:d((()=>[s(Qe,{modelValue:_e.value.costPrice,"onUpdate:modelValue":l[10]||(l[10]=e=>_e.value.costPrice=e),precision:2,min:0,placeholder:"请输入成本价格"},null,8,["modelValue"])])),_:1}),s(o,{label:"佣金",prop:"commission"},{default:d((()=>[s(Qe,{modelValue:_e.value.commission,"onUpdate:modelValue":l[11]||(l[11]=e=>_e.value.commission=e),precision:2,min:0,placeholder:"请输入佣金"},null,8,["modelValue"])])),_:1}),s(o,{label:"运费",prop:"shippingCost"},{default:d((()=>[s(Qe,{modelValue:_e.value.shippingCost,"onUpdate:modelValue":l[12]||(l[12]=e=>_e.value.shippingCost=e),precision:2,min:0,placeholder:"请输入运费"},null,8,["modelValue"])])),_:1}),s(o,{label:"其他费用",prop:"otherCosts"},{default:d((()=>[s(Qe,{modelValue:_e.value.otherCosts,"onUpdate:modelValue":l[13]||(l[13]=e=>_e.value.otherCosts=e),precision:2,min:0,placeholder:"请输入其他费用"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["modelValue"]),s(Ze,{modelValue:ge.value,"onUpdate:modelValue":l[21]||(l[21]=e=>ge.value=e),title:"编辑广告费用",width:"500px"},{footer:d((()=>[n("div",G,[s(Pe,{onClick:l[20]||(l[20]=e=>ge.value=!1)},{default:d((()=>l[41]||(l[41]=[m("取消")]))),_:1}),s(Pe,{type:"primary",onClick:Ae},{default:d((()=>l[42]||(l[42]=[m("确定")]))),_:1})])])),default:d((()=>[s(Ne,{ref_key:"advertisingFormRef",ref:$,model:he.value,rules:be,"label-width":"100px"},{default:d((()=>[s(o,{label:"月份",prop:"month"},{default:d((()=>[s(y,{modelValue:he.value.month,"onUpdate:modelValue":l[17]||(l[17]=e=>he.value.month=e),disabled:""},null,8,["modelValue"])])),_:1}),s(o,{label:"广告费用",prop:"advertisingCost"},{default:d((()=>[s(Qe,{modelValue:he.value.advertisingCost,"onUpdate:modelValue":l[18]||(l[18]=e=>he.value.advertisingCost=e),precision:2,min:0,placeholder:"请输入广告费用",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),s(o,{label:"备注",prop:"notes"},{default:d((()=>[s(y,{modelValue:he.value.notes,"onUpdate:modelValue":l[19]||(l[19]=e=>he.value.notes=e),type:"textarea",rows:3,placeholder:"请输入备注"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue"]),s(Ze,{modelValue:te.value,"onUpdate:modelValue":l[23]||(l[23]=e=>te.value=e),title:"删除确认",width:"500px"},{footer:d((()=>[n("div",H,[s(Pe,{onClick:l[22]||(l[22]=e=>te.value=!1)},{default:d((()=>l[43]||(l[43]=[m("取消")]))),_:1}),s(Pe,{type:"danger",onClick:qe,loading:ae.value},{default:d((()=>l[44]||(l[44]=[m("确认删除")]))),_:1},8,["loading"])])])),default:d((()=>[s(Je,{title:"警告：此操作将永久删除所有利润数据和广告费用记录，且无法恢复！",type:"warning",closable:!1,style:{"margin-bottom":"20px"}}),l[45]||(l[45]=n("p",{style:{"margin-bottom":"20px",color:"#606266"}}," 确定要删除所有利润数据吗？这将包括： ",-1)),l[46]||(l[46]=n("ul",{style:{"margin-bottom":"20px",color:"#606266","padding-left":"20px"}},[n("li",null,"所有订单利润记录"),n("li",null,"所有月度广告费用记录")],-1)),l[47]||(l[47]=n("p",{style:{color:"#F56C6C","font-weight":"bold"}}," 此操作不可撤销，请谨慎操作！ ",-1))])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-ae9dce50"]]);export{J as default};
