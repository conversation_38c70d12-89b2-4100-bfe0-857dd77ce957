/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{f as e,c as l,u as a}from"./087AC4D233B64EB0ozonShop.DxUsuQjM.js";import{aj as u,u as o,a as t,r,g as d,c as s,o as n,b as p,f as c,w as m,F as i,D as v,i as y,h as f,aA as b,E as h}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const V={class:"gva-form-box"},_=Object.assign({name:"OzonShopForm"},{__name:"ozonShopForm",setup(_){const I=u(),D=o(),A=t(!1),w=t(""),U=t([]),g=t({name:"",clientID:"",APIKey:"",shopType:"",currency:"CNY"}),C=r({}),K=t();(async()=>{if(I.query.id){const l=await e({ID:I.query.id});0===l.code&&(g.value=l.data,w.value="update")}else w.value="create";U.value=await b("Store_Type")})();const P=async()=>{var e;A.value=!0,null==(e=K.value)||e.validate((async e=>{if(!e)return A.value=!1;let u;switch(w.value){case"create":default:u=await l(g.value);break;case"update":u=await a(g.value)}A.value=!1,0===u.code&&h({type:"success",message:"创建/更改成功"})}))},S=()=>{D.go(-1)};return(e,l)=>{const a=d("el-input"),u=d("el-form-item"),o=d("el-option"),t=d("el-select"),r=d("el-button"),b=d("el-form");return n(),s("div",null,[p("div",V,[c(b,{model:g.value,ref_key:"elFormRef",ref:K,"label-position":"right",rules:C,"label-width":"80px"},{default:m((()=>[c(u,{label:"店铺名字:",prop:"name"},{default:m((()=>[c(a,{modelValue:g.value.name,"onUpdate:modelValue":l[0]||(l[0]=e=>g.value.name=e),clearable:!0,placeholder:"请输入店铺名字"},null,8,["modelValue"])])),_:1}),c(u,{label:"店铺ID:",prop:"clientID"},{default:m((()=>[c(a,{modelValue:g.value.clientID,"onUpdate:modelValue":l[1]||(l[1]=e=>g.value.clientID=e),clearable:!0,placeholder:"请输入店铺ID"},null,8,["modelValue"])])),_:1}),c(u,{label:"APIKey字段:",prop:"APIKey"},{default:m((()=>[c(a,{modelValue:g.value.APIKey,"onUpdate:modelValue":l[2]||(l[2]=e=>g.value.APIKey=e),clearable:!0,placeholder:"请输入APIKey字段"},null,8,["modelValue"])])),_:1}),c(u,{label:"店铺类型:",prop:"shopType"},{default:m((()=>[c(t,{modelValue:g.value.shopType,"onUpdate:modelValue":l[3]||(l[3]=e=>g.value.shopType=e),placeholder:"请选择店铺类型",style:{width:"100%"},clearable:!0},{default:m((()=>[(n(!0),s(i,null,v(U.value,((e,l)=>(n(),y(o,{key:l,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),c(u,{label:"结算货币:",prop:"currency"},{default:m((()=>[c(t,{modelValue:g.value.currency,"onUpdate:modelValue":l[4]||(l[4]=e=>g.value.currency=e),placeholder:"请选择结算货币",style:{width:"100%"},clearable:!0},{default:m((()=>[c(o,{label:"人民币 (CNY)",value:"CNY"}),c(o,{label:"美元 (USD)",value:"USD"}),c(o,{label:"欧元 (EUR)",value:"EUR"})])),_:1},8,["modelValue"])])),_:1}),c(u,null,{default:m((()=>[c(r,{loading:A.value,type:"primary",onClick:P},{default:m((()=>l[5]||(l[5]=[f("保存")]))),_:1},8,["loading"]),c(r,{type:"primary",onClick:S},{default:m((()=>l[6]||(l[6]=[f("返回")]))),_:1})])),_:1})])),_:1},8,["model","rules"])])])}}});export{_ as default};
