/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{u as o,L as t,_ as e}from"./087AC4D233B64EB0index.Dn-Bz6sd.js";import{I as a,J as s,a as i,i as r,o as l,v as n}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const p={__name:"charts-people-numbers",props:{height:{type:String,default:"128px"},data:{type:Array,default:()=>[]}},setup(p){const c=a(),{config:h}=s(c),f=p,y=o=>({type:"text",bottom:"8",...o,style:{text:"",textAlign:"center",fill:"#4E5969",fontSize:12}}),m=i([y({left:"5%"}),y({right:0})]),{chartOption:u}=o((()=>({grid:{left:"40",right:"0",top:"10",bottom:"30"},xAxis:{type:"category",offset:2,show:!1,boundaryGap:!1,axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1}},yAxis:{type:"value",show:!1,axisLine:{show:!1},axisLabel:{show:!1},splitLine:{show:!1}},graphic:{elements:m.value},series:[{data:f.data,type:"line",smooth:!0,symbolSize:12,emphasis:{focus:"series",itemStyle:{borderWidth:2}},lineStyle:{width:3,color:new t(0,0,1,0,[{offset:0,color:"".concat(h.value.primaryColor,"32")},{offset:.5,color:"".concat(h.value.primaryColor,"64")},{offset:1,color:"".concat(h.value.primaryColor,"FF")}])},showSymbol:!1,areaStyle:{opacity:.8,color:new t(0,0,0,1,[{offset:0,color:"".concat(h.value.primaryColor,"20")},{offset:1,color:"".concat(h.value.primaryColor,"08")}])}}]})));return(o,t)=>(l(),r(e,{height:p.height,option:n(u)},null,8,["height","option"]))}};export{p as default};
