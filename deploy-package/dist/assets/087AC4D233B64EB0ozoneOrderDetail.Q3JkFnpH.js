/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{a as e,I as a,r as t,g as l,c as i,o as n,b as r,f as o,w as s,d,h as u,F as _,i as p,l as c,t as g,v,aa as m,ab as b,E as f}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as y,g as w,f as h,c as k,u as V,d as C,a as j}from"./087AC4D233B64EB0arrayCtrl.BNyxBeGJ.js";const D={class:"gva-search-box"},x={class:"gva-table-box"},z={class:"gva-btn-list"},A={class:"gva-pagination"},U={class:"flex justify-between items-center"},O={class:"text-lg"},I=Object.assign({name:"OzoneOrderDetail"},{__name:"ozoneOrderDetail",setup(I){const T=e(!1),S=a(),B=e(!1),E=e({addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}),N=t({}),q=t({createdAt:[{validator:(e,a,t)=>{Q.value.startCreatedAt&&!Q.value.endCreatedAt?t(new Error("请填写结束日期")):!Q.value.startCreatedAt&&Q.value.endCreatedAt?t(new Error("请填写开始日期")):Q.value.startCreatedAt&&Q.value.endCreatedAt&&(Q.value.startCreatedAt.getTime()===Q.value.endCreatedAt.getTime()||Q.value.startCreatedAt.getTime()>Q.value.endCreatedAt.getTime())?t(new Error("开始日期应当早于结束日期")):t()},trigger:"change"}]}),F=e(),J=e(),R=e(1),M=e(0),P=e(10),K=e([]),Q=e({}),W=()=>{Q.value={},X()},G=()=>{var e;null==(e=J.value)||e.validate((async e=>{e&&(R.value=1,""===Q.value.is_express&&(Q.value.is_express=null),X())}))},H=e=>{P.value=e,X()},L=e=>{R.value=e,X()},X=async()=>{const e=await w({page:R.value,pageSize:P.value,...Q.value});0===e.code&&(K.value=e.data.list,M.value=e.data.total,R.value=e.data.page,P.value=e.data.pageSize)};X();(async()=>{})();const Y=e([]),Z=e=>{Y.value=e},$=async()=>{b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const e=[];if(0===Y.value.length)return void f({type:"warning",message:"请选择要删除的数据"});Y.value&&Y.value.map((a=>{e.push(a.ID)}));0===(await j({IDs:e})).code&&(f({type:"success",message:"删除成功"}),K.value.length===e.length&&R.value>1&&R.value--,X())}))},ee=e(""),ae=async e=>{0===(await C({ID:e.ID})).code&&(f({type:"success",message:"删除成功"}),1===K.value.length&&R.value>1&&R.value--,X())},te=e(!1),le=()=>{te.value=!1,E.value={addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}},ie=async()=>{var e;T.value=!0,null==(e=F.value)||e.validate((async e=>{if(!e)return T.value=!1;let a;switch(ee.value){case"create":default:a=await k(E.value);break;case"update":a=await V(E.value)}T.value=!1,0===a.code&&(f({type:"success",message:"创建/更改成功"}),le(),X())}))},ne=e({}),re=e(!1),oe=async e=>{const a=await h({ID:e.ID});0===a.code&&(ne.value=a.data,re.value=!0)},se=()=>{re.value=!1,ne.value={}};return(e,a)=>{const t=l("QuestionFilled"),f=l("el-icon"),w=l("el-tooltip"),k=l("el-date-picker"),V=l("el-form-item"),C=l("el-button"),j=l("el-form"),I=l("el-table-column"),X=l("InfoFilled"),de=l("el-table"),ue=l("el-pagination"),_e=l("el-switch"),pe=l("el-input"),ce=l("el-drawer"),ge=l("el-descriptions-item"),ve=l("el-descriptions");return n(),i("div",null,[r("div",D,[o(j,{ref_key:"elSearchFormRef",ref:J,inline:!0,model:Q.value,class:"demo-form-inline",rules:q,onKeyup:c(G,["enter"])},{default:s((()=>[o(V,{label:"创建日期",prop:"createdAt"},{label:s((()=>[r("span",null,[a[22]||(a[22]=u(" 创建日期 ")),o(w,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:s((()=>[o(f,null,{default:s((()=>[o(t)])),_:1})])),_:1})])])),default:s((()=>[o(k,{modelValue:Q.value.startCreatedAt,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value.startCreatedAt=e),type:"datetime",placeholder:"开始日期","disabled-date":e=>!!Q.value.endCreatedAt&&e.getTime()>Q.value.endCreatedAt.getTime()},null,8,["modelValue","disabled-date"]),a[23]||(a[23]=u(" — ")),o(k,{modelValue:Q.value.endCreatedAt,"onUpdate:modelValue":a[1]||(a[1]=e=>Q.value.endCreatedAt=e),type:"datetime",placeholder:"结束日期","disabled-date":e=>!!Q.value.startCreatedAt&&e.getTime()<Q.value.startCreatedAt.getTime()},null,8,["modelValue","disabled-date"])])),_:1}),B.value?(n(),i(_,{key:0},[],64)):d("",!0),o(V,null,{default:s((()=>[o(C,{type:"primary",icon:"search",onClick:G},{default:s((()=>a[24]||(a[24]=[u("查询")]))),_:1}),o(C,{icon:"refresh",onClick:W},{default:s((()=>a[25]||(a[25]=[u("重置")]))),_:1}),B.value?(n(),p(C,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:a[3]||(a[3]=e=>B.value=!1)},{default:s((()=>a[27]||(a[27]=[u("收起")]))),_:1})):(n(),p(C,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:a[2]||(a[2]=e=>B.value=!0)},{default:s((()=>a[26]||(a[26]=[u("展开")]))),_:1}))])),_:1})])),_:1},8,["model","rules"])]),r("div",x,[r("div",z,[o(C,{type:"primary",icon:"plus",onClick:a[4]||(a[4]=e=>(ee.value="create",void(te.value=!0)))},{default:s((()=>a[28]||(a[28]=[u("同步订单")]))),_:1}),o(C,{icon:"delete",style:{"margin-left":"10px"},disabled:!Y.value.length,onClick:$},{default:s((()=>a[29]||(a[29]=[u("删除")]))),_:1},8,["disabled"])]),o(de,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:K.value,"row-key":"ID",onSelectionChange:Z},{default:s((()=>[o(I,{type:"selection",width:"55"}),o(I,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:s((e=>[u(g(v(m)(e.row.CreatedAt)),1)])),_:1}),o(I,{label:"收件人联系方式",prop:"addressee",width:"200"},{default:s((e=>a[30]||(a[30]=[u(" [JSON] ")]))),_:1}),o(I,{label:"分析数据",prop:"analytics_data",width:"200"},{default:s((e=>a[31]||(a[31]=[u(" [JSON] ")]))),_:1}),o(I,{label:"买家信息",prop:"customer",width:"200"},{default:s((e=>a[32]||(a[32]=[u(" [JSON] ")]))),_:1}),o(I,{label:"货件条码",prop:"barcodes",width:"200"},{default:s((e=>a[33]||(a[33]=[u(" [JSON] ")]))),_:1}),o(I,{label:"快递方式",prop:"delivery_method",width:"200"},{default:s((e=>a[34]||(a[34]=[u(" [JSON] ")]))),_:1}),o(I,{align:"left",label:"货件所属订单的ID",prop:"order_id",width:"120"}),o(I,{align:"left",label:"货件所属的订单号",prop:"order_number",width:"120"}),o(I,{align:"left",label:"货件号",prop:"posting_number",width:"120"}),o(I,{label:"货运商品列表",prop:"products",width:"200"},{default:s((e=>[o(y,{modelValue:e.row.products,"onUpdate:modelValue":a=>e.row.products=a},null,8,["modelValue","onUpdate:modelValue"])])),_:1}),o(I,{align:"left",label:"操作",fixed:"right","min-width":v(S).operateMinWith},{default:s((e=>[o(C,{type:"primary",link:"",class:"table-button",onClick:a=>oe(e.row)},{default:s((()=>[o(f,{style:{"margin-right":"5px"}},{default:s((()=>[o(X)])),_:1}),a[35]||(a[35]=u("查看"))])),_:2},1032,["onClick"]),o(C,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:a=>(async e=>{const a=await h({ID:e.ID});ee.value="update",0===a.code&&(E.value=a.data,te.value=!0)})(e.row)},{default:s((()=>a[36]||(a[36]=[u("编辑")]))),_:2},1032,["onClick"]),o(C,{type:"primary",link:"",icon:"delete",onClick:a=>{return t=e.row,void b.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((()=>{ae(t)}));var t}},{default:s((()=>a[37]||(a[37]=[u("删除")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),r("div",A,[o(ue,{layout:"total, sizes, prev, pager, next, jumper","current-page":R.value,"page-size":P.value,"page-sizes":[10,30,50,100],total:M.value,onCurrentChange:L,onSizeChange:H},null,8,["current-page","page-size","total"])])]),o(ce,{"destroy-on-close":"",size:v(S).drawerSize,modelValue:te.value,"onUpdate:modelValue":a[18]||(a[18]=e=>te.value=e),"show-close":!1,"before-close":le},{header:s((()=>[r("div",U,[r("span",O,g("create"===ee.value?"新增":"编辑"),1),r("div",null,[o(C,{loading:T.value,type:"primary",onClick:ie},{default:s((()=>a[38]||(a[38]=[u("确 定")]))),_:1},8,["loading"]),o(C,{onClick:le},{default:s((()=>a[39]||(a[39]=[u("取 消")]))),_:1})])])])),default:s((()=>[o(j,{model:E.value,"label-position":"top",ref_key:"elFormRef",ref:F,rules:N,"label-width":"80px"},{default:s((()=>[o(V,{label:"收件人联系方式:",prop:"addressee"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取 "+g(E.value.addressee),1)])),_:1}),o(V,{label:"分析数据:",prop:"analytics_data"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取 "+g(E.value.analytics_data),1)])),_:1}),o(V,{label:"买家信息:",prop:"customer"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取 "+g(E.value.customer),1)])),_:1}),o(V,{label:"货件条码:",prop:"barcodes"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取 "+g(E.value.barcodes),1)])),_:1}),o(V,{label:"货件交付物流的时间:",prop:"delivering_date"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取 "+g(E.value.delivering_date),1)])),_:1}),o(V,{label:"快递方式:",prop:"delivery_method"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取 "+g(E.value.delivery_method),1)])),_:1}),o(V,{label:"有关商品成本、折扣幅度、付款和佣金的信息:",prop:"financial_data"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取 "+g(E.value.financial_data),1)])),_:1}),o(V,{label:"开始处理货件的日期和时间:",prop:"in_process_at"},{default:s((()=>[o(k,{modelValue:E.value.in_process_at,"onUpdate:modelValue":a[5]||(a[5]=e=>E.value.in_process_at=e),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1}),o(V,{label:"如果使用快速物流 Ozon Express —— true:",prop:"is_express"},{default:s((()=>[o(_e,{modelValue:E.value.is_express,"onUpdate:modelValue":a[6]||(a[6]=e=>E.value.is_express=e),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"是","inactive-text":"否",clearable:""},null,8,["modelValue"])])),_:1}),o(V,{label:"带有附加特征的商品列表:",prop:"optional"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取 "+g(E.value.optional),1)])),_:1}),o(V,{label:"货件所属订单的ID:",prop:"order_id"},{default:s((()=>[o(pe,{modelValue:E.value.order_id,"onUpdate:modelValue":a[7]||(a[7]=e=>E.value.order_id=e),modelModifiers:{number:!0},clearable:!0,placeholder:"请输入货件所属订单的ID"},null,8,["modelValue"])])),_:1}),o(V,{label:"货件所属的订单号:",prop:"order_number"},{default:s((()=>[o(pe,{modelValue:E.value.order_number,"onUpdate:modelValue":a[8]||(a[8]=e=>E.value.order_number=e),clearable:!0,placeholder:"请输入货件所属的订单号"},null,8,["modelValue"])])),_:1}),o(V,{label:"快递母件编号，从该母件中拆分出了当前货件:",prop:"parent_posting_number"},{default:s((()=>[o(pe,{modelValue:E.value.parent_posting_number,"onUpdate:modelValue":a[9]||(a[9]=e=>E.value.parent_posting_number=e),clearable:!0,placeholder:"请输入快递母件编号，从该母件中拆分出了当前货件"},null,8,["modelValue"])])),_:1}),o(V,{label:"货件号:",prop:"posting_number"},{default:s((()=>[o(pe,{modelValue:E.value.posting_number,"onUpdate:modelValue":a[10]||(a[10]=e=>E.value.posting_number=e),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])])),_:1}),o(V,{label:"货运商品列表:",prop:"products"},{default:s((()=>[o(y,{modelValue:E.value.products,"onUpdate:modelValue":a[11]||(a[11]=e=>E.value.products=e),editable:""},null,8,["modelValue"])])),_:1}),o(V,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:",prop:"requirements"},{default:s((()=>[u(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取 "+g(E.value.requirements),1)])),_:1}),o(V,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:",prop:"shipment_date"},{default:s((()=>[o(k,{modelValue:E.value.shipment_date,"onUpdate:modelValue":a[12]||(a[12]=e=>E.value.shipment_date=e),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])])),_:1}),o(V,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:",prop:"status"},{default:s((()=>[o(pe,{modelValue:E.value.status,"onUpdate:modelValue":a[13]||(a[13]=e=>E.value.status=e),clearable:!0,placeholder:"请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])])),_:1}),o(V,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:",prop:"substatus"},{default:s((()=>[o(pe,{modelValue:E.value.substatus,"onUpdate:modelValue":a[14]||(a[14]=e=>E.value.substatus=e),clearable:!0,placeholder:"请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])])),_:1}),o(V,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:",prop:"tpl_integration_type"},{default:s((()=>[o(pe,{modelValue:E.value.tpl_integration_type,"onUpdate:modelValue":a[15]||(a[15]=e=>E.value.tpl_integration_type=e),clearable:!0,placeholder:"请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},null,8,["modelValue"])])),_:1}),o(V,{label:"货件跟踪号:",prop:"tracking_number"},{default:s((()=>[o(pe,{modelValue:E.value.tracking_number,"onUpdate:modelValue":a[16]||(a[16]=e=>E.value.tracking_number=e),clearable:!0,placeholder:"请输入货件跟踪号"},null,8,["modelValue"])])),_:1}),o(V,{label:"发运的计费信息:",prop:"tariffication\t"},{default:s((()=>[o(y,{modelValue:E.value.tariffication,"onUpdate:modelValue":a[17]||(a[17]=e=>E.value.tariffication=e),editable:""},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["size","modelValue"]),o(ce,{"destroy-on-close":"",size:v(S).drawerSize,modelValue:re.value,"onUpdate:modelValue":a[21]||(a[21]=e=>re.value=e),"show-close":!0,"before-close":se,title:"查看"},{default:s((()=>[o(ve,{column:1,border:""},{default:s((()=>[o(ge,{label:"收件人联系方式"},{default:s((()=>[u(g(ne.value.addressee),1)])),_:1}),o(ge,{label:"分析数据"},{default:s((()=>[u(g(ne.value.analytics_data),1)])),_:1}),o(ge,{label:"买家信息"},{default:s((()=>[u(g(ne.value.customer),1)])),_:1}),o(ge,{label:"货件条码"},{default:s((()=>[u(g(ne.value.barcodes),1)])),_:1}),o(ge,{label:"货件交付物流的时间"},{default:s((()=>[u(g(ne.value.delivering_date),1)])),_:1}),o(ge,{label:"快递方式"},{default:s((()=>[u(g(ne.value.delivery_method),1)])),_:1}),o(ge,{label:"有关商品成本、折扣幅度、付款和佣金的信息"},{default:s((()=>[u(g(ne.value.financial_data),1)])),_:1}),o(ge,{label:"开始处理货件的日期和时间"},{default:s((()=>[u(g(ne.value.in_process_at),1)])),_:1}),o(ge,{label:"如果使用快速物流 Ozon Express —— true"},{default:s((()=>[u(g(ne.value.is_express),1)])),_:1}),o(ge,{label:"带有附加特征的商品列表"},{default:s((()=>[u(g(ne.value.optional),1)])),_:1}),o(ge,{label:"货件所属订单的ID"},{default:s((()=>[u(g(ne.value.order_id),1)])),_:1}),o(ge,{label:"货件所属的订单号"},{default:s((()=>[u(g(ne.value.order_number),1)])),_:1}),o(ge,{label:"快递母件编号，从该母件中拆分出了当前货件"},{default:s((()=>[u(g(ne.value.parent_posting_number),1)])),_:1}),o(ge,{label:"货件号"},{default:s((()=>[u(g(ne.value.posting_number),1)])),_:1}),o(ge,{label:"货运商品列表"},{default:s((()=>[o(y,{modelValue:ne.value.products,"onUpdate:modelValue":a[19]||(a[19]=e=>ne.value.products=e)},null,8,["modelValue"])])),_:1}),o(ge,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态"},{default:s((()=>[u(g(ne.value.requirements),1)])),_:1}),o(ge,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段"},{default:s((()=>[u(g(ne.value.shipment_date),1)])),_:1}),o(ge,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},{default:s((()=>[u(g(ne.value.status),1)])),_:1}),o(ge,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},{default:s((()=>[u(g(ne.value.substatus),1)])),_:1}),o(ge,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},{default:s((()=>[u(g(ne.value.tpl_integration_type),1)])),_:1}),o(ge,{label:"货件跟踪号"},{default:s((()=>[u(g(ne.value.tracking_number),1)])),_:1}),o(ge,{label:"发运的计费信息"},{default:s((()=>[o(y,{modelValue:ne.value.tariffication,"onUpdate:modelValue":a[20]||(a[20]=e=>ne.value.tariffication=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1},8,["size","modelValue"])])}}});export{I as default};
