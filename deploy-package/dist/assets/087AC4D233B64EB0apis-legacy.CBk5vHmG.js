/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(e,r,n){return(r=function(e){var r=function(e,r){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,r||"default");if("object"!=t(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==t(r)?r:r+""}(r))in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function(){return n};var e,n={},o=Object.prototype,a=o.hasOwnProperty,i=Object.defineProperty||function(t,e,r){t[e]=r.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function f(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{f({},"")}catch(e){f=function(t,e,r){return t[e]=r}}function h(t,e,r,n){var o=e&&e.prototype instanceof w?e:w,a=Object.create(o.prototype),u=new N(n||[]);return i(a,"_invoke",{value:P(t,r,u)}),a}function p(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}n.wrap=h;var v="suspendedStart",d="suspendedYield",y="executing",m="completed",g={};function w(){}function b(){}function x(){}var E={};f(E,c,(function(){return this}));var L=Object.getPrototypeOf,j=L&&L(L(C([])));j&&j!==o&&a.call(j,c)&&(E=j);var _=x.prototype=w.prototype=Object.create(E);function k(t){["next","throw","return"].forEach((function(e){f(t,e,(function(t){return this._invoke(e,t)}))}))}function O(e,r){function n(o,i,u,c){var l=p(e[o],e,i);if("throw"!==l.type){var s=l.arg,f=s.value;return f&&"object"==t(f)&&a.call(f,"__await")?r.resolve(f.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):r.resolve(f).then((function(t){s.value=t,u(s)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var o;i(this,"_invoke",{value:function(t,e){function a(){return new r((function(r,o){n(t,e,r,o)}))}return o=o?o.then(a,a):a()}})}function P(t,r,n){var o=v;return function(a,i){if(o===y)throw Error("Generator is already running");if(o===m){if("throw"===a)throw i;return{value:e,done:!0}}for(n.method=a,n.arg=i;;){var u=n.delegate;if(u){var c=S(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===v)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=y;var l=p(t,r,n);if("normal"===l.type){if(o=n.done?m:d,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function S(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,S(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var a=p(o,t.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,g;var i=a.arg;return i?i.done?(r[t.resultName]=i.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function G(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function N(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function C(r){if(r||""===r){var n=r[c];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,i=function t(){for(;++o<r.length;)if(a.call(r,o))return t.value=r[o],t.done=!1,t;return t.value=e,t.done=!0,t};return i.next=i}}throw new TypeError(t(r)+" is not iterable")}return b.prototype=x,i(_,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=f(x,s,"GeneratorFunction"),n.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,f(t,s,"GeneratorFunction")),t.prototype=Object.create(_),t},n.awrap=function(t){return{__await:t}},k(O.prototype),f(O.prototype,l,(function(){return this})),n.AsyncIterator=O,n.async=function(t,e,r,o,a){void 0===a&&(a=Promise);var i=new O(h(t,e,r,o),a);return n.isGeneratorFunction(e)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(_),f(_,s,"Generator"),f(_,c,(function(){return this})),f(_,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=C,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(G),!t)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],u=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=a.call(i,"catchLoc"),l=a.call(i,"finallyLoc");if(c&&l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),g},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),G(r),g}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;G(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:C(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},n}function n(t,e,r,n,o,a,i){try{var u=t[a](i),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function o(t){return function(){var e=this,r=arguments;return new Promise((function(o,a){var i=t.apply(e,r);function u(t){n(i,o,a,u,c,"next",t)}function c(t){n(i,o,a,u,c,"throw",t)}u(void 0)}))}}System.register(["./087AC4D233B64EB0api-legacy.E3o43UgG.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,n){"use strict";var a,i,u,c,l,s,f,h,p,v,d,y,m;return{setters:[function(t){a=t.j},function(t){i=t.s,u=t.a,c=t.Q,l=t.g,s=t.c,f=t.o,h=t.b,p=t.f,v=t.w,d=t.h,y=t.t,m=t.E}],execute:function(){var n={class:"sticky top-0.5 z-10 flex space-x-2"},g={class:"tree-content"},w={class:"flex items-center justify-between w-full pr-1"},b={class:"max-w-[240px] break-all overflow-ellipsis overflow-hidden"};t("default",Object.assign({name:"Apis"},{__name:"apis",props:{row:{default:function(){return{}},type:Object}},setup:function(t,x){var E=x.expose,L=t,j=u({children:"children",label:"description"}),_=u(""),k=u(""),O=u([]),P=u([]),S=u(""),I=function(){var t=o(r().mark((function t(){var e,n,o;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a();case 2:return e=t.sent,n=e.data.apis,O.value=C(n),t.next=7,r={authorityId:L.row.authorityId},i({url:"/casbin/getPolicyPathByAuthorityId",method:"post",data:r});case 7:o=t.sent,S.value=L.row.authorityId,P.value=[],o.data.paths&&o.data.paths.forEach((function(t){P.value.push("p:"+t.path+"m:"+t.method)}));case 11:case"end":return t.stop()}var r}),t)})));return function(){return t.apply(this,arguments)}}();I();var G=u(!1),N=function(){G.value=!0},C=function(t){var r={};t&&t.forEach((function(t){t.onlyId="p:"+t.path+"m:"+t.method,Object.prototype.hasOwnProperty.call(r,t.apiGroup)?r[t.apiGroup].push(t):Object.assign(r,e({},t.apiGroup,[t]))}));var n=[];for(var o in r){var a={ID:o,description:o+"组",children:r[o]};n.push(a)}return n},T=u(null),A=function(){var t=o(r().mark((function t(){var e,n;return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e=T.value.getCheckedNodes(!0),n=[],e&&e.forEach((function(t){var e={path:t.path,method:t.method};n.push(e)})),t.next=5,r={authorityId:S.value,casbinInfos:n},i({url:"/casbin/updateCasbin",method:"post",data:r});case 5:0===t.sent.code&&m({type:"success",message:"api设置成功"});case 7:case"end":return t.stop()}var r}),t)})));return function(){return t.apply(this,arguments)}}();E({needConfirm:G,enterAndNext:function(){A()}});var V=function(t,e){return!_.value&&!k.value||(r=!_.value||e.description&&e.description.includes(_.value),n=!k.value||e.path&&e.path.includes(k.value),r&&n);var r,n};return c([_,k],(function(){T.value.filter("")})),function(t,e){var r=l("el-input"),o=l("el-button"),a=l("el-tooltip"),i=l("el-tree"),u=l("el-scrollbar");return f(),s("div",null,[h("div",n,[p(r,{modelValue:_.value,"onUpdate:modelValue":e[0]||(e[0]=function(t){return _.value=t}),class:"flex-1",placeholder:"筛选名字"},null,8,["modelValue"]),p(r,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=function(t){return k.value=t}),class:"flex-1",placeholder:"筛选路径"},null,8,["modelValue"]),p(o,{class:"float-right",type:"primary",onClick:A},{default:v((function(){return e[2]||(e[2]=[d("确 定")])})),_:1})]),h("div",g,[p(u,null,{default:v((function(){return[p(i,{ref_key:"apiTree",ref:T,data:O.value,"default-checked-keys":P.value,props:j.value,"default-expand-all":"","highlight-current":"","node-key":"onlyId","show-checkbox":"","filter-node-method":V,onCheck:N},{default:v((function(t){t._;var e=t.data;return[h("div",w,[h("span",null,y(e.description),1),p(a,{content:e.path},{default:v((function(){return[h("span",b,y(e.path),1)]})),_:2},1032,["content"])])]})),_:1},8,["data","default-checked-keys","props"])]})),_:1})])])}}}))}}}))}();
