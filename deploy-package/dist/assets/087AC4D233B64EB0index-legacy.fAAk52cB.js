/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,t){"use strict";var n,u,r,s,a,l,i,c,f;return{setters:[function(e){n=e.k,u=e.c,r=e.b,s=e.f,a=e.h,l=e.w,i=e.g,c=e.u,f=e.o}],execute:function(){var t={class:"w-full h-screen bg-gray-50 flex items-center justify-center"},o={class:"flex flex-col items-center text-2xl gap-4"};e("default",Object.assign({name:"Error"},{__name:"index",setup:function(e){var g=n(),p=c(),d=function(){p.push({name:g.userInfo.authority.defaultRouter})};return function(e,n){var c=i("el-button");return f(),u("div",null,[r("div",t,[r("div",o,[n[1]||(n[1]=r("img",{class:"w-1/3",src:"/assets/087AC4D233B64EB0404.Bk63Q-R4.png"},null,-1)),n[2]||(n[2]=r("p",{class:"text-lg"},"页面被神秘力量吸走了",-1)),n[3]||(n[3]=r("p",{class:"text-lg"}," 常见问题为当前此角色无当前路由，如果确定要使用本路由，请到角色管理进行分配 ",-1)),n[4]||(n[4]=r("p",null,[a(" 项目地址："),r("a",{href:"https://github.com/flipped-aurora/gin-vue-admin",target:"_blank",class:"text-blue-600"},"https://github.com/flipped-aurora/gin-vue-admin")],-1)),s(c,{onClick:d},{default:l((function(){return n[0]||(n[0]=[a("返回首页")])})),_:1})])])])}}}))}}}));
