/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(l){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(l)}function l(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return a};var u,a={},n=Object.prototype,t=n.hasOwnProperty,o=Object.defineProperty||function(e,l,u){e[l]=u.value},r="function"==typeof Symbol?Symbol:{},d=r.iterator||"@@iterator",i=r.asyncIterator||"@@asyncIterator",c=r.toStringTag||"@@toStringTag";function m(e,l,u){return Object.defineProperty(e,l,{value:u,enumerable:!0,configurable:!0,writable:!0}),e[l]}try{m({},"")}catch(u){m=function(e,l,u){return e[l]=u}}function s(e,l,u,a){var n=l&&l.prototype instanceof y?l:y,t=Object.create(n.prototype),r=new C(a||[]);return o(t,"_invoke",{value:z(e,u,r)}),t}function f(e,l,u){try{return{type:"normal",arg:e.call(l,u)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var p="suspendedStart",v="suspendedYield",V="executing",b="completed",h={};function y(){}function _(){}function g(){}var U={};m(U,d,(function(){return this}));var w=Object.getPrototypeOf,q=w&&w(w(j([])));q&&q!==n&&t.call(q,d)&&(U=q);var x=g.prototype=y.prototype=Object.create(U);function M(e){["next","throw","return"].forEach((function(l){m(e,l,(function(e){return this._invoke(l,e)}))}))}function k(l,u){function a(n,o,r,d){var i=f(l[n],l,o);if("throw"!==i.type){var c=i.arg,m=c.value;return m&&"object"==e(m)&&t.call(m,"__await")?u.resolve(m.__await).then((function(e){a("next",e,r,d)}),(function(e){a("throw",e,r,d)})):u.resolve(m).then((function(e){c.value=e,r(c)}),(function(e){return a("throw",e,r,d)}))}d(i.arg)}var n;o(this,"_invoke",{value:function(e,l){function t(){return new u((function(u,n){a(e,l,u,n)}))}return n=n?n.then(t,t):t()}})}function z(e,l,a){var n=p;return function(t,o){if(n===V)throw Error("Generator is already running");if(n===b){if("throw"===t)throw o;return{value:u,done:!0}}for(a.method=t,a.arg=o;;){var r=a.delegate;if(r){var d=L(r,a);if(d){if(d===h)continue;return d}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===p)throw n=b,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=V;var i=f(e,l,a);if("normal"===i.type){if(n=a.done?b:v,i.arg===h)continue;return{value:i.arg,done:a.done}}"throw"===i.type&&(n=b,a.method="throw",a.arg=i.arg)}}}function L(e,l){var a=l.method,n=e.iterator[a];if(n===u)return l.delegate=null,"throw"===a&&e.iterator.return&&(l.method="return",l.arg=u,L(e,l),"throw"===l.method)||"return"!==a&&(l.method="throw",l.arg=new TypeError("The iterator does not provide a '"+a+"' method")),h;var t=f(n,e.iterator,l.arg);if("throw"===t.type)return l.method="throw",l.arg=t.arg,l.delegate=null,h;var o=t.arg;return o?o.done?(l[e.resultName]=o.value,l.next=e.nextLoc,"return"!==l.method&&(l.method="next",l.arg=u),l.delegate=null,h):o:(l.method="throw",l.arg=new TypeError("iterator result is not an object"),l.delegate=null,h)}function E(e){var l={tryLoc:e[0]};1 in e&&(l.catchLoc=e[1]),2 in e&&(l.finallyLoc=e[2],l.afterLoc=e[3]),this.tryEntries.push(l)}function S(e){var l=e.completion||{};l.type="normal",delete l.arg,e.completion=l}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(E,this),this.reset(!0)}function j(l){if(l||""===l){var a=l[d];if(a)return a.call(l);if("function"==typeof l.next)return l;if(!isNaN(l.length)){var n=-1,o=function e(){for(;++n<l.length;)if(t.call(l,n))return e.value=l[n],e.done=!1,e;return e.value=u,e.done=!0,e};return o.next=o}}throw new TypeError(e(l)+" is not iterable")}return _.prototype=g,o(x,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:_,configurable:!0}),_.displayName=m(g,c,"GeneratorFunction"),a.isGeneratorFunction=function(e){var l="function"==typeof e&&e.constructor;return!!l&&(l===_||"GeneratorFunction"===(l.displayName||l.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,m(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},a.awrap=function(e){return{__await:e}},M(k.prototype),m(k.prototype,i,(function(){return this})),a.AsyncIterator=k,a.async=function(e,l,u,n,t){void 0===t&&(t=Promise);var o=new k(s(e,l,u,n),t);return a.isGeneratorFunction(l)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},M(x),m(x,c,"Generator"),m(x,d,(function(){return this})),m(x,"toString",(function(){return"[object Generator]"})),a.keys=function(e){var l=Object(e),u=[];for(var a in l)u.push(a);return u.reverse(),function e(){for(;u.length;){var a=u.pop();if(a in l)return e.value=a,e.done=!1,e}return e.done=!0,e}},a.values=j,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=u,this.done=!1,this.delegate=null,this.method="next",this.arg=u,this.tryEntries.forEach(S),!e)for(var l in this)"t"===l.charAt(0)&&t.call(this,l)&&!isNaN(+l.slice(1))&&(this[l]=u)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var l=this;function a(a,n){return r.type="throw",r.arg=e,l.next=a,n&&(l.method="next",l.arg=u),!!n}for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n],r=o.completion;if("root"===o.tryLoc)return a("end");if(o.tryLoc<=this.prev){var d=t.call(o,"catchLoc"),i=t.call(o,"finallyLoc");if(d&&i){if(this.prev<o.catchLoc)return a(o.catchLoc,!0);if(this.prev<o.finallyLoc)return a(o.finallyLoc)}else if(d){if(this.prev<o.catchLoc)return a(o.catchLoc,!0)}else{if(!i)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return a(o.finallyLoc)}}}},abrupt:function(e,l){for(var u=this.tryEntries.length-1;u>=0;--u){var a=this.tryEntries[u];if(a.tryLoc<=this.prev&&t.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=l&&l<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=l,n?(this.method="next",this.next=n.finallyLoc,h):this.complete(o)},complete:function(e,l){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&l&&(this.next=l),h},finish:function(e){for(var l=this.tryEntries.length-1;l>=0;--l){var u=this.tryEntries[l];if(u.finallyLoc===e)return this.complete(u.completion,u.afterLoc),S(u),h}},catch:function(e){for(var l=this.tryEntries.length-1;l>=0;--l){var u=this.tryEntries[l];if(u.tryLoc===e){var a=u.completion;if("throw"===a.type){var n=a.arg;S(u)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,l,a){return this.delegate={iterator:j(e),resultName:l,nextLoc:a},"next"===this.method&&(this.arg=u),h}},a}function u(e,l,u,a,n,t,o){try{var r=e[t](o),d=r.value}catch(e){return void u(e)}r.done?l(d):Promise.resolve(d).then(a,n)}function a(e){return function(){var l=this,a=arguments;return new Promise((function(n,t){var o=e.apply(l,a);function r(e){u(o,n,t,r,d,"next",e)}function d(e){u(o,n,t,r,d,"throw",e)}r(void 0)}))}}System.register(["./087AC4D233B64EB0system-legacy.BbCF7JZU.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,u){"use strict";var n,t,o,r,d,i,c,m,s,f,p,v,V,b,h,y,_,g,U,w,q,x,M;return{setters:[function(e){n=e.a,t=e.s,o=e.r},function(e){r=e.s,d=e._,i=e.a,c=e.g,m=e.c,s=e.o,f=e.f,p=e.b,v=e.w,V=e.i,b=e.d,h=e.h,y=e.F,_=e.D,g=e.v,U=e.B,w=e.y,q=e.af,x=e.E,M=e.ab}],execute:function(){var u=document.createElement("style");u.textContent=".system[data-v-4261dc95]{border-radius:.25rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:2.25rem}.system[data-v-4261dc95]:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(15 23 42 / var(--tw-bg-opacity, 1))}.system h2[data-v-4261dc95]{margin-top:.625rem;margin-bottom:.625rem;padding:.625rem;font-size:1.125rem;line-height:1.75rem;--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}\n/*$vite$:1*/",document.head.appendChild(u);var k={class:"system"},z={class:"mt-4"},L=Object.assign({name:"Config"},{__name:"system",setup:function(e){var u=i("1"),d=i({system:{"iplimit-count":0,"iplimit-time":0},jwt:{},mysql:{},mssql:{},sqlite:{},pgsql:{},oracle:{},excel:{},autocode:{},redis:{},mongo:{coll:"",options:"",database:"",username:"",password:"","min-pool-size":"","max-pool-size":"","socket-timeout-ms":"","connect-timeout-ms":"","is-zap":!1,hosts:[{host:"",port:""}]},qiniu:{},"tencent-cos":{},"aliyun-oss":{},"hua-wei-obs":{},"cloudflare-r2":{},captcha:{},zap:{},local:{},email:{},timer:{detail:{}}}),L=function(){var e=a(l().mark((function e(){var u;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n();case 2:0===(u=e.sent).code&&(d.value=u.data.config);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();L();var E=function(){M.confirm("确定要重启服务?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(a(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,o();case 2:0===e.sent.code&&x({type:"success",message:"操作成功"});case 4:case"end":return e.stop()}}),e)})))).catch((function(){x({type:"info",message:"取消重启"})}))},S=function(){var e=a(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t({config:d.value});case 2:if(0!==e.sent.code){e.next=7;break}return x({type:"success",message:"配置文件设置成功"}),e.next=7,L();case 7:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),C=function(){var e=a(l().mark((function e(){return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r({url:"/email/emailTest",method:"post",data:void 0});case 2:if(0!==e.sent.code){e.next=9;break}return x({type:"success",message:"邮件发送成功"}),e.next=7,L();case 7:e.next=10;break;case 9:x({type:"error",message:"邮件发送失败"});case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),j=function(){d.value.jwt["signing-key"]=q()},O=function(){d.value.mongo.hosts.push({host:"",port:""})};return function(e,l){var a=c("el-input-number"),n=c("el-form-item"),t=c("el-option"),o=c("el-select"),r=c("el-switch"),i=c("el-input"),q=c("el-tooltip"),x=c("el-tab-pane"),M=c("el-button"),L=c("el-tabs"),I=c("el-form");return s(),m("div",k,[f(I,{ref:"form",model:d.value,"label-width":"240px"},{default:v((function(){return[f(L,{modelValue:u.value,"onUpdate:modelValue":l[144]||(l[144]=function(e){return u.value=e})},{default:v((function(){return[f(x,{label:"系统配置",name:"1",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"端口值"},{default:v((function(){return[f(a,{modelValue:d.value.system.addr,"onUpdate:modelValue":l[0]||(l[0]=function(e){return d.value.system.addr=e}),placeholder:"请输入端口值"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库类型"},{default:v((function(){return[f(o,{modelValue:d.value.system["db-type"],"onUpdate:modelValue":l[1]||(l[1]=function(e){return d.value.system["db-type"]=e}),class:"w-full"},{default:v((function(){return[f(t,{value:"mysql"}),f(t,{value:"pgsql"}),f(t,{value:"mssql"}),f(t,{value:"sqlite"}),f(t,{value:"oracle"})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"Oss类型"},{default:v((function(){return[f(o,{modelValue:d.value.system["oss-type"],"onUpdate:modelValue":l[2]||(l[2]=function(e){return d.value.system["oss-type"]=e}),class:"w-full"},{default:v((function(){return[f(t,{value:"local"},{default:v((function(){return l[145]||(l[145]=[h("本地")])})),_:1}),f(t,{value:"qiniu"},{default:v((function(){return l[146]||(l[146]=[h("七牛")])})),_:1}),f(t,{value:"tencent-cos"},{default:v((function(){return l[147]||(l[147]=[h("腾讯云COS")])})),_:1}),f(t,{value:"aliyun-oss"},{default:v((function(){return l[148]||(l[148]=[h("阿里云OSS")])})),_:1}),f(t,{value:"huawei-obs"},{default:v((function(){return l[149]||(l[149]=[h("华为云OBS")])})),_:1}),f(t,{value:"cloudflare-r2"},{default:v((function(){return l[150]||(l[150]=[h("cloudflare R2")])})),_:1})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"多点登录拦截"},{default:v((function(){return[f(r,{modelValue:d.value.system["use-multipoint"],"onUpdate:modelValue":l[3]||(l[3]=function(e){return d.value.system["use-multipoint"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"开启redis"},{default:v((function(){return[f(r,{modelValue:d.value.system["use-redis"],"onUpdate:modelValue":l[4]||(l[4]=function(e){return d.value.system["use-redis"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"开启Mongo"},{default:v((function(){return[f(r,{modelValue:d.value.system["use-mongo"],"onUpdate:modelValue":l[5]||(l[5]=function(e){return d.value.system["use-mongo"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"严格角色模式"},{default:v((function(){return[f(r,{modelValue:d.value.system["use-strict-auth"],"onUpdate:modelValue":l[6]||(l[6]=function(e){return d.value.system["use-strict-auth"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"限流次数"},{default:v((function(){return[f(a,{modelValue:d.value.system["iplimit-count"],"onUpdate:modelValue":l[7]||(l[7]=function(e){return d.value.system["iplimit-count"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(n,{label:"限流时间"},{default:v((function(){return[f(a,{modelValue:d.value.system["iplimit-time"],"onUpdate:modelValue":l[8]||(l[8]=function(e){return d.value.system["iplimit-time"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(q,{content:"请修改完成后，注意一并修改前端env环境下的VITE_BASE_PATH",placement:"top-start"},{default:v((function(){return[f(n,{label:"全局路由前缀"},{default:v((function(){return[f(i,{modelValue:d.value.system["router-prefix"],"onUpdate:modelValue":l[9]||(l[9]=function(e){return d.value.system["router-prefix"]=e}),modelModifiers:{trim:!0},placeholder:"请输入全局路由前缀"},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1}),f(x,{label:"jwt签名",name:"2",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"jwt签名"},{default:v((function(){return[f(i,{modelValue:d.value.jwt["signing-key"],"onUpdate:modelValue":l[10]||(l[10]=function(e){return d.value.jwt["signing-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入jwt签名"},{append:v((function(){return[f(M,{onClick:j},{default:v((function(){return l[151]||(l[151]=[h("生成")])})),_:1})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"有效期"},{default:v((function(){return[f(i,{modelValue:d.value.jwt["expires-time"],"onUpdate:modelValue":l[11]||(l[11]=function(e){return d.value.jwt["expires-time"]=e}),modelModifiers:{trim:!0},placeholder:"请输入有效期"},null,8,["modelValue"])]})),_:1}),f(n,{label:"缓冲期"},{default:v((function(){return[f(i,{modelValue:d.value.jwt["buffer-time"],"onUpdate:modelValue":l[12]||(l[12]=function(e){return d.value.jwt["buffer-time"]=e}),modelModifiers:{trim:!0},placeholder:"请输入缓冲期"},null,8,["modelValue"])]})),_:1}),f(n,{label:"签发者"},{default:v((function(){return[f(i,{modelValue:d.value.jwt.issuer,"onUpdate:modelValue":l[13]||(l[13]=function(e){return d.value.jwt.issuer=e}),modelModifiers:{trim:!0},placeholder:"请输入签发者"},null,8,["modelValue"])]})),_:1})]})),_:1}),f(x,{label:"Zap日志配置",name:"3",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"级别"},{default:v((function(){return[f(o,{modelValue:d.value.zap.level,"onUpdate:modelValue":l[14]||(l[14]=function(e){return d.value.zap.level=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"输出"},{default:v((function(){return[f(o,{modelValue:d.value.zap.format,"onUpdate:modelValue":l[15]||(l[15]=function(e){return d.value.zap.format=e})},{default:v((function(){return[f(t,{value:"console",label:"console"}),f(t,{value:"json",label:"json"})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"日志前缀"},{default:v((function(){return[f(i,{modelValue:d.value.zap.prefix,"onUpdate:modelValue":l[16]||(l[16]=function(e){return d.value.zap.prefix=e}),modelModifiers:{trim:!0},placeholder:"请输入日志前缀"},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志文件夹"},{default:v((function(){return[f(i,{modelValue:d.value.zap.director,"onUpdate:modelValue":l[17]||(l[17]=function(e){return d.value.zap.director=e}),modelModifiers:{trim:!0},placeholder:"请输入日志文件夹"},null,8,["modelValue"])]})),_:1}),f(n,{label:"编码级"},{default:v((function(){return[f(o,{modelValue:d.value.zap["encode-level"],"onUpdate:modelValue":l[18]||(l[18]=function(e){return d.value.zap["encode-level"]=e}),class:"w-6/12"},{default:v((function(){return[f(t,{value:"LowercaseLevelEncoder",label:"LowercaseLevelEncoder"}),f(t,{value:"LowercaseColorLevelEncoder",label:"LowercaseColorLevelEncoder"}),f(t,{value:"CapitalLevelEncoder",label:"CapitalLevelEncoder"}),f(t,{value:"CapitalColorLevelEncoder",label:"CapitalColorLevelEncoder"})]})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"栈名"},{default:v((function(){return[f(i,{modelValue:d.value.zap["stacktrace-key"],"onUpdate:modelValue":l[19]||(l[19]=function(e){return d.value.zap["stacktrace-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入栈名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志留存时间(默认以天为单位)"},{default:v((function(){return[f(a,{modelValue:d.value.zap["retention-day"],"onUpdate:modelValue":l[20]||(l[20]=function(e){return d.value.zap["retention-day"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"显示行"},{default:v((function(){return[f(r,{modelValue:d.value.zap["show-line"],"onUpdate:modelValue":l[21]||(l[21]=function(e){return d.value.zap["show-line"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"输出控制台"},{default:v((function(){return[f(r,{modelValue:d.value.zap["log-in-console"],"onUpdate:modelValue":l[22]||(l[22]=function(e){return d.value.zap["log-in-console"]=e})},null,8,["modelValue"])]})),_:1})]})),_:1}),d.value.system["use-redis"]?(s(),V(x,{key:0,label:"Redis",name:"4",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"库"},{default:v((function(){return[f(a,{modelValue:d.value.redis.db,"onUpdate:modelValue":l[23]||(l[23]=function(e){return d.value.redis.db=e}),min:"0",max:"16"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.redis.addr,"onUpdate:modelValue":l[24]||(l[24]=function(e){return d.value.redis.addr=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.redis.password,"onUpdate:modelValue":l[25]||(l[25]=function(e){return d.value.redis.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1})]})),_:1})):b("",!0),f(x,{label:"邮箱配置",name:"5",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"接收者邮箱"},{default:v((function(){return[f(i,{modelValue:d.value.email.to,"onUpdate:modelValue":l[26]||(l[26]=function(e){return d.value.email.to=e}),placeholder:"可多个，以逗号分隔"},null,8,["modelValue"])]})),_:1}),f(n,{label:"端口"},{default:v((function(){return[f(a,{modelValue:d.value.email.port,"onUpdate:modelValue":l[27]||(l[27]=function(e){return d.value.email.port=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"发送者邮箱"},{default:v((function(){return[f(i,{modelValue:d.value.email.from,"onUpdate:modelValue":l[28]||(l[28]=function(e){return d.value.email.from=e}),modelModifiers:{trim:!0},placeholder:"请输入发送者邮箱"},null,8,["modelValue"])]})),_:1}),f(n,{label:"host"},{default:v((function(){return[f(i,{modelValue:d.value.email.host,"onUpdate:modelValue":l[29]||(l[29]=function(e){return d.value.email.host=e}),modelModifiers:{trim:!0},placeholder:"请输入host"},null,8,["modelValue"])]})),_:1}),f(n,{label:"是否为ssl"},{default:v((function(){return[f(r,{modelValue:d.value.email["is-ssl"],"onUpdate:modelValue":l[30]||(l[30]=function(e){return d.value.email["is-ssl"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"secret"},{default:v((function(){return[f(i,{modelValue:d.value.email.secret,"onUpdate:modelValue":l[31]||(l[31]=function(e){return d.value.email.secret=e}),modelModifiers:{trim:!0},placeholder:"请输入secret"},null,8,["modelValue"])]})),_:1}),f(n,{label:"测试邮件"},{default:v((function(){return[f(M,{onClick:C},{default:v((function(){return l[152]||(l[152]=[h("测试邮件")])})),_:1})]})),_:1})]})),_:1}),d.value.system["use-mongo"]?(s(),V(x,{key:1,label:"Mongo 数据库配置",name:"14",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"collection name(表名,一般不写)"},{default:v((function(){return[f(i,{modelValue:d.value.mongo.coll,"onUpdate:modelValue":l[32]||(l[32]=function(e){return d.value.mongo.coll=e}),modelModifiers:{trim:!0},placeholder:"请输入collection name"},null,8,["modelValue"])]})),_:1}),f(n,{label:"mongodb 选项"},{default:v((function(){return[f(i,{modelValue:d.value.mongo.options,"onUpdate:modelValue":l[33]||(l[33]=function(e){return d.value.mongo.options=e}),modelModifiers:{trim:!0},placeholder:"请输入mongodb 选项"},null,8,["modelValue"])]})),_:1}),f(n,{label:"database name(数据库名)"},{default:v((function(){return[f(i,{modelValue:d.value.mongo.database,"onUpdate:modelValue":l[34]||(l[34]=function(e){return d.value.mongo.database=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.mongo.username,"onUpdate:modelValue":l[35]||(l[35]=function(e){return d.value.mongo.username=e}),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.mongo.password,"onUpdate:modelValue":l[36]||(l[36]=function(e){return d.value.mongo.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"最小连接池"},{default:v((function(){return[f(a,{modelValue:d.value.mongo["min-pool-size"],"onUpdate:modelValue":l[37]||(l[37]=function(e){return d.value.mongo["min-pool-size"]=e}),min:"0"},null,8,["modelValue"])]})),_:1}),f(n,{label:"最大连接池"},{default:v((function(){return[f(a,{modelValue:d.value.mongo["max-pool-size"],"onUpdate:modelValue":l[38]||(l[38]=function(e){return d.value.mongo["max-pool-size"]=e}),min:"100"},null,8,["modelValue"])]})),_:1}),f(n,{label:"socket超时时间"},{default:v((function(){return[f(a,{modelValue:d.value.mongo["socket-timeout-ms"],"onUpdate:modelValue":l[39]||(l[39]=function(e){return d.value.mongo["socket-timeout-ms"]=e}),min:"0"},null,8,["modelValue"])]})),_:1}),f(n,{label:"连接超时时间"},{default:v((function(){return[f(a,{modelValue:d.value.mongo["socket-timeout-ms"],"onUpdate:modelValue":l[40]||(l[40]=function(e){return d.value.mongo["socket-timeout-ms"]=e}),min:"0"},null,8,["modelValue"])]})),_:1}),f(n,{label:"是否开启zap日志"},{default:v((function(){return[f(r,{modelValue:d.value.mongo["is-zap"],"onUpdate:modelValue":l[41]||(l[41]=function(e){return d.value.mongo["is-zap"]=e})},null,8,["modelValue"])]})),_:1}),(s(!0),m(y,null,_(d.value.mongo.hosts,(function(e,l){return s(),V(n,{key:l,label:"节点 ".concat(l+1)},{default:v((function(){return[(s(!0),m(y,null,_(e,(function(u,a){return s(),m("div",{key:a},[(s(),V(n,{key:l+a,label:a,"label-width":"60"},{default:v((function(){return[f(i,{modelValue:e[a],"onUpdate:modelValue":function(l){return e[a]=l},modelModifiers:{trim:!0},placeholder:"host"===a?"请输入地址":"请输入端口"},null,8,["modelValue","onUpdate:modelValue","placeholder"])]})),_:2},1032,["label"]))])})),128)),l>0?(s(),V(n,{key:0},{default:v((function(){return[f(M,{type:"danger",size:"small",plain:"",icon:g(U),onClick:function(e){return u=l,void d.value.mongo.hosts.splice(u,1);var u},class:"ml-3"},null,8,["icon","onClick"])]})),_:2},1024)):b("",!0)]})),_:2},1032,["label"])})),128)),f(n,null,{default:v((function(){return[f(M,{type:"primary",size:"small",plain:"",icon:g(w),onClick:O},null,8,["icon"])]})),_:1})]})),_:1})):b("",!0),f(x,{label:"验证码配置",name:"7",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"字符长度"},{default:v((function(){return[f(a,{modelValue:d.value.captcha["key-long"],"onUpdate:modelValue":l[42]||(l[42]=function(e){return d.value.captcha["key-long"]=e}),min:4,max:6},null,8,["modelValue"])]})),_:1}),f(n,{label:"图片宽度"},{default:v((function(){return[f(a,{modelValue:d.value.captcha["img-width"],"onUpdate:modelValue":l[43]||(l[43]=function(e){return d.value.captcha["img-width"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1}),f(n,{label:"图片高度"},{default:v((function(){return[f(a,{modelValue:d.value.captcha["img-height"],"onUpdate:modelValue":l[44]||(l[44]=function(e){return d.value.captcha["img-height"]=e}),modelModifiers:{number:!0}},null,8,["modelValue"])]})),_:1})]})),_:1}),f(x,{label:"数据库配置",name:"9",class:"mt-3.5"},{default:v((function(){return["mysql"===d.value.system["db-type"]?(s(),m(y,{key:0},[f(n,{label:""},{default:v((function(){return l[153]||(l[153]=[p("h3",null,"MySQL",-1)])})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.mysql.username,"onUpdate:modelValue":l[45]||(l[45]=function(e){return d.value.mysql.username=e}),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.mysql.password,"onUpdate:modelValue":l[46]||(l[46]=function(e){return d.value.mysql.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.mysql.path,"onUpdate:modelValue":l[47]||(l[47]=function(e){return d.value.mysql.path=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库名称"},{default:v((function(){return[f(i,{modelValue:d.value.mysql["db-name"],"onUpdate:modelValue":l[48]||(l[48]=function(e){return d.value.mysql["db-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"前缀"},{default:v((function(){return[f(i,{modelValue:d.value.mysql.prefix,"onUpdate:modelValue":l[49]||(l[49]=function(e){return d.value.mysql.prefix=e}),modelModifiers:{trim:!0},placeholder:"默认为空"},null,8,["modelValue"])]})),_:1}),f(n,{label:"复数表"},{default:v((function(){return[f(r,{modelValue:d.value.mysql.singular,"onUpdate:modelValue":l[50]||(l[50]=function(e){return d.value.mysql.singular=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"引擎"},{default:v((function(){return[f(i,{modelValue:d.value.mysql.engine,"onUpdate:modelValue":l[51]||(l[51]=function(e){return d.value.mysql.engine=e}),modelModifiers:{trim:!0},placeholder:"默认为InnoDB"},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxIdleConns"},{default:v((function(){return[f(a,{modelValue:d.value.mysql["max-idle-conns"],"onUpdate:modelValue":l[52]||(l[52]=function(e){return d.value.mysql["max-idle-conns"]=e}),min:1},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxOpenConns"},{default:v((function(){return[f(a,{modelValue:d.value.mysql["max-open-conns"],"onUpdate:modelValue":l[53]||(l[53]=function(e){return d.value.mysql["max-open-conns"]=e}),min:1},null,8,["modelValue"])]})),_:1}),f(n,{label:"写入日志"},{default:v((function(){return[f(r,{modelValue:d.value.mysql["log-zap"],"onUpdate:modelValue":l[54]||(l[54]=function(e){return d.value.mysql["log-zap"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志模式"},{default:v((function(){return[f(o,{modelValue:d.value.mysql["log-mode"],"onUpdate:modelValue":l[55]||(l[55]=function(e){return d.value.mysql["log-mode"]=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1})],64)):b("",!0),"pgsql"===d.value.system["db-type"]?(s(),m(y,{key:1},[f(n,{label:""},{default:v((function(){return l[154]||(l[154]=[p("h3",null,"PostgreSQL",-1)])})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql.username,"onUpdate:modelValue":l[56]||(l[56]=function(e){return d.value.pgsql.username=e}),placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql.password,"onUpdate:modelValue":l[57]||(l[57]=function(e){return d.value.pgsql.password=e}),placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql.path,"onUpdate:modelValue":l[58]||(l[58]=function(e){return d.value.pgsql.path=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql["db-name"],"onUpdate:modelValue":l[59]||(l[59]=function(e){return d.value.pgsql["db-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])]})),_:1}),f(n,{label:"前缀"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql.prefix,"onUpdate:modelValue":l[60]||(l[60]=function(e){return d.value.pgsql.prefix=e}),modelModifiers:{trim:!0},placeholder:"请输入前缀"},null,8,["modelValue"])]})),_:1}),f(n,{label:"复数表"},{default:v((function(){return[f(r,{modelValue:d.value.pgsql.singular,"onUpdate:modelValue":l[61]||(l[61]=function(e){return d.value.pgsql.singular=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"引擎"},{default:v((function(){return[f(i,{modelValue:d.value.pgsql.engine,"onUpdate:modelValue":l[62]||(l[62]=function(e){return d.value.pgsql.engine=e}),modelModifiers:{trim:!0},placeholder:"请输入引擎"},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxIdleConns"},{default:v((function(){return[f(a,{modelValue:d.value.pgsql["max-idle-conns"],"onUpdate:modelValue":l[63]||(l[63]=function(e){return d.value.pgsql["max-idle-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxOpenConns"},{default:v((function(){return[f(a,{modelValue:d.value.pgsql["max-open-conns"],"onUpdate:modelValue":l[64]||(l[64]=function(e){return d.value.pgsql["max-open-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"写入日志"},{default:v((function(){return[f(r,{modelValue:d.value.pgsql["log-zap"],"onUpdate:modelValue":l[65]||(l[65]=function(e){return d.value.pgsql["log-zap"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志模式"},{default:v((function(){return[f(o,{modelValue:d.value.pgsql["log-mode"],"onUpdate:modelValue":l[66]||(l[66]=function(e){return d.value.pgsql["log-mode"]=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1})],64)):b("",!0),"mssql"===d.value.system["db-type"]?(s(),m(y,{key:2},[f(n,{label:""},{default:v((function(){return l[155]||(l[155]=[p("h3",null,"MsSQL",-1)])})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.username,"onUpdate:modelValue":l[67]||(l[67]=function(e){return d.value.mssql.username=e}),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{"label.trim":"密码"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.password,"onUpdate:modelValue":l[68]||(l[68]=function(e){return d.value.mssql.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.path,"onUpdate:modelValue":l[69]||(l[69]=function(e){return d.value.mssql.path=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"端口"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.port,"onUpdate:modelValue":l[70]||(l[70]=function(e){return d.value.mssql.port=e}),modelModifiers:{trim:!0},placeholder:"请输入端口"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库"},{default:v((function(){return[f(i,{modelValue:d.value.mssql["db-name"],"onUpdate:modelValue":l[71]||(l[71]=function(e){return d.value.mssql["db-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])]})),_:1}),f(n,{label:"前缀"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.prefix,"onUpdate:modelValue":l[72]||(l[72]=function(e){return d.value.mssql.prefix=e}),modelModifiers:{trim:!0},placeholder:"请输入前缀"},null,8,["modelValue"])]})),_:1}),f(n,{label:"复数表"},{default:v((function(){return[f(r,{modelValue:d.value.mssql.singular,"onUpdate:modelValue":l[73]||(l[73]=function(e){return d.value.mssql.singular=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"引擎"},{default:v((function(){return[f(i,{modelValue:d.value.mssql.engine,"onUpdate:modelValue":l[74]||(l[74]=function(e){return d.value.mssql.engine=e}),modelModifiers:{trim:!0},placeholder:"请输入引擎"},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxIdleConns"},{default:v((function(){return[f(a,{modelValue:d.value.mssql["max-idle-conns"],"onUpdate:modelValue":l[75]||(l[75]=function(e){return d.value.mssql["max-idle-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxOpenConns"},{default:v((function(){return[f(a,{modelValue:d.value.mssql["max-open-conns"],"onUpdate:modelValue":l[76]||(l[76]=function(e){return d.value.mssql["max-open-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"写入日志"},{default:v((function(){return[f(r,{modelValue:d.value.mssql["log-zap"],"onUpdate:modelValue":l[77]||(l[77]=function(e){return d.value.mssql["log-zap"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志模式"},{default:v((function(){return[f(o,{modelValue:d.value.mssql["log-mode"],"onUpdate:modelValue":l[78]||(l[78]=function(e){return d.value.mssql["log-mode"]=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1})],64)):b("",!0),"sqlite"===d.value.system["db-type"]?(s(),m(y,{key:3},[f(n,{label:""},{default:v((function(){return l[156]||(l[156]=[p("h3",null,"sqlite",-1)])})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.sqlite.username,"onUpdate:modelValue":l[79]||(l[79]=function(e){return d.value.sqlite.username=e}),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.sqlite.password,"onUpdate:modelValue":l[80]||(l[80]=function(e){return d.value.sqlite.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.sqlite.path,"onUpdate:modelValue":l[81]||(l[81]=function(e){return d.value.sqlite.path=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"端口"},{default:v((function(){return[f(i,{modelValue:d.value.sqlite.port,"onUpdate:modelValue":l[82]||(l[82]=function(e){return d.value.sqlite.port=e}),modelModifiers:{trim:!0},placeholder:"请输入端口"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库"},{default:v((function(){return[f(i,{modelValue:d.value.sqlite["db-name"],"onUpdate:modelValue":l[83]||(l[83]=function(e){return d.value.sqlite["db-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库"},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxIdleConns"},{default:v((function(){return[f(a,{modelValue:d.value.sqlite["max-idle-conns"],"onUpdate:modelValue":l[84]||(l[84]=function(e){return d.value.sqlite["max-idle-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxOpenConns"},{default:v((function(){return[f(a,{modelValue:d.value.sqlite["max-open-conns"],"onUpdate:modelValue":l[85]||(l[85]=function(e){return d.value.sqlite["max-open-conns"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"写入日志"},{default:v((function(){return[f(r,{modelValue:d.value.sqlite["log-zap"],"onUpdate:modelValue":l[86]||(l[86]=function(e){return d.value.sqlite["log-zap"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志模式"},{default:v((function(){return[f(o,{modelValue:d.value.sqlite["log-mode"],"onUpdate:modelValue":l[87]||(l[87]=function(e){return d.value.sqlite["log-mode"]=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1})],64)):b("",!0),"oracle"===d.value.system["db-type"]?(s(),m(y,{key:4},[f(n,{label:""},{default:v((function(){return l[157]||(l[157]=[p("h3",null,"oracle",-1)])})),_:1}),f(n,{label:"用户名"},{default:v((function(){return[f(i,{modelValue:d.value.oracle.username,"onUpdate:modelValue":l[88]||(l[88]=function(e){return d.value.oracle.username=e}),modelModifiers:{trim:!0},placeholder:"请输入用户名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"密码"},{default:v((function(){return[f(i,{modelValue:d.value.oracle.password,"onUpdate:modelValue":l[89]||(l[89]=function(e){return d.value.oracle.password=e}),modelModifiers:{trim:!0},placeholder:"请输入密码"},null,8,["modelValue"])]})),_:1}),f(n,{label:"地址"},{default:v((function(){return[f(i,{modelValue:d.value.oracle.path,"onUpdate:modelValue":l[90]||(l[90]=function(e){return d.value.oracle.path=e}),modelModifiers:{trim:!0},placeholder:"请输入地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"数据库名称"},{default:v((function(){return[f(i,{modelValue:d.value.oracle["db-name"],"onUpdate:modelValue":l[91]||(l[91]=function(e){return d.value.oracle["db-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入数据库名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"前缀"},{default:v((function(){return[f(i,{modelValue:d.value.oracle.prefix,"onUpdate:modelValue":l[92]||(l[92]=function(e){return d.value.oracle.prefix=e}),modelModifiers:{trim:!0},placeholder:"默认为空"},null,8,["modelValue"])]})),_:1}),f(n,{label:"复数表"},{default:v((function(){return[f(r,{modelValue:d.value.oracle.singular,"onUpdate:modelValue":l[93]||(l[93]=function(e){return d.value.oracle.singular=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"引擎"},{default:v((function(){return[f(i,{modelValue:d.value.oracle.engine,"onUpdate:modelValue":l[94]||(l[94]=function(e){return d.value.oracle.engine=e}),modelModifiers:{trim:!0},placeholder:"默认为InnoDB"},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxIdleConns"},{default:v((function(){return[f(a,{modelValue:d.value.oracle["max-idle-conns"],"onUpdate:modelValue":l[95]||(l[95]=function(e){return d.value.oracle["max-idle-conns"]=e}),min:1},null,8,["modelValue"])]})),_:1}),f(n,{label:"maxOpenConns"},{default:v((function(){return[f(a,{modelValue:d.value.oracle["max-open-conns"],"onUpdate:modelValue":l[96]||(l[96]=function(e){return d.value.oracle["max-open-conns"]=e}),min:1},null,8,["modelValue"])]})),_:1}),f(n,{label:"写入日志"},{default:v((function(){return[f(r,{modelValue:d.value.oracle["log-zap"],"onUpdate:modelValue":l[97]||(l[97]=function(e){return d.value.oracle["log-zap"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"日志模式"},{default:v((function(){return[f(o,{modelValue:d.value.oracle["log-mode"],"onUpdate:modelValue":l[98]||(l[98]=function(e){return d.value.oracle["log-mode"]=e})},{default:v((function(){return[f(t,{value:"off",label:"关闭"}),f(t,{value:"fatal",label:"致命"}),f(t,{value:"error",label:"错误"}),f(t,{value:"warn",label:"警告"}),f(t,{value:"info",label:"信息"}),f(t,{value:"debug",label:"调试"}),f(t,{value:"trace",label:"跟踪"})]})),_:1},8,["modelValue"])]})),_:1})],64)):b("",!0)]})),_:1}),f(x,{label:"oss配置",name:"10",class:"mt-3.5"},{default:v((function(){return["local"===d.value.system["oss-type"]?(s(),m(y,{key:0},[l[158]||(l[158]=p("h2",null,"本地配置",-1)),f(n,{label:"本地文件访问路径"},{default:v((function(){return[f(i,{modelValue:d.value.local.path,"onUpdate:modelValue":l[99]||(l[99]=function(e){return d.value.local.path=e}),modelModifiers:{trim:!0},placeholder:"请输入本地文件访问路径"},null,8,["modelValue"])]})),_:1}),f(n,{label:"本地文件存储路径"},{default:v((function(){return[f(i,{modelValue:d.value.local["store-path"],"onUpdate:modelValue":l[100]||(l[100]=function(e){return d.value.local["store-path"]=e}),modelModifiers:{trim:!0},placeholder:"请输入本地文件存储路径"},null,8,["modelValue"])]})),_:1})],64)):b("",!0),"qiniu"===d.value.system["oss-type"]?(s(),m(y,{key:1},[l[160]||(l[160]=p("h2",null,"七牛上传配置",-1)),f(n,{label:"存储区域"},{default:v((function(){return[f(i,{modelValue:d.value.qiniu.zone,"onUpdate:modelValue":l[101]||(l[101]=function(e){return d.value.qiniu.zone=e}),modelModifiers:{trim:!0},placeholder:"请输入存储区域"},null,8,["modelValue"])]})),_:1}),f(n,{label:"空间名称"},{default:v((function(){return[f(i,{modelValue:d.value.qiniu.bucket,"onUpdate:modelValue":l[102]||(l[102]=function(e){return d.value.qiniu.bucket=e}),modelModifiers:{trim:!0},placeholder:"请输入空间名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"CDN加速域名"},{default:v((function(){return[f(i,{modelValue:d.value.qiniu["img-path"],"onUpdate:modelValue":l[103]||(l[103]=function(e){return d.value.qiniu["img-path"]=e}),modelModifiers:{trim:!0},placeholder:"请输入CDN加速域名"},null,8,["modelValue"])]})),_:1}),f(n,{label:"是否使用https"},{default:v((function(){return[f(r,{modelValue:d.value.qiniu["use-https"],"onUpdate:modelValue":l[104]||(l[104]=function(e){return d.value.qiniu["use-https"]=e})},{default:v((function(){return l[159]||(l[159]=[h("开启")])})),_:1},8,["modelValue"])]})),_:1}),f(n,{label:"accessKey"},{default:v((function(){return[f(i,{modelValue:d.value.qiniu["access-key"],"onUpdate:modelValue":l[105]||(l[105]=function(e){return d.value.qiniu["access-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入accessKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"secretKey"},{default:v((function(){return[f(i,{modelValue:d.value.qiniu["secret-key"],"onUpdate:modelValue":l[106]||(l[106]=function(e){return d.value.qiniu["secret-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"上传是否使用CDN上传加速"},{default:v((function(){return[f(r,{modelValue:d.value.qiniu["use-cdn-domains"],"onUpdate:modelValue":l[107]||(l[107]=function(e){return d.value.qiniu["use-cdn-domains"]=e})},null,8,["modelValue"])]})),_:1})],64)):b("",!0),"tencent-cos"===d.value.system["oss-type"]?(s(),m(y,{key:2},[l[161]||(l[161]=p("h2",null,"腾讯云COS上传配置",-1)),f(n,{label:"存储桶名称"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"].bucket,"onUpdate:modelValue":l[108]||(l[108]=function(e){return d.value["tencent-cos"].bucket=e}),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"所属地域"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"].region,"onUpdate:modelValue":l[109]||(l[109]=function(e){return d.value["tencent-cos"].region=e}),modelModifiers:{trim:!0},placeholder:"请输入所属地域"},null,8,["modelValue"])]})),_:1}),f(n,{label:"secretID"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"]["secret-id"],"onUpdate:modelValue":l[110]||(l[110]=function(e){return d.value["tencent-cos"]["secret-id"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretID"},null,8,["modelValue"])]})),_:1}),f(n,{label:"secretKey"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"]["secret-key"],"onUpdate:modelValue":l[111]||(l[111]=function(e){return d.value["tencent-cos"]["secret-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"路径前缀"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"]["path-prefix"],"onUpdate:modelValue":l[112]||(l[112]=function(e){return d.value["tencent-cos"]["path-prefix"]=e}),modelModifiers:{trim:!0},placeholder:"请输入路径前缀"},null,8,["modelValue"])]})),_:1}),f(n,{label:"访问域名"},{default:v((function(){return[f(i,{modelValue:d.value["tencent-cos"]["base-url"],"onUpdate:modelValue":l[113]||(l[113]=function(e){return d.value["tencent-cos"]["base-url"]=e}),modelModifiers:{trim:!0},placeholder:"请输入访问域名"},null,8,["modelValue"])]})),_:1})],64)):b("",!0),"aliyun-oss"===d.value.system["oss-type"]?(s(),m(y,{key:3},[l[162]||(l[162]=p("h2",null,"阿里云OSS上传配置",-1)),f(n,{label:"区域"},{default:v((function(){return[f(i,{modelValue:d.value["aliyun-oss"].endpoint,"onUpdate:modelValue":l[114]||(l[114]=function(e){return d.value["aliyun-oss"].endpoint=e}),modelModifiers:{trim:!0},placeholder:"请输入区域"},null,8,["modelValue"])]})),_:1}),f(n,{label:"accessKeyId"},{default:v((function(){return[f(i,{modelValue:d.value["aliyun-oss"]["access-key-id"],"onUpdate:modelValue":l[115]||(l[115]=function(e){return d.value["aliyun-oss"]["access-key-id"]=e}),modelModifiers:{trim:!0},placeholder:"请输入accessKeyId"},null,8,["modelValue"])]})),_:1}),f(n,{label:"accessKeySecret"},{default:v((function(){return[f(i,{modelValue:d.value["aliyun-oss"]["access-key-secret"],"onUpdate:modelValue":l[116]||(l[116]=function(e){return d.value["aliyun-oss"]["access-key-secret"]=e}),modelModifiers:{trim:!0},placeholder:"请输入accessKeySecret"},null,8,["modelValue"])]})),_:1}),f(n,{label:"存储桶名称"},{default:v((function(){return[f(i,{modelValue:d.value["aliyun-oss"]["bucket-name"],"onUpdate:modelValue":l[117]||(l[117]=function(e){return d.value["aliyun-oss"]["bucket-name"]=e}),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"访问域名"},{default:v((function(){return[f(i,{modelValue:d.value["aliyun-oss"]["bucket-url"],"onUpdate:modelValue":l[118]||(l[118]=function(e){return d.value["aliyun-oss"]["bucket-url"]=e}),modelModifiers:{trim:!0},placeholder:"请输入访问域名"},null,8,["modelValue"])]})),_:1})],64)):b("",!0),"huawei-obs"===d.value.system["oss-type"]?(s(),m(y,{key:4},[l[163]||(l[163]=p("h2",null,"华为云OBS上传配置",-1)),f(n,{label:"路径"},{default:v((function(){return[f(i,{modelValue:d.value["hua-wei-obs"].path,"onUpdate:modelValue":l[119]||(l[119]=function(e){return d.value["hua-wei-obs"].path=e}),modelModifiers:{trim:!0},placeholder:"请输入路径"},null,8,["modelValue"])]})),_:1}),f(n,{label:"存储桶名称"},{default:v((function(){return[f(i,{modelValue:d.value["hua-wei-obs"].bucket,"onUpdate:modelValue":l[120]||(l[120]=function(e){return d.value["hua-wei-obs"].bucket=e}),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"区域"},{default:v((function(){return[f(i,{modelValue:d.value["hua-wei-obs"].endpoint,"onUpdate:modelValue":l[121]||(l[121]=function(e){return d.value["hua-wei-obs"].endpoint=e}),modelModifiers:{trim:!0},placeholder:"请输入区域"},null,8,["modelValue"])]})),_:1}),f(n,{label:"accessKey"},{default:v((function(){return[f(i,{modelValue:d.value["hua-wei-obs"]["access-key"],"onUpdate:modelValue":l[122]||(l[122]=function(e){return d.value["hua-wei-obs"]["access-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入accessKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"secretKey"},{default:v((function(){return[f(i,{modelValue:d.value["hua-wei-obs"]["secret-key"],"onUpdate:modelValue":l[123]||(l[123]=function(e){return d.value["hua-wei-obs"]["secret-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1})],64)):b("",!0),"cloudflare-r2"===d.value.system["oss-type"]?(s(),m(y,{key:5},[l[164]||(l[164]=p("h2",null,"Cloudflare R2上传配置",-1)),f(n,{label:"路径"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"].path,"onUpdate:modelValue":l[124]||(l[124]=function(e){return d.value["cloudflare-r2"].path=e}),modelModifiers:{trim:!0},placeholder:"请输入路径"},null,8,["modelValue"])]})),_:1}),f(n,{label:"存储桶名称"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"].bucket,"onUpdate:modelValue":l[125]||(l[125]=function(e){return d.value["cloudflare-r2"].bucket=e}),modelModifiers:{trim:!0},placeholder:"请输入存储桶名称"},null,8,["modelValue"])]})),_:1}),f(n,{label:"Base URL"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"]["base-url"],"onUpdate:modelValue":l[126]||(l[126]=function(e){return d.value["cloudflare-r2"]["base-url"]=e}),modelModifiers:{trim:!0},placeholder:"请输入Base URL"},null,8,["modelValue"])]})),_:1}),f(n,{label:"Account ID"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"]["account-id"],"onUpdate:modelValue":l[127]||(l[127]=function(e){return d.value["cloudflare-r2"]["account-id"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"Access Key ID"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"]["access-key-id"],"onUpdate:modelValue":l[128]||(l[128]=function(e){return d.value["cloudflare-r2"]["access-key-id"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1}),f(n,{label:"Secret Access Key"},{default:v((function(){return[f(i,{modelValue:d.value["cloudflare-r2"]["secret-access-key"],"onUpdate:modelValue":l[129]||(l[129]=function(e){return d.value["cloudflare-r2"]["secret-access-key"]=e}),modelModifiers:{trim:!0},placeholder:"请输入secretKey"},null,8,["modelValue"])]})),_:1})],64)):b("",!0)]})),_:1}),f(x,{label:"Excel上传配置",name:"11",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"合成目标地址"},{default:v((function(){return[f(i,{modelValue:d.value.excel.dir,"onUpdate:modelValue":l[130]||(l[130]=function(e){return d.value.excel.dir=e}),modelModifiers:{trim:!0},placeholder:"请输入合成目标地址"},null,8,["modelValue"])]})),_:1})]})),_:1}),f(x,{label:"自动化代码配置",name:"12",class:"mt-3.5"},{default:v((function(){return[f(n,{label:"是否自动重启(linux)"},{default:v((function(){return[f(r,{modelValue:d.value.autocode["transfer-restart"],"onUpdate:modelValue":l[131]||(l[131]=function(e){return d.value.autocode["transfer-restart"]=e})},null,8,["modelValue"])]})),_:1}),f(n,{label:"root(项目根路径)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode.root,"onUpdate:modelValue":l[132]||(l[132]=function(e){return d.value.autocode.root=e}),disabled:""},null,8,["modelValue"])]})),_:1}),f(n,{label:"Server(后端代码地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode.server,"onUpdate:modelValue":l[133]||(l[133]=function(e){return d.value.autocode.server=e}),modelModifiers:{trim:!0},placeholder:"请输入后端代码地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SApi(后端api文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-api"],"onUpdate:modelValue":l[134]||(l[134]=function(e){return d.value.autocode["server-api"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端api文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SInitialize(后端Initialize文件夹)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-initialize"],"onUpdate:modelValue":l[135]||(l[135]=function(e){return d.value.autocode["server-initialize"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端Initialize文件夹"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SModel(后端Model文件地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-model"],"onUpdate:modelValue":l[136]||(l[136]=function(e){return d.value.autocode["server-model"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端Model文件地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SRequest(后端Request文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-request"],"onUpdate:modelValue":l[137]||(l[137]=function(e){return d.value.autocode["server-request"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端Request文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SRouter(后端Router文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-router"],"onUpdate:modelValue":l[138]||(l[138]=function(e){return d.value.autocode["server-router"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端Router文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"SService(后端Service文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["server-service"],"onUpdate:modelValue":l[139]||(l[139]=function(e){return d.value.autocode["server-service"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端Service文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"Web(前端文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode.web,"onUpdate:modelValue":l[140]||(l[140]=function(e){return d.value.autocode.web=e}),modelModifiers:{trim:!0},placeholder:"请输入前端文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"WApi(后端WApi文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["web-api"],"onUpdate:modelValue":l[141]||(l[141]=function(e){return d.value.autocode["web-api"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端WApi文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"WForm(后端WForm文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["web-form"],"onUpdate:modelValue":l[142]||(l[142]=function(e){return d.value.autocode["web-form"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端WForm文件夹地址"},null,8,["modelValue"])]})),_:1}),f(n,{label:"WTable(后端WTable文件夹地址)"},{default:v((function(){return[f(i,{modelValue:d.value.autocode["web-table"],"onUpdate:modelValue":l[143]||(l[143]=function(e){return d.value.autocode["web-table"]=e}),modelModifiers:{trim:!0},placeholder:"请输入后端WTable文件夹地址"},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1},8,["modelValue"])]})),_:1},8,["model"]),p("div",z,[f(M,{type:"primary",onClick:S},{default:v((function(){return l[165]||(l[165]=[h("立即更新 ")])})),_:1}),f(M,{type:"primary",onClick:E},{default:v((function(){return l[166]||(l[166]=[h("重启服务 ")])})),_:1})])])}}});e("default",d(L,[["__scopeId","data-v-4261dc95"]]))}}}))}();
