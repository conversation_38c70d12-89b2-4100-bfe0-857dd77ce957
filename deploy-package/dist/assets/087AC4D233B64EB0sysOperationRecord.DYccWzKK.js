/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as e,a,g as l,c as t,o,b as n,f as s,w as u,h as r,t as d,v as i,aa as p,i as c,ab as v,E as f}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const g={class:"gva-search-box"},h={class:"gva-table-box"},m={class:"gva-btn-list"},y={class:"popover-box"},b={key:1},w={class:"popover-box"},_={key:1},k={class:"gva-pagination"},x=Object.assign({name:"SysOperationRecord"},{__name:"sysOperationRecord",setup(x){const C=a(1),O=a(0),S=a(10),V=a([]),R=a({}),z=()=>{R.value={}},B=()=>{C.value=1,""===R.value.status&&(R.value.status=null),T()},I=e=>{S.value=e,T()},D=e=>{C.value=e,T()},T=async()=>{const a=await(l={page:C.value,pageSize:S.value,...R.value},e({url:"/sysOperationRecord/getSysOperationRecordList",method:"get",params:l}));var l;0===a.code&&(V.value=a.data.list,O.value=a.data.total,C.value=a.data.page,S.value=a.data.pageSize)};T();const j=a([]),E=e=>{j.value=e},N=async()=>{v.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const a=[];j.value&&j.value.forEach((e=>{a.push(e.ID)}));var l;0===(await(l={ids:a},e({url:"/sysOperationRecord/deleteSysOperationRecordByIds",method:"delete",data:l}))).code&&(f({type:"success",message:"删除成功"}),V.value.length===a.length&&C.value>1&&C.value--,T())}))},U=async a=>{v.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{var l;0===(await(l={ID:a.ID},e({url:"/sysOperationRecord/deleteSysOperationRecord",method:"delete",data:l}))).code&&(f({type:"success",message:"删除成功"}),1===V.value.length&&C.value>1&&C.value--,T())}))},A=e=>{try{return JSON.parse(e)}catch(a){return e}};return(e,a)=>{const v=l("el-input"),f=l("el-form-item"),x=l("el-button"),T=l("el-form"),J=l("el-table-column"),L=l("el-tag"),P=l("warning"),q=l("el-icon"),F=l("el-popover"),G=l("el-table"),H=l("el-pagination");return o(),t("div",null,[n("div",g,[s(T,{inline:!0,model:R.value},{default:u((()=>[s(f,{label:"请求方法"},{default:u((()=>[s(v,{modelValue:R.value.method,"onUpdate:modelValue":a[0]||(a[0]=e=>R.value.method=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(f,{label:"请求路径"},{default:u((()=>[s(v,{modelValue:R.value.path,"onUpdate:modelValue":a[1]||(a[1]=e=>R.value.path=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(f,{label:"结果状态码"},{default:u((()=>[s(v,{modelValue:R.value.status,"onUpdate:modelValue":a[2]||(a[2]=e=>R.value.status=e),placeholder:"搜索条件"},null,8,["modelValue"])])),_:1}),s(f,null,{default:u((()=>[s(x,{type:"primary",icon:"search",onClick:B},{default:u((()=>a[3]||(a[3]=[r("查询")]))),_:1}),s(x,{icon:"refresh",onClick:z},{default:u((()=>a[4]||(a[4]=[r("重置")]))),_:1})])),_:1})])),_:1},8,["model"])]),n("div",h,[n("div",m,[s(x,{icon:"delete",disabled:!j.value.length,onClick:N},{default:u((()=>a[5]||(a[5]=[r("删除")]))),_:1},8,["disabled"])]),s(G,{ref:"multipleTable",data:V.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID",onSelectionChange:E},{default:u((()=>[s(J,{align:"left",type:"selection",width:"55"}),s(J,{align:"left",label:"操作人",width:"140"},{default:u((e=>[n("div",null,d(e.row.user.userName)+"("+d(e.row.user.nickName)+") ",1)])),_:1}),s(J,{align:"left",label:"日期",width:"180"},{default:u((e=>[r(d(i(p)(e.row.CreatedAt)),1)])),_:1}),s(J,{align:"left",label:"状态码",prop:"status",width:"120"},{default:u((e=>[n("div",null,[s(L,{type:"success"},{default:u((()=>[r(d(e.row.status),1)])),_:2},1024)])])),_:1}),s(J,{align:"left",label:"请求IP",prop:"ip",width:"120"}),s(J,{align:"left",label:"请求方法",prop:"method",width:"120"}),s(J,{align:"left",label:"请求路径",prop:"path",width:"240"}),s(J,{align:"left",label:"请求",prop:"path",width:"80"},{default:u((e=>[n("div",null,[e.row.body?(o(),c(F,{key:0,placement:"left-start",width:444},{reference:u((()=>[s(q,{style:{cursor:"pointer"}},{default:u((()=>[s(P)])),_:1})])),default:u((()=>[n("div",y,[n("pre",null,d(A(e.row.body)),1)])])),_:2},1024)):(o(),t("span",b,"无"))])])),_:1}),s(J,{align:"left",label:"响应",prop:"path",width:"80"},{default:u((e=>[n("div",null,[e.row.resp?(o(),c(F,{key:0,placement:"left-start",width:444},{reference:u((()=>[s(q,{style:{cursor:"pointer"}},{default:u((()=>[s(P)])),_:1})])),default:u((()=>[n("div",w,[n("pre",null,d(A(e.row.resp)),1)])])),_:2},1024)):(o(),t("span",_,"无"))])])),_:1}),s(J,{align:"left",label:"操作"},{default:u((e=>[s(x,{icon:"delete",type:"primary",link:"",onClick:a=>U(e.row)},{default:u((()=>a[6]||(a[6]=[r("删除")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),n("div",k,[s(H,{"current-page":C.value,"page-size":S.value,"page-sizes":[10,30,50,100],total:O.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:D,onSizeChange:I},null,8,["current-page","page-size","total"])])])])}}});export{x as default};
