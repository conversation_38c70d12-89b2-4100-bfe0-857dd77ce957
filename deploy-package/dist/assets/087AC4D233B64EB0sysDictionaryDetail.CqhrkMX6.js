/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as e,I as a,a as l,Q as t,g as i,c as s,o as u,b as r,f as o,w as n,h as d,t as c,v as p,aa as y,aT as v,ab as m,E as D}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const f=a=>e({url:"/sysDictionaryDetail/createSysDictionaryDetail",method:"post",data:a}),b={class:"gva-table-box"},g={class:"gva-btn-list justify-between"},w={class:"gva-pagination"},h={class:"flex justify-between items-center"},V={class:"text-lg"},_=Object.assign({name:"SysDictionaryDetail"},{__name:"sysDictionaryDetail",props:{sysDictionaryID:{type:Number,default:0}},setup(_){const x=a(),I=_,k=l({label:null,value:null,status:!0,sort:null}),C=l({label:[{required:!0,message:"请输入展示值",trigger:"blur"}],value:[{required:!0,message:"请输入字典值",trigger:"blur"}],sort:[{required:!0,message:"排序标记",trigger:"blur"}]}),S=l(1),z=l(0),U=l(10),j=l([]),q=e=>{U.value=e,T()},B=e=>{S.value=e,T()},T=async()=>{if(!I.sysDictionaryID)return;const a=await(l={page:S.value,pageSize:U.value,sysDictionaryID:I.sysDictionaryID},e({url:"/sysDictionaryDetail/getSysDictionaryDetailList",method:"get",params:l}));var l;0===a.code&&(j.value=a.data.list,z.value=a.data.total,S.value=a.data.page,U.value=a.data.pageSize)};T();const A=l(""),E=l(!1),M=async a=>{N.value&&N.value.clearValidate();const l=await(t={ID:a.ID},e({url:"/sysDictionaryDetail/findSysDictionaryDetail",method:"get",params:t}));var t;A.value="update",0===l.code&&(k.value=l.data.reSysDictionaryDetail,E.value=!0)},F=()=>{E.value=!1,k.value={label:null,value:null,status:!0,sort:null,sysDictionaryID:I.sysDictionaryID}},L=async a=>{m.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{var l;0===(await(l={ID:a.ID},e({url:"/sysDictionaryDetail/deleteSysDictionaryDetail",method:"delete",data:l}))).code&&(D({type:"success",message:"删除成功"}),1===j.value.length&&S.value>1&&S.value--,T())}))},N=l(null),O=async()=>{N.value.validate((async a=>{if(k.value.sysDictionaryID=I.sysDictionaryID,!a)return;let l;switch(A.value){case"create":default:l=await f(k.value);break;case"update":l=await(t=k.value,e({url:"/sysDictionaryDetail/updateSysDictionaryDetail",method:"put",data:t}))}var t;0===l.code&&(D({type:"success",message:"创建/更改成功"}),F(),T())}))},Q=()=>{A.value="create",N.value&&N.value.clearValidate(),E.value=!0};return t((()=>I.sysDictionaryID),(()=>{T()})),(e,a)=>{const l=i("el-button"),t=i("el-table-column"),m=i("el-table"),D=i("el-pagination"),f=i("el-input"),_=i("el-form-item"),I=i("el-switch"),T=i("el-input-number"),W=i("el-form"),G=i("el-drawer");return u(),s("div",null,[r("div",b,[r("div",g,[a[7]||(a[7]=r("span",{class:"text font-bold"},"字典详细内容",-1)),o(l,{type:"primary",icon:"plus",onClick:Q},{default:n((()=>a[6]||(a[6]=[d(" 新增字典项 ")]))),_:1})]),o(m,{ref:"multipleTable",data:j.value,style:{width:"100%"},"tooltip-effect":"dark","row-key":"ID"},{default:n((()=>[o(t,{type:"selection",width:"55"}),o(t,{align:"left",label:"日期",width:"180"},{default:n((e=>[d(c(p(y)(e.row.CreatedAt)),1)])),_:1}),o(t,{align:"left",label:"展示值",prop:"label"}),o(t,{align:"left",label:"字典值",prop:"value"}),o(t,{align:"left",label:"扩展值",prop:"extend"}),o(t,{align:"left",label:"启用状态",prop:"status",width:"120"},{default:n((e=>[d(c(p(v)(e.row.status)),1)])),_:1}),o(t,{align:"left",label:"排序标记",prop:"sort",width:"120"}),o(t,{align:"left",label:"操作","min-width":p(x).operateMinWith},{default:n((e=>[o(l,{type:"primary",link:"",icon:"edit",onClick:a=>M(e.row)},{default:n((()=>a[8]||(a[8]=[d(" 变更 ")]))),_:2},1032,["onClick"]),o(l,{type:"primary",link:"",icon:"delete",onClick:a=>L(e.row)},{default:n((()=>a[9]||(a[9]=[d(" 删除 ")]))),_:2},1032,["onClick"])])),_:1},8,["min-width"])])),_:1},8,["data"]),r("div",w,[o(D,{"current-page":S.value,"page-size":U.value,"page-sizes":[10,30,50,100],total:z.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:B,onSizeChange:q},null,8,["current-page","page-size","total"])])]),o(G,{modelValue:E.value,"onUpdate:modelValue":a[5]||(a[5]=e=>E.value=e),size:p(x).drawerSize,"show-close":!1,"before-close":F},{header:n((()=>[r("div",h,[r("span",V,c("create"===A.value?"添加字典项":"修改字典项"),1),r("div",null,[o(l,{onClick:F},{default:n((()=>a[10]||(a[10]=[d(" 取 消 ")]))),_:1}),o(l,{type:"primary",onClick:O},{default:n((()=>a[11]||(a[11]=[d(" 确 定 ")]))),_:1})])])])),default:n((()=>[o(W,{ref_key:"drawerForm",ref:N,model:k.value,rules:C.value,"label-width":"110px"},{default:n((()=>[o(_,{label:"展示值",prop:"label"},{default:n((()=>[o(f,{modelValue:k.value.label,"onUpdate:modelValue":a[0]||(a[0]=e=>k.value.label=e),placeholder:"请输入展示值",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),o(_,{label:"字典值",prop:"value"},{default:n((()=>[o(f,{modelValue:k.value.value,"onUpdate:modelValue":a[1]||(a[1]=e=>k.value.value=e),placeholder:"请输入字典值",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),o(_,{label:"扩展值",prop:"extend"},{default:n((()=>[o(f,{modelValue:k.value.extend,"onUpdate:modelValue":a[2]||(a[2]=e=>k.value.extend=e),placeholder:"请输入扩展值",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),o(_,{label:"启用状态",prop:"status",required:""},{default:n((()=>[o(I,{modelValue:k.value.status,"onUpdate:modelValue":a[3]||(a[3]=e=>k.value.status=e),"active-text":"开启","inactive-text":"停用"},null,8,["modelValue"])])),_:1}),o(_,{label:"排序标记",prop:"sort"},{default:n((()=>[o(T,{modelValue:k.value.sort,"onUpdate:modelValue":a[4]||(a[4]=e=>k.value.sort=e),modelModifiers:{number:!0},placeholder:"排序标记"},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","size"])])}}});export{_ as default};
