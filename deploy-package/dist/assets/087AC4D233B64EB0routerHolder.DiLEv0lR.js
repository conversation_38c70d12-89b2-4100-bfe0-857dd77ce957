/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{ac as e,g as a,c as s,o as n,f as t,w as o,a9 as l,i as r,ad as u,v as d,b as i,Y as c}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const m=Object.assign({name:"RouterHolder"},{__name:"routerHolder",setup(m){const f=e();return(e,m)=>{const p=a("router-view");return n(),s("div",null,[t(p,null,{default:o((({Component:e})=>[t(l,{mode:"out-in",name:"el-fade-in-linear"},{default:o((()=>[(n(),r(u,{include:d(f).keepAliveRouters},[i("div",null,[(n(),r(c(e)))])],1032,["include"]))])),_:2},1024)])),_:1})])}}});export{m as default};
