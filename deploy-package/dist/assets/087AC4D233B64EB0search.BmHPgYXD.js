/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import a from"./087AC4D233B64EB0index.DWlWX-fs.js";import{_ as e,a as o,R as s,g as t,c,o as n,b as i,f as l,n as r,v as m,av as u,aw as d,ao as v}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const g={class:"search-component items-center"},p=e(Object.assign({name:"BtnBox"},{__name:"search",setup(e){const p=o("true"===localStorage.getItem("isDark")||!0);s((()=>{p.value?(document.documentElement.classList.add("dark"),localStorage.setItem("isDark",!0)):(document.documentElement.classList.remove("dark"),localStorage.setItem("isDark",!1))}));const I=o(!1),h=()=>{I.value=!0,v.emit("reload"),setTimeout((()=>{I.value=!1}),500)},k=()=>{window.open("https://support.qq.com/product/371961")},B=a=>{p.value=a};return(e,o)=>{const s=t("el-switch");return n(),c("div",g,[i("div",{class:r(["gvaIcon gvaIcon-refresh",[I.value?"reloading":""]]),onClick:h},null,2),l(a,{class:"search-icon"}),i("div",{class:"gvaIcon gvaIcon-customer-service",onClick:k}),l(s,{modelValue:p.value,"onUpdate:modelValue":o[0]||(o[0]=a=>p.value=a),"active-action-icon":m(d),"inactive-action-icon":m(u),onChange:B},null,8,["modelValue","active-action-icon","inactive-action-icon"])])}}}),[["__scopeId","data-v-c687800a"]]);export{p as default};
