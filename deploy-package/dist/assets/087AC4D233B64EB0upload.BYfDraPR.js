/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e,C as a,a as l,U as t,g as s,b as n,d as o,e as i,c as u,f as d,i as r}from"./087AC4D233B64EB0QR-code.DGKmWLV2.js";import{C as c}from"./087AC4D233B64EB0index.DYYajVL4.js";import{a as p,g as m,ae as v,a7 as g,c as f,o as w,b as h,f as y,w as k,n as I,t as b,i as x,d as C,h as _,v as D,aa as B,E,ab as V,af as O}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as j}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import"./087AC4D233B64EB0image.DZXThNBc.js";import"./087AC4D233B64EB0vue-cropper.es.DCPGOIyb.js";import"./087AC4D233B64EB0logo.D8P6F9wK.js";const A=(e,a)=>{var l=new Image;l.setAttribute("crossOrigin","anonymous"),l.onload=function(){var e=document.createElement("canvas");e.width=l.width,e.height=l.height,e.getContext("2d").drawImage(l,0,0,l.width,l.height);var t=e.toDataURL("image/png"),s=document.createElement("a"),n=new MouseEvent("click");s.download=a||"photo",s.href=t,s.dispatchEvent(n)},l.src=e},U={class:"flex gap-4 p-2"},S={class:"flex-none w-64 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4"},z={class:"flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900"},T={class:"gva-table-box mt-0 mb-0"},M={class:"gva-btn-list gap-3"},P=["onClick"],L={class:"gva-pagination"},R=Object.assign({name:"Upload"},{__name:"upload",setup(R){const F=p(!1),q=p("/api"),N=p(""),Q=p(""),G=p(1),H=p(0),J=p(10),K=p({keyword:null,classId:0}),W=p([]),X=e=>{J.value=e,$()},Y=e=>{G.value=e,$()},Z=()=>{K.value.classId=0,G.value=1,$()},$=async()=>{const e=await s({page:G.value,pageSize:J.value,...K.value});0===e.code&&(W.value=e.data.list,H.value=e.data.total,G.value=e.data.page,J.value=e.data.pageSize)};$();const ee=()=>{V.prompt("格式：文件名|链接或者仅链接。","导入",{confirmButtonText:"确定",cancelButtonText:"取消",inputType:"textarea",inputPlaceholder:"我的图片|https://my-oss.com/my.png\nhttps://my-oss.com/my_1.png",inputPattern:/\S/,inputErrorMessage:"不能为空"}).then((async({value:e})=>{let a=e.split("\n"),l=[];a.forEach((e=>{let a,t,s=e.trim().split("|");if(s.length>1)t=s[0].trim(),a=s[1];else{a=s[0].trim();let e=a.substring(a.lastIndexOf("/")+1);t=e.substring(0,e.lastIndexOf("."))}a&&l.push({name:t,url:a,classId:K.value.classId,tag:a.substring(a.lastIndexOf(".")+1),key:O()})}));0===(await r(l)).code&&(E({type:"success",message:"导入成功!"}),await $())})).catch((()=>{E({type:"info",message:"取消导入"})}))},ae=()=>{K.value.keyword=null,G.value=1,$()},le={children:"children",label:"name",value:"ID"},te=p([]),se=async()=>{const e=await n();let a={name:"全部分类",ID:0,pid:0,children:[]};0===e.code&&(te.value=e.data||[],te.value.unshift(a))},ne=e=>{K.value.keyword=null,K.value.classId=e.ID,G.value=1,$()},oe=p(!1),ie=p({ID:0,pid:0,name:""}),ue=p(null),de=p({name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{max:20,message:"最多20位字符",trigger:"blur"}]}),re=async()=>{ue.value.validate((async e=>{if(e){0===(await d(ie.value)).code&&(E({type:"success",message:"操作成功"}),await se(),ce())}}))},ce=()=>{oe.value=!1,ie.value={ID:0,pid:0,name:""}};return se(),(s,n)=>{const d=m("MoreFilled"),r=m("el-icon"),p=m("Plus"),O=m("el-dropdown-item"),R=m("el-dropdown-menu"),pe=m("el-dropdown"),me=m("el-tree"),ve=m("el-scrollbar"),ge=m("el-button"),fe=m("el-input"),we=m("el-table-column"),he=m("el-tag"),ye=m("el-table"),ke=m("el-pagination"),Ie=m("el-tree-select"),be=m("el-form-item"),xe=m("el-form"),Ce=m("el-dialog"),_e=v("loading");return g((w(),f("div",null,[h("div",U,[h("div",S,[y(ve,{style:{height:"calc(100vh - 300px)"}},{default:k((()=>[y(me,{data:te.value,"node-key":"id",props:le,onNodeClick:ne,"default-expand-all":""},{default:k((({node:e,data:a})=>[h("div",{class:I(["w-36",K.value.classId===a.ID?"text-blue-500 font-bold":""])},b(a.name),3),y(pe,null,{dropdown:k((()=>[y(R,null,{default:k((()=>[y(O,{onClick:e=>{return l=a,oe.value=!0,ie.value.ID=0,void(ie.value.pid=l.ID);var l}},{default:k((()=>n[4]||(n[4]=[_("添加分类")]))),_:2},1032,["onClick"]),a.ID>0?(w(),x(O,{key:0,onClick:e=>{return l=a,ie.value={ID:l.ID,pid:l.pid,name:l.name},void(oe.value=!0);var l}},{default:k((()=>n[5]||(n[5]=[_("编辑分类")]))),_:2},1032,["onClick"])):C("",!0),a.ID>0?(w(),x(O,{key:1,onClick:e=>(async e=>{0===(await o({id:e})).code&&(E.success({type:"success",message:"删除成功"}),await se())})(a.ID)},{default:k((()=>n[6]||(n[6]=[_("删除分类")]))),_:2},1032,["onClick"])):C("",!0)])),_:2},1024)])),default:k((()=>[a.ID>0?(w(),x(r,{key:0,class:"ml-3 text-right"},{default:k((()=>[y(d)])),_:1})):(w(),x(r,{key:1,class:"ml-3 text-right mt-1"},{default:k((()=>[y(p)])),_:1}))])),_:2},1024)])),_:1},8,["data"])])),_:1})]),h("div",z,[h("div",T,[y(j,{title:"点击“文件名”可以编辑；选择的类别即是上传的类别。"}),h("div",M,[y(e,{"image-common":Q.value,classId:K.value.classId,onOnSuccess:ae},null,8,["image-common","classId"]),y(a,{classId:K.value.classId,onOnSuccess:ae},null,8,["classId"]),y(l,{classId:K.value.classId,onOnSuccess:ae},null,8,["classId"]),y(t,{"image-url":N.value,"file-size":512,"max-w-h":1080,classId:K.value.classId,onOnSuccess:ae},null,8,["image-url","classId"]),y(ge,{type:"primary",icon:"upload",onClick:ee},{default:k((()=>n[7]||(n[7]=[_(" 导入URL ")]))),_:1}),y(fe,{modelValue:K.value.keyword,"onUpdate:modelValue":n[0]||(n[0]=e=>K.value.keyword=e),class:"w-72",placeholder:"请输入文件名或备注"},null,8,["modelValue"]),y(ge,{type:"primary",icon:"search",onClick:Z},{default:k((()=>n[8]||(n[8]=[_("查询 ")]))),_:1})]),y(ye,{data:W.value},{default:k((()=>[y(we,{align:"left",label:"预览",width:"100"},{default:k((e=>[y(c,{"pic-type":"file","pic-src":e.row.url,preview:""},null,8,["pic-src"])])),_:1}),y(we,{align:"left",label:"日期",prop:"UpdatedAt",width:"180"},{default:k((e=>[h("div",null,b(D(B)(e.row.UpdatedAt)),1)])),_:1}),y(we,{align:"left",label:"文件名/备注",prop:"name",width:"180"},{default:k((e=>[h("div",{class:"cursor-pointer",onClick:a=>(async e=>{V.prompt("请输入文件名或者备注","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/\S/,inputErrorMessage:"不能为空",inputValue:e.name}).then((async({value:a})=>{e.name=a,0===(await i(e)).code&&(E({type:"success",message:"编辑成功!"}),await $())})).catch((()=>{E({type:"info",message:"取消修改"})}))})(e.row)},b(e.row.name),9,P)])),_:1}),y(we,{align:"left",label:"链接",prop:"url","min-width":"300"}),y(we,{align:"left",label:"标签",prop:"tag",width:"100"},{default:k((e=>{var a;return[y(he,{type:"jpg"===(null==(a=e.row.tag)?void 0:a.toLowerCase())?"info":"success","disable-transitions":""},{default:k((()=>[_(b(e.row.tag),1)])),_:2},1032,["type"])]})),_:1}),y(we,{align:"left",label:"操作",width:"160"},{default:k((e=>[y(ge,{icon:"download",type:"primary",link:"",onClick:a=>{var l;(l=e.row).url.indexOf("http://")>-1||l.url.indexOf("https://")>-1?A(l.url,l.name):A(q.value+"/"+l.url,l.name)}},{default:k((()=>n[9]||(n[9]=[_("下载 ")]))),_:2},1032,["onClick"]),y(ge,{icon:"delete",type:"primary",link:"",onClick:a=>(async e=>{V.confirm("此操作将永久删除文件, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await u(e)).code&&(E({type:"success",message:"删除成功!"}),1===W.value.length&&G.value>1&&G.value--,await $())})).catch((()=>{E({type:"info",message:"已取消删除"})}))})(e.row)},{default:k((()=>n[10]||(n[10]=[_("删除 ")]))),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"]),h("div",L,[y(ke,{"current-page":G.value,"page-size":J.value,"page-sizes":[10,30,50,100],style:{float:"right",padding:"20px"},total:H.value,layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:Y,onSizeChange:X},null,8,["current-page","page-size","total"])])])])]),y(Ce,{modelValue:oe.value,"onUpdate:modelValue":n[3]||(n[3]=e=>oe.value=e),onClose:ce,width:"520",title:(0===ie.value.ID?"添加":"编辑")+"分类",draggable:""},{footer:k((()=>[y(ge,{onClick:ce},{default:k((()=>n[11]||(n[11]=[_("取消")]))),_:1}),y(ge,{type:"primary",onClick:re},{default:k((()=>n[12]||(n[12]=[_("确定")]))),_:1})])),default:k((()=>[y(xe,{ref_key:"categoryForm",ref:ue,rules:de.value,model:ie.value,"label-width":"80px"},{default:k((()=>[y(be,{label:"上级分类"},{default:k((()=>[y(Ie,{modelValue:ie.value.pid,"onUpdate:modelValue":n[1]||(n[1]=e=>ie.value.pid=e),data:te.value,"check-strictly":"",props:le,"render-after-expand":!1,style:{width:"240px"}},null,8,["modelValue","data"])])),_:1}),y(be,{label:"分类名称",prop:"name"},{default:k((()=>[y(fe,{modelValue:ie.value.name,"onUpdate:modelValue":n[2]||(n[2]=e=>ie.value.name=e),modelModifiers:{trim:!0},placeholder:"分类名称"},null,8,["modelValue"])])),_:1})])),_:1},8,["rules","model"])])),_:1},8,["modelValue","title"])])),[[_e,F.value,void 0,{fullscreen:!0,lock:!0}]])}}});export{R as default};
