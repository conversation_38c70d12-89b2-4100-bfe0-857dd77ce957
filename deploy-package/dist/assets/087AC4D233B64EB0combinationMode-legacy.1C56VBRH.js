/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.Dvj98d63.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0menuItem-legacy.fHn_s5TX.js","./087AC4D233B64EB0asyncSubmenu-legacy.BIXEZreJ.js"],(function(e,t){"use strict";var a,n,l,u,r,o,i,s,c,d,f,v,p,m,h,y,g,b,x,w,k,_,S;return{setters:[function(e){a=e.default},function(e){n=e.I,l=e.J,u=e.aj,r=e.u,o=e.ac,i=e.a,s=e.K,c=e.R,d=e.g,f=e.c,v=e.o,p=e.d,m=e.f,h=e.w,y=e.F,g=e.D,b=e.v,x=e.i,w=e.X,k=e.n,_=e.b,S=e.ak},null,null],execute:function(){var t={class:"h-full"},A={key:0,class:"bg-white h-[calc(100%-4px)] text-slate-700 dark:text-slate-300 mx-2 dark:bg-slate-900 flex items-center w-[calc(100vw-600px)] overflow-auto"};e("default",Object.assign({name:"GvaAside"},{__name:"combinationMode",props:{mode:{type:String,default:"normal"}},setup:function(e){var B=n(),C=l(B),j=C.device,q=C.config,D=u(),O=r(),M=o(),E=localStorage.getItem("sidebarCollapsed"),I=i(!E||JSON.parse(E)),J=i(""),N=s((function(){return I.value?q.value.layout_side_collapsed_width:q.value.layout_side_width}));c((function(){J.value=D.meta.activeName||D.name})),c((function(){"mobile"===j.value&&(I.value=!0)})),c((function(){localStorage.setItem("sidebarCollapsed",JSON.stringify(I.value))})),S("isCollapse",I);var z=function(e,t,a,n){var l,u,r={},o={};if((null===(l=M.routeMap[e])||void 0===l?void 0:l.parameters)&&(null===(u=M.routeMap[e])||void 0===u||u.parameters.forEach((function(e){"query"===e.type?r[e.key]=e.value:o[e.key]=e.value}))),e!==D.name)if(e.indexOf("http://")>-1||e.indexOf("https://")>-1)window.open(e,"_blank");else if(n){var i=M.setLeftMenu(e);if(i){var s=i.find((function(e){return!e.hidden&&-1===e.path.indexOf("http://")&&-1===e.path.indexOf("https://")}));O.push({name:s.name,query:r,params:o})}else O.push({name:e,query:r,params:o})}else O.push({name:e,query:r,params:o})},L=function(){I.value=!I.value};return function(n,l){var u=d("el-menu"),r=d("el-scrollbar"),o=d("DArrowLeft"),i=d("el-icon"),s=d("DArrowRight");return v(),f("div",t,["head"===e.mode?(v(),f("div",A,[m(u,{"default-active":b(M).topActive,mode:"horizontal",class:"border-r-0 border-b-0 w-full flex gap-1 items-center box-border h-[calc(100%-1px)]","unique-opened":"",onSelect:l[0]||(l[0]=function(e,t,a){return z(e,0,0,!0)})},{default:h((function(){return[(v(!0),f(y,null,g(b(M).topMenu,(function(e){return v(),f(y,null,[e.hidden?p("",!0):(v(),x(a,{key:e.name,"router-info":e,mode:"horizontal"},null,8,["router-info"]))],64)})),256))]})),_:1},8,["default-active"])])):p("",!0),"normal"===e.mode?(v(),f("div",{key:1,class:k(["relative h-full bg-white text-slate-700 dark:text-slate-300 dark:bg-slate-900 border-r shadow dark:shadow-gray-700",I.value?"":"  px-2"]),style:w({width:N.value+"px"})},[m(r,null,{default:h((function(){return[m(u,{collapse:I.value,"collapse-transition":!1,"default-active":J.value,class:"border-r-0 w-full","unique-opened":"",onSelect:l[1]||(l[1]=function(e,t,a){return z(e,0,0,!1)})},{default:h((function(){return[(v(!0),f(y,null,g(b(M).leftMenu,(function(e){return v(),f(y,null,[e.hidden?p("",!0):(v(),x(a,{key:e.name,"router-info":e},null,8,["router-info"]))],64)})),256))]})),_:1},8,["collapse","default-active"])]})),_:1}),_("div",{class:k(["absolute bottom-8 right-2 w-8 h-8 bg-gray-50 dark:bg-slate-800 flex items-center justify-center rounded cursor-pointer",I.value?"right-0 left-0 mx-auto":"right-2"]),onClick:L},[I.value?(v(),x(i,{key:1},{default:h((function(){return[m(s)]})),_:1})):(v(),x(i,{key:0},{default:h((function(){return[m(o)]})),_:1}))],2)],6)):p("",!0)])}}}))}}}));
