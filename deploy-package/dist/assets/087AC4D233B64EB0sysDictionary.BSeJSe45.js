/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{I as e,a,g as l,c as t,o as s,f as u,b as r,w as d,h as n,F as i,D as c,n as o,t as m,au as p,v,aN as y,aO as f,aP as b,ab as x,aQ as g,E as w,aR as h,aS as k}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{_ as D}from"./087AC4D233B64EB0warningBar.D2ZmzbYL.js";import V from"./087AC4D233B64EB0sysDictionaryDetail.CqhrkMX6.js";const _={class:"flex gap-4 p-2"},C={class:"flex-none w-52 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900 rounded p-4"},B={class:"flex justify-between items-center"},I=["onClick"],j={class:"max-w-[160px] truncate"},U={class:"min-w-[40px]"},q={class:"flex-1 bg-white text-slate-700 dark:text-slate-400 dark:bg-slate-900"},E={class:"flex justify-between items-center"},z={class:"text-lg"},A=Object.assign({name:"SysDictionary"},{__name:"sysDictionary",setup(A){const S=e(),F=a(0),O=a({name:null,type:null,status:!0,desc:null}),T=a({name:[{required:!0,message:"请输入字典名（中）",trigger:"blur"}],type:[{required:!0,message:"请输入字典名（英）",trigger:"blur"}],desc:[{required:!0,message:"请输入描述",trigger:"blur"}]}),N=a([]),P=async()=>{const e=await f();0===e.code&&(N.value=e.data,F.value=e.data[0].ID)};P();const Q=a(!1),R=a(""),Z=()=>{Q.value=!1,O.value={name:null,type:null,status:!0,desc:null}},G=a(null),H=async()=>{G.value.validate((async e=>{if(!e)return;let a;switch(R.value){case"create":default:a=await h(O.value);break;case"update":a=await k(O.value)}0===a.code&&(w.success("操作成功"),Z(),P())}))},J=()=>{R.value="create",G.value&&G.value.clearValidate(),Q.value=!0};return(e,a)=>{const f=l("el-button"),h=l("el-icon"),k=l("Delete"),A=l("el-scrollbar"),K=l("el-input"),L=l("el-form-item"),M=l("el-switch"),W=l("el-form"),X=l("el-drawer");return s(),t("div",null,[u(D,{title:"获取字典且缓存方法已在前端utils/dictionary 已经封装完成 不必自己书写 使用方法查看文件内注释"}),r("div",_,[r("div",C,[r("div",B,[a[6]||(a[6]=r("span",{class:"text font-bold"},"字典列表",-1)),u(f,{type:"primary",onClick:J},{default:d((()=>a[5]||(a[5]=[n(" 新增 ")]))),_:1})]),u(A,{class:"mt-4",style:{height:"calc(100vh - 300px)"}},{default:d((()=>[(s(!0),t(i,null,c(N.value,(e=>(s(),t("div",{key:e.ID,class:o(["rounded flex justify-between items-center px-2 py-4 cursor-pointer mt-2 hover:bg-blue-50 dark:hover:bg-blue-900 bg-gray-50 dark:bg-gray-800 gap-4",F.value===e.ID?"text-active":"text-slate-700 dark:text-slate-50"]),onClick:a=>{return l=e,void(F.value=l.ID);var l}},[r("span",j,m(e.name),1),r("div",U,[u(h,{class:"text-blue-500",onClick:p((a=>(async e=>{const a=await b({ID:e.ID,status:e.status});R.value="update",0===a.code&&(O.value=a.data.resysDictionary,Q.value=!0)})(e)),["stop"])},{default:d((()=>[u(v(y))])),_:2},1032,["onClick"]),u(h,{class:"ml-2 text-red-500",onClick:a=>(async e=>{x.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{0===(await g({ID:e.ID})).code&&(w({type:"success",message:"删除成功"}),P())}))})(e)},{default:d((()=>[u(k)])),_:2},1032,["onClick"])])],10,I)))),128))])),_:1})]),r("div",q,[u(V,{"sys-dictionary-i-d":F.value},null,8,["sys-dictionary-i-d"])])]),u(X,{modelValue:Q.value,"onUpdate:modelValue":a[4]||(a[4]=e=>Q.value=e),size:v(S).drawerSize,"show-close":!1,"before-close":Z},{header:d((()=>[r("div",E,[r("span",z,m("create"===R.value?"添加字典":"修改字典"),1),r("div",null,[u(f,{onClick:Z},{default:d((()=>a[7]||(a[7]=[n(" 取 消 ")]))),_:1}),u(f,{type:"primary",onClick:H},{default:d((()=>a[8]||(a[8]=[n(" 确 定 ")]))),_:1})])])])),default:d((()=>[u(W,{ref_key:"drawerForm",ref:G,model:O.value,rules:T.value,"label-width":"110px"},{default:d((()=>[u(L,{label:"字典名（中）",prop:"name"},{default:d((()=>[u(K,{modelValue:O.value.name,"onUpdate:modelValue":a[0]||(a[0]=e=>O.value.name=e),placeholder:"请输入字典名（中）",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),u(L,{label:"字典名（英）",prop:"type"},{default:d((()=>[u(K,{modelValue:O.value.type,"onUpdate:modelValue":a[1]||(a[1]=e=>O.value.type=e),placeholder:"请输入字典名（英）",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1}),u(L,{label:"状态",prop:"status",required:""},{default:d((()=>[u(M,{modelValue:O.value.status,"onUpdate:modelValue":a[2]||(a[2]=e=>O.value.status=e),"active-text":"开启","inactive-text":"停用"},null,8,["modelValue"])])),_:1}),u(L,{label:"描述",prop:"desc"},{default:d((()=>[u(K,{modelValue:O.value.desc,"onUpdate:modelValue":a[3]||(a[3]=e=>O.value.desc=e),placeholder:"请输入描述",clearable:"",style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","size"])])}}});export{A as default};
