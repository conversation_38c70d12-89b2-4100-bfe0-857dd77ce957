/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,n){"use strict";var t,a,o,r,c,u,i,l,s,d,f;return{setters:[function(e){t=e.k,a=e.g,o=e.c,r=e.o,c=e.f,u=e.w,i=e.b,l=e.h,s=e.v,d=e.x,f=e.E}],execute:function(){var n={class:"gva-form-box"};e("default",{__name:"index",setup:function(e){var p=t().token,v=function(e){if(0===e.code){var n="";e.data&&e.data.forEach((function(e,t){n+="".concat(t+1,".").concat(e.msg,"\n")})),alert(n)}else f.error(e.msg)};return function(e,t){var f=a("upload-filled"),g=a("el-icon"),_=a("el-upload");return r(),o("div",n,[c(_,{drag:"",action:"".concat(s(d)(),"/autoCode/installPlugin"),"show-file-list":!1,"on-success":v,"on-error":v,headers:{"x-token":s(p)},name:"plug"},{tip:u((function(){return t[0]||(t[0]=[i("div",{class:"el-upload__tip"},"请把安装包的zip拖拽至此处上传",-1)])})),default:u((function(){return[c(g,{class:"el-icon--upload"},{default:u((function(){return[c(f)]})),_:1}),t[1]||(t[1]=i("div",{class:"el-upload__text"},[l("拖拽或"),i("em",null,"点击上传")],-1))]})),_:1},8,["action","headers"])])}}})}}}));
