/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return n};var r,n={},a=Object.prototype,o=a.hasOwnProperty,l=Object.defineProperty||function(e,t,r){e[t]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(r){d=function(e,t,r){return e[t]=r}}function f(e,t,r,n){var a=t&&t.prototype instanceof g?t:g,o=Object.create(a.prototype),i=new O(n||[]);return l(o,"_invoke",{value:V(e,r,i)}),o}function p(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}n.wrap=f;var h="suspendedStart",m="suspendedYield",y="executing",b="completed",v={};function g(){}function w(){}function x(){}var k={};d(k,s,(function(){return this}));var _=Object.getPrototypeOf,q=_&&_(_(S([])));q&&q!==a&&o.call(q,s)&&(k=q);var N=x.prototype=g.prototype=Object.create(k);function L(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function P(t,r){function n(a,l,i,s){var u=p(t[a],t,l);if("throw"!==u.type){var c=u.arg,d=c.value;return d&&"object"==e(d)&&o.call(d,"__await")?r.resolve(d.__await).then((function(e){n("next",e,i,s)}),(function(e){n("throw",e,i,s)})):r.resolve(d).then((function(e){c.value=e,i(c)}),(function(e){return n("throw",e,i,s)}))}s(u.arg)}var a;l(this,"_invoke",{value:function(e,t){function o(){return new r((function(r,a){n(e,t,r,a)}))}return a=a?a.then(o,o):o()}})}function V(e,t,n){var a=h;return function(o,l){if(a===y)throw Error("Generator is already running");if(a===b){if("throw"===o)throw l;return{value:r,done:!0}}for(n.method=o,n.arg=l;;){var i=n.delegate;if(i){var s=E(i,n);if(s){if(s===v)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(a===h)throw a=b,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);a=y;var u=p(e,t,n);if("normal"===u.type){if(a=n.done?b:m,u.arg===v)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(a=b,n.method="throw",n.arg=u.arg)}}}function E(e,t){var n=t.method,a=e.iterator[n];if(a===r)return t.delegate=null,"throw"===n&&e.iterator.return&&(t.method="return",t.arg=r,E(e,t),"throw"===t.method)||"return"!==n&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var o=p(a,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,v;var l=o.arg;return l?l.done?(t[e.resultName]=l.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=r),t.delegate=null,v):l:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,v)}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function S(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,l=function e(){for(;++a<t.length;)if(o.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=r,e.done=!0,e};return l.next=l}}throw new TypeError(e(t)+" is not iterable")}return w.prototype=x,l(N,"constructor",{value:x,configurable:!0}),l(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,c,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,d(e,c,"GeneratorFunction")),e.prototype=Object.create(N),e},n.awrap=function(e){return{__await:e}},L(P.prototype),d(P.prototype,u,(function(){return this})),n.AsyncIterator=P,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var l=new P(f(e,t,r,a),o);return n.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},L(N),d(N,c,"Generator"),d(N,s,(function(){return this})),d(N,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},n.values=S,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=r,this.done=!1,this.delegate=null,this.method="next",this.arg=r,this.tryEntries.forEach(T),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=r)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(n,a){return i.type="throw",i.arg=e,t.next=n,a&&(t.method="next",t.arg=r),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var l=this.tryEntries[a],i=l.completion;if("root"===l.tryLoc)return n("end");if(l.tryLoc<=this.prev){var s=o.call(l,"catchLoc"),u=o.call(l,"finallyLoc");if(s&&u){if(this.prev<l.catchLoc)return n(l.catchLoc,!0);if(this.prev<l.finallyLoc)return n(l.finallyLoc)}else if(s){if(this.prev<l.catchLoc)return n(l.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return n(l.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&o.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var a=n;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),T(r),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var a=n.arg;T(r)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:S(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=r),v}},n}function r(e,t,r,n,a,o,l){try{var i=e[o](l),s=i.value}catch(e){return void r(e)}i.done?t(s):Promise.resolve(s).then(n,a)}System.register(["./087AC4D233B64EB0initdb-legacy.BdL50Td-.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,n){"use strict";var a,o,l,i,s,u,c,d,f,p,h,m,y,b,v,g,w,x,k;return{setters:[function(e){a=e._,o=e.i},function(e){l=e._,i=e.r,s=e.a,u=e.c,c=e.b,d=e.d,f=e.n,p=e.e,h=e.f,m=e.w,y=e.g,b=e.u,v=e.o,g=e.h,w=e.i,x=e.E,k=e.j}],execute:function(){var n=document.createElement("style");n.textContent=".slide-in-fwd-top[data-v-e790578a]{animation:slide-in-fwd-top-e790578a .4s cubic-bezier(.25,.46,.45,.94) both}.slide-out-right[data-v-e790578a]{animation:slide-out-right-e790578a .5s cubic-bezier(.55,.085,.68,.53) both}.slide-in-left[data-v-e790578a]{animation:slide-in-left-e790578a .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-fwd-top-e790578a{0%{transform:translateZ(-1400px) translateY(-800px);opacity:0}to{transform:translateZ(0) translateY(0);opacity:1}}@keyframes slide-out-right-e790578a{0%{transform:translate(0);opacity:1}to{transform:translate(1000px);opacity:0}}@keyframes slide-in-left-e790578a{0%{transform:translate(-1000px);opacity:0}to{transform:translate(0);opacity:1}}@media (max-width: 750px){.form[data-v-e790578a]{width:94vw!important;padding:0}}\n/*$vite$:1*/",document.head.appendChild(n);var _={class:"rounded-lg flex items-center justify-evenly w-full h-full relative md:w-screen md:h-screen md:bg-[#194bfb] overflow-hidden"},q={class:"rounded-md w-full h-full flex items-center justify-center overflow-hidden"},N={class:"text-lg"},L={class:"flex items-center justify-between mt-8"},P={style:{"text-align":"right"}},V=Object.assign({name:"Init"},{__name:"index",setup:function(e){var n=b(),l=i({showReadme:!1,showForm:!1}),V=function(){l.showReadme=!1,setTimeout((function(){l.showForm=!0}),20)},E=function(){window.open("https://www.gin-vue-admin.com/guide/start-quickly/env.html")},j=s(!1),T=i({adminPassword:"123456",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""}),O=function(e){switch(e){case"mysql":Object.assign(T,{adminPassword:"123456",reAdminPassword:"",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""});break;case"pgsql":Object.assign(T,{adminPassword:"123456",dbType:"pgsql",host:"127.0.0.1",port:"5432",userName:"postgres",password:"",dbName:"gva",dbPath:"",template:"template0"});break;case"oracle":Object.assign(T,{adminPassword:"123456",dbType:"oracle",host:"127.0.0.1",port:"1521",userName:"oracle",password:"",dbName:"gva",dbPath:""});break;case"mssql":Object.assign(T,{adminPassword:"123456",dbType:"mssql",host:"127.0.0.1",port:"1433",userName:"mssql",password:"",dbName:"gva",dbPath:""});break;case"sqlite":Object.assign(T,{adminPassword:"123456",dbType:"sqlite",host:"",port:"",userName:"",password:"",dbName:"gva",dbPath:""});break;default:Object.assign(T,{adminPassword:"123456",dbType:"mysql",host:"127.0.0.1",port:"3306",userName:"root",password:"",dbName:"gva",dbPath:""})}},S=function(){var e,a=(e=t().mark((function e(){var r,a;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!(T.adminPassword.length<6)){e.next=3;break}return x({type:"error",message:"密码长度不能小于6位"}),e.abrupt("return");case 3:return r=k.service({lock:!0,text:"正在初始化数据库，请稍候",spinner:"loading",background:"rgba(0, 0, 0, 0.7)"}),e.prev=4,e.next=7,o(T);case 7:0===(a=e.sent).code&&(j.value=!0,x({type:"success",message:a.msg}),n.push({name:"Login"})),r.close(),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(4),r.close();case 15:case"end":return e.stop()}}),e,null,[[4,12]])})),function(){var t=this,n=arguments;return new Promise((function(a,o){var l=e.apply(t,n);function i(e){r(l,a,o,i,s,"next",e)}function s(e){r(l,a,o,i,s,"throw",e)}i(void 0)}))});return function(){return a.apply(this,arguments)}}();return function(e,t){var r=y("el-button"),n=y("el-input"),o=y("el-form-item"),i=y("el-option"),s=y("el-select"),b=y("el-form");return v(),u("div",_,[c("div",q,[t[13]||(t[13]=c("div",{class:"oblique h-[130%] w-3/5 bg-white dark:bg-slate-900 transform -rotate-12 absolute -ml-80"},null,-1)),l.showForm?d("",!0):(v(),u("div",{key:0,class:f([l.showReadme?"slide-out-right":"slide-in-fwd-top"])},[c("div",N,[t[11]||(t[11]=p('<div class="font-sans text-4xl font-bold text-center mb-4 dark:text-white" data-v-e790578a> GIN-VUE-ADMIN </div><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a>初始化须知</p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 1.您需有用一定的VUE和GOLANG基础 </p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 2.请您确认是否已经阅读过<a class="text-blue-600 font-bold" href="https://www.gin-vue-admin.com" target="_blank" data-v-e790578a>官方文档</a><a class="text-blue-600 font-bold" href="https://www.bilibili.com/video/BV1kv4y1g7nT?p=2" target="_blank" data-v-e790578a>初始化视频</a></p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 3.请您确认是否了解后续的配置流程 </p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 4.如果您使用mysql数据库，请确认数据库引擎为<span class="text-red-600 font-bold text-3xl ml-2" data-v-e790578a>innoDB</span></p><p class="text-gray-600 dark:text-gray-300 mb-2" data-v-e790578a> 注：开发组不为文档中书写过的内容提供无偿服务 </p>',7)),c("p",L,[h(r,{type:"primary",size:"large",onClick:E},{default:m((function(){return t[9]||(t[9]=[g(" 阅读文档 ")])})),_:1}),h(r,{type:"primary",size:"large",onClick:V},{default:m((function(){return t[10]||(t[10]=[g(" 我已确认 ")])})),_:1})])])],2)),l.showForm?(v(),u("div",{key:1,class:f([[l.showForm?"slide-in-left":"slide-out-right"],"w-96"])},[h(b,{ref:"formRef",model:T,"label-width":"100px",size:"large"},{default:m((function(){return[h(o,{label:"管理员密码"},{default:m((function(){return[h(n,{modelValue:T.adminPassword,"onUpdate:modelValue":t[0]||(t[0]=function(e){return T.adminPassword=e}),placeholder:"admin账号的默认密码"},null,8,["modelValue"])]})),_:1}),h(o,{label:"数据库类型"},{default:m((function(){return[h(s,{modelValue:T.dbType,"onUpdate:modelValue":t[1]||(t[1]=function(e){return T.dbType=e}),placeholder:"请选择",class:"w-full",onChange:O},{default:m((function(){return[h(i,{key:"mysql",label:"mysql",value:"mysql"}),h(i,{key:"pgsql",label:"pgsql",value:"pgsql"}),h(i,{key:"oracle",label:"oracle",value:"oracle"}),h(i,{key:"mssql",label:"mssql",value:"mssql"}),h(i,{key:"sqlite",label:"sqlite",value:"sqlite"})]})),_:1},8,["modelValue"])]})),_:1}),"sqlite"!==T.dbType?(v(),w(o,{key:0,label:"host"},{default:m((function(){return[h(n,{modelValue:T.host,"onUpdate:modelValue":t[2]||(t[2]=function(e){return T.host=e}),placeholder:"请输入数据库链接"},null,8,["modelValue"])]})),_:1})):d("",!0),"sqlite"!==T.dbType?(v(),w(o,{key:1,label:"port"},{default:m((function(){return[h(n,{modelValue:T.port,"onUpdate:modelValue":t[3]||(t[3]=function(e){return T.port=e}),placeholder:"请输入数据库端口"},null,8,["modelValue"])]})),_:1})):d("",!0),"sqlite"!==T.dbType?(v(),w(o,{key:2,label:"userName"},{default:m((function(){return[h(n,{modelValue:T.userName,"onUpdate:modelValue":t[4]||(t[4]=function(e){return T.userName=e}),placeholder:"请输入数据库用户名"},null,8,["modelValue"])]})),_:1})):d("",!0),"sqlite"!==T.dbType?(v(),w(o,{key:3,label:"password"},{default:m((function(){return[h(n,{modelValue:T.password,"onUpdate:modelValue":t[5]||(t[5]=function(e){return T.password=e}),placeholder:"请输入数据库密码（没有则为空）"},null,8,["modelValue"])]})),_:1})):d("",!0),h(o,{label:"dbName"},{default:m((function(){return[h(n,{modelValue:T.dbName,"onUpdate:modelValue":t[6]||(t[6]=function(e){return T.dbName=e}),placeholder:"请输入数据库名称"},null,8,["modelValue"])]})),_:1}),"sqlite"===T.dbType?(v(),w(o,{key:4,label:"dbPath"},{default:m((function(){return[h(n,{modelValue:T.dbPath,"onUpdate:modelValue":t[7]||(t[7]=function(e){return T.dbPath=e}),placeholder:"请输入sqlite数据库文件存放路径"},null,8,["modelValue"])]})),_:1})):d("",!0),"pgsql"===T.dbType?(v(),w(o,{key:5,label:"template"},{default:m((function(){return[h(n,{modelValue:T.template,"onUpdate:modelValue":t[8]||(t[8]=function(e){return T.template=e}),placeholder:"请输入postgresql指定template"},null,8,["modelValue"])]})),_:1})):d("",!0),h(o,null,{default:m((function(){return[c("div",P,[h(r,{type:"primary",onClick:S},{default:m((function(){return t[12]||(t[12]=[g("立即初始化")])})),_:1})])]})),_:1})]})),_:1},8,["model"])],2)):d("",!0)]),t[14]||(t[14]=c("div",{class:"hidden md:block w-1/2 h-full float-right bg-[#194bfb]"},[c("img",{class:"h-full",src:a,alt:"banner"})],-1))])}}});e("default",l(V,[["__scopeId","data-v-e790578a"]]))}}}))}();
