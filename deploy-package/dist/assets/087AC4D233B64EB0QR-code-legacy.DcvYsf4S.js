/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function t(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function e(e){for(var r=1;r<arguments.length;r++){var n=null!=arguments[r]?arguments[r]:{};r%2?t(Object(n),!0).forEach((function(t){u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):t(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function r(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,u=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){u=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(u)throw o}}return s}}(t,e)||o(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=o(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,i=function(){};return{s:i,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,l=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){l=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(l)throw a}}}}function o(t,e){if(t){if("string"==typeof t)return i(t,e);var r={}.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(t,e):void 0}}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",u=i.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),s=new L(n||[]);return o(a,"_invoke",{value:D(t,r,s)}),a}function h(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var d="suspendedStart",g="suspendedYield",v="executing",m="completed",y={};function b(){}function w(){}function x(){}var k={};c(k,s,(function(){return this}));var C=Object.getPrototypeOf,A=C&&C(C(R([])));A&&A!==r&&n.call(A,s)&&(k=A);var P=x.prototype=b.prototype=Object.create(k);function B(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function E(t,e){function r(o,i,a,s){var l=h(t[o],t,i);if("throw"!==l.type){var u=l.arg,c=u.value;return c&&"object"==p(c)&&n.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,a,s)}),(function(t){r("throw",t,a,s)})):e.resolve(c).then((function(t){u.value=t,a(u)}),(function(t){return r("throw",t,a,s)}))}s(l.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function D(e,r,n){var o=d;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var s=n.delegate;if(s){var l=_(s,n);if(l){if(l===y)continue;return l}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===d)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=v;var u=h(e,r,n);if("normal"===u.type){if(o=n.done?m:g,u.arg===y)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(o=m,n.method="throw",n.arg=u.arg)}}}function _(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,_(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),y;var i=h(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,y;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,y):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,y)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function R(e){if(e||""===e){var r=e[s];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(p(e)+" is not iterable")}return w.prototype=x,o(P,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:w,configurable:!0}),w.displayName=c(x,u,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,c(t,u,"GeneratorFunction")),t.prototype=Object.create(P),t},e.awrap=function(t){return{__await:t}},B(E.prototype),c(E.prototype,l,(function(){return this})),e.AsyncIterator=E,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new E(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},B(P),c(P,u,"Generator"),c(P,s,(function(){return this})),c(P,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=R,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return s.type="throw",s.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=n.call(a,"catchLoc"),u=n.call(a,"finallyLoc");if(l&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),y},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),T(r),y}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;T(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:R(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),y}},e}function s(t,e,r,n,o,i,a){try{var s=t[i](a),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(n,o)}function l(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){s(i,n,o,a,l,"next",t)}function l(t){s(i,n,o,a,l,"throw",t)}a(void 0)}))}}function u(t,e,r){return(e=d(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function c(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function f(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,d(n.key),n)}}function h(t,e,r){return e&&f(t.prototype,e),r&&f(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function d(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}function p(t){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},p(t)}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0image-legacy.CRPxTu2y.js","./087AC4D233B64EB0vue-cropper.es-legacy.CQhcf0py.js","./087AC4D233B64EB0logo-legacy.BFIBdCh0.js"],(function(t,o){"use strict";var i,s,f,d,g,v,m,y,b,w,x,k,C,A,P,B,E,D,_,S,T,L,R,I,O,M,j,N,U,z,F;return{setters:[function(t){i=t.s,s=t._,f=t.k,d=t.g,g=t.c,v=t.o,m=t.f,y=t.w,b=t.v,w=t.ah,x=t.h,k=t.x,C=t.E,A=t.a,P=t.q,B=t.b,E=t.z,D=t.A,_=t.y,S=t.B,T=t.F,L=t.D,R=t.i,I=t.X,O=t.t,M=t.d},function(t){j=t.I,N=t.i,U=t.a},function(t){z=t.M},function(t){F=t.l}],execute:function(){var o=document.createElement("style");o.textContent=".image-uploader[data-v-f8955cc8]{border:1px dashed #d9d9d9;width:180px;border-radius:6px;cursor:pointer;position:relative;overflow:hidden;border-color:#409eff}.image-uploader-icon[data-v-f8955cc8]{font-size:28px;color:#8c939d;width:178px;height:178px;line-height:178px;text-align:center}.image[data-v-f8955cc8]{width:178px;height:178px;display:block}[data-v-7f698837] .vue-cropper{background:transparent}\n/*$vite$:1*/",document.head.appendChild(o);t("g",(function(t){return i({url:"/fileUploadAndDownload/getFileList",method:"post",data:t})})),t("c",(function(t){return i({url:"/fileUploadAndDownload/deleteFile",method:"post",data:t})})),t("e",(function(t){return i({url:"/fileUploadAndDownload/editFileName",method:"post",data:t})})),t("i",(function(t){return i({url:"/fileUploadAndDownload/importURL",method:"post",data:t})})),t("U",s(Object.assign({name:"UploadImage"},{__name:"image",props:{imageUrl:{type:String,default:""},fileSize:{type:Number,default:2048},maxWH:{type:Number,default:1920},classId:{type:Number,default:0}},emits:["on-success"],setup:function(t,e){var r=e.emit,n=t,o=f().token,i=function(t){var e,r,o="image/jpeg"===(null===(e=t.type)||void 0===e?void 0:e.toLowerCase()),i="image/png"===(null===(r=t.type)||void 0===r?void 0:r.toLowerCase());if(!o&&!i)return C.error("上传头像图片只能是 jpg或png 格式!"),!1;var a=t.size/1024<n.fileSize;return a||new j(t,n.fileSize,n.maxWH).compress()},a=function(t){var e=t.data;e.file&&r("on-success",e.file.url)};return function(t,e){var r=d("el-button"),s=d("el-upload");return v(),g("div",null,[m(s,{action:"".concat(b(k)(),"/fileUploadAndDownload/upload"),"show-file-list":!1,"on-success":a,"before-upload":i,multiple:!1,data:{classId:n.classId},headers:{"x-token":b(o)}},{default:y((function(){return[m(r,{type:"primary",icon:b(w)},{default:y((function(){return e[0]||(e[0]=[x("压缩上传")])})),_:1},8,["icon"])]})),_:1},8,["action","data","headers"])])}}}),[["__scopeId","data-v-f8955cc8"]])),t("_",Object.assign({name:"UploadCommon"},{__name:"common",props:{classId:{type:Number,default:0}},emits:["on-success"],setup:function(t,e){var r=e.emit,n=f().token,o=t,i=r,a=A(!1),s=function(t){a.value=!0;var e=t.size/1024/1024<.5,r=t.size/1024/1024<5,n=N(t.type),o=U(t.type),i=!0;return n||o||(C.error("上传图片只能是 jpg,png,svg,webp 格式, 上传视频只能是 mp4,webm 格式!"),a.value=!1,i=!1),!r&&n&&(C.error("上传视频大小不能超过 5MB"),a.value=!1,i=!1),!e&&o&&(C.error("未压缩的上传图片大小不能超过 500KB，请使用压缩上传"),a.value=!1,i=!1),i},l=function(t){var e=t.data;e.file&&i("on-success",e.file.url)},u=function(){C({type:"error",message:"上传失败"}),a.value=!1};return function(t,e){var r=d("el-button"),i=d("el-upload");return v(),g("div",null,[m(i,{action:"".concat(b(k)(),"/fileUploadAndDownload/upload"),"before-upload":s,"on-error":u,"on-success":l,"show-file-list":!1,data:{classId:o.classId},headers:{"x-token":b(n)},multiple:"",class:"upload-btn"},{default:y((function(){return[m(r,{type:"primary",icon:b(w)},{default:y((function(){return e[0]||(e[0]=[x("普通上传")])})),_:1},8,["icon"])]})),_:1},8,["action","data","headers"])])}}})),t("b",(function(){return i({url:"/attachmentCategory/getCategoryList",method:"get"})})),t("f",(function(t){return i({url:"/attachmentCategory/addCategory",method:"post",data:t})})),t("d",(function(t){return i({url:"/attachmentCategory/deleteCategory",method:"post",data:t})}));var G={class:"flex gap-[30px] h-[600px]"},H={class:"flex flex-col flex-1"},q={class:"flex-1 bg-[#f8f8f8] rounded-lg overflow-hidden"},Q={class:"mt-[20px] flex items-center p-[10px] bg-white rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]"},K={class:"w-[340px]"},X={class:"bg-white p-5 rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]"},V={class:"w-full h-full relative overflow-hidden"},$=["src"],Y={class:"dialog-footer"};t("C",s(Object.assign({name:"CropperImage"},{__name:"cropper",props:{classId:{type:Number,default:0}},emits:["on-success"],setup:function(t,e){var r=e.emit,n=t,o=A(null),i=A(!1),a=A(""),s=A(null),l=P().proxy,u=A({}),c=A(!1),f=function(t){l.$refs.cropperRef.changeScale(t)},h=A([{label:"1:1",value:[1,1]},{label:"16:9",value:[16,9]},{label:"9:16",value:[9,16]},{label:"4:3",value:[4,3]},{label:"自由比例",value:[]}]),p=A([1,1]),w=A(300),M=A(300),j=A(!1),N=A(4),U=function(){switch(p.value=h.value[N.value].value,N.value){case 0:w.value=300,M.value=300,j.value=!0;break;case 1:w.value=300,M.value=168.75,j.value=!0;break;case 2:w.value=168.75,M.value=300,j.value=!0;break;case 3:w.value=300,M.value=225,j.value=!0;break;default:w.value=300,M.value=300,j.value=!1}},F=function(t){if(t.raw.type.includes("image")){if(t.raw.size/1024/1024>8)return C.error("文件大小不能超过8MB!"),!1;var e=new FileReader;e.onload=function(t){a.value=t.target.result,i.value=!0},e.readAsDataURL(t.raw)}else C.error("请选择图片文件")},W=function(t){-90===t?l.$refs.cropperRef.rotateLeft():l.$refs.cropperRef.rotateRight()},J=function(t){u.value=t},Z=function(){c.value=!0,l.$refs.cropperRef.getCropBlob((function(t){try{var e=new File([t],"".concat(Date.now(),".jpg"),{type:"image/jpeg"});o.value.clearFiles(),o.value.handleStart(e),o.value.submit()}catch(r){c.value=!1,C.error("上传失败: "+r.message)}}))},tt=function(t){var e=t.data;e&&setTimeout((function(){c.value=!1,i.value=!1,u.value={},C.success("上传成功"),r("on-success",e.url)}),1e3)};return function(t,e){var r=d("el-button"),l=d("el-upload"),C=d("el-tooltip"),A=d("el-button-group"),P=d("el-option"),et=d("el-select"),rt=d("el-dialog");return v(),g(T,null,[m(l,{ref_key:"uploadRef",ref:o,action:"".concat(b(k)(),"/fileUploadAndDownload/upload"),accept:"image/*","show-file-list":!1,"auto-upload":!1,data:{classId:n.classId},"on-success":tt,"on-change":F,headers:{"x-token":t.token}},{default:y((function(){return[m(r,{type:"primary",icon:"crop"},{default:y((function(){return e[8]||(e[8]=[x(" 裁剪上传")])})),_:1})]})),_:1},8,["action","data","headers"]),m(rt,{modelValue:i.value,"onUpdate:modelValue":e[6]||(e[6]=function(t){return i.value=t}),title:"图片裁剪",width:"1200px","append-to-body":"",onClose:e[7]||(e[7]=function(t){return i.value=!1}),"close-on-click-modal":!1,draggable:""},{footer:y((function(){return[B("div",Y,[m(r,{onClick:e[5]||(e[5]=function(t){return i.value=!1})},{default:y((function(){return e[10]||(e[10]=[x("取 消")])})),_:1}),m(r,{type:"primary",onClick:Z,loading:c.value},{default:y((function(){return[x(O(c.value?"上传中...":"上 传"),1)]})),_:1},8,["loading"])])]})),default:y((function(){return[B("div",G,[B("div",H,[B("div",q,[m(b(z),{ref_key:"cropperRef",ref:s,img:a.value,outputType:"jpeg",autoCrop:!0,autoCropWidth:w.value,autoCropHeight:M.value,fixedBox:!1,fixed:j.value,fixedNumber:p.value,centerBox:!0,canMoveBox:!0,full:!1,maxImgSize:1200,original:!0,onRealTime:J},null,8,["img","autoCropWidth","autoCropHeight","fixed","fixedNumber"])]),B("div",Q,[m(A,null,{default:y((function(){return[m(C,{content:"向左旋转"},{default:y((function(){return[m(r,{onClick:e[0]||(e[0]=function(t){return W(-90)}),icon:b(E)},null,8,["icon"])]})),_:1}),m(C,{content:"向右旋转"},{default:y((function(){return[m(r,{onClick:e[1]||(e[1]=function(t){return W(90)}),icon:b(D)},null,8,["icon"])]})),_:1}),m(r,{icon:b(_),onClick:e[2]||(e[2]=function(t){return f(1)})},null,8,["icon"]),m(r,{icon:b(S),onClick:e[3]||(e[3]=function(t){return f(-1)})},null,8,["icon"])]})),_:1}),m(et,{modelValue:N.value,"onUpdate:modelValue":e[4]||(e[4]=function(t){return N.value=t}),placeholder:"选择比例",class:"w-32 ml-4",onChange:U},{default:y((function(){return[(v(!0),g(T,null,L(h.value,(function(t,e){return v(),R(P,{key:e,label:t.label,value:e},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])])]),B("div",K,[B("div",X,[e[9]||(e[9]=B("div",{class:"mb-[15px] text-gray-600"},"裁剪预览",-1)),B("div",{class:"bg-white p-5 rounded-lg shadow-[0_2px_12px_rgba(0,0,0,0.1)]",style:I({width:u.value.w+"px",height:u.value.h+"px"})},[B("div",V,[B("img",{src:u.value.url,style:I(u.value.img),alt:"",class:"max-w-none absolute transition-all duration-300 ease-in-out image-render-pixelated origin-[0_0]"},null,12,$)])],4)])])])]})),_:1},8,["modelValue"])],64)}}}),[["__scopeId","data-v-7f698837"]]));function W(t){return""===t?t:"true"===t||"1"==t}function J(t,e){return new Promise((function(e,r){var n=new XMLHttpRequest;n.responseType="blob",n.onload=function(){var t=new FileReader;t.onloadend=function(){e(t.result)},t.readAsArrayBuffer(n.response)},n.open("GET",t),n.send()}))}function Z(t){if("string"!=typeof t)throw new TypeError("Path must be a string. Received "+JSON.stringify(t))}function tt(t,e){for(var r,n="",o=0,i=-1,a=0,s=0;s<=t.length;++s){if(s<t.length)r=t.charCodeAt(s);else{if(47===r)break;r=47}if(47===r){if(i===s-1||1===a);else if(i!==s-1&&2===a){if(n.length<2||2!==o||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2))if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",o=0):o=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),i=s,a=0;continue}}else if(2===n.length||1===n.length){n="",o=0,i=s,a=0;continue}e&&(n.length>0?n+="/..":n="..",o=2)}else n.length>0?n+="/"+t.slice(i+1,s):n=t.slice(i+1,s),o=s-i-1;i=s,a=0}else 46===r&&-1!==a?++a:a=-1}return n}var et={resolve:function(){for(var t,e="",r=!1,n=arguments.length-1;n>=-1&&!r;n--){var o;n>=0?o=arguments[n]:(void 0===t&&(t=process.cwd()),o=t),Z(o),0!==o.length&&(e=o+"/"+e,r=47===o.charCodeAt(0))}return e=tt(e,!r),r?e.length>0?"/"+e:"/":e.length>0?e:"."},normalize:function(t){if(Z(t),0===t.length)return".";var e=47===t.charCodeAt(0),r=47===t.charCodeAt(t.length-1);return 0!==(t=tt(t,!e)).length||e||(t="."),t.length>0&&r&&(t+="/"),e?"/"+t:t},isAbsolute:function(t){return Z(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,e=0;e<arguments.length;++e){var r=arguments[e];Z(r),r.length>0&&(void 0===t?t=r:t+="/"+r)}return void 0===t?".":et.normalize(t)},relative:function(t,e){if(Z(t),Z(e),t===e)return"";if((t=et.resolve(t))===(e=et.resolve(e)))return"";for(var r=1;r<t.length&&47===t.charCodeAt(r);++r);for(var n=t.length,o=n-r,i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var a=e.length-i,s=o<a?o:a,l=-1,u=0;u<=s;++u){if(u===s){if(a>s){if(47===e.charCodeAt(i+u))return e.slice(i+u+1);if(0===u)return e.slice(i+u)}else o>s&&(47===t.charCodeAt(r+u)?l=u:0===u&&(l=0));break}var c=t.charCodeAt(r+u);if(c!==e.charCodeAt(i+u))break;47===c&&(l=u)}var f="";for(u=r+l+1;u<=n;++u)u!==n&&47!==t.charCodeAt(u)||(0===f.length?f+="..":f+="/..");return f.length>0?f+e.slice(i+l):(i+=l,47===e.charCodeAt(i)&&++i,e.slice(i))},_makeLong:function(t){return t},dirname:function(t){if(Z(t),0===t.length)return".";for(var e=t.charCodeAt(0),r=47===e,n=-1,o=!0,i=t.length-1;i>=1;--i)if(47===(e=t.charCodeAt(i))){if(!o){n=i;break}}else o=!1;return-1===n?r?"/":".":r&&1===n?"//":t.slice(0,n)},basename:function(t,e){if(void 0!==e&&"string"!=typeof e)throw new TypeError('"ext" argument must be a string');Z(t);var r,n=0,o=-1,i=!0;if(void 0!==e&&e.length>0&&e.length<=t.length){if(e.length===t.length&&e===t)return"";var a=e.length-1,s=-1;for(r=t.length-1;r>=0;--r){var l=t.charCodeAt(r);if(47===l){if(!i){n=r+1;break}}else-1===s&&(i=!1,s=r+1),a>=0&&(l===e.charCodeAt(a)?-1==--a&&(o=r):(a=-1,o=s))}return n===o?o=s:-1===o&&(o=t.length),t.slice(n,o)}for(r=t.length-1;r>=0;--r)if(47===t.charCodeAt(r)){if(!i){n=r+1;break}}else-1===o&&(i=!1,o=r+1);return-1===o?"":t.slice(n,o)},extname:function(t){Z(t);for(var e=-1,r=0,n=-1,o=!0,i=0,a=t.length-1;a>=0;--a){var s=t.charCodeAt(a);if(47!==s)-1===n&&(o=!1,n=a+1),46===s?-1===e?e=a:1!==i&&(i=1):-1!==e&&(i=-1);else if(!o){r=a+1;break}}return-1===e||-1===n||0===i||1===i&&e===n-1&&e===r+1?"":t.slice(e,n)},format:function(t){if(null===t||"object"!==p(t))throw new TypeError('The "pathObject" argument must be of type Object. Received type '+p(t));return function(t,e){var r=e.dir||e.root,n=e.base||(e.name||"")+(e.ext||"");return r?r===e.root?r+n:r+t+n:n}("/",t)},parse:function(t){Z(t);var e={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return e;var r,n=t.charCodeAt(0),o=47===n;o?(e.root="/",r=1):r=0;for(var i=-1,a=0,s=-1,l=!0,u=t.length-1,c=0;u>=r;--u)if(47!==(n=t.charCodeAt(u)))-1===s&&(l=!1,s=u+1),46===n?-1===i?i=u:1!==c&&(c=1):-1!==i&&(c=-1);else if(!l){a=u+1;break}return-1===i||-1===s||0===c||1===c&&i===s-1&&i===a+1?-1!==s&&(e.base=e.name=0===a&&o?t.slice(1,s):t.slice(a,s)):(0===a&&o?(e.name=t.slice(1,i),e.base=t.slice(1,s)):(e.name=t.slice(a,i),e.base=t.slice(a,s)),e.ext=t.slice(i,s)),a>0?e.dir=t.slice(0,a-1):o&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};et.posix=et;var rt=et.extname,nt=et.basename,ot=function(){return h((function t(){c(this,t);var e="undefined"==typeof global,r="image/png",n="image/jpeg",o="image/jpeg",i="image/webp",a="application/pdf",s="image/svg+xml";Object.assign(this,{toMime:this.toMime.bind(this),fromMime:this.fromMime.bind(this),expected:e?'"png", "jpg", or "webp"':'"png", "jpg", "pdf", or "svg"',formats:e?{png:r,jpg:n,jpeg:o,webp:i}:{png:r,jpg:n,jpeg:o,pdf:a,svg:s},mimes:e?u(u(u({},r,"png"),n,"jpg"),i,"webp"):u(u(u(u({},r,"png"),n,"jpg"),a,"pdf"),s,"svg")})}),[{key:"toMime",value:function(t){return this.formats[(t||"").replace(/^\./,"").toLowerCase()]}},{key:"fromMime",value:function(t){return this.mimes[t]}}])}();var it=function(){function t(){c(this,t),this.crc=-1}return h(t,[{key:"get",value:function(){return~this.crc}},{key:"append",value:function(t){for(var e=0|this.crc,r=this.table,n=0,o=0|t.length;n<o;n++)e=e>>>8^r[255&(e^t[n])];return this.crc=e,this}}],[{key:"for",value:function(e){return(new t).append(e).get()}}])}();function at(t){var e=new Uint8Array(t),r=new DataView(e.buffer),n={array:e,view:r,size:t,set8:function(t,e){return r.setUint8(t,e),n},set16:function(t,e){return r.setUint16(t,e,!0),n},set32:function(t,e){return r.setUint32(t,e,!0),n},bytes:function(t,r){return e.set(r,t),n}};return n}it.prototype.table=function(){var t,e,r,n=[];for(t=0;t<256;t++){for(r=t,e=0;e<8;e++)r=1&r?r>>>1^3988292384:r>>>1;n[t]=r}return n}();var st=function(){function t(e){c(this,t);var r=new Date;Object.assign(this,{directory:e,offset:0,files:[],time:(r.getHours()<<6|r.getMinutes())<<5|r.getSeconds()/2,date:(r.getFullYear()-1980<<4|r.getMonth()+1)<<5|r.getDate()}),this.add(e)}return h(t,[{key:"add",value:(e=l(a().mark((function e(r,n){var o,i,s,l,u,c,f,h;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=!n,i=t.encoder.encode("".concat(this.directory,"/").concat(o?"":r)),e.t0=Uint8Array,!o){e.next=7;break}e.t1=0,e.next=10;break;case 7:return e.next=9,n.arrayBuffer();case 9:e.t1=e.sent;case 10:e.t2=e.t1,s=new e.t0(e.t2),l=30+i.length,u=l+s.length,c=this.offset,f=at(26).set32(0,134742036).set16(6,this.time).set16(8,this.date).set32(10,it.for(s)).set32(14,s.length).set32(18,s.length).set16(22,i.length),c+=l,h=at(l+s.length+16).set32(0,67324752).bytes(4,f.array).bytes(30,i).bytes(l,s),c+=s.length,h.set32(u,134695760).bytes(u+4,f.array.slice(10,22)),c+=16,this.files.push({offset:c,folder:o,name:i,header:f,payload:h}),this.offset=c;case 24:case"end":return e.stop()}}),e,this)}))),function(t,r){return e.apply(this,arguments)})},{key:"toBuffer",value:function(){var t,e=this.files.reduce((function(t,e){return 46+e.name.length+t}),0),r=at(e+22),o=0,i=n(this.files);try{for(i.s();!(t=i.n()).done;){var a=t.value,s=a.offset,l=a.name,u=a.header,c=a.folder;r.set32(o,33639248).set16(o+4,20).bytes(o+6,u.array).set8(o+38,c?16:0).set32(o+42,s).bytes(o+46,l),o+=46+l.length}}catch(v){i.e(v)}finally{i.f()}r.set32(o,101010256).set16(o+8,this.files.length).set16(o+10,this.files.length).set32(o+12,e).set32(o+16,this.offset);var f,h=new Uint8Array(this.offset+r.size),d=0,p=n(this.files);try{for(p.s();!(f=p.n()).done;){var g=f.value.payload;h.set(g.array,d),d+=g.size}}catch(v){p.e(v)}finally{p.f()}return h.set(r.array,d),h}},{key:"blob",get:function(){return new Blob([this.toBuffer()],{type:"application/zip"})}}]);var e}();st.encoder=new TextEncoder;var lt=function(t,e,r,n){if(n){var o=t,i=o.width,a=o.height,s=Object.assign(document.createElement("canvas"),{width:i,height:a}),l=s.getContext("2d");l.fillStyle=n,l.fillRect(0,0,i,a),l.drawImage(t,0,0),t=s}return new Promise((function(n,o){return t.toBlob(n,e,r)}))},ut=function(){var t=l(a().mark((function t(e,r,n,o,i){return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.t0=ft,t.t1=i,t.next=4,lt(e,r,n,o);case 4:t.t2=t.sent,(0,t.t0)(t.t1,t.t2);case 6:case"end":return t.stop()}}),t)})));return function(e,r,n,o,i){return t.apply(this,arguments)}}(),ct=function(){var t=l(a().mark((function t(e,r,n,o,i,s,u){var c,f,h;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return c=function(t){return s.replace("{}",String(t+1).padStart(u,"0"))},f=nt(i,".zip")||"archive",h=new st(f),t.next=3,Promise.all(e.map(function(){var t=l(a().mark((function t(e,i){var s;return a().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return s=c(i),t.t0=h,t.t1=s,t.next=5,lt(e,r,n,o);case 5:return t.t2=t.sent,t.next=8,t.t0.add.call(t.t0,t.t1,t.t2);case 8:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()));case 3:ft("".concat(f,".zip"),h.blob);case 4:case"end":return t.stop()}}),t)})));return function(e,r,n,o,i,a,s){return t.apply(this,arguments)}}(),ft=function(t,e){var r=window.URL.createObjectURL(e),n=document.createElement("a");n.style.display="none",n.href=r,n.setAttribute("download",t),void 0===n.download&&n.setAttribute("target","_blank"),document.body.appendChild(n),n.click(),document.body.removeChild(n),setTimeout((function(){return window.URL.revokeObjectURL(r)}),100)},ht={asBuffer:function(){return lt.apply(void 0,arguments).then((function(t){return t.arrayBuffer()}))},asDownload:ut,asZipDownload:ct,atScale:function(t,e,r){return t.map((function(t){if(1==e&&!r)return t.canvas;var n=document.createElement("canvas"),o=n.getContext("2d"),i=t.canvas?t.canvas:t;return n.width=i.width*e,n.height=i.height*e,r&&(o.fillStyle=r,o.fillRect(0,0,n.width,n.height)),o.scale(e,e),o.drawImage(i,0,0),n}))},options:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.filename,n=void 0===r?"":r,o=e.extension,i=void 0===o?"":o,a=e.format,s=e.page,l=e.quality,u=e.matte,c=e.density,f=e.outline;return function(e,r){var o=new ot,a=o.fromMime,h=o.toMime,d=o.expected,p=(r=r||"canvas",e||i.replace(/@\d+x$/i,"")||rt(n)),g=(e=a(h(p)||p),h(e)),v=t.length;if(!p)throw new Error("Cannot determine image format (use a filename extension or 'format' argument)");if(!e)throw new Error('Unsupported file format "'.concat(p,'" (expected ').concat(d,")"));if(!v)throw new RangeError("Canvas has no associated contexts (try calling getContext or newPage first)");var m,y,b=n.replace(/{(\d*)}/g,(function(t,e){return y=!0,e=parseInt(e,10),m=isFinite(e)?e:isFinite(m)?m:-1,"{}"})),w=s>0?s-1:s<0?v+s:void 0;if(isFinite(w)&&w<0||w>=v)throw new RangeError(1==v?"Canvas only has a ‘page 1’ (".concat(w," is out of bounds)"):"Canvas has pages 1–".concat(v," (").concat(w," is out of bounds)"));if(t=isFinite(w)?[t[w]]:y||"pdf"==e?t:t.slice(-1),void 0===l)l=.92;else if("number"!=typeof l||!isFinite(l)||l<0||l>1)throw new TypeError("The quality option must be an number in the 0.0–1.0 range");if(void 0===c){var x=(i||nt(n,p)).match(/@(\d+)x$/i);c=x?parseInt(x[1],10):1}else if("number"!=typeof c||!Number.isInteger(c)||c<1)throw new TypeError("The density option must be a non-negative integer");return void 0===f?f=!0:"svg"==e&&(f=!!f),{filename:n,pattern:b,format:e,mime:g,pages:t,padding:m,quality:l,matte:u,density:c,outline:f,archive:r}}(a,e.archive)}},dt=ht.asBuffer,pt=ht.asDownload,gt=ht.asZipDownload,vt=ht.atScale,mt=ht.options,yt=Symbol.for("toDataURL"),bt=h((function t(n,o){c(this,t);var i=document.createElement("canvas"),a=[];Object.defineProperty(i,"async",{value:!0,writable:!1,enumerable:!0});for(var s=0,l=Object.entries({png:function(){return dt(i,"image/png")},jpg:function(){return dt(i,"image/jpeg")},pages:function(){return a.concat(i).map((function(t){return t.getContext("2d")}))}});s<l.length;s++){var f=r(l[s],2),h=f[0],d=f[1];Object.defineProperty(i,h,{get:d})}return Object.assign(i,u(u({width:n,height:o,newPage:function(){var t=i.width,e=i.height,n=Object.assign(document.createElement("canvas"),{width:t,height:e});n.getContext("2d").drawImage(i,0,0),a.push(n);for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];var u=r(s.length?s:[t,e],2);t=u[0],e=u[1];return Object.assign(i,{width:t,height:e}).getContext("2d")},saveAs:function(t,r){r="number"==typeof r?{quality:r}:r;var n=mt(this.pages,e({filename:t},r)),o=n.pattern,i=n.padding,a=n.mime,s=n.quality,l=n.matte,u=n.density,c=n.archive,f=vt(n.pages,u);return null==i?pt(f[0],a,s,l,t):gt(f,a,s,l,c,o,i)},toBuffer:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r="number"==typeof r?{quality:r}:r;var n=mt(this.pages,e({extension:t},r)),o=n.mime,i=n.quality,a=n.matte,s=n.pages,l=n.density,u=vt(s,l,a)[0];return dt(u,o,i,a)}},yt,i.toDataURL.bind(i)),"toDataURL",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"png",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r="number"==typeof r?{quality:r}:r;var n=mt(this.pages,e({extension:t},r)),o=n.mime,a=n.quality,s=n.matte,l=n.pages,u=n.density,c=vt(l,u,s)[0],f=c[c===i?yt:"toDataURL"](o,a);return Promise.resolve(f)})))})),wt={Canvas:bt},xt=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:r;if(Array.isArray(e))e.forEach((function(e){return xt(t,e,r,n)}));else if("function"==typeof e)e(t,r,n,xt);else{var o=Object.keys(e)[0];Array.isArray(e[o])?(n[o]={},xt(t,e[o],r,n[o])):n[o]=e[o](t,r,n,xt)}return r},kt=function(t,e){return function(r,n,o,i){e(r,n,o)&&i(r,t,n,o)}},Ct=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return function(e){return e.data[e.pos+t]}},At=function(t){return function(e){return e.data.subarray(e.pos,e.pos+=t)}},Pt=function(t){return function(e){return e.data.subarray(e.pos,e.pos+t)}},Bt=function(t){return function(e){return Array.from(At(t)(e)).map((function(t){return String.fromCharCode(t)})).join("")}},Et=function(t){return function(e){var r=At(2)(e);return t?(r[1]<<8)+r[0]:(r[0]<<8)+r[1]}},Dt=function(t,e){return function(r,n,o){for(var i="function"==typeof e?e(r,n,o):e,a=At(t),s=new Array(i),l=0;l<i;l++)s[l]=a(r);return s}},_t=function(t){return function(e){for(var r=function(t){return t.data[t.pos++]}(e),n=new Array(8),o=0;o<8;o++)n[7-o]=!!(r&1<<o);return Object.keys(t).reduce((function(e,r){var o=t[r];return o.length?e[r]=function(t,e,r){for(var n=0,o=0;o<r;o++)n+=t[e+o]&&Math.pow(2,r-o-1);return n}(n,o.index,o.length):e[r]=n[o.index],e}),{})}},St={blocks:function(t){for(var e=[],r=t.data.length,n=0,o=function(t){return t.data[t.pos++]}(t);0!==o&&o;o=function(t){return t.data[t.pos++]}(t)){if(t.pos+o>=r){var i=r-t.pos;e.push(At(i)(t)),n+=i;break}e.push(At(o)(t)),n+=o}for(var a=new Uint8Array(n),s=0,l=0;l<e.length;l++)a.set(e[l],s),s+=e[l].length;return a}},Tt=kt({gce:[{codes:At(2)},{byteSize:function(t){return t.data[t.pos++]}},{extras:_t({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:Et(!0)},{transparentColorIndex:function(t){return t.data[t.pos++]}},{terminator:function(t){return t.data[t.pos++]}}]},(function(t){var e=Pt(2)(t);return 33===e[0]&&249===e[1]})),Lt=kt({image:[{code:function(t){return t.data[t.pos++]}},{descriptor:[{left:Et(!0)},{top:Et(!0)},{width:Et(!0)},{height:Et(!0)},{lct:_t({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},kt({lct:Dt(3,(function(t,e,r){return Math.pow(2,r.descriptor.lct.size+1)}))},(function(t,e,r){return r.descriptor.lct.exists})),{data:[{minCodeSize:function(t){return t.data[t.pos++]}},St]}]},(function(t){return 44===Ct()(t)})),Rt=kt({text:[{codes:At(2)},{blockSize:function(t){return t.data[t.pos++]}},{preData:function(t,e,r){return At(r.text.blockSize)(t)}},St]},(function(t){var e=Pt(2)(t);return 33===e[0]&&1===e[1]})),It=kt({application:[{codes:At(2)},{blockSize:function(t){return t.data[t.pos++]}},{id:function(t,e,r){return Bt(r.blockSize)(t)}},St]},(function(t){var e=Pt(2)(t);return 33===e[0]&&255===e[1]})),Ot=kt({comment:[{codes:At(2)},St]},(function(t){var e=Pt(2)(t);return 33===e[0]&&254===e[1]})),Mt=[{header:[{signature:Bt(3)},{version:Bt(3)}]},{lsd:[{width:Et(!0)},{height:Et(!0)},{gct:_t({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:function(t){return t.data[t.pos++]}},{pixelAspectRatio:function(t){return t.data[t.pos++]}}]},kt({gct:Dt(3,(function(t,e){return Math.pow(2,e.lsd.gct.size+1)}))},(function(t,e){return e.lsd.gct.exists})),{frames:function(t,e){return function(r,n,o,i){for(var a=[],s=r.pos;e(r,n,o);){var l={};if(i(r,t,n,l),r.pos===s)break;s=r.pos,a.push(l)}return a}}([Tt,It,Ot,Lt,Rt],(function(t){var e=Ct()(t);return 33===e||44===e}))}],jt=function(t,e,r){if(t.image){var n=t.image,o=n.descriptor.width*n.descriptor.height,i=function(t,e,r){var n,o,i,a,s,l,u,c,f,h,d,p,g,v,m,y,b=4096,w=r,x=new Array(r),k=new Array(b),C=new Array(b),A=new Array(4097);for(s=1+(o=1<<(h=t)),n=o+2,u=-1,i=(1<<(a=h+1))-1,c=0;c<o;c++)k[c]=0,C[c]=c;for(d=p=g=v=m=y=0,f=0;f<w;){if(0===v){if(p<a){d+=e[y]<<p,p+=8,y++;continue}if(c=d&i,d>>=a,p-=a,c>n||c==s)break;if(c==o){i=(1<<(a=h+1))-1,n=o+2,u=-1;continue}if(-1==u){A[v++]=C[c],u=c,g=c;continue}for(l=c,c==n&&(A[v++]=g,c=u);c>o;)A[v++]=C[c],c=k[c];g=255&C[c],A[v++]=g,n<b&&(k[n]=u,C[n]=g,!(++n&i)&&n<b&&(a++,i+=n)),u=l}v--,x[m++]=A[v],f++}for(f=m;f<w;f++)x[f]=0;return x}(n.data.minCodeSize,n.data.blocks,o);n.descriptor.lct.interlaced&&(i=function(t,e){for(var r=new Array(t.length),n=t.length/e,o=function(n,o){var i=t.slice(o*e,(o+1)*e);r.splice.apply(r,[n*e,e].concat(i))},i=[0,4,2,1],a=[8,8,4,2],s=0,l=0;l<4;l++)for(var u=i[l];u<n;u+=a[l])o(u,s),s++;return r}(i,n.descriptor.width));var a={pixels:i,dims:{top:t.image.descriptor.top,left:t.image.descriptor.left,width:t.image.descriptor.width,height:t.image.descriptor.height}};return n.descriptor.lct&&n.descriptor.lct.exists?a.colorTable=n.lct:a.colorTable=e,t.gce&&(a.delay=10*(t.gce.delay||10),a.disposalType=t.gce.extras.disposal,t.gce.extras.transparentColorGiven&&(a.transparentIndex=t.gce.transparentColorIndex)),a.patch=function(t){for(var e=t.pixels.length,r=new Uint8ClampedArray(4*e),n=0;n<e;n++){var o=4*n,i=t.pixels[n],a=t.colorTable[i];r[o]=a[0],r[o+1]=a[1],r[o+2]=a[2],r[o+3]=i!==t.transparentIndex?255:0}return r}(a),a}},Nt=function(t,e){return t.frames.filter((function(t){return t.image})).map((function(e){return jt(e,t.gct)}))};function Ut(t){var e=encodeURI(t).toString().replace(/\%[0-9a-fA-F]{2}/g,"a");return e.length+(e.length!=Number(t)?3:0)}var zt=function(){return h((function t(e){c(this,t),this.mode=Ht.MODE_8BIT_BYTE,this.parsedData=[],this.data=e;for(var r=[],n=0,o=this.data.length;n<o;n++){var i=[],a=this.data.charCodeAt(n);a>65536?(i[0]=240|(1835008&a)>>>18,i[1]=128|(258048&a)>>>12,i[2]=128|(4032&a)>>>6,i[3]=128|63&a):a>2048?(i[0]=224|(61440&a)>>>12,i[1]=128|(4032&a)>>>6,i[2]=128|63&a):a>128?(i[0]=192|(1984&a)>>>6,i[1]=128|63&a):i[0]=a,r.push(i)}this.parsedData=Array.prototype.concat.apply([],r),this.parsedData.length!=this.data.length&&(this.parsedData.unshift(191),this.parsedData.unshift(187),this.parsedData.unshift(239))}),[{key:"getLength",value:function(){return this.parsedData.length}},{key:"write",value:function(t){for(var e=0,r=this.parsedData.length;e<r;e++)t.put(this.parsedData[e],8)}}])}(),Ft=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Gt.L;c(this,t),this.moduleCount=0,this.dataList=[],this.typeNumber=e,this.errorCorrectLevel=r,this.moduleCount=0,this.dataList=[]}return h(t,[{key:"addData",value:function(t){if(this.typeNumber<=0)this.typeNumber=function(t,e){for(var r=1,n=Ut(t),o=0,i=Yt.length;o<i;o++){var a=0;switch(e){case Gt.L:a=Yt[o][0];break;case Gt.M:a=Yt[o][1];break;case Gt.Q:a=Yt[o][2];break;case Gt.H:a=Yt[o][3]}if(n<=a)break;r++}if(r>Yt.length)throw new Error("Too long data");return r}(t,this.errorCorrectLevel);else{if(this.typeNumber>40)throw new Error("Invalid QR version: ".concat(this.typeNumber));if(!function(t,e,r){var n=Ut(e),o=t-1,i=0;switch(r){case Gt.L:i=Yt[o][0];break;case Gt.M:i=Yt[o][1];break;case Gt.Q:i=Yt[o][2];break;case Gt.H:i=Yt[o][3]}return n<=i}(this.typeNumber,t,this.errorCorrectLevel))throw new Error("Data is too long for QR version: ".concat(this.typeNumber))}var e=new zt(t);this.dataList.push(e),this.dataCache=void 0}},{key:"isDark",value:function(t,e){if(t<0||this.moduleCount<=t||e<0||this.moduleCount<=e)throw new Error("".concat(t,",").concat(e));return this.modules[t][e]}},{key:"getModuleCount",value:function(){return this.moduleCount}},{key:"make",value:function(){this.makeImpl(!1,this.getBestMaskPattern())}},{key:"makeImpl",value:function(e,r){this.moduleCount=4*this.typeNumber+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var o=0;o<this.moduleCount;o++)this.modules[n][o]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,r),this.typeNumber>=7&&this.setupTypeNumber(e),null==this.dataCache&&(this.dataCache=t.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,r)}},{key:"setupPositionProbePattern",value:function(t,e){for(var r=-1;r<=7;r++)if(!(t+r<=-1||this.moduleCount<=t+r))for(var n=-1;n<=7;n++)e+n<=-1||this.moduleCount<=e+n||(this.modules[t+r][e+n]=0<=r&&r<=6&&(0==n||6==n)||0<=n&&n<=6&&(0==r||6==r)||2<=r&&r<=4&&2<=n&&n<=4)}},{key:"getBestMaskPattern",value:function(){if(Number.isInteger(this.maskPattern)&&Object.values(qt).includes(this.maskPattern))return this.maskPattern;for(var t=0,e=0,r=0;r<8;r++){this.makeImpl(!0,r);var n=Qt.getLostPoint(this);(0==r||t>n)&&(t=n,e=r)}return e}},{key:"setupTimingPattern",value:function(){for(var t=8;t<this.moduleCount-8;t++)null==this.modules[t][6]&&(this.modules[t][6]=t%2==0);for(var e=8;e<this.moduleCount-8;e++)null==this.modules[6][e]&&(this.modules[6][e]=e%2==0)}},{key:"setupPositionAdjustPattern",value:function(){for(var t=Qt.getPatternPosition(this.typeNumber),e=0;e<t.length;e++)for(var r=0;r<t.length;r++){var n=t[e],o=t[r];if(null==this.modules[n][o])for(var i=-2;i<=2;i++)for(var a=-2;a<=2;a++)this.modules[n+i][o+a]=-2==i||2==i||-2==a||2==a||0==i&&0==a}}},{key:"setupTypeNumber",value:function(t){for(var e=Qt.getBCHTypeNumber(this.typeNumber),r=0;r<18;r++){var n=!t&&1==(e>>r&1);this.modules[Math.floor(r/3)][r%3+this.moduleCount-8-3]=n}for(r=0;r<18;r++){n=!t&&1==(e>>r&1);this.modules[r%3+this.moduleCount-8-3][Math.floor(r/3)]=n}}},{key:"setupTypeInfo",value:function(t,e){for(var r=this.errorCorrectLevel<<3|e,n=Qt.getBCHTypeInfo(r),o=0;o<15;o++){var i=!t&&1==(n>>o&1);o<6?this.modules[o][8]=i:o<8?this.modules[o+1][8]=i:this.modules[this.moduleCount-15+o][8]=i}for(o=0;o<15;o++){i=!t&&1==(n>>o&1);o<8?this.modules[8][this.moduleCount-o-1]=i:o<9?this.modules[8][15-o-1+1]=i:this.modules[8][15-o-1]=i}this.modules[this.moduleCount-8][8]=!t}},{key:"mapData",value:function(t,e){for(var r=-1,n=this.moduleCount-1,o=7,i=0,a=this.moduleCount-1;a>0;a-=2)for(6==a&&a--;;){for(var s=0;s<2;s++)if(null==this.modules[n][a-s]){var l=!1;i<t.length&&(l=1==(t[i]>>>o&1)),Qt.getMask(e,n,a-s)&&(l=!l),this.modules[n][a-s]=l,-1==--o&&(i++,o=7)}if((n+=r)<0||this.moduleCount<=n){n-=r,r=-r;break}}}}],[{key:"createData",value:function(e,r,n){for(var o=Vt.getRSBlocks(e,r),i=new $t,a=0;a<n.length;a++){var s=n[a];i.put(s.mode,4),i.put(s.getLength(),Qt.getLengthInBits(s.mode,e)),s.write(i)}var l=0;for(a=0;a<o.length;a++)l+=o[a].dataCount;if(i.getLengthInBits()>8*l)throw new Error("code length overflow. (".concat(i.getLengthInBits(),">").concat(8*l,")"));for(i.getLengthInBits()+4<=8*l&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=8*l||(i.put(t.PAD0,8),i.getLengthInBits()>=8*l));)i.put(t.PAD1,8);return t.createBytes(i,o)}},{key:"createBytes",value:function(t,e){for(var r=0,n=0,o=0,i=new Array(e.length),a=new Array(e.length),s=0;s<e.length;s++){var l=e[s].dataCount,u=e[s].totalCount-l;n=Math.max(n,l),o=Math.max(o,u),i[s]=new Array(l);for(var c=0;c<i[s].length;c++)i[s][c]=255&t.buffer[c+r];r+=l;var f=Qt.getErrorCorrectPolynomial(u),h=new Xt(i[s],f.getLength()-1).mod(f);a[s]=new Array(f.getLength()-1);for(c=0;c<a[s].length;c++){var d=c+h.getLength()-a[s].length;a[s][c]=d>=0?h.get(d):0}}var p=0;for(c=0;c<e.length;c++)p+=e[c].totalCount;var g=new Array(p),v=0;for(c=0;c<n;c++)for(s=0;s<e.length;s++)c<i[s].length&&(g[v++]=i[s][c]);for(c=0;c<o;c++)for(s=0;s<e.length;s++)c<a[s].length&&(g[v++]=a[s][c]);return g}}])}();Ft.PAD0=236,Ft.PAD1=17;var Gt={L:1,M:0,Q:3,H:2},Ht={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8},qt={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},Qt=function(){function t(){c(this,t)}return h(t,null,[{key:"getBCHTypeInfo",value:function(e){for(var r=e<<10;t.getBCHDigit(r)-t.getBCHDigit(t.G15)>=0;)r^=t.G15<<t.getBCHDigit(r)-t.getBCHDigit(t.G15);return(e<<10|r)^t.G15_MASK}},{key:"getBCHTypeNumber",value:function(e){for(var r=e<<12;t.getBCHDigit(r)-t.getBCHDigit(t.G18)>=0;)r^=t.G18<<t.getBCHDigit(r)-t.getBCHDigit(t.G18);return e<<12|r}},{key:"getBCHDigit",value:function(t){for(var e=0;0!=t;)e++,t>>>=1;return e}},{key:"getPatternPosition",value:function(e){return t.PATTERN_POSITION_TABLE[e-1]}},{key:"getMask",value:function(t,e,r){switch(t){case qt.PATTERN000:return(e+r)%2==0;case qt.PATTERN001:return e%2==0;case qt.PATTERN010:return r%3==0;case qt.PATTERN011:return(e+r)%3==0;case qt.PATTERN100:return(Math.floor(e/2)+Math.floor(r/3))%2==0;case qt.PATTERN101:return e*r%2+e*r%3==0;case qt.PATTERN110:return(e*r%2+e*r%3)%2==0;case qt.PATTERN111:return(e*r%3+(e+r)%2)%2==0;default:throw new Error("bad maskPattern:".concat(t))}}},{key:"getErrorCorrectPolynomial",value:function(t){for(var e=new Xt([1],0),r=0;r<t;r++)e=e.multiply(new Xt([1,Kt.gexp(r)],0));return e}},{key:"getLengthInBits",value:function(t,e){if(1<=e&&e<10)switch(t){case Ht.MODE_NUMBER:return 10;case Ht.MODE_ALPHA_NUM:return 9;case Ht.MODE_8BIT_BYTE:case Ht.MODE_KANJI:return 8;default:throw new Error("mode:".concat(t))}else if(e<27)switch(t){case Ht.MODE_NUMBER:return 12;case Ht.MODE_ALPHA_NUM:return 11;case Ht.MODE_8BIT_BYTE:return 16;case Ht.MODE_KANJI:return 10;default:throw new Error("mode:".concat(t))}else{if(!(e<41))throw new Error("type:".concat(e));switch(t){case Ht.MODE_NUMBER:return 14;case Ht.MODE_ALPHA_NUM:return 13;case Ht.MODE_8BIT_BYTE:return 16;case Ht.MODE_KANJI:return 12;default:throw new Error("mode:".concat(t))}}}},{key:"getLostPoint",value:function(t){for(var e=t.getModuleCount(),r=0,n=0;n<e;n++)for(var o=0;o<e;o++){for(var i=0,a=t.isDark(n,o),s=-1;s<=1;s++)if(!(n+s<0||e<=n+s))for(var l=-1;l<=1;l++)o+l<0||e<=o+l||0==s&&0==l||a==t.isDark(n+s,o+l)&&i++;i>5&&(r+=3+i-5)}for(n=0;n<e-1;n++)for(o=0;o<e-1;o++){var u=0;t.isDark(n,o)&&u++,t.isDark(n+1,o)&&u++,t.isDark(n,o+1)&&u++,t.isDark(n+1,o+1)&&u++,0!=u&&4!=u||(r+=3)}for(n=0;n<e;n++)for(o=0;o<e-6;o++)t.isDark(n,o)&&!t.isDark(n,o+1)&&t.isDark(n,o+2)&&t.isDark(n,o+3)&&t.isDark(n,o+4)&&!t.isDark(n,o+5)&&t.isDark(n,o+6)&&(r+=40);for(o=0;o<e;o++)for(n=0;n<e-6;n++)t.isDark(n,o)&&!t.isDark(n+1,o)&&t.isDark(n+2,o)&&t.isDark(n+3,o)&&t.isDark(n+4,o)&&!t.isDark(n+5,o)&&t.isDark(n+6,o)&&(r+=40);var c=0;for(o=0;o<e;o++)for(n=0;n<e;n++)t.isDark(n,o)&&c++;return r+=10*(Math.abs(100*c/e/e-50)/5)}}])}();Qt.PATTERN_POSITION_TABLE=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],Qt.G15=1335,Qt.G18=7973,Qt.G15_MASK=21522;var Kt=function(){function t(){c(this,t)}return h(t,null,[{key:"glog",value:function(e){if(e<1)throw new Error("glog(".concat(e,")"));return t.LOG_TABLE[e]}},{key:"gexp",value:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return t.EXP_TABLE[e]}}])}();Kt.EXP_TABLE=new Array(256),Kt.LOG_TABLE=new Array(256),Kt._constructor=function(){for(var t=0;t<8;t++)Kt.EXP_TABLE[t]=1<<t;for(t=8;t<256;t++)Kt.EXP_TABLE[t]=Kt.EXP_TABLE[t-4]^Kt.EXP_TABLE[t-5]^Kt.EXP_TABLE[t-6]^Kt.EXP_TABLE[t-8];for(t=0;t<255;t++)Kt.LOG_TABLE[Kt.EXP_TABLE[t]]=t}();var Xt=function(){function t(e,r){if(c(this,t),null==e.length)throw new Error("".concat(e.length,"/").concat(r));for(var n=0;n<e.length&&0==e[n];)n++;this.num=new Array(e.length-n+r);for(var o=0;o<e.length-n;o++)this.num[o]=e[o+n]}return h(t,[{key:"get",value:function(t){return this.num[t]}},{key:"getLength",value:function(){return this.num.length}},{key:"multiply",value:function(e){for(var r=new Array(this.getLength()+e.getLength()-1),n=0;n<this.getLength();n++)for(var o=0;o<e.getLength();o++)r[n+o]^=Kt.gexp(Kt.glog(this.get(n))+Kt.glog(e.get(o)));return new t(r,0)}},{key:"mod",value:function(e){if(this.getLength()-e.getLength()<0)return this;for(var r=Kt.glog(this.get(0))-Kt.glog(e.get(0)),n=new Array(this.getLength()),o=0;o<this.getLength();o++)n[o]=this.get(o);for(o=0;o<e.getLength();o++)n[o]^=Kt.gexp(Kt.glog(e.get(o))+r);return new t(n,0).mod(e)}}])}(),Vt=function(){function t(e,r){c(this,t),this.totalCount=e,this.dataCount=r}return h(t,null,[{key:"getRSBlocks",value:function(e,r){var n=t.getRsBlockTable(e,r);if(null==n)throw new Error("bad rs block @ typeNumber:".concat(e,"/errorCorrectLevel:").concat(r));for(var o=n.length/3,i=[],a=0;a<o;a++)for(var s=n[3*a+0],l=n[3*a+1],u=n[3*a+2],c=0;c<s;c++)i.push(new t(l,u));return i}},{key:"getRsBlockTable",value:function(e,r){switch(r){case Gt.L:return t.RS_BLOCK_TABLE[4*(e-1)+0];case Gt.M:return t.RS_BLOCK_TABLE[4*(e-1)+1];case Gt.Q:return t.RS_BLOCK_TABLE[4*(e-1)+2];case Gt.H:return t.RS_BLOCK_TABLE[4*(e-1)+3];default:return}}}])}();Vt.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];var $t=function(){return h((function t(){c(this,t),this.buffer=[],this.length=0}),[{key:"get",value:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)}},{key:"put",value:function(t,e){for(var r=0;r<e;r++)this.putBit(1==(t>>>e-r-1&1))}},{key:"getLengthInBits",value:function(){return this.length}},{key:"putBit",value:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}}])}(),Yt=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]],Wt=256,Jt=1024,Zt=1<<18;function te(t,e){var r,n,o,i,a;function s(t,e,n,o,i){r[e][0]-=t*(r[e][0]-n)/Jt,r[e][1]-=t*(r[e][1]-o)/Jt,r[e][2]-=t*(r[e][2]-i)/Jt}function l(t,e,n,o,i){for(var s,l,u=Math.abs(e-t),c=Math.min(e+t,Wt),f=e+1,h=e-1,d=1;f<c||h>u;)l=a[d++],f<c&&((s=r[f++])[0]-=l*(s[0]-n)/Zt,s[1]-=l*(s[1]-o)/Zt,s[2]-=l*(s[2]-i)/Zt),h>u&&((s=r[h--])[0]-=l*(s[0]-n)/Zt,s[1]-=l*(s[1]-o)/Zt,s[2]-=l*(s[2]-i)/Zt)}function u(t,e,n){var a,s,l,u,c,f=2147483647,h=f,d=-1,p=d;for(a=0;a<Wt;a++)s=r[a],(l=Math.abs(s[0]-t)+Math.abs(s[1]-e)+Math.abs(s[2]-n))<f&&(f=l,d=a),(u=l-(o[a]>>12))<h&&(h=u,p=a),c=i[a]>>10,i[a]-=c,o[a]+=c<<10;return i[d]+=64,o[d]-=65536,p}this.buildColormap=function(){!function(){var t,e;for(r=[],n=new Int32Array(256),o=new Int32Array(Wt),i=new Int32Array(Wt),a=new Int32Array(32),t=0;t<Wt;t++)e=(t<<12)/Wt,r[t]=new Float64Array([e,e,e,0]),i[t]=256,o[t]=0}(),function(){var r,n,o,i,c,f,h=t.length,d=30+(e-1)/3,p=h/(3*e),g=~~(p/100),v=Jt,m=2048,y=m>>6;for(y<=1&&(y=0),r=0;r<y;r++)a[r]=v*(256*(y*y-r*r)/(y*y));h<1509?(e=1,n=3):n=h%499!=0?1497:h%491!=0?1473:h%487!=0?1461:1509;var b=0;for(r=0;r<p;)if(s(v,f=u(o=(255&t[b])<<4,i=(255&t[b+1])<<4,c=(255&t[b+2])<<4),o,i,c),0!==y&&l(y,f,o,i,c),(b+=n)>=h&&(b-=h),0===g&&(g=1),++r%g==0)for(v-=v/d,(y=(m-=m/30)>>6)<=1&&(y=0),f=0;f<y;f++)a[f]=v*(256*(y*y-f*f)/(y*y))}(),function(){for(var t=0;t<Wt;t++)r[t][0]>>=4,r[t][1]>>=4,r[t][2]>>=4,r[t][3]=t}(),function(){var t,e,o,i,a,s,l=0,u=0;for(t=0;t<Wt;t++){for(a=t,s=(o=r[t])[1],e=t+1;e<Wt;e++)(i=r[e])[1]<s&&(a=e,s=i[1]);if(i=r[a],t!=a&&(e=i[0],i[0]=o[0],o[0]=e,e=i[1],i[1]=o[1],o[1]=e,e=i[2],i[2]=o[2],o[2]=e,e=i[3],i[3]=o[3],o[3]=e),s!=l){for(n[l]=u+t>>1,e=l+1;e<s;e++)n[e]=t;l=s,u=t}}for(n[l]=u+255>>1,e=l+1;e<256;e++)n[e]=255}()},this.getColormap=function(){for(var t=[],e=[],n=0;n<Wt;n++)e[r[n][3]]=n;for(var o=0,i=0;i<Wt;i++){var a=e[i];t[o++]=r[a][0],t[o++]=r[a][1],t[o++]=r[a][2]}return t},this.lookupRGB=function(t,e,o){for(var i,a,s,l=1e3,u=-1,c=n[e],f=c-1;c<Wt||f>=0;)c<Wt&&((s=(a=r[c])[1]-e)>=l?c=Wt:(c++,s<0&&(s=-s),(i=a[0]-t)<0&&(i=-i),(s+=i)<l&&((i=a[2]-o)<0&&(i=-i),(s+=i)<l&&(l=s,u=a[3])))),f>=0&&((s=e-(a=r[f])[1])>=l?f=-1:(f--,s<0&&(s=-s),(i=a[0]-t)<0&&(i=-i),(s+=i)<l&&((i=a[2]-o)<0&&(i=-i),(s+=i)<l&&(l=s,u=a[3]))));return u}}var ee=5003,re=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535];function ne(t,e,r,n){var o,i,a,s,l,u,c,f,h,d=Math.max(2,n),p=new Uint8Array(256),g=new Int32Array(ee),v=new Int32Array(ee),m=0,y=0,b=!1;function w(t,e){p[i++]=t,i>=254&&C(e)}function x(t){k(ee),y=l+2,b=!0,B(l,t)}function k(t){for(var e=0;e<t;++e)g[e]=-1}function C(t){i>0&&(t.writeByte(i),t.writeBytes(p,0,i),i=0)}function A(t){return(1<<t)-1}function P(){return 0===c?-1:(--c,255&r[f++])}function B(t,e){for(o&=re[m],m>0?o|=t<<m:o=t,m+=h;m>=8;)w(255&o,e),o>>=8,m-=8;if((y>a||b)&&(b?(a=A(h=s),b=!1):(++h,a=12==h?4096:A(h))),t==u){for(;m>0;)w(255&o,e),o>>=8,m-=8;C(e)}}this.encode=function(r){r.writeByte(d),c=t*e,f=0,function(t,e){var r,n,o,c,f,d,p;for(b=!1,a=A(h=s=t),u=1+(l=1<<t-1),y=l+2,i=0,c=P(),p=0,r=ee;r<65536;r*=2)++p;p=8-p,k(d=ee),B(l,e);t:for(;-1!=(n=P());)if(r=(n<<12)+c,g[o=n<<p^c]!==r){if(g[o]>=0){f=d-o,0===o&&(f=1);do{if((o-=f)<0&&(o+=d),g[o]===r){c=v[o];continue t}}while(g[o]>=0)}B(c,e),c=n,y<4096?(v[o]=y++,g[o]=r):x(e)}else c=v[o];B(c,e),B(u,e)}(d+1,r),r.writeByte(0)}}function oe(){this.page=-1,this.pages=[],this.newPage()}oe.pageSize=4096,oe.charMap={};for(var ie=0;ie<256;ie++)oe.charMap[ie]=String.fromCharCode(ie);function ae(t,e){this.width=~~t,this.height=~~e,this.transparent=null,this.transIndex=0,this.repeat=-1,this.delay=0,this.image=null,this.pixels=null,this.indexedPixels=null,this.colorDepth=null,this.colorTab=null,this.neuQuant=null,this.usedEntry=new Array,this.palSize=7,this.dispose=-1,this.firstFrame=!0,this.sample=10,this.dither=!1,this.globalPalette=!1,this.out=new oe}oe.prototype.newPage=function(){this.pages[++this.page]=new Uint8Array(oe.pageSize),this.cursor=0},oe.prototype.getData=function(){for(var t="",e=0;e<this.pages.length;e++)for(var r=0;r<oe.pageSize;r++)t+=oe.charMap[this.pages[e][r]];return t},oe.prototype.toFlattenUint8Array=function(){for(var t=[],e=0;e<this.pages.length;e++)if(e===this.pages.length-1){var r=Uint8Array.from(this.pages[e].slice(0,this.cursor));t.push(r)}else t.push(this.pages[e]);var n=new Uint8Array(t.reduce((function(t,e){return t+e.length}),0));return t.reduce((function(t,e){return n.set(e,t),t+e.length}),0),n},oe.prototype.writeByte=function(t){this.cursor>=oe.pageSize&&this.newPage(),this.pages[this.page][this.cursor++]=t},oe.prototype.writeUTFBytes=function(t){for(var e=t.length,r=0;r<e;r++)this.writeByte(t.charCodeAt(r))},oe.prototype.writeBytes=function(t,e,r){for(var n=r||t.length,o=e||0;o<n;o++)this.writeByte(t[o])},ae.prototype.setDelay=function(t){this.delay=Math.round(t/10)},ae.prototype.setFrameRate=function(t){this.delay=Math.round(100/t)},ae.prototype.setDispose=function(t){t>=0&&(this.dispose=t)},ae.prototype.setRepeat=function(t){this.repeat=t},ae.prototype.setTransparent=function(t){this.transparent=t},ae.prototype.addFrame=function(t){this.image=t,this.colorTab=this.globalPalette&&this.globalPalette.slice?this.globalPalette:null,this.getImagePixels(),this.analyzePixels(),!0===this.globalPalette&&(this.globalPalette=this.colorTab),this.firstFrame&&(this.writeHeader(),this.writeLSD(),this.writePalette(),this.repeat>=0&&this.writeNetscapeExt()),this.writeGraphicCtrlExt(),this.writeImageDesc(),this.firstFrame||this.globalPalette||this.writePalette(),this.writePixels(),this.firstFrame=!1},ae.prototype.finish=function(){this.out.writeByte(59)},ae.prototype.setQuality=function(t){t<1&&(t=1),this.sample=t},ae.prototype.setDither=function(t){!0===t&&(t="FloydSteinberg"),this.dither=t},ae.prototype.setGlobalPalette=function(t){this.globalPalette=t},ae.prototype.getGlobalPalette=function(){return this.globalPalette&&this.globalPalette.slice&&this.globalPalette.slice(0)||this.globalPalette},ae.prototype.writeHeader=function(){this.out.writeUTFBytes("GIF89a")},ae.prototype.analyzePixels=function(){this.colorTab||(this.neuQuant=new te(this.pixels,this.sample),this.neuQuant.buildColormap(),this.colorTab=this.neuQuant.getColormap()),this.dither?this.ditherPixels(this.dither.replace("-serpentine",""),null!==this.dither.match(/-serpentine/)):this.indexPixels(),this.pixels=null,this.colorDepth=8,this.palSize=7,null!==this.transparent&&(this.transIndex=this.findClosest(this.transparent,!0))},ae.prototype.indexPixels=function(t){var e=this.pixels.length/3;this.indexedPixels=new Uint8Array(e);for(var r=0,n=0;n<e;n++){var o=this.findClosestRGB(255&this.pixels[r++],255&this.pixels[r++],255&this.pixels[r++]);this.usedEntry[o]=!0,this.indexedPixels[n]=o}},ae.prototype.ditherPixels=function(t,e){var r={FalseFloydSteinberg:[[3/8,1,0],[3/8,0,1],[2/8,1,1]],FloydSteinberg:[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]],Stucki:[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]],Atkinson:[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]]};if(!t||!r[t])throw"Unknown dithering kernel: "+t;var n=r[t],o=0,i=this.height,a=this.width,s=this.pixels,l=e?-1:1;this.indexedPixels=new Uint8Array(this.pixels.length/3);for(var u=0;u<i;u++){e&&(l*=-1);for(var c=1==l?0:a-1,f=1==l?a:0;c!==f;c+=l){var h=3*(o=u*a+c),d=s[h],p=s[h+1],g=s[h+2];h=this.findClosestRGB(d,p,g),this.usedEntry[h]=!0,this.indexedPixels[o]=h,h*=3;for(var v=d-this.colorTab[h],m=p-this.colorTab[h+1],y=g-this.colorTab[h+2],b=1==l?0:n.length-1,w=1==l?n.length:0;b!==w;b+=l){var x=n[b][1],k=n[b][2];if(x+c>=0&&x+c<a&&k+u>=0&&k+u<i){var C=n[b][0];h=o+x+k*a,s[h*=3]=Math.max(0,Math.min(255,s[h]+v*C)),s[h+1]=Math.max(0,Math.min(255,s[h+1]+m*C)),s[h+2]=Math.max(0,Math.min(255,s[h+2]+y*C))}}}}},ae.prototype.findClosest=function(t,e){return this.findClosestRGB((16711680&t)>>16,(65280&t)>>8,255&t,e)},ae.prototype.findClosestRGB=function(t,e,r,n){if(null===this.colorTab)return-1;if(this.neuQuant&&!n)return this.neuQuant.lookupRGB(t,e,r);for(var o=0,i=16777216,a=this.colorTab.length,s=0,l=0;s<a;l++){var u=t-(255&this.colorTab[s++]),c=e-(255&this.colorTab[s++]),f=r-(255&this.colorTab[s++]),h=u*u+c*c+f*f;(!n||this.usedEntry[l])&&h<i&&(i=h,o=l)}return o},ae.prototype.getImagePixels=function(){var t=this.width,e=this.height;this.pixels=new Uint8Array(t*e*3);for(var r=this.image,n=0,o=0,i=0;i<e;i++)for(var a=0;a<t;a++)this.pixels[o++]=r[n++],this.pixels[o++]=r[n++],this.pixels[o++]=r[n++],n++},ae.prototype.writeGraphicCtrlExt=function(){var t,e;this.out.writeByte(33),this.out.writeByte(249),this.out.writeByte(4),null===this.transparent?(t=0,e=0):(t=1,e=2),this.dispose>=0&&(e=7&this.dispose),e<<=2,this.out.writeByte(e|t),this.writeShort(this.delay),this.out.writeByte(this.transIndex),this.out.writeByte(0)},ae.prototype.writeImageDesc=function(){this.out.writeByte(44),this.writeShort(0),this.writeShort(0),this.writeShort(this.width),this.writeShort(this.height),this.firstFrame||this.globalPalette?this.out.writeByte(0):this.out.writeByte(128|this.palSize)},ae.prototype.writeLSD=function(){this.writeShort(this.width),this.writeShort(this.height),this.out.writeByte(240|this.palSize),this.out.writeByte(0),this.out.writeByte(0)},ae.prototype.writeNetscapeExt=function(){this.out.writeByte(33),this.out.writeByte(255),this.out.writeByte(11),this.out.writeUTFBytes("NETSCAPE2.0"),this.out.writeByte(3),this.out.writeByte(1),this.writeShort(this.repeat),this.out.writeByte(0)},ae.prototype.writePalette=function(){this.out.writeBytes(this.colorTab);for(var t=768-this.colorTab.length,e=0;e<t;e++)this.out.writeByte(0)},ae.prototype.writeShort=function(t){this.out.writeByte(255&t),this.out.writeByte(t>>8&255)},ae.prototype.writePixels=function(){new ne(this.width,this.height,this.indexedPixels,this.colorDepth).encode(this.out)},ae.prototype.stream=function(){return this.out};var se=function(t,e,r,n){return new(r||(r=Promise))((function(o,i){function a(t){try{l(n.next(t))}catch(e){i(e)}}function s(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,s)}l((n=n.apply(t,e||[])).next())}))},le=wt.Canvas,ue=.4;function ce(t){if(t)return new Promise((function(r,n){if("data"==t.slice(0,4)){var o=new Image;return o.onload=function(){r(o),e(o)},o.onerror=function(){n("Image load error"),e(o)},void(o.src=t)}var i=new Image;i.setAttribute("crossOrigin","Anonymous"),i.onload=function(){r(i)},i.onerror=function(){n("Image load error")},i.src=t}));function e(t){t.onload=null,t.onerror=null}}var fe=function(){function t(e){c(this,t);var r=Object.assign({},e);if(Object.keys(t.defaultOptions).forEach((function(e){e in r||Object.defineProperty(r,e,{value:t.defaultOptions[e],enumerable:!0,writable:!0})})),r.components?"object"===p(r.components)&&Object.keys(t.defaultComponentOptions).forEach((function(e){e in r.components?Object.defineProperty(r.components,e,{value:Object.assign(Object.assign({},t.defaultComponentOptions[e]),r.components[e]),enumerable:!0,writable:!0}):Object.defineProperty(r.components,e,{value:t.defaultComponentOptions[e],enumerable:!0,writable:!0})})):r.components=t.defaultComponentOptions,null!==r.dotScale&&void 0!==r.dotScale){if(r.dotScale<=0||r.dotScale>1)throw new Error("dotScale should be in range (0, 1].");r.components.data.scale=r.dotScale,r.components.timing.scale=r.dotScale,r.components.alignment.scale=r.dotScale}this.options=r,this.canvas=new le(e.size,e.size),this.canvasContext=this.canvas.getContext("2d"),this.qrCode=new Ft(-1,this.options.correctLevel),Number.isInteger(this.options.maskPattern)&&(this.qrCode.maskPattern=this.options.maskPattern),Number.isInteger(this.options.version)&&(this.qrCode.typeNumber=this.options.version),this.qrCode.addData(this.options.text),this.qrCode.make()}return h(t,[{key:"draw",value:function(){var t=this;return new Promise((function(e){return t._draw().then(e)}))}},{key:"_clear",value:function(){this.canvasContext.clearRect(0,0,this.canvas.width,this.canvas.height)}},{key:"_draw",value:function(){var e,r,n,o,i,s,l,u,c,f,h,d,p,g,v,m,y,b,w;return se(this,void 0,void 0,a().mark((function x(){var k,C,A,P,B,E,D,_,S,T,L,R,I,O,M,j,N,U,z,F,G,H,q,Q,K,X,V,$,Y,W,J,Z,tt,et,rt,nt,ot,it,at,st,lt,ut,ct,ft,ht,dt,pt,gt,vt,mt,yt,bt,wt,kt,Ct,At,Pt,Bt,Et,Dt,_t,St,Tt,Lt,Rt,It,Ot,jt,Ut,zt,Ft,Gt;return a().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(k=null===(e=this.qrCode)||void 0===e?void 0:e.moduleCount,C=this.options.size,((A=this.options.margin)<0||2*A>=C)&&(A=0),P=Math.ceil(A),B=C-2*A,E=this.options.whiteMargin,D=this.options.backgroundDimming,_=Math.ceil(B/k),L=new le(T=(S=_*k)+2*P,T),R=L.getContext("2d"),this._clear(),R.save(),R.translate(P,P),I=new le(T,T),O=I.getContext("2d"),M=null,j=[],!this.options.gifBackground){a.next=47;break}if(x=this.options.gifBackground,Ht=void 0,Ht=new Uint8Array(x),N=xt({data:Ht,pos:0},Mt),M=N,j=Nt(N),!this.options.autoColor){a.next=45;break}U=0,z=0,F=0,G=0,H=0;case 28:if(!(H<j[0].colorTable.length)){a.next=41;break}if(!((q=j[0].colorTable[H])[0]>200||q[1]>200||q[2]>200)){a.next=32;break}return a.abrupt("continue",38);case 32:if(0!==q[0]||0!==q[1]||0!==q[2]){a.next=34;break}return a.abrupt("continue",38);case 34:G++,U+=q[0],z+=q[1],F+=q[2];case 38:H++,a.next=28;break;case 41:U=~~(U/G),z=~~(z/G),F=~~(F/G),this.options.colorDark="rgb(".concat(U,",").concat(z,",").concat(F,")");case 45:a.next=61;break;case 47:if(!this.options.backgroundImage){a.next=58;break}return a.next=50,ce(this.options.backgroundImage);case 50:Q=a.sent,this.options.autoColor&&(K=t._getAverageRGB(Q),this.options.colorDark="rgb(".concat(K.r,",").concat(K.g,",").concat(K.b,")")),O.drawImage(Q,0,0,Q.width,Q.height,0,0,T,T),O.rect(0,0,T,T),O.fillStyle=D,O.fill(),a.next=61;break;case 58:O.rect(0,0,T,T),O.fillStyle=this.options.colorLight,O.fill();case 61:for(X=Qt.getPatternPosition(this.qrCode.typeNumber),V=(null===(n=null===(r=this.options.components)||void 0===r?void 0:r.data)||void 0===n?void 0:n.scale)||ue,$=.5*(1-V),Y=0;Y<k;Y++)for(W=0;W<k;W++){for(J=this.qrCode.isDark(Y,W),Z=6==Y&&W>=8&&W<=k-8||6==W&&Y>=8&&Y<=k-8,tt=W<8&&(Y<8||Y>=k-8)||W>=k-8&&Y<8||Z,et=1;et<X.length-1;et++)tt=tt||Y>=X[et]-2&&Y<=X[et]+2&&W>=X[et]-2&&W<=X[et]+2;rt=W*_+(tt?0:$*_),nt=Y*_+(tt?0:$*_),R.strokeStyle=J?this.options.colorDark:this.options.colorLight,R.lineWidth=.5,R.fillStyle=J?this.options.colorDark:this.options.colorLight,0===X.length?tt||R.fillRect(rt,nt,(tt?1:V)*_,(tt?1:V)*_):(ot=W<k-4&&W>=k-4-5&&Y<k-4&&Y>=k-4-5,tt||ot||R.fillRect(rt,nt,(tt?1:V)*_,(tt?1:V)*_))}if(it=X[X.length-1],at=this.options.colorLight,R.fillStyle=at,R.fillRect(0,0,8*_,8*_),R.fillRect(0,(k-8)*_,8*_,8*_),R.fillRect((k-8)*_,0,8*_,8*_),(null===(i=null===(o=this.options.components)||void 0===o?void 0:o.timing)||void 0===i?void 0:i.protectors)&&(R.fillRect(8*_,6*_,(k-8-8)*_,_),R.fillRect(6*_,8*_,_,(k-8-8)*_)),(null===(l=null===(s=this.options.components)||void 0===s?void 0:s.cornerAlignment)||void 0===l?void 0:l.protectors)&&t._drawAlignProtector(R,it,it,_),!(null===(c=null===(u=this.options.components)||void 0===u?void 0:u.alignment)||void 0===c?void 0:c.protectors)){a.next=99;break}st=0;case 75:if(!(st<X.length)){a.next=99;break}lt=0;case 77:if(!(lt<X.length)){a.next=96;break}if(ut=X[lt],ct=X[st],6!==ut||6!==ct&&ct!==it){a.next=84;break}return a.abrupt("continue",93);case 84:if(6!==ct||6!==ut&&ut!==it){a.next=88;break}return a.abrupt("continue",93);case 88:if(ut!==it||ct!==it){a.next=92;break}return a.abrupt("continue",93);case 92:t._drawAlignProtector(R,ut,ct,_);case 93:lt++,a.next=77;break;case 96:st++,a.next=75;break;case 99:for(R.fillStyle=this.options.colorDark,R.fillRect(0,0,7*_,_),R.fillRect((k-7)*_,0,7*_,_),R.fillRect(0,6*_,7*_,_),R.fillRect((k-7)*_,6*_,7*_,_),R.fillRect(0,(k-7)*_,7*_,_),R.fillRect(0,(k-7+6)*_,7*_,_),R.fillRect(0,0,_,7*_),R.fillRect(6*_,0,_,7*_),R.fillRect((k-7)*_,0,_,7*_),R.fillRect((k-7+6)*_,0,_,7*_),R.fillRect(0,(k-7)*_,_,7*_),R.fillRect(6*_,(k-7)*_,_,7*_),R.fillRect(2*_,2*_,3*_,3*_),R.fillRect((k-7+2)*_,2*_,3*_,3*_),R.fillRect(2*_,(k-7+2)*_,3*_,3*_),ft=(null===(h=null===(f=this.options.components)||void 0===f?void 0:f.timing)||void 0===h?void 0:h.scale)||ue,ht=.5*(1-ft),dt=0;dt<k-8;dt+=2)t._drawDot(R,8+dt,6,_,ht,ft),t._drawDot(R,6,8+dt,_,ht,ft);pt=(null===(p=null===(d=this.options.components)||void 0===d?void 0:d.cornerAlignment)||void 0===p?void 0:p.scale)||ue,gt=.5*(1-pt),t._drawAlign(R,it,it,_,gt,pt,this.options.colorDark,(null===(v=null===(g=this.options.components)||void 0===g?void 0:g.cornerAlignment)||void 0===v?void 0:v.protectors)||!1),vt=(null===(y=null===(m=this.options.components)||void 0===m?void 0:m.alignment)||void 0===y?void 0:y.scale)||ue,mt=.5*(1-vt),yt=0;case 124:if(!(yt<X.length)){a.next=148;break}bt=0;case 126:if(!(bt<X.length)){a.next=145;break}if(wt=X[bt],kt=X[yt],6!==wt||6!==kt&&kt!==it){a.next=133;break}return a.abrupt("continue",142);case 133:if(6!==kt||6!==wt&&wt!==it){a.next=137;break}return a.abrupt("continue",142);case 137:if(wt!==it||kt!==it){a.next=141;break}return a.abrupt("continue",142);case 141:t._drawAlign(R,wt,kt,_,mt,vt,this.options.colorDark,(null===(w=null===(b=this.options.components)||void 0===b?void 0:b.alignment)||void 0===w?void 0:w.protectors)||!1);case 142:bt++,a.next=126;break;case 145:yt++,a.next=124;break;case 148:if(E&&(R.fillStyle=this.options.backgroundColor,R.fillRect(-P,-P,T,P),R.fillRect(-P,S,T,P),R.fillRect(S,-P,P,T),R.fillRect(-P,-P,P,T)),!this.options.logoImage){a.next=179;break}return a.next=152,ce(this.options.logoImage);case 152:Ct=a.sent,At=this.options.logoScale,Pt=this.options.logoMargin,Bt=this.options.logoCornerRadius,(At<=0||At>=1)&&(At=.2),Pt<0&&(Pt=0),Bt<0&&(Bt=0),_t=Dt=.5*(T-(Et=S*At)),R.restore(),R.fillStyle=this.options.logoBackgroundColor,R.save(),t._prepareRoundedCornerClip(R,Dt-Pt,_t-Pt,Et+2*Pt,Et+2*Pt,Bt+Pt),R.clip(),St=R.globalCompositeOperation,R.globalCompositeOperation="destination-out",R.fill(),R.globalCompositeOperation=St,R.restore(),R.save(),t._prepareRoundedCornerClip(R,Dt,_t,Et,Et,Bt),R.clip(),R.drawImage(Ct,Dt,_t,Et,Et),R.restore(),R.save(),R.translate(P,P);case 179:if(!M){a.next=191;break}if(j.forEach((function(t){Tt||((Tt=new ae(C,C)).setDelay(t.delay),Tt.setRepeat(0));var e=t.dims,r=e.width,n=e.height;Lt||(Lt=new le(r,n),(Rt=Lt.getContext("2d")).rect(0,0,Lt.width,Lt.height),Rt.fillStyle="#ffffff",Rt.fill()),It&&jt&&r===It.width&&n===It.height||(It=new le(r,n),Ot=It.getContext("2d"),jt=Ot.createImageData(r,n)),jt.data.set(t.patch),Ot.putImageData(jt,0,0),Rt.drawImage(It.getContext("2d").canvas,t.dims.left,t.dims.top);var o=new le(T,T),i=o.getContext("2d");i.drawImage(Lt.getContext("2d").canvas,0,0,T,T),i.rect(0,0,T,T),i.fillStyle=D,i.fill(),i.drawImage(L.getContext("2d").canvas,0,0,T,T);var a=new le(C,C),s=a.getContext("2d");s.drawImage(o.getContext("2d").canvas,0,0,C,C),Tt.addFrame(s.getImageData(0,0,a.width,a.height).data)})),Tt){a.next=183;break}throw new Error("No frames.");case 183:if(Tt.finish(),!he(this.canvas)){a.next=188;break}return Ut=Tt.stream().toFlattenUint8Array(),zt=Ut.reduce((function(t,e){return t+String.fromCharCode(e)}),""),a.abrupt("return",Promise.resolve("data:image/gif;base64,".concat(window.btoa(zt))));case 188:return a.abrupt("return",Promise.resolve(Buffer.from(Tt.stream().toFlattenUint8Array())));case 191:if(O.drawImage(L.getContext("2d").canvas,0,0,T,T),R.drawImage(I.getContext("2d").canvas,-P,-P,T,T),Ft=new le(C,C),Ft.getContext("2d").drawImage(L.getContext("2d").canvas,0,0,C,C),this.canvas=Ft,Gt=this.options.gifBackground?"gif":"png",!he(this.canvas)){a.next=200;break}return a.abrupt("return",Promise.resolve(this.canvas.toDataURL(Gt)));case 200:return a.abrupt("return",Promise.resolve(this.canvas.toBuffer(Gt)));case 201:case"end":return a.stop()}var x,Ht}),x,this)})))}}],[{key:"_prepareRoundedCornerClip",value:function(t,e,r,n,o,i){t.beginPath(),t.moveTo(e,r),t.arcTo(e+n,r,e+n,r+o,i),t.arcTo(e+n,r+o,e,r+o,i),t.arcTo(e,r+o,e,r,i),t.arcTo(e,r,e+n,r,i),t.closePath()}},{key:"_getAverageRGB",value:function(t){var e,r,n={r:0,g:0,b:0},o=-4,i={r:0,g:0,b:0},a=0;r=t.naturalHeight||t.height,e=t.naturalWidth||t.width;var s,l=new le(e,r).getContext("2d");if(!l)return n;l.drawImage(t,0,0);try{s=l.getImageData(0,0,e,r)}catch(u){return n}for(;(o+=20)<s.data.length;)s.data[o]>200||s.data[o+1]>200||s.data[o+2]>200||(++a,i.r+=s.data[o],i.g+=s.data[o+1],i.b+=s.data[o+2]);return i.r=~~(i.r/a),i.g=~~(i.g/a),i.b=~~(i.b/a),i}},{key:"_drawDot",value:function(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1;t.fillRect((e+o)*n,(r+o)*n,i*n,i*n)}},{key:"_drawAlignProtector",value:function(t,e,r,n){t.clearRect((e-2)*n,(r-2)*n,5*n,5*n),t.fillRect((e-2)*n,(r-2)*n,5*n,5*n)}},{key:"_drawAlign",value:function(e,r,n,o){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,s=arguments.length>6?arguments[6]:void 0,l=arguments.length>7?arguments[7]:void 0,u=e.fillStyle;e.fillStyle=s,new Array(4).fill(0).map((function(s,l){t._drawDot(e,r-2+l,n-2,o,i,a),t._drawDot(e,r+2,n-2+l,o,i,a),t._drawDot(e,r+2-l,n+2,o,i,a),t._drawDot(e,r-2,n+2-l,o,i,a)})),t._drawDot(e,r,n,o,i,a),l||(e.fillStyle="rgba(255, 255, 255, 0.6)",new Array(2).fill(0).map((function(s,l){t._drawDot(e,r-1+l,n-1,o,i,a),t._drawDot(e,r+1,n-1+l,o,i,a),t._drawDot(e,r+1-l,n+1,o,i,a),t._drawDot(e,r-1,n+1-l,o,i,a)}))),e.fillStyle=u}}])}();function he(t){try{return t instanceof HTMLElement}catch(e){return"object"===p(t)&&1===t.nodeType&&"object"===p(t.style)&&"object"===p(t.ownerDocument)}}fe.CorrectLevel=Gt,fe.defaultComponentOptions={data:{scale:.4},timing:{scale:.5,protectors:!1},alignment:{scale:.5,protectors:!1},cornerAlignment:{scale:.5,protectors:!0}},fe.defaultOptions={text:"",size:400,margin:20,colorDark:"#000000",colorLight:"rgba(255, 255, 255, 0.6)",correctLevel:Gt.M,backgroundImage:void 0,backgroundDimming:"rgba(0,0,0,0)",logoImage:void 0,logoScale:.2,logoMargin:4,logoCornerRadius:8,whiteMargin:!0,components:fe.defaultComponentOptions,autoColor:!0,logoBackgroundColor:"#ffffff",backgroundColor:"#ffffff"};var de={props:{text:{type:String,required:!0},qid:{type:String},correctLevel:{type:Number,default:1},size:{type:Number,default:200},margin:{type:Number,default:20},colorDark:{type:String,default:"#000000"},colorLight:{type:String,default:"#FFFFFF"},bgSrc:{type:String,default:void 0},background:{type:String,default:"rgba(0,0,0,0)"},backgroundDimming:{type:String,default:"rgba(0,0,0,0)"},logoSrc:{type:String,default:void 0},logoBackgroundColor:{type:String,default:"rgba(255,255,255,1)"},gifBgSrc:{type:String,default:void 0},logoScale:{type:Number,default:.2},logoMargin:{type:Number,default:0},logoCornerRadius:{type:Number,default:8},whiteMargin:{type:[Boolean,String],default:!0},dotScale:{type:Number,default:1},autoColor:{type:[Boolean,String],default:!0},binarize:{type:[Boolean,String],default:!1},binarizeThreshold:{type:Number,default:128},callback:{type:Function,default:function(){}},bindElement:{type:Boolean,default:!0},backgroundColor:{type:String,default:"#FFFFFF"},components:{default:function(){return{data:{scale:1},timing:{scale:1,protectors:!1},alignment:{scale:1,protectors:!1},cornerAlignment:{scale:1,protectors:!0}}}}},name:"vue-qr",data:function(){return{imgUrl:""}},watch:{$props:{deep:!0,handler:function(){this.main()}}},mounted:function(){this.main()},methods:{main:function(){var t=this;return l(a().mark((function e(){var r,n,o,i;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.gifBgSrc){e.next=7;break}return e.next=3,J(t.gifBgSrc);case 3:return r=e.sent,n=t.logoSrc,t.render(void 0,n,r),e.abrupt("return");case 7:o=t.bgSrc,i=t.logoSrc,t.render(o,i);case 10:case"end":return e.stop()}}),e)})))()},render:function(t,e,r){var n=this;return l(a().mark((function o(){var i;return a().wrap((function(o){for(;;)switch(o.prev=o.next){case 0:new fe({gifBackground:r,text:(i=n).text,size:i.size,margin:i.margin,colorDark:i.colorDark,colorLight:i.colorLight,backgroundColor:i.backgroundColor,backgroundImage:t,backgroundDimming:i.backgroundDimming,logoImage:e,logoScale:i.logoScale,logoBackgroundColor:i.logoBackgroundColor,correctLevel:i.correctLevel,logoMargin:i.logoMargin,logoCornerRadius:i.logoCornerRadius,whiteMargin:W(i.whiteMargin),dotScale:i.dotScale,autoColor:W(i.autoColor),binarize:W(i.binarize),binarizeThreshold:i.binarizeThreshold,components:i.components}).draw().then((function(t){n.imgUrl=t,i.callback&&i.callback(t,i.qid)}));case 2:case"end":return o.stop()}}),o)})))()}}},pe=["src"];var ge=s(de,[["render",function(t,e,r,n,o,i){return r.bindElement?(v(),g("img",{key:0,style:{display:"inline-block"},src:o.imgUrl},null,8,pe)):M("",!0)}]]),ve={class:"m-2"},me={class:"dialog-footer"};t("a",Object.assign({name:"QRCodeUpload"},{__name:"QR-code",props:{classId:{type:Number,default:0}},emits:["on-success"],setup:function(t,e){var r=e.emit,n=t,o=A(!1),i=f(),a=A(""),s=function(){var t=window.location;a.value=t.protocol+"//"+t.host+"/#/scanUpload?id="+n.classId+"&token="+i.token+"&t="+Date.now(),o.value=!0},l=function(){o.value=!1,a.value="",r("on-success","")};return function(t,e){var r=d("el-button"),n=d("el-dialog");return v(),g(T,null,[B("div",null,[m(r,{type:"primary",icon:"iphone",onClick:s},{default:y((function(){return e[2]||(e[2]=[x(" 扫码上传")])})),_:1})]),m(n,{modelValue:o.value,"onUpdate:modelValue":e[1]||(e[1]=function(t){return o.value=t}),title:"扫码上传",width:"320px","show-close":!1,"append-to-body":"","close-on-click-modal":!1,draggable:""},{footer:y((function(){return[B("div",me,[m(r,{onClick:e[0]||(e[0]=function(t){return o.value=!1})},{default:y((function(){return e[3]||(e[3]=[x("取 消")])})),_:1}),m(r,{type:"primary",onClick:l},{default:y((function(){return e[4]||(e[4]=[x("完成上传")])})),_:1})])]})),default:y((function(){return[B("div",ve,[m(ge,{logoSrc:b(F),size:291,margin:0,autoColor:!0,dotScale:1,text:a.value,colorDark:"green",colorLight:"white",ref:"qrcode"},null,8,["logoSrc","text"])])]})),_:1},8,["modelValue"])],64)}}}))}}}))}();
