/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,l){"use strict";var n,u,a,t,o,i,r,v,c,f,d,s;return{setters:[function(e){n=e.a,u=e.Q,a=e.p,t=e.aU,o=e.g,i=e.c,r=e.o,v=e.i,c=e.f,f=e.w,d=e.h,s=e.t}],execute:function(){var l={class:"flex justify-between items-center gap-2 w-full"};e("default",{__name:"components-cascader",props:{component:{type:String,default:""}},emits:["change"],setup:function(e,p){var m=p.emit,h=e,g=m,x=n([]),b=n(""),y=n([]),V=n(!0),w=function(){var e,l;V.value?b.value=(null===(e=y.value)||void 0===e?void 0:e.join("/"))||"":y.value=(null===(l=b.value)||void 0===l?void 0:l.split("/"))||[],V.value=!V.value,B()};u((function(){return h.component}),(function(e){j(e)})),a((function(){x.value=function(e){var l=[];for(var n in e)for(var u=e[n],a=n.split("/").filter(Boolean),t="src"===a[0]?1:0,o=l,i=function(){var e=a[r],l=o.find((function(l){return l.value===e}));l||(l={value:e,label:e,children:[]},o.push(l)),r===a.length-1&&(l.label=u,delete l.children),o=l.children||[]},r=t;r<a.length;r++)i();return l}(t),j(h.component)}));var j=function(e){if(""!==e){if(t["/src/".concat(e)])return y.value=e.split("/").filter(Boolean),b.value="",void(V.value=!0);b.value=e,y.value=[],V.value=!1}else V.value=!0},B=function(){var e;g("change",V.value?null===(e=y.value)||void 0===e?void 0:e.join("/"):b.value)};return function(e,n){var u=o("el-cascader"),a=o("el-input"),t=o("el-button");return r(),i("div",l,[V.value?(r(),v(u,{key:0,placeholder:"请选择文件路径",options:x.value,modelValue:y.value,"onUpdate:modelValue":n[0]||(n[0]=function(e){return y.value=e}),filterable:"",class:"w-full",clearable:"",onChange:B},null,8,["options","modelValue"])):(r(),v(a,{key:1,modelValue:b.value,"onUpdate:modelValue":n[1]||(n[1]=function(e){return b.value=e}),placeholder:"页面:view/xxx/xx.vue 插件:plugin/xx/xx.vue",onChange:B},null,8,["modelValue"])),c(t,{onClick:w},{default:f((function(){return[d(s(V.value?"手动输入":"快捷选择"),1)]})),_:1})])}}})}}}));
