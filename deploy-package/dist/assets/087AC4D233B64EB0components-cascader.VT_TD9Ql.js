/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{a as e,Q as l,p as a,aU as n,g as u,c as o,o as t,i as s,f as v,w as i,h as c,t as r}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const d={class:"flex justify-between items-center gap-2 w-full"},p={__name:"components-cascader",props:{component:{type:String,default:""}},emits:["change"],setup(p,{emit:f}){const m=p,h=f,x=e([]),g=e(""),b=e([]),V=e(!0),w=()=>{var e,l;V.value?g.value=(null==(e=b.value)?void 0:e.join("/"))||"":b.value=(null==(l=g.value)?void 0:l.split("/"))||[],V.value=!V.value,y()};l((()=>m.component),(e=>{j(e)})),a((()=>{x.value=function(e){const l=[];for(const a in e){const n=e[a],u=a.split("/").filter(Boolean);let o=l;for(let e="src"===u[0]?1:0;e<u.length;e++){const l=u[e];let a=o.find((e=>e.value===l));a||(a={value:l,label:l,children:[]},o.push(a)),e===u.length-1&&(a.label=n,delete a.children),o=a.children||[]}}return l}(n),j(m.component)}));const j=e=>{if(""!==e){if(n["/src/".concat(e)])return b.value=e.split("/").filter(Boolean),g.value="",void(V.value=!0);g.value=e,b.value=[],V.value=!1}else V.value=!0},y=()=>{var e;h("change",V.value?null==(e=b.value)?void 0:e.join("/"):g.value)};return(e,l)=>{const a=u("el-cascader"),n=u("el-input"),p=u("el-button");return t(),o("div",d,[V.value?(t(),s(a,{key:0,placeholder:"请选择文件路径",options:x.value,modelValue:b.value,"onUpdate:modelValue":l[0]||(l[0]=e=>b.value=e),filterable:"",class:"w-full",clearable:"",onChange:y},null,8,["options","modelValue"])):(t(),s(n,{key:1,modelValue:g.value,"onUpdate:modelValue":l[1]||(l[1]=e=>g.value=e),placeholder:"页面:view/xxx/xx.vue 插件:plugin/xx/xx.vue",onChange:y},null,8,["modelValue"])),v(p,{onClick:w},{default:i((()=>[c(r(V.value?"手动输入":"快捷选择"),1)])),_:1})])}}};export{p as default};
