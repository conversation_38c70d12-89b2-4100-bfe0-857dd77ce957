/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,r){"use strict";var t;return{setters:[function(e){t=e.s}],execute:function(){e("c",(function(e){return t({url:"/od/createOrder",method:"post",data:e})})),e("d",(function(e){return t({url:"/od/deleteOrder",method:"delete",params:e})})),e("b",(function(e){return t({url:"/od/deleteOrderByIds",method:"delete",params:e})})),e("u",(function(e){return t({url:"/od/updateOrder",method:"put",data:e})})),e("f",(function(e){return t({url:"/od/findOrder",method:"get",params:e})})),e("g",(function(e){return t({url:"/od/getOrderList",method:"get",params:e})})),e("p",(function(){return t({url:"/od/pullOrders",method:"post"})})),e("a",(function(){return t({url:"/od/pullYearOrders",method:"post"})}))}}}));
