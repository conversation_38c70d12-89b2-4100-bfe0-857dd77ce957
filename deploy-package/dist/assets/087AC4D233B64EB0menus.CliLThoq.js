/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{_ as e,a,Q as t,g as l,c as o,o as s,b as u,f as n,w as r,h as d,d as c,t as i,au as m,X as p,aK as y,aL as f,E as h,T as v,aM as w}from"./087AC4D233B64EB0index.Dnk2pLCR.js";import{u as k}from"./087AC4D233B64EB0authority.YPU6vVUR.js";import{g as I,s as g}from"./087AC4D233B64EB0authorityBtn.COa_YS-K.js";const b={class:"sticky top-0.5 z-10"},_={class:"tree-content clear-both"},C={class:"custom-tree-node"},D={key:0},R={key:1},B={class:"dialog-footer"},x=e(Object.assign({name:"<PERSON><PERSON>"},{__name:"menus",props:{row:{default:function(){return{}},type:Object}},emits:["changeRow"],setup(e,{expose:x,emit:E}){const A=e,V=E,j=a(""),N=a([]),O=a([]),T=a(!1),M=a({children:"children",label:function(e){return e.meta.title},disabled:function(e){return A.row.defaultRouter===e.name}});(async()=>{const e=await y();N.value=e.data.menus;const a=(await f({authorityId:A.row.authorityId})).data.menus,t=[];a.forEach((e=>{a.some((a=>a.parentId===e.menuId))||t.push(Number(e.menuId))})),O.value=t})();const S=()=>{T.value=!0},U=a(null),z=async()=>{const e=U.value.getCheckedNodes(!1,!0);0===(await w({menus:e,authorityId:A.row.authorityId})).code&&h({type:"success",message:"菜单设置成功!"})};x({enterAndNext:()=>{z()},needConfirm:T});const G=a(!1),H=a([]),K=a([]),L=a();let Q="";const X=e=>{K.value=e},q=e=>{G.value=!0,H.value=e.menuBtn},F=()=>{G.value=!1},J=async()=>{const e=K.value.map((e=>e.ID));0===(await g({menuID:Q,selected:e,authorityId:A.row.authorityId})).code&&(h({type:"success",message:"设置成功"}),G.value=!1)},P=(e,a)=>!e||-1!==a.meta.title.indexOf(e);return t(j,(e=>{U.value.filter(e)})),(a,t)=>{const y=l("el-input"),f=l("el-button"),w=l("el-tree"),g=l("el-scrollbar"),x=l("el-table-column"),E=l("el-table"),T=l("el-dialog");return s(),o("div",null,[u("div",b,[n(y,{modelValue:j.value,"onUpdate:modelValue":t[0]||(t[0]=e=>j.value=e),class:"w-3/5",placeholder:"筛选"},null,8,["modelValue"]),n(f,{class:"float-right",type:"primary",onClick:z},{default:r((()=>t[2]||(t[2]=[d("确 定")]))),_:1})]),u("div",_,[n(g,null,{default:r((()=>[n(w,{ref_key:"menuTree",ref:U,data:N.value,"default-checked-keys":O.value,props:M.value,"default-expand-all":"","highlight-current":"","node-key":"ID","show-checkbox":"","filter-node-method":P,onCheck:S},{default:r((({node:a,data:l})=>[u("span",C,[u("span",null,i(a.label),1),a.checked?(s(),o("span",D,[n(f,{type:"primary",link:"",style:p({color:e.row.defaultRouter===l.name?"#E6A23C":"#85ce61"}),onClick:m((()=>(async e=>{const a=await k({authorityId:A.row.authorityId,AuthorityName:A.row.authorityName,parentId:A.row.parentId,defaultRouter:e.name});0===a.code&&(h({type:"success",message:"设置成功"}),V("changeRow","defaultRouter",a.data.authority.defaultRouter))})(l)),["stop"])},{default:r((()=>[d(i(e.row.defaultRouter===l.name?"首页":"设为首页"),1)])),_:2},1032,["style","onClick"])])):c("",!0),l.menuBtn.length?(s(),o("span",R,[n(f,{type:"primary",link:"",onClick:()=>(async e=>{Q=e.ID;const a=await I({menuID:Q,authorityId:A.row.authorityId});0===a.code&&(q(e),await v(),a.data.selected&&a.data.selected.forEach((e=>{H.value.some((a=>{a.ID===e&&L.value.toggleRowSelection(a,!0)}))})))})(l)},{default:r((()=>t[3]||(t[3]=[d(" 分配按钮 ")]))),_:2},1032,["onClick"])])):c("",!0)])])),_:1},8,["data","default-checked-keys","props"])])),_:1})]),n(T,{modelValue:G.value,"onUpdate:modelValue":t[1]||(t[1]=e=>G.value=e),title:"分配按钮","destroy-on-close":""},{footer:r((()=>[u("div",B,[n(f,{onClick:F},{default:r((()=>t[4]||(t[4]=[d("取 消")]))),_:1}),n(f,{type:"primary",onClick:J},{default:r((()=>t[5]||(t[5]=[d("确 定")]))),_:1})])])),default:r((()=>[n(E,{ref_key:"btnTableRef",ref:L,data:H.value,"row-key":"ID",onSelectionChange:X},{default:r((()=>[n(x,{type:"selection",width:"55"}),n(x,{label:"按钮名称",prop:"name"}),n(x,{label:"按钮备注",prop:"desc"})])),_:1},8,["data"])])),_:1},8,["modelValue"])])}}}),[["__scopeId","data-v-a4aa9771"]]);export{x as default};
