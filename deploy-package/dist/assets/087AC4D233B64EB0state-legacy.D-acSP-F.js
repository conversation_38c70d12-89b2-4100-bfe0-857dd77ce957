/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */e=function(){return r};var n,r={},u=Object.prototype,o=u.hasOwnProperty,a=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},c=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",f=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(n){s=function(t,e,n){return t[e]=n}}function d(t,e,n,r){var u=e&&e.prototype instanceof _?e:_,o=Object.create(u.prototype),i=new M(r||[]);return a(o,"_invoke",{value:O(t,n,i)}),o}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}r.wrap=d;var h="suspendedStart",v="suspendedYield",y="executing",g="completed",m={};function _(){}function b(){}function w(){}var x={};s(x,c,(function(){return this}));var L=Object.getPrototypeOf,E=L&&L(L(F([])));E&&E!==u&&o.call(E,c)&&(x=E);var k=w.prototype=_.prototype=Object.create(x);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function G(e,n){function r(u,a,i,c){var l=p(e[u],e,a);if("throw"!==l.type){var f=l.arg,s=f.value;return s&&"object"==t(s)&&o.call(s,"__await")?n.resolve(s.__await).then((function(t){r("next",t,i,c)}),(function(t){r("throw",t,i,c)})):n.resolve(s).then((function(t){f.value=t,i(f)}),(function(t){return r("throw",t,i,c)}))}c(l.arg)}var u;a(this,"_invoke",{value:function(t,e){function o(){return new n((function(n,u){r(t,e,n,u)}))}return u=u?u.then(o,o):o()}})}function O(t,e,r){var u=h;return function(o,a){if(u===y)throw Error("Generator is already running");if(u===g){if("throw"===o)throw a;return{value:n,done:!0}}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var c=P(i,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(u===h)throw u=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);u=y;var l=p(t,e,r);if("normal"===l.type){if(u=r.done?g:v,l.arg===m)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(u=g,r.method="throw",r.arg=l.arg)}}}function P(t,e){var r=e.method,u=t.iterator[r];if(u===n)return e.delegate=null,"throw"===r&&t.iterator.return&&(e.method="return",e.arg=n,P(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var o=p(u,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,m;var a=o.arg;return a?a.done?(e[t.resultName]=a.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=n),e.delegate=null,m):a:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,m)}function S(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function B(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function M(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function F(e){if(e||""===e){var r=e[c];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var u=-1,a=function t(){for(;++u<e.length;)if(o.call(e,u))return t.value=e[u],t.done=!1,t;return t.value=n,t.done=!0,t};return a.next=a}}throw new TypeError(t(e)+" is not iterable")}return b.prototype=w,a(k,"constructor",{value:w,configurable:!0}),a(w,"constructor",{value:b,configurable:!0}),b.displayName=s(w,f,"GeneratorFunction"),r.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===b||"GeneratorFunction"===(e.displayName||e.name))},r.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,s(t,f,"GeneratorFunction")),t.prototype=Object.create(k),t},r.awrap=function(t){return{__await:t}},j(G.prototype),s(G.prototype,l,(function(){return this})),r.AsyncIterator=G,r.async=function(t,e,n,u,o){void 0===o&&(o=Promise);var a=new G(d(t,e,n,u),o);return r.isGeneratorFunction(e)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(k),s(k,f,"Generator"),s(k,c,(function(){return this})),s(k,"toString",(function(){return"[object Generator]"})),r.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},r.values=F,M.prototype={constructor:M,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(B),!t)for(var e in this)"t"===e.charAt(0)&&o.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=n)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function r(r,u){return i.type="throw",i.arg=t,e.next=r,u&&(e.method="next",e.arg=n),!!u}for(var u=this.tryEntries.length-1;u>=0;--u){var a=this.tryEntries[u],i=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),l=o.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var u=r;break}}u&&("break"===t||"continue"===t)&&u.tryLoc<=e&&e<=u.finallyLoc&&(u=null);var a=u?u.completion:{};return a.type=t,a.arg=e,u?(this.method="next",this.next=u.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),B(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var u=r.arg;B(n)}return u}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:F(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=n),m}},r}function n(t,e,n,r,u,o,a){try{var i=t[o](a),c=i.value}catch(t){return void n(t)}i.done?e(c):Promise.resolve(c).then(r,u)}System.register(["./087AC4D233B64EB0system-legacy.BbCF7JZU.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,r){"use strict";var u,o,a,i,c,l,f,s,d,p,h,v,y,g,m;return{setters:[function(t){u=t.g},function(t){o=t.a,a=t.at,i=t.g,c=t.c,l=t.o,f=t.f,s=t.w,d=t.i,p=t.d,h=t.b,v=t.h,y=t.t,g=t.F,m=t.D}],execute:function(){var r=document.createElement("style");r.textContent=".card_item{margin:.5rem;height:20rem;border-radius:.25rem;--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));padding:1.5rem;font-size:1.25rem;line-height:1.75rem;--tw-text-opacity: 1;color:rgb(51 65 85 / var(--tw-text-opacity, 1))}.card_item:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(30 41 59 / var(--tw-bg-opacity, 1));--tw-text-opacity: 1;color:rgb(148 163 184 / var(--tw-text-opacity, 1))}\n/*$vite$:1*/",document.head.appendChild(r);t("default",Object.assign({name:"State"},{__name:"state",setup:function(t){var r=o(null),_=o({}),b=o([{color:"#5cb87a",percentage:20},{color:"#e6a23c",percentage:40},{color:"#f56c6c",percentage:80}]),w=function(){var t,r=(t=e().mark((function t(){var n,r;return e().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,u();case 2:n=t.sent,r=n.data,_.value=r.server;case 5:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(u,o){var a=t.apply(e,r);function i(t){n(a,u,o,i,c,"next",t)}function c(t){n(a,u,o,i,c,"throw",t)}i(void 0)}))});return function(){return r.apply(this,arguments)}}();return w(),r.value=setInterval((function(){w()}),1e4),a((function(){clearInterval(r.value),r.value=null})),function(t,e){var n=i("el-col"),r=i("el-row"),u=i("el-card"),o=i("el-progress");return l(),c("div",null,[f(r,{gutter:15,class:"py-1"},{default:s((function(){return[f(n,{span:12},{default:s((function(){return[_.value.os?(l(),d(u,{key:0,class:"card_item"},{header:s((function(){return e[0]||(e[0]=[h("div",null,"Runtime",-1)])})),default:s((function(){return[h("div",null,[f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[1]||(e[1]=[v("os:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.os.goos),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[2]||(e[2]=[v("cpu nums:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.os.numCpu),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[3]||(e[3]=[v("compiler:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.os.compiler),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[4]||(e[4]=[v("go version:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.os.goVersion),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[5]||(e[5]=[v("goroutine nums:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.os.numGoroutine),1)]})),_:1})]})),_:1})])]})),_:1})):p("",!0)]})),_:1}),f(n,{span:12},{default:s((function(){return[_.value.disk?(l(),d(u,{key:0,class:"card_item","body-style":{height:"180px","overflow-y":"scroll"}},{header:s((function(){return e[6]||(e[6]=[h("div",null,"Disk",-1)])})),default:s((function(){return[h("div",null,[(l(!0),c(g,null,m(_.value.disk,(function(t,u){return l(),d(r,{key:u,gutter:10,style:{"margin-bottom":"2rem"}},{default:s((function(){return[f(n,{span:12},{default:s((function(){return[f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[7]||(e[7]=[v("MountPoint")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(t.mountPoint),1)]})),_:2},1024)]})),_:2},1024),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[8]||(e[8]=[v("total (MB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(t.totalMb),1)]})),_:2},1024)]})),_:2},1024),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[9]||(e[9]=[v("used (MB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(t.usedMb),1)]})),_:2},1024)]})),_:2},1024),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[10]||(e[10]=[v("total (GB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(t.totalGb),1)]})),_:2},1024)]})),_:2},1024),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[11]||(e[11]=[v("used (GB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(t.usedGb),1)]})),_:2},1024)]})),_:2},1024)]})),_:2},1024),f(n,{span:12},{default:s((function(){return[f(o,{type:"dashboard",percentage:t.usedPercent,color:b.value},null,8,["percentage","color"])]})),_:2},1024)]})),_:2},1024)})),128))])]})),_:1})):p("",!0)]})),_:1})]})),_:1}),f(r,{gutter:15,class:"py-1"},{default:s((function(){return[f(n,{span:12},{default:s((function(){return[_.value.cpu?(l(),d(u,{key:0,class:"card_item","body-style":{height:"180px","overflow-y":"scroll"}},{header:s((function(){return e[12]||(e[12]=[h("div",null,"CPU",-1)])})),default:s((function(){return[h("div",null,[f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[13]||(e[13]=[v("physical number of cores:")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.cpu.cores),1)]})),_:1})]})),_:1}),(l(!0),c(g,null,m(_.value.cpu.cpus,(function(t,e){return l(),d(r,{key:e,gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return[v("core "+y(e)+":",1)]})),_:2},1024),f(n,{span:12},{default:s((function(){return[f(o,{type:"line",percentage:+t.toFixed(0),color:b.value},null,8,["percentage","color"])]})),_:2},1024)]})),_:2},1024)})),128))])]})),_:1})):p("",!0)]})),_:1}),f(n,{span:12},{default:s((function(){return[_.value.ram?(l(),d(u,{key:0,class:"card_item"},{header:s((function(){return e[14]||(e[14]=[h("div",null,"Ram",-1)])})),default:s((function(){return[h("div",null,[f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return[f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[15]||(e[15]=[v("total (MB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.ram.totalMb),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[16]||(e[16]=[v("used (MB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.ram.usedMb),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[17]||(e[17]=[v("total (GB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y(_.value.ram.totalMb/1024),1)]})),_:1})]})),_:1}),f(r,{gutter:10},{default:s((function(){return[f(n,{span:12},{default:s((function(){return e[18]||(e[18]=[v("used (GB)")])})),_:1}),f(n,{span:12},{default:s((function(){return[v(y((_.value.ram.usedMb/1024).toFixed(2)),1)]})),_:1})]})),_:1})]})),_:1}),f(n,{span:12},{default:s((function(){return[f(o,{type:"dashboard",percentage:_.value.ram.usedPercent,color:b.value},null,8,["percentage","color"])]})),_:1})]})),_:1})])]})),_:1})):p("",!0)]})),_:1})]})),_:1})])}}}))}}}))}();
