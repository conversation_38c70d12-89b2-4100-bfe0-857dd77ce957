/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{g as e}from"./087AC4D233B64EB0system.CvtIEjYM.js";import{a,at as t,g as l,c as u,o as s,f as d,w as n,i as r,d as o,b as f,h as p,t as _,F as c,D as g}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const v=Object.assign({name:"State"},{__name:"state",setup(v){const i=a(null),m=a({}),y=a([{color:"#5cb87a",percentage:20},{color:"#e6a23c",percentage:40},{color:"#f56c6c",percentage:80}]),b=async()=>{const{data:a}=await e();m.value=a.server};return b(),i.value=setInterval((()=>{b()}),1e4),t((()=>{clearInterval(i.value),i.value=null})),(e,a)=>{const t=l("el-col"),v=l("el-row"),i=l("el-card"),b=l("el-progress");return s(),u("div",null,[d(v,{gutter:15,class:"py-1"},{default:n((()=>[d(t,{span:12},{default:n((()=>[m.value.os?(s(),r(i,{key:0,class:"card_item"},{header:n((()=>a[0]||(a[0]=[f("div",null,"Runtime",-1)]))),default:n((()=>[f("div",null,[d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[1]||(a[1]=[p("os:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.os.goos),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[2]||(a[2]=[p("cpu nums:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.os.numCpu),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[3]||(a[3]=[p("compiler:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.os.compiler),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[4]||(a[4]=[p("go version:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.os.goVersion),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[5]||(a[5]=[p("goroutine nums:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.os.numGoroutine),1)])),_:1})])),_:1})])])),_:1})):o("",!0)])),_:1}),d(t,{span:12},{default:n((()=>[m.value.disk?(s(),r(i,{key:0,class:"card_item","body-style":{height:"180px","overflow-y":"scroll"}},{header:n((()=>a[6]||(a[6]=[f("div",null,"Disk",-1)]))),default:n((()=>[f("div",null,[(s(!0),u(c,null,g(m.value.disk,((e,l)=>(s(),r(v,{key:l,gutter:10,style:{"margin-bottom":"2rem"}},{default:n((()=>[d(t,{span:12},{default:n((()=>[d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[7]||(a[7]=[p("MountPoint")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(e.mountPoint),1)])),_:2},1024)])),_:2},1024),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[8]||(a[8]=[p("total (MB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(e.totalMb),1)])),_:2},1024)])),_:2},1024),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[9]||(a[9]=[p("used (MB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(e.usedMb),1)])),_:2},1024)])),_:2},1024),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[10]||(a[10]=[p("total (GB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(e.totalGb),1)])),_:2},1024)])),_:2},1024),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[11]||(a[11]=[p("used (GB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(e.usedGb),1)])),_:2},1024)])),_:2},1024)])),_:2},1024),d(t,{span:12},{default:n((()=>[d(b,{type:"dashboard",percentage:e.usedPercent,color:y.value},null,8,["percentage","color"])])),_:2},1024)])),_:2},1024)))),128))])])),_:1})):o("",!0)])),_:1})])),_:1}),d(v,{gutter:15,class:"py-1"},{default:n((()=>[d(t,{span:12},{default:n((()=>[m.value.cpu?(s(),r(i,{key:0,class:"card_item","body-style":{height:"180px","overflow-y":"scroll"}},{header:n((()=>a[12]||(a[12]=[f("div",null,"CPU",-1)]))),default:n((()=>[f("div",null,[d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[13]||(a[13]=[p("physical number of cores:")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.cpu.cores),1)])),_:1})])),_:1}),(s(!0),u(c,null,g(m.value.cpu.cpus,((e,a)=>(s(),r(v,{key:a,gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>[p("core "+_(a)+":",1)])),_:2},1024),d(t,{span:12},{default:n((()=>[d(b,{type:"line",percentage:+e.toFixed(0),color:y.value},null,8,["percentage","color"])])),_:2},1024)])),_:2},1024)))),128))])])),_:1})):o("",!0)])),_:1}),d(t,{span:12},{default:n((()=>[m.value.ram?(s(),r(i,{key:0,class:"card_item"},{header:n((()=>a[14]||(a[14]=[f("div",null,"Ram",-1)]))),default:n((()=>[f("div",null,[d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>[d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[15]||(a[15]=[p("total (MB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.ram.totalMb),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[16]||(a[16]=[p("used (MB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.ram.usedMb),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[17]||(a[17]=[p("total (GB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_(m.value.ram.totalMb/1024),1)])),_:1})])),_:1}),d(v,{gutter:10},{default:n((()=>[d(t,{span:12},{default:n((()=>a[18]||(a[18]=[p("used (GB)")]))),_:1}),d(t,{span:12},{default:n((()=>[p(_((m.value.ram.usedMb/1024).toFixed(2)),1)])),_:1})])),_:1})])),_:1}),d(t,{span:12},{default:n((()=>[d(b,{type:"dashboard",percentage:m.value.ram.usedPercent,color:y.value},null,8,["percentage","color"])])),_:1})])),_:1})])])),_:1})):o("",!0)])),_:1})])),_:1})])}}});export{v as default};
