/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
System.register(["./087AC4D233B64EB0menuItem-legacy.fHn_s5TX.js","./087AC4D233B64EB0asyncSubmenu-legacy.BIXEZreJ.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,n){"use strict";var t,r,u,o,i,f,c,l,d,a,s,m;return{setters:[function(e){t=e.default},function(e){r=e.default},function(e){u=e.K,o=e.g,i=e.i,f=e.d,c=e.o,l=e.w,d=e.c,a=e.F,s=e.D,m=e.Y}],execute:function(){e("default",Object.assign({name:"AsideComponent"},{__name:"index",props:{routerInfo:{type:Object,default:function(){return null}},mode:{type:String,default:"vertical"}},setup:function(e){var n=e,y=u((function(){return n.routerInfo.children&&n.routerInfo.children.filter((function(e){return!e.hidden})).length?r:t}));return function(n,t){var r=o("AsideComponent");return e.routerInfo.hidden?f("",!0):(c(),i(m(y.value),{key:0,"router-info":e.routerInfo},{default:l((function(){return[e.routerInfo.children&&e.routerInfo.children.length?(c(!0),d(a,{key:0},s(e.routerInfo.children,(function(e){return c(),i(r,{key:e.name,"router-info":e},null,8,["router-info"])})),128)):f("",!0)]})),_:1},8,["router-info"]))}}}))}}}));
