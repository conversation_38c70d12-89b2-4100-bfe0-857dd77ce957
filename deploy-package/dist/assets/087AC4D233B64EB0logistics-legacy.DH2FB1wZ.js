/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return r};var n,r={},a=Object.prototype,o=a.hasOwnProperty,l=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},u=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(n){d=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),i=new z(r||[]);return l(o,"_invoke",{value:D(e,n,i)}),o}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}r.wrap=f;var v="suspendedStart",m="suspendedYield",h="executing",y="completed",g={};function b(){}function w(){}function x(){}var k={};d(k,u,(function(){return this}));var _=Object.getPrototypeOf,V=_&&_(_(I([])));V&&V!==a&&o.call(V,u)&&(k=V);var L=x.prototype=b.prototype=Object.create(k);function P(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function E(t,n){function r(a,l,i,u){var c=p(t[a],t,l);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==e(d)&&o.call(d,"__await")?n.resolve(d.__await).then((function(e){r("next",e,i,u)}),(function(e){r("throw",e,i,u)})):n.resolve(d).then((function(e){s.value=e,i(s)}),(function(e){return r("throw",e,i,u)}))}u(c.arg)}var a;l(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(o,o):o()}})}function D(e,t,r){var a=v;return function(o,l){if(a===h)throw Error("Generator is already running");if(a===y){if("throw"===o)throw l;return{value:n,done:!0}}for(r.method=o,r.arg=l;;){var i=r.delegate;if(i){var u=C(i,r);if(u){if(u===g)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===v)throw a=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=h;var c=p(e,t,r);if("normal"===c.type){if(a=r.done?y:m,c.arg===g)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=y,r.method="throw",r.arg=c.arg)}}}function C(e,t){var r=t.method,a=e.iterator[r];if(a===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=n,C(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var o=p(a,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,g;var l=o.arg;return l?l.done?(t[e.resultName]=l.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,g):l:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,g)}function O(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function U(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function z(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(O,this),this.reset(!0)}function I(t){if(t||""===t){var r=t[u];if(r)return r.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var a=-1,l=function e(){for(;++a<t.length;)if(o.call(t,a))return e.value=t[a],e.done=!1,e;return e.value=n,e.done=!0,e};return l.next=l}}throw new TypeError(e(t)+" is not iterable")}return w.prototype=x,l(L,"constructor",{value:x,configurable:!0}),l(x,"constructor",{value:w,configurable:!0}),w.displayName=d(x,s,"GeneratorFunction"),r.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},r.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,d(e,s,"GeneratorFunction")),e.prototype=Object.create(L),e},r.awrap=function(e){return{__await:e}},P(E.prototype),d(E.prototype,c,(function(){return this})),r.AsyncIterator=E,r.async=function(e,t,n,a,o){void 0===o&&(o=Promise);var l=new E(f(e,t,n,a),o);return r.isGeneratorFunction(t)?l:l.next().then((function(e){return e.done?e.value:l.next()}))},P(L),d(L,s,"Generator"),d(L,u,(function(){return this})),d(L,"toString",(function(){return"[object Generator]"})),r.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},r.values=I,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(U),!e)for(var t in this)"t"===t.charAt(0)&&o.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,a){return i.type="throw",i.arg=e,t.next=r,a&&(t.method="next",t.arg=n),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var l=this.tryEntries[a],i=l.completion;if("root"===l.tryLoc)return r("end");if(l.tryLoc<=this.prev){var u=o.call(l,"catchLoc"),c=o.call(l,"finallyLoc");if(u&&c){if(this.prev<l.catchLoc)return r(l.catchLoc,!0);if(this.prev<l.finallyLoc)return r(l.finallyLoc)}else if(u){if(this.prev<l.catchLoc)return r(l.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<l.finallyLoc)return r(l.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var l=a?a.completion:{};return l.type=e,l.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(l)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),U(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;U(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:I(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),g}},r}function n(e,t,n,r,a,o,l){try{var i=e[o](l),u=i.value}catch(e){return void n(e)}i.done?t(u):Promise.resolve(u).then(r,a)}function r(e){return function(){var t=this,r=arguments;return new Promise((function(a,o){var l=e.apply(t,r);function i(e){n(l,a,o,i,u,"next",e)}function u(e){n(l,a,o,i,u,"throw",e)}i(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(e,n){"use strict";var a,o,l,i,u,c,s,d,f,p,v,m,h,y,g,b,w,x,k,_,V,L,P,E,D;return{setters:[function(e){a=e.s,o=e._,l=e.a,i=e.r,u=e.p,c=e.g,s=e.c,d=e.o,f=e.b,p=e.f,v=e.w,m=e.h,h=e.l,y=e.F,g=e.D,b=e.i,w=e.d,x=e.t,k=e.v,_=e.ax,V=e.a7,L=e.a8,P=e.E,E=e.ab,D=e.j}],execute:function(){var n=document.createElement("style");n.textContent=".text-gray-400[data-v-a168163c]{color:#9ca3af}.shop-tabs[data-v-a168163c]{margin-top:20px}.warehouse-container[data-v-a168163c]{padding:10px 0}.warehouse-title[data-v-a168163c]{display:flex;align-items:center;gap:10px;width:100%}.warehouse-icon[data-v-a168163c]{color:#409eff}.warehouse-name[data-v-a168163c]{font-weight:500;flex:1}.logistics-count[data-v-a168163c]{margin-left:auto}.warehouse-status[data-v-a168163c]{margin-left:10px}.no-logistics[data-v-a168163c],.no-warehouses[data-v-a168163c]{padding:40px 0;text-align:center}.el-collapse-item__content[data-v-a168163c]{padding-bottom:0}.el-table[data-v-a168163c]{margin-top:10px}.flex[data-v-a168163c]{display:flex}.flex-col[data-v-a168163c]{flex-direction:column}.gap-1[data-v-a168163c]{gap:4px}\n/*$vite$:1*/",document.head.appendChild(n);var C=function(e){return a({url:"/logistics/createLogistics",method:"post",data:e})},O=function(e){return a({url:"/logistics/findLogistics",method:"get",params:e})},U={class:"gva-search-box"},z={class:"gva-table-box"},I={class:"gva-btn-list"},T={class:"warehouse-container"},A={class:"warehouse-title"},S={class:"warehouse-name"},j={key:0},F={key:1,class:"text-gray-400"},N={key:0},W={key:1,class:"text-gray-400"},B={key:0},K={key:1,class:"text-gray-400"},R={class:"flex flex-col gap-1"},G={key:0,class:"no-logistics"},Y={key:0,class:"no-warehouses"},$={class:"dialog-footer"},q=Object.assign({name:"Logistics"},{__name:"logistics",setup:function(e){var n=l([]),o=l(""),q=l([]),H=l({}),J=l([]),M=l({shopName:"",shopClientID:"",ozonDeliveryID:void 0,ozonWarehouseID:void 0,name:"",provider:"",serviceType:"",description:"",isActive:!0,basePrice:void 0,pricePerKg:void 0,pricePerCubic:void 0,minWeight:void 0,maxWeight:void 0,estimatedDays:void 0,commissionRate:void 0,pricingFormula:"",formulaParams:void 0,sortOrder:0,ozonData:void 0});i({});var Q=l(),X=l(),Z=l(!1),ee=l(""),te=function(){var e=r(t().mark((function e(){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,a({url:"/logistics/getShopWarehouseLogistics",method:"get"});case 3:0===(r=e.sent).code&&(n.value=r.data||[],n.value.length>0&&!o.value&&(o.value=n.value[0].name)),e.next=11;break;case 7:e.prev=7,e.t0=e.catch(0),P.error("获取数据失败");case 11:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(){return e.apply(this,arguments)}}(),ne=function(e){o.value=e.name,q.value=[]},re=function(e){return e?e.filter((function(e){var t,n,r=!H.value.name||(null===(t=e.name)||void 0===t?void 0:t.includes(H.value.name)),a=!H.value.provider||(null===(n=e.provider)||void 0===n?void 0:n.includes(H.value.provider)),o=void 0===H.value.isActive||""===H.value.isActive||e.isActive.toString()===H.value.isActive;return r&&a&&o})):[]},ae=function(){H.value={}},oe=function(){},le=function(e){J.value=e};u((function(){te()}));var ie=function(){var e=r(t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:E.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(r(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n=[],0!==J.value.length){e.next=4;break}return P({type:"warning",message:"请选择要删除的数据"}),e.abrupt("return");case 4:return J.value&&J.value.map((function(e){n.push(e.ID)})),e.next=7,a({url:"/logistics/deleteLogisticsByIds",method:"delete",params:{IDs:n}});case 7:0===e.sent.code&&(P({type:"success",message:"删除成功"}),te());case 9:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ue=function(){var e=r(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=1,e.next=4,O({ID:n.ID});case 4:r=e.sent,ee.value="update",0===r.code?(M.value=r.data,Z.value=!0):P.error("获取物流方式详情失败: "+r.msg),e.next=14;break;case 10:e.prev=10,e.t0=e.catch(1),P.error("编辑物流方式失败: "+e.t0.message);case 14:case"end":return e.stop()}}),e,null,[[1,10]])})));return function(t){return e.apply(this,arguments)}}(),ce=function(){var e=r(t().mark((function e(n){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t={ID:n.ID},a({url:"/logistics/deleteLogistics",method:"delete",params:t});case 2:0===e.sent.code&&(P({type:"success",message:"删除成功"}),te());case 4:case"end":return e.stop()}var t}),e)})));return function(t){return e.apply(this,arguments)}}(),se=function(){ee.value="create",Z.value=!0},de=function(){Z.value=!1,M.value={shopName:"",shopClientID:"",ozonDeliveryID:void 0,ozonWarehouseID:void 0,name:"",provider:"",serviceType:"",description:"",isActive:!0,basePrice:void 0,pricePerKg:void 0,pricePerCubic:void 0,minWeight:void 0,maxWeight:void 0,estimatedDays:void 0,commissionRate:void 0,pricingFormula:"",formulaParams:void 0,sortOrder:0,ozonData:void 0}},fe=function(){var e=r(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:null===(n=X.value)||void 0===n||n.validate(function(){var e=r(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:e.t0=ee.value,e.next="create"===e.t0?5:"update"===e.t0?9:13;break;case 5:return e.next=7,C(M.value);case 7:return r=e.sent,e.abrupt("break",17);case 9:return e.next=11,t=M.value,a({url:"/logistics/updateLogistics",method:"put",data:t});case 11:return r=e.sent,e.abrupt("break",17);case 13:return e.next=15,C(M.value);case 15:return r=e.sent,e.abrupt("break",17);case 17:0===r.code&&(P({type:"success",message:"创建/更改成功"}),de(),te());case 18:case"end":return e.stop()}var t}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();l({});var pe=function(){var e=r(t().mark((function e(n){var r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O({ID:n.ID});case 2:0===(r=e.sent).code&&(M.value=r.data,ee.value="look",Z.value=!0);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ve=function(){var e=r(t().mark((function e(){return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:E.confirm("确定要同步Ozon物流信息吗？这将从所有Ozon店铺拉取最新的物流方式信息。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}).then(r(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=D.service({lock:!0,text:"正在同步Ozon物流信息...",background:"rgba(0, 0, 0, 0.7)"}),e.prev=1,e.next=4,a({url:"/logistics/syncOzonLogistics",method:"post"});case 4:0===e.sent.code&&(P({type:"success",message:"同步成功"}),te()),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1),P({type:"error",message:"同步失败: "+e.t0.message});case 11:return e.prev=11,n.close(),e.finish(11);case 14:case"end":return e.stop()}}),e,null,[[1,8,11,14]])}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return function(e,t){var r=c("el-input"),a=c("el-form-item"),l=c("el-option"),i=c("el-select"),u=c("el-button"),P=c("el-form"),D=c("el-icon"),C=c("el-tag"),O=c("el-table-column"),te=c("el-table"),me=c("el-empty"),he=c("el-collapse-item"),ye=c("el-collapse"),ge=c("el-tab-pane"),be=c("el-tabs"),we=c("el-switch"),xe=c("el-input-number"),ke=c("el-dialog");return d(),s("div",null,[f("div",U,[p(P,{ref_key:"elSearchFormRef",ref:Q,inline:!0,model:H.value,class:"demo-form-inline",onKeyup:h(oe,["enter"])},{default:v((function(){return[p(a,{label:"物流方式名称",prop:"name"},{default:v((function(){return[p(r,{modelValue:H.value.name,"onUpdate:modelValue":t[0]||(t[0]=function(e){return H.value.name=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(a,{label:"物流服务商",prop:"provider"},{default:v((function(){return[p(r,{modelValue:H.value.provider,"onUpdate:modelValue":t[1]||(t[1]=function(e){return H.value.provider=e}),placeholder:"搜索条件"},null,8,["modelValue"])]})),_:1}),p(a,{label:"是否启用",prop:"isActive"},{default:v((function(){return[p(i,{modelValue:H.value.isActive,"onUpdate:modelValue":t[2]||(t[2]=function(e){return H.value.isActive=e}),clearable:"",placeholder:"请选择"},{default:v((function(){return[p(l,{label:"启用",value:"true"}),p(l,{label:"禁用",value:"false"})]})),_:1},8,["modelValue"])]})),_:1}),p(a,null,{default:v((function(){return[p(u,{type:"primary",icon:"search",onClick:oe},{default:v((function(){return t[21]||(t[21]=[m("查询")])})),_:1}),p(u,{icon:"refresh",onClick:ae},{default:v((function(){return t[22]||(t[22]=[m("重置")])})),_:1})]})),_:1})]})),_:1},8,["model"])]),f("div",z,[f("div",I,[p(u,{type:"primary",icon:"plus",onClick:se},{default:v((function(){return t[23]||(t[23]=[m("新增")])})),_:1}),p(u,{type:"success",icon:"refresh",onClick:ve},{default:v((function(){return t[24]||(t[24]=[m("同步Ozon物流")])})),_:1}),p(u,{icon:"delete",style:{"margin-left":"10px"},disabled:!J.value.length,onClick:ie},{default:v((function(){return t[25]||(t[25]=[m("删除")])})),_:1},8,["disabled"])]),p(be,{modelValue:o.value,"onUpdate:modelValue":t[4]||(t[4]=function(e){return o.value=e}),onTabClick:ne,class:"shop-tabs"},{default:v((function(){return[(d(!0),s(y,null,g(n.value,(function(e){return d(),b(ge,{key:e.name,label:e.name,name:e.name},{default:v((function(){return[f("div",T,[p(ye,{modelValue:q.value,"onUpdate:modelValue":t[3]||(t[3]=function(e){return q.value=e}),accordion:""},{default:v((function(){return[(d(!0),s(y,null,g(e.warehouses,(function(e){return d(),b(he,{key:e.id,title:"".concat(e.name," (").concat(e.logistics.length,"个物流方式)"),name:e.id.toString()},{title:v((function(){return[f("div",A,[p(D,{class:"warehouse-icon"},{default:v((function(){return[p(k(_))]})),_:1}),f("span",S,x(e.name),1),p(C,{size:"small",type:"info",class:"logistics-count"},{default:v((function(){return[m(x(e.logistics.length)+"个物流方式 ",1)]})),_:2},1024),p(C,{size:"small",type:"created"===e.status?"success":"warning",class:"warehouse-status"},{default:v((function(){return[m(x("created"===e.status?"已创建":e.status),1)]})),_:2},1032,["type"])])]})),default:v((function(){return[p(te,{data:re(e.logistics),style:{width:"100%"},size:"small",onSelectionChange:le},{default:v((function(){return[p(O,{type:"selection",width:"55"}),p(O,{align:"left",label:"物流方式名称",prop:"name",width:"200"}),p(O,{align:"left",label:"物流服务商",prop:"provider",width:"150"}),p(O,{align:"left",label:"服务类型",prop:"serviceType",width:"120"}),p(O,{align:"left",label:"是否启用",prop:"isActive",width:"100"},{default:v((function(e){return[p(C,{type:e.row.isActive?"success":"danger",size:"small"},{default:v((function(){return[m(x(e.row.isActive?"启用":"禁用"),1)]})),_:2},1032,["type"])]})),_:1}),p(O,{align:"left",label:"基础价格",prop:"basePrice",width:"100"},{default:v((function(e){return[e.row.basePrice?(d(),s("span",j,x(e.row.basePrice.toFixed(2)),1)):(d(),s("span",F,"未设置"))]})),_:1}),p(O,{align:"left",label:"每公斤价格",prop:"pricePerKg",width:"100"},{default:v((function(e){return[e.row.pricePerKg?(d(),s("span",N,x(e.row.pricePerKg.toFixed(2)),1)):(d(),s("span",W,"未设置"))]})),_:1}),p(O,{align:"left",label:"预计天数",prop:"estimatedDays",width:"100"}),p(O,{align:"left",label:"佣金比率",prop:"commissionRate",width:"100"},{default:v((function(e){return[e.row.commissionRate?(d(),s("span",B,x((100*e.row.commissionRate).toFixed(2))+"%",1)):(d(),s("span",K,"使用产品佣金"))]})),_:1}),p(O,{align:"left",label:"计价公式",prop:"pricingFormula",width:"200","show-overflow-tooltip":""}),p(O,{align:"left",label:"操作",fixed:"right","min-width":"200"},{default:v((function(e){return[f("div",R,[p(u,{type:"primary",link:"",icon:"view",size:"small",onClick:function(t){return pe(e.row)}},{default:v((function(){return t[26]||(t[26]=[m("查看")])})),_:2},1032,["onClick"]),p(u,{type:"primary",link:"",icon:"edit",size:"small",onClick:function(t){return ue(e.row)}},{default:v((function(){return t[27]||(t[27]=[m("编辑")])})),_:2},1032,["onClick"]),p(u,{type:"primary",link:"",icon:"delete",size:"small",onClick:function(t){return n=e.row,void E.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){ce(n)}));var n}},{default:v((function(){return t[28]||(t[28]=[m("删除")])})),_:2},1032,["onClick"])])]})),_:1})]})),_:2},1032,["data"]),0===e.logistics.length?(d(),s("div",G,[p(me,{description:"该仓库暂无物流方式","image-size":60})])):w("",!0)]})),_:2},1032,["title","name"])})),128))]})),_:2},1032,["modelValue"]),0===e.warehouses.length?(d(),s("div",Y,[p(me,{description:"该店铺暂无仓库信息","image-size":80})])):w("",!0)])]})),_:2},1032,["label","name"])})),128))]})),_:1},8,["modelValue"])]),p(ke,{modelValue:Z.value,"onUpdate:modelValue":t[20]||(t[20]=function(e){return Z.value=e}),"before-close":de,title:"create"===ee.value?"新增物流方式":"update"===ee.value?"修改物流方式":"查看物流方式"},{footer:v((function(){return[f("div",$,[p(u,{onClick:de},{default:v((function(){return t[29]||(t[29]=[m("取 消")])})),_:1}),V(p(u,{type:"primary",onClick:fe},{default:v((function(){return t[30]||(t[30]=[m("确 定")])})),_:1},512),[[L,"look"!==ee.value]])])]})),default:v((function(){return[p(P,{ref_key:"elFormRef",ref:X,model:M.value,"label-position":"right","label-width":"120px",style:{width:"90%"}},{default:v((function(){return[p(a,{label:"店铺名称"},{default:v((function(){return[p(r,{modelValue:M.value.shopName,"onUpdate:modelValue":t[5]||(t[5]=function(e){return M.value.shopName=e}),clearable:"",readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"物流方式名称"},{default:v((function(){return[p(r,{modelValue:M.value.name,"onUpdate:modelValue":t[6]||(t[6]=function(e){return M.value.name=e}),clearable:"",readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"物流服务商"},{default:v((function(){return[p(r,{modelValue:M.value.provider,"onUpdate:modelValue":t[7]||(t[7]=function(e){return M.value.provider=e}),clearable:"",readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"服务类型"},{default:v((function(){return[p(r,{modelValue:M.value.serviceType,"onUpdate:modelValue":t[8]||(t[8]=function(e){return M.value.serviceType=e}),clearable:"",readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"描述"},{default:v((function(){return[p(r,{modelValue:M.value.description,"onUpdate:modelValue":t[9]||(t[9]=function(e){return M.value.description=e}),type:"textarea",readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"是否启用"},{default:v((function(){return[p(we,{modelValue:M.value.isActive,"onUpdate:modelValue":t[10]||(t[10]=function(e){return M.value.isActive=e}),disabled:"look"===ee.value},null,8,["modelValue","disabled"])]})),_:1}),p(a,{label:"基础价格"},{default:v((function(){return[p(xe,{modelValue:M.value.basePrice,"onUpdate:modelValue":t[11]||(t[11]=function(e){return M.value.basePrice=e}),precision:2,readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"每公斤价格"},{default:v((function(){return[p(xe,{modelValue:M.value.pricePerKg,"onUpdate:modelValue":t[12]||(t[12]=function(e){return M.value.pricePerKg=e}),precision:2,readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"每立方米价格"},{default:v((function(){return[p(xe,{modelValue:M.value.pricePerCubic,"onUpdate:modelValue":t[13]||(t[13]=function(e){return M.value.pricePerCubic=e}),precision:2,readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"最小重量(kg)"},{default:v((function(){return[p(xe,{modelValue:M.value.minWeight,"onUpdate:modelValue":t[14]||(t[14]=function(e){return M.value.minWeight=e}),precision:2,readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"最大重量(kg)"},{default:v((function(){return[p(xe,{modelValue:M.value.maxWeight,"onUpdate:modelValue":t[15]||(t[15]=function(e){return M.value.maxWeight=e}),precision:2,readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"预计送达天数"},{default:v((function(){return[p(xe,{modelValue:M.value.estimatedDays,"onUpdate:modelValue":t[16]||(t[16]=function(e){return M.value.estimatedDays=e}),readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"佣金比率"},{default:v((function(){return[p(xe,{modelValue:M.value.commissionRate,"onUpdate:modelValue":t[17]||(t[17]=function(e){return M.value.commissionRate=e}),min:0,max:1,step:.01,precision:4,readonly:"look"===ee.value,placeholder:"如0.12表示12%，留空则使用产品佣金比率"},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"计价公式"},{default:v((function(){return[p(r,{modelValue:M.value.pricingFormula,"onUpdate:modelValue":t[18]||(t[18]=function(e){return M.value.pricingFormula=e}),clearable:"",readonly:"look"===ee.value,placeholder:"例如: basePrice + weight * pricePerKg"},null,8,["modelValue","readonly"])]})),_:1}),p(a,{label:"排序"},{default:v((function(){return[p(xe,{modelValue:M.value.sortOrder,"onUpdate:modelValue":t[19]||(t[19]=function(e){return M.value.sortOrder=e}),readonly:"look"===ee.value},null,8,["modelValue","readonly"])]})),_:1})]})),_:1},8,["model"])]})),_:1},8,["modelValue","title"])])}}});e("default",o(q,[["__scopeId","data-v-a168163c"]]))}}}))}();
