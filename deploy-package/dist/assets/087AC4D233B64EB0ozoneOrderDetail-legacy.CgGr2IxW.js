/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function n(e){for(var n=1;n<arguments.length;n++){var a=null!=arguments[n]?arguments[n]:{};n%2?t(Object(a),!0).forEach((function(t){r(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):t(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}function r(t,n,r){return(n=function(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var a=r.call(t,n||"default");if("object"!=e(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,t}function a(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */a=function(){return n};var t,n={},r=Object.prototype,o=r.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},u="function"==typeof Symbol?Symbol:{},l=u.iterator||"@@iterator",c=u.asyncIterator||"@@asyncIterator",s=u.toStringTag||"@@toStringTag";function d(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(t){d=function(e,t,n){return e[t]=n}}function p(e,t,n,r){var a=t&&t.prototype instanceof b?t:b,o=Object.create(a.prototype),u=new z(r||[]);return i(o,"_invoke",{value:D(e,n,u)}),o}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}n.wrap=p;var _="suspendedStart",v="suspendedYield",g="executing",m="completed",h={};function b(){}function y(){}function w(){}var k={};d(k,l,(function(){return this}));var x=Object.getPrototypeOf,V=x&&x(x(A([])));V&&V!==r&&o.call(V,l)&&(k=V);var j=w.prototype=b.prototype=Object.create(k);function O(e){["next","throw","return"].forEach((function(t){d(e,t,(function(e){return this._invoke(t,e)}))}))}function C(t,n){function r(a,i,u,l){var c=f(t[a],t,i);if("throw"!==c.type){var s=c.arg,d=s.value;return d&&"object"==e(d)&&o.call(d,"__await")?n.resolve(d.__await).then((function(e){r("next",e,u,l)}),(function(e){r("throw",e,u,l)})):n.resolve(d).then((function(e){s.value=e,u(s)}),(function(e){return r("throw",e,u,l)}))}l(c.arg)}var a;i(this,"_invoke",{value:function(e,t){function o(){return new n((function(n,a){r(e,t,n,a)}))}return a=a?a.then(o,o):o()}})}function D(e,n,r){var a=_;return function(o,i){if(a===g)throw Error("Generator is already running");if(a===m){if("throw"===o)throw i;return{value:t,done:!0}}for(r.method=o,r.arg=i;;){var u=r.delegate;if(u){var l=E(u,r);if(l){if(l===h)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(a===_)throw a=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);a=g;var c=f(e,n,r);if("normal"===c.type){if(a=r.done?m:v,c.arg===h)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(a=m,r.method="throw",r.arg=c.arg)}}}function E(e,n){var r=n.method,a=e.iterator[r];if(a===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,E(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),h;var o=f(a,e.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,h;var i=o.arg;return i?i.done?(n[e.resultName]=i.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,h):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,h)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function z(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function A(n){if(n||""===n){var r=n[l];if(r)return r.call(n);if("function"==typeof n.next)return n;if(!isNaN(n.length)){var a=-1,i=function e(){for(;++a<n.length;)if(o.call(n,a))return e.value=n[a],e.done=!1,e;return e.value=t,e.done=!0,e};return i.next=i}}throw new TypeError(e(n)+" is not iterable")}return y.prototype=w,i(j,"constructor",{value:w,configurable:!0}),i(w,"constructor",{value:y,configurable:!0}),y.displayName=d(w,s,"GeneratorFunction"),n.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},n.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,d(e,s,"GeneratorFunction")),e.prototype=Object.create(j),e},n.awrap=function(e){return{__await:e}},O(C.prototype),d(C.prototype,c,(function(){return this})),n.AsyncIterator=C,n.async=function(e,t,r,a,o){void 0===o&&(o=Promise);var i=new C(p(e,t,r,a),o);return n.isGeneratorFunction(t)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},O(j),d(j,s,"Generator"),d(j,l,(function(){return this})),d(j,"toString",(function(){return"[object Generator]"})),n.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},n.values=A,z.prototype={constructor:z,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(S),!e)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function r(r,a){return u.type="throw",u.arg=e,n.next=r,a&&(n.method="next",n.arg=t),!!a}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],u=i.completion;if("root"===i.tryLoc)return r("end");if(i.tryLoc<=this.prev){var l=o.call(i,"catchLoc"),c=o.call(i,"finallyLoc");if(l&&c){if(this.prev<i.catchLoc)return r(i.catchLoc,!0);if(this.prev<i.finallyLoc)return r(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return r(i.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return r(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,h):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),h},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),h}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var a=r.arg;S(n)}return a}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:A(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),h}},n}function o(e,t,n,r,a,o,i){try{var u=e[o](i),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,a)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function u(e){o(i,r,a,u,l,"next",e)}function l(e){o(i,r,a,u,l,"throw",e)}u(void 0)}))}}System.register(["./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0arrayCtrl-legacy.C4_FsKwQ.js"],(function(e,t){"use strict";var r,o,u,l,c,s,d,p,f,_,v,g,m,h,b,y,w,k,x,V,j,O,C,D,E,L;return{setters:[function(e){r=e.a,o=e.I,u=e.r,l=e.g,c=e.c,s=e.o,d=e.b,p=e.f,f=e.w,_=e.d,v=e.h,g=e.F,m=e.i,h=e.l,b=e.t,y=e.v,w=e.aa,k=e.ab,x=e.E},function(e){V=e._,j=e.g,O=e.f,C=e.c,D=e.u,E=e.d,L=e.a}],execute:function(){var t={class:"gva-search-box"},S={class:"gva-table-box"},z={class:"gva-btn-list"},A={class:"gva-pagination"},P={class:"flex justify-between items-center"},U={class:"text-lg"};e("default",Object.assign({name:"OzoneOrderDetail"},{__name:"ozoneOrderDetail",setup:function(e){var T=r(!1),I=o(),N=r(!1),B=r({addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}),F=u({}),G=u({createdAt:[{validator:function(e,t,n){Q.value.startCreatedAt&&!Q.value.endCreatedAt?n(new Error("请填写结束日期")):!Q.value.startCreatedAt&&Q.value.endCreatedAt?n(new Error("请填写开始日期")):Q.value.startCreatedAt&&Q.value.endCreatedAt&&(Q.value.startCreatedAt.getTime()===Q.value.endCreatedAt.getTime()||Q.value.startCreatedAt.getTime()>Q.value.endCreatedAt.getTime())?n(new Error("开始日期应当早于结束日期")):n()},trigger:"change"}]}),q=r(),J=r(),R=r(1),M=r(0),Y=r(10),K=r([]),Q=r({}),W=function(){Q.value={},$()},H=function(){var e;null===(e=J.value)||void 0===e||e.validate(function(){var e=i(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:R.value=1,""===Q.value.is_express&&(Q.value.is_express=null),$();case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},X=function(e){Y.value=e,$()},Z=function(e){R.value=e,$()},$=function(){var e=i(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j(n({page:R.value,pageSize:Y.value},Q.value));case 2:0===(t=e.sent).code&&(K.value=t.data.list,M.value=t.data.total,R.value=t.data.page,Y.value=t.data.pageSize);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();$();var ee=function(){var e=i(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();ee();var te=r([]),ne=function(e){te.value=e},re=function(){var e=i(a().mark((function e(){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(i(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=[],0!==te.value.length){e.next=4;break}return x({type:"warning",message:"请选择要删除的数据"}),e.abrupt("return");case 4:return te.value&&te.value.map((function(e){t.push(e.ID)})),e.next=7,L({IDs:t});case 7:0===e.sent.code&&(x({type:"success",message:"删除成功"}),K.value.length===t.length&&R.value>1&&R.value--,$());case 9:case"end":return e.stop()}}),e)}))));case 1:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ae=r(""),oe=function(){var e=i(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O({ID:t.ID});case 2:n=e.sent,ae.value="update",0===n.code&&(B.value=n.data,ue.value=!0);case 5:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ie=function(){var e=i(a().mark((function e(t){return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,E({ID:t.ID});case 2:0===e.sent.code&&(x({type:"success",message:"删除成功"}),1===K.value.length&&R.value>1&&R.value--,$());case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),ue=r(!1),le=function(){ue.value=!1,B.value={addressee:{},analytics_data:{},customer:{},barcodes:{},delivering_date:{},delivery_method:{},financial_data:{},in_process_at:new Date,is_express:!1,optional:{},order_id:void 0,order_number:"",parent_posting_number:"",posting_number:"",products:[],requirements:{},shipment_date:new Date,status:"",substatus:"",tpl_integration_type:"",tracking_number:"",tariffication:[]}},ce=function(){var e=i(a().mark((function e(){var t;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:T.value=!0,null===(t=q.value)||void 0===t||t.validate(function(){var e=i(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return",T.value=!1);case 2:e.t0=ae.value,e.next="create"===e.t0?5:"update"===e.t0?9:13;break;case 5:return e.next=7,C(B.value);case 7:return n=e.sent,e.abrupt("break",17);case 9:return e.next=11,D(B.value);case 11:return n=e.sent,e.abrupt("break",17);case 13:return e.next=15,C(B.value);case 15:return n=e.sent,e.abrupt("break",17);case 17:T.value=!1,0===n.code&&(x({type:"success",message:"创建/更改成功"}),le(),$());case 19:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 2:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),se=r({}),de=r(!1),pe=function(){var e=i(a().mark((function e(t){var n;return a().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,O({ID:t.ID});case 2:0===(n=e.sent).code&&(se.value=n.data,de.value=!0);case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),fe=function(){de.value=!1,se.value={}};return function(e,n){var r=l("QuestionFilled"),a=l("el-icon"),o=l("el-tooltip"),i=l("el-date-picker"),u=l("el-form-item"),x=l("el-button"),j=l("el-form"),O=l("el-table-column"),C=l("InfoFilled"),D=l("el-table"),E=l("el-pagination"),L=l("el-switch"),$=l("el-input"),ee=l("el-drawer"),_e=l("el-descriptions-item"),ve=l("el-descriptions");return s(),c("div",null,[d("div",t,[p(j,{ref_key:"elSearchFormRef",ref:J,inline:!0,model:Q.value,class:"demo-form-inline",rules:G,onKeyup:h(H,["enter"])},{default:f((function(){return[p(u,{label:"创建日期",prop:"createdAt"},{label:f((function(){return[d("span",null,[n[22]||(n[22]=v(" 创建日期 ")),p(o,{content:"搜索范围是开始日期（包含）至结束日期（不包含）"},{default:f((function(){return[p(a,null,{default:f((function(){return[p(r)]})),_:1})]})),_:1})])]})),default:f((function(){return[p(i,{modelValue:Q.value.startCreatedAt,"onUpdate:modelValue":n[0]||(n[0]=function(e){return Q.value.startCreatedAt=e}),type:"datetime",placeholder:"开始日期","disabled-date":function(e){return!!Q.value.endCreatedAt&&e.getTime()>Q.value.endCreatedAt.getTime()}},null,8,["modelValue","disabled-date"]),n[23]||(n[23]=v(" — ")),p(i,{modelValue:Q.value.endCreatedAt,"onUpdate:modelValue":n[1]||(n[1]=function(e){return Q.value.endCreatedAt=e}),type:"datetime",placeholder:"结束日期","disabled-date":function(e){return!!Q.value.startCreatedAt&&e.getTime()<Q.value.startCreatedAt.getTime()}},null,8,["modelValue","disabled-date"])]})),_:1}),N.value?(s(),c(g,{key:0},[],64)):_("",!0),p(u,null,{default:f((function(){return[p(x,{type:"primary",icon:"search",onClick:H},{default:f((function(){return n[24]||(n[24]=[v("查询")])})),_:1}),p(x,{icon:"refresh",onClick:W},{default:f((function(){return n[25]||(n[25]=[v("重置")])})),_:1}),N.value?(s(),m(x,{key:1,link:"",type:"primary",icon:"arrow-up",onClick:n[3]||(n[3]=function(e){return N.value=!1})},{default:f((function(){return n[27]||(n[27]=[v("收起")])})),_:1})):(s(),m(x,{key:0,link:"",type:"primary",icon:"arrow-down",onClick:n[2]||(n[2]=function(e){return N.value=!0})},{default:f((function(){return n[26]||(n[26]=[v("展开")])})),_:1}))]})),_:1})]})),_:1},8,["model","rules"])]),d("div",S,[d("div",z,[p(x,{type:"primary",icon:"plus",onClick:n[4]||(n[4]=function(e){return ae.value="create",void(ue.value=!0)})},{default:f((function(){return n[28]||(n[28]=[v("同步订单")])})),_:1}),p(x,{icon:"delete",style:{"margin-left":"10px"},disabled:!te.value.length,onClick:re},{default:f((function(){return n[29]||(n[29]=[v("删除")])})),_:1},8,["disabled"])]),p(D,{ref:"multipleTable",style:{width:"100%"},"tooltip-effect":"dark",data:K.value,"row-key":"ID",onSelectionChange:ne},{default:f((function(){return[p(O,{type:"selection",width:"55"}),p(O,{align:"left",label:"日期",prop:"createdAt",width:"180"},{default:f((function(e){return[v(b(y(w)(e.row.CreatedAt)),1)]})),_:1}),p(O,{label:"收件人联系方式",prop:"addressee",width:"200"},{default:f((function(e){return n[30]||(n[30]=[v(" [JSON] ")])})),_:1}),p(O,{label:"分析数据",prop:"analytics_data",width:"200"},{default:f((function(e){return n[31]||(n[31]=[v(" [JSON] ")])})),_:1}),p(O,{label:"买家信息",prop:"customer",width:"200"},{default:f((function(e){return n[32]||(n[32]=[v(" [JSON] ")])})),_:1}),p(O,{label:"货件条码",prop:"barcodes",width:"200"},{default:f((function(e){return n[33]||(n[33]=[v(" [JSON] ")])})),_:1}),p(O,{label:"快递方式",prop:"delivery_method",width:"200"},{default:f((function(e){return n[34]||(n[34]=[v(" [JSON] ")])})),_:1}),p(O,{align:"left",label:"货件所属订单的ID",prop:"order_id",width:"120"}),p(O,{align:"left",label:"货件所属的订单号",prop:"order_number",width:"120"}),p(O,{align:"left",label:"货件号",prop:"posting_number",width:"120"}),p(O,{label:"货运商品列表",prop:"products",width:"200"},{default:f((function(e){return[p(V,{modelValue:e.row.products,"onUpdate:modelValue":function(t){return e.row.products=t}},null,8,["modelValue","onUpdate:modelValue"])]})),_:1}),p(O,{align:"left",label:"操作",fixed:"right","min-width":y(I).operateMinWith},{default:f((function(e){return[p(x,{type:"primary",link:"",class:"table-button",onClick:function(t){return pe(e.row)}},{default:f((function(){return[p(a,{style:{"margin-right":"5px"}},{default:f((function(){return[p(C)]})),_:1}),n[35]||(n[35]=v("查看"))]})),_:2},1032,["onClick"]),p(x,{type:"primary",link:"",icon:"edit",class:"table-button",onClick:function(t){return oe(e.row)}},{default:f((function(){return n[36]||(n[36]=[v("编辑")])})),_:2},1032,["onClick"]),p(x,{type:"primary",link:"",icon:"delete",onClick:function(t){return n=e.row,void k.confirm("确定要删除吗?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){ie(n)}));var n}},{default:f((function(){return n[37]||(n[37]=[v("删除")])})),_:2},1032,["onClick"])]})),_:1},8,["min-width"])]})),_:1},8,["data"]),d("div",A,[p(E,{layout:"total, sizes, prev, pager, next, jumper","current-page":R.value,"page-size":Y.value,"page-sizes":[10,30,50,100],total:M.value,onCurrentChange:Z,onSizeChange:X},null,8,["current-page","page-size","total"])])]),p(ee,{"destroy-on-close":"",size:y(I).drawerSize,modelValue:ue.value,"onUpdate:modelValue":n[18]||(n[18]=function(e){return ue.value=e}),"show-close":!1,"before-close":le},{header:f((function(){return[d("div",P,[d("span",U,b("create"===ae.value?"新增":"编辑"),1),d("div",null,[p(x,{loading:T.value,type:"primary",onClick:ce},{default:f((function(){return n[38]||(n[38]=[v("确 定")])})),_:1},8,["loading"]),p(x,{onClick:le},{default:f((function(){return n[39]||(n[39]=[v("取 消")])})),_:1})])])]})),default:f((function(){return[p(j,{model:B.value,"label-position":"top",ref_key:"elFormRef",ref:q,rules:F,"label-width":"80px"},{default:f((function(){return[p(u,{label:"收件人联系方式:",prop:"addressee"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取 "+b(B.value.addressee),1)]})),_:1}),p(u,{label:"分析数据:",prop:"analytics_data"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取 "+b(B.value.analytics_data),1)]})),_:1}),p(u,{label:"买家信息:",prop:"customer"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取 "+b(B.value.customer),1)]})),_:1}),p(u,{label:"货件条码:",prop:"barcodes"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取 "+b(B.value.barcodes),1)]})),_:1}),p(u,{label:"货件交付物流的时间:",prop:"delivering_date"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取 "+b(B.value.delivering_date),1)]})),_:1}),p(u,{label:"快递方式:",prop:"delivery_method"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取 "+b(B.value.delivery_method),1)]})),_:1}),p(u,{label:"有关商品成本、折扣幅度、付款和佣金的信息:",prop:"financial_data"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取 "+b(B.value.financial_data),1)]})),_:1}),p(u,{label:"开始处理货件的日期和时间:",prop:"in_process_at"},{default:f((function(){return[p(i,{modelValue:B.value.in_process_at,"onUpdate:modelValue":n[5]||(n[5]=function(e){return B.value.in_process_at=e}),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),p(u,{label:"如果使用快速物流 Ozon Express —— true:",prop:"is_express"},{default:f((function(){return[p(L,{modelValue:B.value.is_express,"onUpdate:modelValue":n[6]||(n[6]=function(e){return B.value.is_express=e}),"active-color":"#13ce66","inactive-color":"#ff4949","active-text":"是","inactive-text":"否",clearable:""},null,8,["modelValue"])]})),_:1}),p(u,{label:"带有附加特征的商品列表:",prop:"optional"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取 "+b(B.value.optional),1)]})),_:1}),p(u,{label:"货件所属订单的ID:",prop:"order_id"},{default:f((function(){return[p($,{modelValue:B.value.order_id,"onUpdate:modelValue":n[7]||(n[7]=function(e){return B.value.order_id=e}),modelModifiers:{number:!0},clearable:!0,placeholder:"请输入货件所属订单的ID"},null,8,["modelValue"])]})),_:1}),p(u,{label:"货件所属的订单号:",prop:"order_number"},{default:f((function(){return[p($,{modelValue:B.value.order_number,"onUpdate:modelValue":n[8]||(n[8]=function(e){return B.value.order_number=e}),clearable:!0,placeholder:"请输入货件所属的订单号"},null,8,["modelValue"])]})),_:1}),p(u,{label:"快递母件编号，从该母件中拆分出了当前货件:",prop:"parent_posting_number"},{default:f((function(){return[p($,{modelValue:B.value.parent_posting_number,"onUpdate:modelValue":n[9]||(n[9]=function(e){return B.value.parent_posting_number=e}),clearable:!0,placeholder:"请输入快递母件编号，从该母件中拆分出了当前货件"},null,8,["modelValue"])]})),_:1}),p(u,{label:"货件号:",prop:"posting_number"},{default:f((function(){return[p($,{modelValue:B.value.posting_number,"onUpdate:modelValue":n[10]||(n[10]=function(e){return B.value.posting_number=e}),clearable:!0,placeholder:"请输入货件号"},null,8,["modelValue"])]})),_:1}),p(u,{label:"货运商品列表:",prop:"products"},{default:f((function(){return[p(V,{modelValue:B.value.products,"onUpdate:modelValue":n[11]||(n[11]=function(e){return B.value.products=e}),editable:""},null,8,["modelValue"])]})),_:1}),p(u,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:",prop:"requirements"},{default:f((function(){return[v(" // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取 "+b(B.value.requirements),1)]})),_:1}),p(u,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:",prop:"shipment_date"},{default:f((function(){return[p(i,{modelValue:B.value.shipment_date,"onUpdate:modelValue":n[12]||(n[12]=function(e){return B.value.shipment_date=e}),type:"date",style:{width:"100%"},placeholder:"选择日期",clearable:!0},null,8,["modelValue"])]})),_:1}),p(u,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:",prop:"status"},{default:f((function(){return[p($,{modelValue:B.value.status,"onUpdate:modelValue":n[13]||(n[13]=function(e){return B.value.status=e}),clearable:!0,placeholder:"请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])]})),_:1}),p(u,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:",prop:"substatus"},{default:f((function(){return[p($,{modelValue:B.value.substatus,"onUpdate:modelValue":n[14]||(n[14]=function(e){return B.value.substatus=e}),clearable:!0,placeholder:"请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},null,8,["modelValue"])]})),_:1}),p(u,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:",prop:"tpl_integration_type"},{default:f((function(){return[p($,{modelValue:B.value.tpl_integration_type,"onUpdate:modelValue":n[15]||(n[15]=function(e){return B.value.tpl_integration_type=e}),clearable:!0,placeholder:"请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},null,8,["modelValue"])]})),_:1}),p(u,{label:"货件跟踪号:",prop:"tracking_number"},{default:f((function(){return[p($,{modelValue:B.value.tracking_number,"onUpdate:modelValue":n[16]||(n[16]=function(e){return B.value.tracking_number=e}),clearable:!0,placeholder:"请输入货件跟踪号"},null,8,["modelValue"])]})),_:1}),p(u,{label:"发运的计费信息:",prop:"tariffication\t"},{default:f((function(){return[p(V,{modelValue:B.value.tariffication,"onUpdate:modelValue":n[17]||(n[17]=function(e){return B.value.tariffication=e}),editable:""},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"])]})),_:1},8,["size","modelValue"]),p(ee,{"destroy-on-close":"",size:y(I).drawerSize,modelValue:de.value,"onUpdate:modelValue":n[21]||(n[21]=function(e){return de.value=e}),"show-close":!0,"before-close":fe,title:"查看"},{default:f((function(){return[p(ve,{column:1,border:""},{default:f((function(){return[p(_e,{label:"收件人联系方式"},{default:f((function(){return[v(b(se.value.addressee),1)]})),_:1}),p(_e,{label:"分析数据"},{default:f((function(){return[v(b(se.value.analytics_data),1)]})),_:1}),p(_e,{label:"买家信息"},{default:f((function(){return[v(b(se.value.customer),1)]})),_:1}),p(_e,{label:"货件条码"},{default:f((function(){return[v(b(se.value.barcodes),1)]})),_:1}),p(_e,{label:"货件交付物流的时间"},{default:f((function(){return[v(b(se.value.delivering_date),1)]})),_:1}),p(_e,{label:"快递方式"},{default:f((function(){return[v(b(se.value.delivery_method),1)]})),_:1}),p(_e,{label:"有关商品成本、折扣幅度、付款和佣金的信息"},{default:f((function(){return[v(b(se.value.financial_data),1)]})),_:1}),p(_e,{label:"开始处理货件的日期和时间"},{default:f((function(){return[v(b(se.value.in_process_at),1)]})),_:1}),p(_e,{label:"如果使用快速物流 Ozon Express —— true"},{default:f((function(){return[v(b(se.value.is_express),1)]})),_:1}),p(_e,{label:"带有附加特征的商品列表"},{default:f((function(){return[v(b(se.value.optional),1)]})),_:1}),p(_e,{label:"货件所属订单的ID"},{default:f((function(){return[v(b(se.value.order_id),1)]})),_:1}),p(_e,{label:"货件所属的订单号"},{default:f((function(){return[v(b(se.value.order_number),1)]})),_:1}),p(_e,{label:"快递母件编号，从该母件中拆分出了当前货件"},{default:f((function(){return[v(b(se.value.parent_posting_number),1)]})),_:1}),p(_e,{label:"货件号"},{default:f((function(){return[v(b(se.value.posting_number),1)]})),_:1}),p(_e,{label:"货运商品列表"},{default:f((function(){return[p(V,{modelValue:se.value.products,"onUpdate:modelValue":n[19]||(n[19]=function(e){return se.value.products=e})},null,8,["modelValue"])]})),_:1}),p(_e,{label:"需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态"},{default:f((function(){return[v(b(se.value.requirements),1)]})),_:1}),p(_e,{label:"必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段"},{default:f((function(){return[v(b(se.value.shipment_date),1)]})),_:1}),p(_e,{label:"货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。"},{default:f((function(){return[v(b(se.value.status),1)]})),_:1}),p(_e,{label:"发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。"},{default:f((function(){return[v(b(se.value.substatus),1)]})),_:1}),p(_e,{label:"快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。"},{default:f((function(){return[v(b(se.value.tpl_integration_type),1)]})),_:1}),p(_e,{label:"货件跟踪号"},{default:f((function(){return[v(b(se.value.tracking_number),1)]})),_:1}),p(_e,{label:"发运的计费信息"},{default:f((function(){return[p(V,{modelValue:se.value.tariffication,"onUpdate:modelValue":n[20]||(n[20]=function(e){return se.value.tariffication=e})},null,8,["modelValue"])]})),_:1})]})),_:1})]})),_:1},8,["size","modelValue"])])}}}))}}}))}();
