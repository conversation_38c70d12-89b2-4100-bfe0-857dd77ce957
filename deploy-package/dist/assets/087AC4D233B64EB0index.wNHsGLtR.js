/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{k as e,c as s,b as t,f as a,h as l,w as n,g as r,u,o as i}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const c={class:"w-full h-screen bg-gray-50 flex items-center justify-center"},o={class:"flex flex-col items-center text-2xl gap-4"},p=Object.assign({name:"Error"},{__name:"index",setup(p){const f=e(),g=u(),d=()=>{g.push({name:f.userInfo.authority.defaultRouter})};return(e,u)=>{const p=r("el-button");return i(),s("div",null,[t("div",c,[t("div",o,[u[1]||(u[1]=t("img",{class:"w-1/3",src:"/assets/087AC4D233B64EB0404.Bk63Q-R4.png"},null,-1)),u[2]||(u[2]=t("p",{class:"text-lg"},"页面被神秘力量吸走了",-1)),u[3]||(u[3]=t("p",{class:"text-lg"}," 常见问题为当前此角色无当前路由，如果确定要使用本路由，请到角色管理进行分配 ",-1)),u[4]||(u[4]=t("p",null,[l(" 项目地址："),t("a",{href:"https://github.com/flipped-aurora/gin-vue-admin",target:"_blank",class:"text-blue-600"},"https://github.com/flipped-aurora/gin-vue-admin")],-1)),a(p,{onClick:d},{default:n((()=>u[0]||(u[0]=[l("返回首页")]))),_:1})])])])}}});export{p as default};
