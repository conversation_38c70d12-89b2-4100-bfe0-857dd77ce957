/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
import{s as t}from"./087AC4D233B64EB0index.Dnk2pLCR.js";const a=a=>t({url:"/authority/getAuthorityList",method:"post",data:a}),o=a=>t({url:"/authority/deleteAuthority",method:"post",data:a}),r=a=>t({url:"/authority/createAuthority",method:"post",data:a}),u=a=>t({url:"/authority/copyAuthority",method:"post",data:a}),h=a=>t({url:"/authority/setDataAuthority",method:"post",data:a}),s=a=>t({url:"/authority/updateAuthority",method:"put",data:a});export{r as a,u as c,o as d,a as g,h as s,s as u};
