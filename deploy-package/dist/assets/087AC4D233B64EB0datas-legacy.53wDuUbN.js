/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function t(r){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(r)}function r(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */r=function(){return n};var e,n={},o=Object.prototype,i=o.hasOwnProperty,a=Object.defineProperty||function(t,r,e){t[r]=e.value},u="function"==typeof Symbol?Symbol:{},c=u.iterator||"@@iterator",l=u.asyncIterator||"@@asyncIterator",f=u.toStringTag||"@@toStringTag";function h(t,r,e){return Object.defineProperty(t,r,{value:e,enumerable:!0,configurable:!0,writable:!0}),t[r]}try{h({},"")}catch(e){h=function(t,r,e){return t[r]=e}}function s(t,r,e,n){var o=r&&r.prototype instanceof w?r:w,i=Object.create(o.prototype),u=new C(n||[]);return a(i,"_invoke",{value:O(t,e,u)}),i}function y(t,r,e){try{return{type:"normal",arg:t.call(r,e)}}catch(t){return{type:"throw",arg:t}}}n.wrap=s;var p="suspendedStart",v="suspendedYield",d="executing",m="completed",g={};function w(){}function b(){}function x(){}var E={};h(E,c,(function(){return this}));var L=Object.getPrototypeOf,_=L&&L(L(P([])));_&&_!==o&&i.call(_,c)&&(E=_);var I=x.prototype=w.prototype=Object.create(E);function j(t){["next","throw","return"].forEach((function(r){h(t,r,(function(t){return this._invoke(r,t)}))}))}function A(r,e){function n(o,a,u,c){var l=y(r[o],r,a);if("throw"!==l.type){var f=l.arg,h=f.value;return h&&"object"==t(h)&&i.call(h,"__await")?e.resolve(h.__await).then((function(t){n("next",t,u,c)}),(function(t){n("throw",t,u,c)})):e.resolve(h).then((function(t){f.value=t,u(f)}),(function(t){return n("throw",t,u,c)}))}c(l.arg)}var o;a(this,"_invoke",{value:function(t,r){function i(){return new e((function(e,o){n(t,r,e,o)}))}return o=o?o.then(i,i):i()}})}function O(t,r,n){var o=p;return function(i,a){if(o===d)throw Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:e,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=k(u,n);if(c){if(c===g)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=m,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var l=y(t,r,n);if("normal"===l.type){if(o=n.done?m:v,l.arg===g)continue;return{value:l.arg,done:n.done}}"throw"===l.type&&(o=m,n.method="throw",n.arg=l.arg)}}}function k(t,r){var n=r.method,o=t.iterator[n];if(o===e)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=e,k(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),g;var i=y(o,t.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,g;var a=i.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=e),r.delegate=null,g):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,g)}function S(t){var r={tryLoc:t[0]};1 in t&&(r.catchLoc=t[1]),2 in t&&(r.finallyLoc=t[2],r.afterLoc=t[3]),this.tryEntries.push(r)}function N(t){var r=t.completion||{};r.type="normal",delete r.arg,t.completion=r}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(S,this),this.reset(!0)}function P(r){if(r||""===r){var n=r[c];if(n)return n.call(r);if("function"==typeof r.next)return r;if(!isNaN(r.length)){var o=-1,a=function t(){for(;++o<r.length;)if(i.call(r,o))return t.value=r[o],t.done=!1,t;return t.value=e,t.done=!0,t};return a.next=a}}throw new TypeError(t(r)+" is not iterable")}return b.prototype=x,a(I,"constructor",{value:x,configurable:!0}),a(x,"constructor",{value:b,configurable:!0}),b.displayName=h(x,f,"GeneratorFunction"),n.isGeneratorFunction=function(t){var r="function"==typeof t&&t.constructor;return!!r&&(r===b||"GeneratorFunction"===(r.displayName||r.name))},n.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,x):(t.__proto__=x,h(t,f,"GeneratorFunction")),t.prototype=Object.create(I),t},n.awrap=function(t){return{__await:t}},j(A.prototype),h(A.prototype,l,(function(){return this})),n.AsyncIterator=A,n.async=function(t,r,e,o,i){void 0===i&&(i=Promise);var a=new A(s(t,r,e,o),i);return n.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(I),h(I,f,"Generator"),h(I,c,(function(){return this})),h(I,"toString",(function(){return"[object Generator]"})),n.keys=function(t){var r=Object(t),e=[];for(var n in r)e.push(n);return e.reverse(),function t(){for(;e.length;){var n=e.pop();if(n in r)return t.value=n,t.done=!1,t}return t.done=!0,t}},n.values=P,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(N),!t)for(var r in this)"t"===r.charAt(0)&&i.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=e)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var r=this;function n(n,o){return u.type="throw",u.arg=t,r.next=n,o&&(r.method="next",r.arg=e),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],u=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var c=i.call(a,"catchLoc"),l=i.call(a,"finallyLoc");if(c&&l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(t,r){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc<=this.prev&&i.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=r&&r<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=t,a.arg=r,o?(this.method="next",this.next=o.finallyLoc,g):this.complete(a)},complete:function(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),g},finish:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.finallyLoc===t)return this.complete(e.completion,e.afterLoc),N(e),g}},catch:function(t){for(var r=this.tryEntries.length-1;r>=0;--r){var e=this.tryEntries[r];if(e.tryLoc===t){var n=e.completion;if("throw"===n.type){var o=n.arg;N(e)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,r,n){return this.delegate={iterator:P(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=e),g}},n}function e(t,r,e,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void e(t)}u.done?r(c):Promise.resolve(c).then(n,o)}function n(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(t){if("string"==typeof t)return o(t,r);var e={}.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?o(t,r):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=Array(r);e<r;e++)n[e]=t[e];return n}System.register(["./087AC4D233B64EB0authority-legacy.DVZAS3a8.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js"],(function(t,o){"use strict";var i,a,u,c,l,f,h,s,y,p,v,d,m,g,w;return{setters:[function(t){i=t.s},function(t){a=t._},function(t){u=t.a,c=t.g,l=t.c,f=t.o,h=t.f,s=t.b,y=t.w,p=t.h,v=t.F,d=t.D,m=t.i,g=t.t,w=t.E}],execute:function(){var o={class:"sticky top-0.5 z-10 my-4"},b={class:"clear-both pt-4"};t("default",Object.assign({name:"Datas"},{__name:"datas",props:{row:{default:function(){return{}},type:Object},authority:{default:function(){return[]},type:Array}},emits:["changeRow"],setup:function(t,x){var E=x.expose,L=x.emit,_=t,I=u([]),j=u(!1),A=function(t){t&&t.forEach((function(t){var r={};r.authorityId=t.authorityId,r.authorityName=t.authorityName,I.value.push(r),t.children&&t.children.length&&A(t.children)}))},O=u([]);A(_.authority),_.row.dataAuthorityId&&_.row.dataAuthorityId.forEach((function(t){var r=I.value&&I.value.filter((function(r){return r.authorityId===t.authorityId}))&&I.value.filter((function(r){return r.authorityId===t.authorityId}))[0];O.value.push(r)}));var k=L,S=function(){O.value=n(I.value),k("changeRow","dataAuthorityId",O.value),j.value=!0},N=function(){O.value=I.value.filter((function(t){return t.authorityId===_.row.authorityId})),k("changeRow","dataAuthorityId",O.value),j.value=!0},C=function(){var t=[];P(_.row,t),O.value=I.value.filter((function(r){return t.indexOf(r.authorityId)>-1})),k("changeRow","dataAuthorityId",O.value),j.value=!0},P=function(t,r){r.push(t.authorityId),t.children&&t.children.forEach((function(t){P(t,r)}))},G=function(){var t,n=(t=r().mark((function t(){return r().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,i(_.row);case 2:0===t.sent.code&&w({type:"success",message:"资源设置成功"});case 4:case"end":return t.stop()}}),t)})),function(){var r=this,n=arguments;return new Promise((function(o,i){var a=t.apply(r,n);function u(t){e(a,o,i,u,c,"next",t)}function c(t){e(a,o,i,u,c,"throw",t)}u(void 0)}))});return function(){return n.apply(this,arguments)}}(),B=function(){O.value=O.value.filter((function(t){return t})),k("changeRow","dataAuthorityId",O.value),j.value=!0};return E({enterAndNext:function(){G()},needConfirm:j}),function(t,r){var e=c("el-button"),n=c("el-checkbox"),i=c("el-checkbox-group");return f(),l("div",null,[h(a,{title:"此功能仅用于创建角色和角色的many2many关系表，具体使用还须自己结合表实现业务，详情参考示例代码（客户示例）。此功能不建议使用，建议使用插件市场【组织管理功能（点击前往）】来管理资源权限。",href:"https://plugin.gin-vue-admin.com/#/layout/newPluginInfo?id=36"}),s("div",o,[h(e,{class:"float-left",type:"primary",onClick:S},{default:y((function(){return r[1]||(r[1]=[p("全选")])})),_:1}),h(e,{class:"float-left",type:"primary",onClick:N},{default:y((function(){return r[2]||(r[2]=[p("本角色")])})),_:1}),h(e,{class:"float-left",type:"primary",onClick:C},{default:y((function(){return r[3]||(r[3]=[p("本角色及子角色")])})),_:1}),h(e,{class:"float-right",type:"primary",onClick:G},{default:y((function(){return r[4]||(r[4]=[p("确 定")])})),_:1})]),s("div",b,[h(i,{modelValue:O.value,"onUpdate:modelValue":r[0]||(r[0]=function(t){return O.value=t}),onChange:B},{default:y((function(){return[(f(!0),l(v,null,d(I.value,(function(t,r){return f(),m(n,{key:r,label:t},{default:y((function(){return[p(g(t.authorityName),1)]})),_:2},1032,["label"])})),128))]})),_:1},8,["modelValue"])])])}}}))}}}))}();
