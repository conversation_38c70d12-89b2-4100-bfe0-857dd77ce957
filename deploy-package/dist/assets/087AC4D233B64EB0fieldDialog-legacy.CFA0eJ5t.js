/*! 
 Build based on gin-vue-admin 
 Time : 1752160838000 */
!function(){function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */t=function(){return a};var n,a={},l=Object.prototype,r=l.hasOwnProperty,u=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",d=o.toStringTag||"@@toStringTag";function f(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{f({},"")}catch(n){f=function(e,t,n){return e[t]=n}}function s(e,t,n,a){var l=t&&t.prototype instanceof g?t:g,r=Object.create(l.prototype),o=new j(a||[]);return u(r,"_invoke",{value:L(e,n,o)}),r}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var m="suspendedStart",v="suspendedYield",y="executing",h="completed",b={};function g(){}function V(){}function _(){}var w={};f(w,i,(function(){return this}));var x=Object.getPrototypeOf,N=x&&x(x(D([])));N&&N!==l&&r.call(N,i)&&(w=N);var S=_.prototype=g.prototype=Object.create(w);function T(e){["next","throw","return"].forEach((function(t){f(e,t,(function(e){return this._invoke(t,e)}))}))}function E(t,n){function a(l,u,o,i){var c=p(t[l],t,u);if("throw"!==c.type){var d=c.arg,f=d.value;return f&&"object"==e(f)&&r.call(f,"__await")?n.resolve(f.__await).then((function(e){a("next",e,o,i)}),(function(e){a("throw",e,o,i)})):n.resolve(f).then((function(e){d.value=e,o(d)}),(function(e){return a("throw",e,o,i)}))}i(c.arg)}var l;u(this,"_invoke",{value:function(e,t){function r(){return new n((function(n,l){a(e,t,n,l)}))}return l=l?l.then(r,r):r()}})}function L(e,t,a){var l=m;return function(r,u){if(l===y)throw Error("Generator is already running");if(l===h){if("throw"===r)throw u;return{value:n,done:!0}}for(a.method=r,a.arg=u;;){var o=a.delegate;if(o){var i=U(o,a);if(i){if(i===b)continue;return i}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(l===m)throw l=h,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);l=y;var c=p(e,t,a);if("normal"===c.type){if(l=a.done?h:v,c.arg===b)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(l=h,a.method="throw",a.arg=c.arg)}}}function U(e,t){var a=t.method,l=e.iterator[a];if(l===n)return t.delegate=null,"throw"===a&&e.iterator.return&&(t.method="return",t.arg=n,U(e,t),"throw"===t.method)||"return"!==a&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+a+"' method")),b;var r=p(l,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,b;var u=r.arg;return u?u.done?(t[e.resultName]=u.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,b):u:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,b)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function D(t){if(t||""===t){var a=t[i];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var l=-1,u=function e(){for(;++l<t.length;)if(r.call(t,l))return e.value=t[l],e.done=!1,e;return e.value=n,e.done=!0,e};return u.next=u}}throw new TypeError(e(t)+" is not iterable")}return V.prototype=_,u(S,"constructor",{value:_,configurable:!0}),u(_,"constructor",{value:V,configurable:!0}),V.displayName=f(_,d,"GeneratorFunction"),a.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===V||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,f(e,d,"GeneratorFunction")),e.prototype=Object.create(S),e},a.awrap=function(e){return{__await:e}},T(E.prototype),f(E.prototype,c,(function(){return this})),a.AsyncIterator=E,a.async=function(e,t,n,l,r){void 0===r&&(r=Promise);var u=new E(s(e,t,n,l),r);return a.isGeneratorFunction(t)?u:u.next().then((function(e){return e.done?e.value:u.next()}))},T(S),f(S,d,"Generator"),f(S,i,(function(){return this})),f(S,"toString",(function(){return"[object Generator]"})),a.keys=function(e){var t=Object(e),n=[];for(var a in t)n.push(a);return n.reverse(),function e(){for(;n.length;){var a=n.pop();if(a in t)return e.value=a,e.done=!1,e}return e.done=!0,e}},a.values=D,j.prototype={constructor:j,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function a(a,l){return o.type="throw",o.arg=e,t.next=a,l&&(t.method="next",t.arg=n),!!l}for(var l=this.tryEntries.length-1;l>=0;--l){var u=this.tryEntries[l],o=u.completion;if("root"===u.tryLoc)return a("end");if(u.tryLoc<=this.prev){var i=r.call(u,"catchLoc"),c=r.call(u,"finallyLoc");if(i&&c){if(this.prev<u.catchLoc)return a(u.catchLoc,!0);if(this.prev<u.finallyLoc)return a(u.finallyLoc)}else if(i){if(this.prev<u.catchLoc)return a(u.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<u.finallyLoc)return a(u.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var a=this.tryEntries[n];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var l=a;break}}l&&("break"===e||"continue"===e)&&l.tryLoc<=t&&t<=l.finallyLoc&&(l=null);var u=l?l.completion:{};return u.type=e,u.arg=t,l?(this.method="next",this.next=l.finallyLoc,b):this.complete(u)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var a=n.completion;if("throw"===a.type){var l=a.arg;O(n)}return l}}throw Error("illegal catch attempt")},delegateYield:function(e,t,a){return this.delegate={iterator:D(e),resultName:t,nextLoc:a},"next"===this.method&&(this.arg=n),b}},a}function n(e,t,n,a,l,r,u){try{var o=e[r](u),i=o.value}catch(e){return void n(e)}o.done?t(i):Promise.resolve(i).then(a,l)}function a(e){return function(){var t=this,a=arguments;return new Promise((function(l,r){var u=e.apply(t,a);function o(e){n(u,l,r,o,i,"next",e)}function i(e){n(u,l,r,o,i,"throw",e)}o(void 0)}))}}System.register(["./087AC4D233B64EB0stringFun-legacy.2vIcgB7Q.js","./087AC4D233B64EB0index-legacy.D7r6sPSg.js","./087AC4D233B64EB0warningBar-legacy.sld5Oer7.js","./087AC4D233B64EB0autoCode-legacy.Bny7MGFb.js"],(function(e,n){"use strict";var l,r,u,o,i,c,d,f,s,p,m,v,y,h,b,g,V,_,w,x,N,S;return{setters:[function(e){l=e.a,r=e.t},function(e){u=e.a,o=e.p,i=e.g,c=e.c,d=e.o,f=e.f,s=e.w,p=e.b,m=e.F,v=e.D,y=e.i,h=e.t,b=e.h,g=e.d,V=e.aO,_=e.ab},function(e){w=e._},function(e){x=e.g,N=e.a,S=e.c}],execute:function(){var n={style:{float:"right",color:"#8492a6","font-size":"13px"}},T={style:{"font-weight":"bold"}},E={style:{float:"left"}},L={style:{float:"right","margin-left":"5px",color:"var(--el-text-color-secondary)","font-size":"13px"}},U={style:{"font-weight":"bold"}},k={style:{float:"left"}},O={style:{float:"right","margin-left":"5px",color:"var(--el-text-color-secondary)","font-size":"13px"}},j={key:0};e("default",Object.assign({name:"FieldDialog"},{__name:"fieldDialog",props:{dialogMiddle:{type:Object,default:function(){return{}}},typeOptions:{type:Array,default:function(){return[]}},typeSearchOptions:{type:Array,default:function(){return[]}},typeIndexOptions:{type:Array,default:function(){return[]}}},setup:function(e,D){var B=D.expose,P=e,A=u([]),C=u({}),F=u([]),I=u([]),G=function(){var e=a(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,x();case 2:0===(n=e.sent).code&&(I.value=n.data.dbList);case 4:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),q=u({fieldName:[{required:!0,message:"请输入字段英文名",trigger:"blur"}],fieldDesc:[{required:!0,message:"请输入字段中文名",trigger:"blur"}],fieldJson:[{required:!0,message:"请输入字段格式化json",trigger:"blur"}],columnName:[{required:!0,message:"请输入数据库字段",trigger:"blur"}],fieldType:[{required:!0,message:"请选择字段类型",trigger:"blur"}],dataTypeLong:[{validator:function(e,t,n){"enum"!=C.value.fieldType||/^('([^']*)'(?:,'([^']+)'*)*)$/.test(t)?n():n(new Error("枚举值校验错误"))},trigger:"blur"}]}),J=function(){var e=a(t().mark((function e(){var n;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return C.value=P.dialogMiddle,e.next=3,V({page:1,pageSize:999999});case 3:n=e.sent,F.value=n.data;case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();J();var K=function(){C.value.fieldJson=l(C.value.fieldName),C.value.columnName=r(C.value.fieldJson)},z=function(e){var t=C.value.fieldType;if("richtext"===t)return"LIKE"!==e;if("string"!==t&&"LIKE"===e)return!0;return!(["int","time.Time","float64"].includes(t)||!["BETWEEN","NOT BETWEEN"].includes(e))},H=function(){C.value.fieldSearchType="",C.value.dictType=""},M=function(e){2===e&&_.confirm("一对多关联模式下，数据类型会改变为数组，后端表现为json，具体表现为数组模式，是否继续？","提示",{confirmButtonText:"继续",cancelButtonText:"取消",type:"warning"}).then((function(){C.value.fieldType="array"})).catch((function(){C.value.dataSource.association=1}))},W=function(){C.value.dataSource.value="",C.value.dataSource.label=""},Y=function(){Q(),C.value.dataSource.table="",W()},$=u([]),Q=function(){var e=a(t().mark((function e(){var n,a;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,S({businessDB:C.value.dataSource.dbName});case 2:0===(n=e.sent).code&&(a=n.data.tables,$.value=a.map((function(e){return{tableName:e.tableName,value:e.tableName}}))),W();case 5:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),R=u([]),X=function(){var e=a(t().mark((function e(n,a){var l,r;return t().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return C.value.dataSource.hasDeletedAt=!1,C.value.dataSource.table=n,e.next=4,N({businessDB:C.value.dataSource.dbName,tableName:n});case 4:0===(l=e.sent).code&&(r=l.data.columns,R.value=r.map((function(e){return"deleted_at"===e.columnName&&(C.value.dataSource.hasDeletedAt=!0),{columnName:e.columnName,value:e.columnName,type:e.dataType,isPrimary:e.primaryKey,comment:e.columnComment}})),R.value.length>0&&!a&&(C.value.dataSource.label=R.value[0].columnName,C.value.dataSource.value=R.value[0].columnName));case 6:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),Z=u(null);return B({fieldDialogForm:Z}),o((function(){G(),C.value.dataSource.table&&X(C.value.dataSource.table,!0)})),function(t,a){var l=i("el-input"),r=i("el-button"),u=i("el-form-item"),o=i("el-option"),V=i("el-select"),_=i("el-checkbox"),x=i("el-switch"),N=i("el-form"),S=i("el-col"),D=i("el-tag"),B=i("block"),P=i("el-row"),G=i("el-collapse-item"),J=i("el-collapse");return d(),c("div",null,[f(w,{title:"id , created_at , updated_at , deleted_at 会自动生成请勿重复创建。搜索时如果条件为LIKE只支持字符串"}),f(N,{ref_key:"fieldDialogForm",ref:Z,model:C.value,"label-width":"120px","label-position":"right",rules:q.value,class:"grid grid-cols-2"},{default:s((function(){return[f(u,{label:"字段名称",prop:"fieldName"},{default:s((function(){return[f(l,{modelValue:C.value.fieldName,"onUpdate:modelValue":a[0]||(a[0]=function(e){return C.value.fieldName=e}),autocomplete:"off",style:{width:"80%"}},null,8,["modelValue"]),f(r,{style:{width:"18%","margin-left":"2%"},onClick:K},{default:s((function(){return a[27]||(a[27]=[p("span",{style:{"font-size":"12px"}},"自动填充",-1)])})),_:1})]})),_:1}),f(u,{label:"字段中文名",prop:"fieldDesc"},{default:s((function(){return[f(l,{modelValue:C.value.fieldDesc,"onUpdate:modelValue":a[1]||(a[1]=function(e){return C.value.fieldDesc=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),f(u,{label:"字段JSON",prop:"fieldJson"},{default:s((function(){return[f(l,{modelValue:C.value.fieldJson,"onUpdate:modelValue":a[2]||(a[2]=function(e){return C.value.fieldJson=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),f(u,{label:"数据库字段名",prop:"columnName"},{default:s((function(){return[f(l,{modelValue:C.value.columnName,"onUpdate:modelValue":a[3]||(a[3]=function(e){return C.value.columnName=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),f(u,{label:"数据库字段描述",prop:"comment"},{default:s((function(){return[f(l,{modelValue:C.value.comment,"onUpdate:modelValue":a[4]||(a[4]=function(e){return C.value.comment=e}),autocomplete:"off"},null,8,["modelValue"])]})),_:1}),f(u,{label:"字段类型",prop:"fieldType"},{default:s((function(){return[f(V,{modelValue:C.value.fieldType,"onUpdate:modelValue":a[5]||(a[5]=function(e){return C.value.fieldType=e}),style:{width:"100%"},placeholder:"请选择字段类型",clearable:"",onChange:H},{default:s((function(){return[(d(!0),c(m,null,v(e.typeOptions,(function(e){return d(),y(o,{key:e.value,label:e.label,value:e.value,disabled:e.disabled},null,8,["label","value","disabled"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),f(u,{label:"enum"===C.value.fieldType?"枚举值":"类型长度",prop:"dataTypeLong"},{default:s((function(){return[f(l,{modelValue:C.value.dataTypeLong,"onUpdate:modelValue":a[6]||(a[6]=function(e){return C.value.dataTypeLong=e}),placeholder:"enum"===C.value.fieldType?"例:'北京','天津'":"数据库类型长度"},null,8,["modelValue","placeholder"])]})),_:1},8,["label"]),f(u,{label:"字段查询条件",prop:"fieldSearchType"},{default:s((function(){return[f(V,{modelValue:C.value.fieldSearchType,"onUpdate:modelValue":a[7]||(a[7]=function(e){return C.value.fieldSearchType=e}),disabled:"json"===C.value.fieldType,style:{width:"100%"},placeholder:"请选择字段查询条件",clearable:""},{default:s((function(){return[(d(!0),c(m,null,v(e.typeSearchOptions,(function(e){return d(),y(o,{key:e.value,label:e.label,value:e.value,disabled:z(e.value)},null,8,["label","value","disabled"])})),128))]})),_:1},8,["modelValue","disabled"])]})),_:1}),f(u,{label:"关联字典",prop:"dictType"},{default:s((function(){return[f(V,{modelValue:C.value.dictType,"onUpdate:modelValue":a[8]||(a[8]=function(e){return C.value.dictType=e}),style:{width:"100%"},disabled:"string"!==C.value.fieldType&&"array"!==C.value.fieldType,placeholder:"请选择字典",clearable:""},{default:s((function(){return[(d(!0),c(m,null,v(F.value,(function(e){return d(),y(o,{key:e.type,label:"".concat(e.type,"(").concat(e.name,")"),value:e.type},null,8,["label","value"])})),128))]})),_:1},8,["modelValue","disabled"])]})),_:1}),f(u,{label:"默认值"},{default:s((function(){return[f(l,{modelValue:C.value.defaultValue,"onUpdate:modelValue":a[9]||(a[9]=function(e){return C.value.defaultValue=e}),placeholder:"请输入默认值"},null,8,["modelValue"])]})),_:1}),f(u,{label:"主键"},{default:s((function(){return[f(_,{modelValue:C.value.primaryKey,"onUpdate:modelValue":a[10]||(a[10]=function(e){return C.value.primaryKey=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"索引类型",prop:"fieldIndexType"},{default:s((function(){return[f(V,{modelValue:C.value.fieldIndexType,"onUpdate:modelValue":a[11]||(a[11]=function(e){return C.value.fieldIndexType=e}),disabled:"json"===C.value.fieldType,style:{width:"100%"},placeholder:"请选择字段索引类型",clearable:""},{default:s((function(){return[(d(!0),c(m,null,v(e.typeIndexOptions,(function(e){return d(),y(o,{key:e.value,label:e.label,value:e.value,disabled:z(e.value)},null,8,["label","value","disabled"])})),128))]})),_:1},8,["modelValue","disabled"])]})),_:1}),f(u,{label:"前端新建/编辑"},{default:s((function(){return[f(x,{modelValue:C.value.form,"onUpdate:modelValue":a[12]||(a[12]=function(e){return C.value.form=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"前端表格列"},{default:s((function(){return[f(x,{modelValue:C.value.table,"onUpdate:modelValue":a[13]||(a[13]=function(e){return C.value.table=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"前端详情"},{default:s((function(){return[f(x,{modelValue:C.value.desc,"onUpdate:modelValue":a[14]||(a[14]=function(e){return C.value.desc=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"导入/导出"},{default:s((function(){return[f(x,{modelValue:C.value.excel,"onUpdate:modelValue":a[15]||(a[15]=function(e){return C.value.excel=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"是否排序"},{default:s((function(){return[f(x,{modelValue:C.value.sort,"onUpdate:modelValue":a[16]||(a[16]=function(e){return C.value.sort=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"是否必填"},{default:s((function(){return[f(x,{modelValue:C.value.require,"onUpdate:modelValue":a[17]||(a[17]=function(e){return C.value.require=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"是否可清空"},{default:s((function(){return[f(x,{modelValue:C.value.clearable,"onUpdate:modelValue":a[18]||(a[18]=function(e){return C.value.clearable=e})},null,8,["modelValue"])]})),_:1}),f(u,{label:"隐藏查询条件"},{default:s((function(){return[f(x,{modelValue:C.value.fieldSearchHide,"onUpdate:modelValue":a[19]||(a[19]=function(e){return C.value.fieldSearchHide=e}),disabled:!C.value.fieldSearchType},null,8,["modelValue","disabled"])]})),_:1}),f(u,{label:"校验失败文案"},{default:s((function(){return[f(l,{modelValue:C.value.errorText,"onUpdate:modelValue":a[20]||(a[20]=function(e){return C.value.errorText=e})},null,8,["modelValue"])]})),_:1})]})),_:1},8,["model","rules"]),f(J,{modelValue:A.value,"onUpdate:modelValue":a[26]||(a[26]=function(e){return A.value=e})},{default:s((function(){return[f(G,{title:"数据源配置（此配置为高级配置，如编程基础不牢，可能导致自动化代码不可用）",name:"1"},{default:s((function(){return[f(P,{gutter:8},{default:s((function(){return[f(S,{span:4},{default:s((function(){return[f(V,{modelValue:C.value.dataSource.dbName,"onUpdate:modelValue":a[21]||(a[21]=function(e){return C.value.dataSource.dbName=e}),placeholder:"数据库【不填则为GVA库】",onChange:Y,clearable:""},{default:s((function(){return[(d(!0),c(m,null,v(I.value,(function(e){return d(),y(o,{key:e.aliasName,value:e.aliasName,label:e.aliasName,disabled:e.disable},{default:s((function(){return[p("div",null,[p("span",null,h(e.aliasName),1),p("span",n,h(e.dbName),1)])]})),_:2},1032,["value","label","disabled"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),f(S,{span:4},{default:s((function(){return[f(V,{modelValue:C.value.dataSource.association,"onUpdate:modelValue":a[22]||(a[22]=function(e){return C.value.dataSource.association=e}),placeholder:"关联模式",onChange:M},{default:s((function(){return[f(o,{label:"一对一",value:1}),f(o,{label:"一对多",value:2})]})),_:1},8,["modelValue"])]})),_:1}),f(S,{span:5},{default:s((function(){return[f(V,{modelValue:C.value.dataSource.table,"onUpdate:modelValue":a[23]||(a[23]=function(e){return C.value.dataSource.table=e}),placeholder:"请选择数据源表",filterable:"","allow-create":"",clearable:"",onFocus:Q,onChange:X,onClear:W},{default:s((function(){return[(d(!0),c(m,null,v($.value,(function(e){return d(),y(o,{key:e.tableName,label:e.tableName,value:e.tableName},null,8,["label","value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),f(S,{span:5},{default:s((function(){return[f(V,{modelValue:C.value.dataSource.value,"onUpdate:modelValue":a[24]||(a[24]=function(e){return C.value.dataSource.value=e}),placeholder:"请先选择需要存储的数据"},{label:s((function(e){var t=e.value;return[a[28]||(a[28]=p("span",null,"存储: ",-1)),p("span",T,h(t),1)]})),default:s((function(){return[(d(!0),c(m,null,v(R.value,(function(e){return d(),y(o,{key:e.columnName,value:e.columnName},{default:s((function(){return[p("span",E,[f(D,{type:e.isPrimary?"primary":"info"},{default:s((function(){return[b(h(e.isPrimary?"主 键":"非主键"),1)]})),_:2},1032,["type"]),b(" "+h(e.columnName),1)]),p("span",L,[b(" 类型："+h(e.type)+" ",1),""!=e.comment?(d(),y(B,{key:0},{default:s((function(){return[b("，字段说明："+h(e.comment),1)]})),_:2},1024)):g("",!0)])]})),_:2},1032,["value"])})),128))]})),_:1},8,["modelValue"])]})),_:1}),f(S,{span:5},{default:s((function(){return[f(V,{modelValue:C.value.dataSource.label,"onUpdate:modelValue":a[25]||(a[25]=function(e){return C.value.dataSource.label=e}),placeholder:"请先选择需要展示的数据"},{label:s((function(e){var t=e.value;return[a[29]||(a[29]=p("span",null,"展示: ",-1)),p("span",U,h(t),1)]})),default:s((function(){return[(d(!0),c(m,null,v(R.value,(function(e){return d(),y(o,{key:e.columnName,value:e.columnName},{default:s((function(){return[p("span",k,[f(D,{type:e.isPrimary?"primary":"info"},{default:s((function(){return[b(h(e.isPrimary?"主 键":"非主键"),1)]})),_:2},1032,["type"]),b(" "+h(e.columnName),1)]),p("span",O,[b(" 类型："+h(e.type)+" ",1),""!=e.comment?(d(),c("span",j,"，字段说明："+h(e.comment),1)):g("",!0)])]})),_:2},1032,["value"])})),128))]})),_:1},8,["modelValue"])]})),_:1})]})),_:1})]})),_:1})]})),_:1},8,["modelValue"])])}}}))}}}))}();
