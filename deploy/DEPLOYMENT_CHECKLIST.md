# 🚀 XRT项目部署检查清单

## 📋 部署前准备

### 1. 服务器要求检查
- [ ] CPU: 2核心以上
- [ ] 内存: 4GB以上
- [ ] 存储: 20GB以上可用空间
- [ ] 操作系统: Ubuntu 18.04+ 或 CentOS 7+
- [ ] 网络: 稳定的互联网连接

### 2. 权限检查
- [ ] 有服务器SSH访问权限
- [ ] 有sudo权限
- [ ] 可以安装软件包

### 3. 端口检查
- [ ] 端口80可用（HTTP）
- [ ] 端口443可用（HTTPS，可选）
- [ ] 端口8080可用（前端）
- [ ] 端口8888可用（后端）
- [ ] 端口13306可用（MySQL）
- [ ] 端口16379可用（Redis）

## 🐳 Docker部署步骤

### 步骤1: 上传部署文件
```bash
# 在本地执行
scp -r deploy/ user@your-server-ip:/opt/xrt-deploy/
```
- [ ] 文件上传成功
- [ ] 所有文件完整

### 步骤2: 连接服务器
```bash
ssh user@your-server-ip
```
- [ ] SSH连接成功
- [ ] 可以执行sudo命令

### 步骤3: 检查系统环境
```bash
# 检查操作系统
cat /etc/os-release

# 检查可用空间
df -h

# 检查内存
free -h

# 检查CPU
nproc
```
- [ ] 系统信息正常
- [ ] 资源充足

### 步骤4: 安装Docker
```bash
cd /opt/xrt-deploy
chmod +x install-docker.sh
./install-docker.sh
```
- [ ] Docker安装成功
- [ ] Docker Compose安装成功
- [ ] 用户添加到docker组
- [ ] 重新登录生效

### 步骤5: 验证Docker安装
```bash
# 重新登录后执行
docker --version
docker-compose --version
docker run hello-world
```
- [ ] Docker版本显示正常
- [ ] Docker Compose版本显示正常
- [ ] hello-world测试成功

### 步骤6: 部署XRT项目
```bash
cd /opt/xrt-deploy/docker-compose
chmod +x start.sh
./start.sh
```
- [ ] 启动脚本执行成功
- [ ] 所有容器启动正常
- [ ] 服务健康检查通过

### 步骤7: 验证部署
```bash
# 检查容器状态
docker-compose ps

# 检查服务健康
curl http://localhost:8080
curl http://localhost:8888/health
```
- [ ] 所有容器状态为Up
- [ ] 前端页面可访问
- [ ] 后端API响应正常

## 🔧 传统部署步骤（备选）

### 步骤1: 使用生产部署包
```bash
# 在本地生成部署包
./deploy/deploy.sh production

# 上传部署包
scp -r deploy-package/ user@your-server-ip:/opt/xrt/
```
- [ ] 部署包生成成功
- [ ] 文件上传完整

### 步骤2: 安装依赖
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx mysql-server

# CentOS/RHEL
sudo yum update
sudo yum install nginx mysql-server
```
- [ ] Nginx安装成功
- [ ] MySQL安装成功

### 步骤3: 配置数据库
```bash
sudo mysql -u root -p
CREATE DATABASE xrt CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xrt_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xrt.* TO 'xrt_user'@'localhost';
FLUSH PRIVILEGES;
```
- [ ] 数据库创建成功
- [ ] 用户权限配置正确

### 步骤4: 配置Nginx
```bash
sudo cp /opt/xrt-deploy/nginx/xrt.conf /etc/nginx/sites-available/xrt
sudo ln -s /etc/nginx/sites-available/xrt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```
- [ ] Nginx配置文件复制成功
- [ ] 配置语法检查通过
- [ ] Nginx重载成功

### 步骤5: 启动应用
```bash
cd /opt/xrt
./start.sh
```
- [ ] 应用启动成功
- [ ] 日志无错误

## 🔍 故障排除

### 常见问题检查

#### Docker相关
```bash
# 检查Docker服务状态
sudo systemctl status docker

# 检查容器日志
docker-compose logs -f

# 重启Docker服务
sudo systemctl restart docker
```

#### 端口冲突
```bash
# 检查端口占用
sudo lsof -i :8080
sudo lsof -i :8888

# 杀死占用进程
sudo kill -9 PID
```

#### 权限问题
```bash
# 检查文件权限
ls -la /opt/xrt-deploy/

# 修复权限
sudo chown -R $USER:$USER /opt/xrt-deploy/
chmod +x /opt/xrt-deploy/docker-compose/start.sh
```

#### 磁盘空间
```bash
# 检查磁盘使用
df -h

# 清理Docker
docker system prune -a
```

## ✅ 部署完成检查

### 功能验证
- [ ] 前端页面正常加载 (http://your-server-ip:8080)
- [ ] 可以正常登录 (admin/123456)
- [ ] 后端API响应正常 (http://your-server-ip:8888)
- [ ] 数据库连接正常
- [ ] 文件上传功能正常

### 性能检查
- [ ] 页面加载速度正常
- [ ] API响应时间正常
- [ ] 内存使用合理
- [ ] CPU使用正常

### 安全检查
- [ ] 修改默认密码
- [ ] 配置防火墙
- [ ] 关闭不必要的端口
- [ ] 配置SSL证书（可选）

## 📞 需要帮助时

如果遇到问题，请提供以下信息：

1. **错误信息**：完整的错误日志
2. **系统信息**：操作系统版本
3. **执行的命令**：具体执行了什么命令
4. **环境状态**：容器状态、服务状态等

### 获取调试信息的命令
```bash
# 系统信息
uname -a
cat /etc/os-release

# Docker信息
docker --version
docker-compose --version
docker-compose ps
docker-compose logs

# 系统资源
free -h
df -h
top
```

---

**📝 注意事项**
- 每个步骤完成后请打勾确认
- 遇到错误立即停止并寻求帮助
- 保存重要的配置和密码
- 定期备份数据
