#!/bin/bash

# Docker和Docker Compose自动安装脚本
# 支持Ubuntu/Debian和CentOS/RHEL系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        VER=$(lsb_release -sr)
    elif [ -f /etc/lsb-release ]; then
        . /etc/lsb-release
        OS=$DISTRIB_ID
        VER=$DISTRIB_RELEASE
    elif [ -f /etc/debian_version ]; then
        OS=Debian
        VER=$(cat /etc/debian_version)
    elif [ -f /etc/SuSe-release ]; then
        OS=openSUSE
    elif [ -f /etc/redhat-release ]; then
        OS=RedHat
    else
        OS=$(uname -s)
        VER=$(uname -r)
    fi
    
    log_info "检测到操作系统: $OS $VER"
}

# 检查是否已安装Docker
check_docker_installed() {
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version | cut -d' ' -f3 | cut -d',' -f1)
        log_warning "Docker已安装 (版本: $DOCKER_VERSION)"
        return 0
    else
        return 1
    fi
}

# 检查是否已安装Docker Compose
check_compose_installed() {
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version | cut -d' ' -f3 | cut -d',' -f1)
        log_warning "Docker Compose已安装 (版本: $COMPOSE_VERSION)"
        return 0
    else
        return 1
    fi
}

# Ubuntu/Debian系统安装Docker
install_docker_ubuntu() {
    log_info "在Ubuntu/Debian系统上安装Docker..."
    
    # 更新包索引
    sudo apt-get update
    
    # 安装必要的包
    sudo apt-get install -y \
        apt-transport-https \
        ca-certificates \
        curl \
        gnupg \
        lsb-release
    
    # 添加Docker官方GPG密钥
    curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # 添加Docker仓库
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # 更新包索引
    sudo apt-get update
    
    # 安装Docker Engine
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io
    
    log_success "Docker安装完成"
}

# CentOS/RHEL系统安装Docker
install_docker_centos() {
    log_info "在CentOS/RHEL系统上安装Docker..."
    
    # 安装必要的包
    sudo yum install -y yum-utils
    
    # 添加Docker仓库
    sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
    
    # 安装Docker Engine
    sudo yum install -y docker-ce docker-ce-cli containerd.io
    
    log_success "Docker安装完成"
}

# 安装Docker Compose
install_docker_compose() {
    log_info "安装Docker Compose..."
    
    # 获取最新版本号
    COMPOSE_VERSION=$(curl -s https://api.github.com/repos/docker/compose/releases/latest | grep 'tag_name' | cut -d\" -f4)
    
    if [ -z "$COMPOSE_VERSION" ]; then
        log_warning "无法获取最新版本，使用默认版本"
        COMPOSE_VERSION="v2.20.0"
    fi
    
    log_info "下载Docker Compose $COMPOSE_VERSION..."
    
    # 下载Docker Compose
    sudo curl -L "https://github.com/docker/compose/releases/download/$COMPOSE_VERSION/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 添加执行权限
    sudo chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    sudo ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    log_success "Docker Compose安装完成"
}

# 配置Docker服务
configure_docker() {
    log_info "配置Docker服务..."
    
    # 启动Docker服务
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # 将当前用户添加到docker组
    sudo usermod -aG docker $USER
    
    # 配置Docker镜像加速器（中国用户）
    if [[ "$1" == "--china" ]]; then
        log_info "配置Docker镜像加速器..."
        sudo mkdir -p /etc/docker
        sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
        sudo systemctl restart docker
        log_success "Docker镜像加速器配置完成"
    fi
    
    log_success "Docker服务配置完成"
}

# 验证安装
verify_installation() {
    log_info "验证Docker安装..."
    
    # 验证Docker
    if docker --version; then
        log_success "Docker验证成功"
    else
        log_error "Docker验证失败"
        exit 1
    fi
    
    # 验证Docker Compose
    if docker-compose --version; then
        log_success "Docker Compose验证成功"
    else
        log_error "Docker Compose验证失败"
        exit 1
    fi
    
    # 测试Docker运行
    log_info "测试Docker运行..."
    if sudo docker run hello-world; then
        log_success "Docker运行测试成功"
    else
        log_error "Docker运行测试失败"
        exit 1
    fi
}

# 显示安装后信息
show_post_install_info() {
    echo ""
    log_success "🎉 Docker和Docker Compose安装完成！"
    echo ""
    log_info "安装信息："
    echo "  Docker版本: $(docker --version)"
    echo "  Docker Compose版本: $(docker-compose --version)"
    echo ""
    log_info "重要提示："
    echo "  1. 请重新登录或执行 'newgrp docker' 使用户组权限生效"
    echo "  2. 之后可以不使用sudo运行docker命令"
    echo "  3. 现在可以部署XRT项目了"
    echo ""
    log_info "下一步："
    echo "  1. 上传XRT项目文件到服务器"
    echo "  2. 运行: cd deploy/docker-compose && ./start.sh"
    echo "  3. 或运行: ./deploy/deploy.sh docker"
}

# 主函数
main() {
    echo "🐳 Docker和Docker Compose自动安装脚本"
    echo "========================================"
    
    # 检查是否为root用户或有sudo权限
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到root用户，建议使用普通用户运行此脚本"
    elif ! sudo -n true 2>/dev/null; then
        log_error "需要sudo权限，请确保当前用户有sudo权限"
        exit 1
    fi
    
    # 检测操作系统
    detect_os
    
    # 检查是否已安装
    DOCKER_INSTALLED=false
    COMPOSE_INSTALLED=false
    
    if check_docker_installed; then
        DOCKER_INSTALLED=true
    fi
    
    if check_compose_installed; then
        COMPOSE_INSTALLED=true
    fi
    
    # 安装Docker
    if [ "$DOCKER_INSTALLED" = false ]; then
        case $OS in
            "Ubuntu"|"Debian"*)
                install_docker_ubuntu
                ;;
            "CentOS"*|"Red Hat"*|"RedHat"*)
                install_docker_centos
                ;;
            *)
                log_error "不支持的操作系统: $OS"
                log_info "请手动安装Docker和Docker Compose"
                exit 1
                ;;
        esac
    fi
    
    # 安装Docker Compose
    if [ "$COMPOSE_INSTALLED" = false ]; then
        install_docker_compose
    fi
    
    # 配置Docker
    configure_docker "$1"
    
    # 验证安装
    verify_installation
    
    # 显示安装后信息
    show_post_install_info
}

# 脚本入口
main "$@"
