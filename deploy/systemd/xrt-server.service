# XRT服务器系统服务配置文件
# 复制到 /etc/systemd/system/xrt-server.service
# 然后执行:
#   sudo systemctl daemon-reload
#   sudo systemctl enable xrt-server
#   sudo systemctl start xrt-server

[Unit]
Description=XRT Server - E-commerce Management System
Documentation=https://github.com/your-repo/xrt
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/opt/xrt
ExecStart=/opt/xrt/xrt-server -c /opt/xrt/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID

# 重启策略
Restart=always
RestartSec=5
StartLimitInterval=0

# 环境变量
Environment=GIN_MODE=release
Environment=TZ=Asia/Shanghai

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/xrt/log /opt/xrt/uploads /opt/xrt/resource

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=xrt-server

[Install]
WantedBy=multi-user.target
