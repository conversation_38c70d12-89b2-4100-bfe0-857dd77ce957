# 🚀 XRT项目快速部署指南

## 📦 部署包已准备完成

您的生产环境部署包已经成功创建在 `deploy-package/` 目录中，包含以下文件：

```
deploy-package/
├── README.md           # 部署说明
├── config.yaml         # 生产环境配置文件
├── dist/               # 前端静态文件
├── resource/           # 后端资源文件
├── start.sh           # 启动脚本
├── stop.sh            # 停止脚本
└── xrt-server         # 后端可执行文件
```

## ⚡ 快速部署步骤

### 1. 上传部署包到服务器

```bash
# 将 deploy-package 文件夹上传到服务器
scp -r deploy-package/ user@your-server:/opt/xrt/
```

### 2. 配置数据库

```bash
# 在服务器上登录MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE xrt CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xrt_user'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON xrt.* TO 'xrt_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 修改配置文件

编辑 `/opt/xrt/config.yaml` 中的数据库连接信息：

```yaml
mysql:
  path: "localhost"
  port: "3306"
  db-name: "xrt"
  username: "xrt_user"
  password: "your_password"
```

### 4. 配置Nginx

复制并修改Nginx配置：

```bash
sudo cp /opt/xrt/../deploy/nginx/xrt.conf /etc/nginx/sites-available/xrt
sudo ln -s /etc/nginx/sites-available/xrt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 5. 配置系统服务

```bash
sudo cp /opt/xrt/../deploy/systemd/xrt-server.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable xrt-server
sudo systemctl start xrt-server
```

### 6. 启动服务

```bash
cd /opt/xrt
./start.sh
```

## 🔧 使用自动化部署脚本

您也可以使用我们提供的自动化部署脚本：

```bash
# 生产环境部署包
./deploy/deploy.sh production

# Docker部署
./deploy/deploy.sh docker

# 本地开发构建
./deploy/deploy.sh local

# 查看帮助
./deploy/deploy.sh help
```

## 🐳 Docker快速部署

如果您的服务器支持Docker，可以使用最简单的Docker部署方式：

```bash
cd deploy/docker-compose
docker-compose up -d
```

这将自动启动：
- 前端服务 (端口8080)
- 后端服务 (端口8888)
- MySQL数据库 (端口13306)
- Redis缓存 (端口16379)

## 📋 部署后检查

### 1. 检查服务状态

```bash
# 检查后端服务
sudo systemctl status xrt-server

# 检查Nginx状态
sudo systemctl status nginx

# 检查端口占用
sudo netstat -tlnp | grep :8888
sudo netstat -tlnp | grep :80
```

### 2. 访问应用

- 前端地址: http://your-domain.com
- 后端API: http://your-domain.com/api
- 默认账号: admin / 123456

### 3. 查看日志

```bash
# 查看后端日志
sudo journalctl -u xrt-server -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/xrt_access.log
sudo tail -f /var/log/nginx/xrt_error.log
```

## 🔒 安全配置建议

1. **修改默认密码**: 首次登录后立即修改admin账号密码
2. **配置SSL证书**: 使用Let's Encrypt配置HTTPS
3. **配置防火墙**: 只开放必要的端口(22, 80, 443)
4. **定期备份**: 设置数据库和文件的定期备份

## 📞 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :8888
   sudo kill -9 PID
   ```

2. **权限问题**
   ```bash
   sudo chown -R www-data:www-data /opt/xrt
   sudo chmod +x /opt/xrt/xrt-server
   ```

3. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证配置文件中的数据库信息

### 性能优化

1. **启用Nginx gzip压缩**
2. **配置静态文件缓存**
3. **调整MySQL缓冲池大小**
4. **监控系统资源使用情况**

## 📚 详细文档

- 完整部署指南: `deploy/production-deploy.md`
- Nginx配置模板: `deploy/nginx/xrt.conf`
- 系统服务配置: `deploy/systemd/xrt-server.service`
- Docker配置: `deploy/docker-compose/`

---

**🎉 恭喜！您的XRT电商管理系统已准备好部署到生产环境！**

如有任何问题，请参考详细文档或联系技术支持。
