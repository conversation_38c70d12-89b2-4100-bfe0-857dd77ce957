#!/bin/bash

# XRT项目自动化部署脚本
# 使用方法: ./deploy.sh [production|staging|docker]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查必要的命令
check_dependencies() {
    log_info "检查依赖..."
    check_command "node"
    check_command "npm"
    check_command "go"
    log_success "依赖检查完成"
}

# 清理旧的构建文件
clean_build() {
    log_info "清理旧的构建文件..."
    rm -rf web/dist
    rm -f server/xrt-server
    rm -rf deploy-package
    log_success "清理完成"
}

# 构建前端
build_frontend() {
    log_info "开始构建前端..."
    cd web
    
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    log_info "构建前端项目..."
    npm run build
    
    if [ ! -d "dist" ]; then
        log_error "前端构建失败"
        exit 1
    fi
    
    cd ..
    log_success "前端构建完成"
}

# 构建后端
build_backend() {
    log_info "开始构建后端..."
    cd server
    
    log_info "下载Go依赖..."
    go mod tidy
    
    log_info "构建后端项目..."
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "-s -w" -o xrt-server .
    
    if [ ! -f "xrt-server" ]; then
        log_error "后端构建失败"
        exit 1
    fi
    
    cd ..
    log_success "后端构建完成"
}

# 创建部署包
create_deploy_package() {
    log_info "创建部署包..."
    
    mkdir -p deploy-package
    
    # 复制前端文件
    cp -r web/dist deploy-package/
    
    # 复制后端文件
    cp server/xrt-server deploy-package/
    chmod +x deploy-package/xrt-server
    
    # 复制配置文件
    if [ "$1" = "production" ]; then
        cp server/config.production.yaml deploy-package/config.yaml
    else
        cp server/config.yaml deploy-package/
    fi
    
    # 复制资源文件
    cp -r server/resource deploy-package/
    
    # 创建启动脚本
    cat > deploy-package/start.sh << 'EOF'
#!/bin/bash
echo "启动XRT服务器..."
./xrt-server -c config.yaml
EOF
    chmod +x deploy-package/start.sh
    
    # 创建停止脚本
    cat > deploy-package/stop.sh << 'EOF'
#!/bin/bash
echo "停止XRT服务器..."
pkill -f xrt-server
EOF
    chmod +x deploy-package/stop.sh
    
    log_success "部署包创建完成: deploy-package/"
}

# 生产环境部署
deploy_production() {
    log_info "开始生产环境部署..."
    
    check_dependencies
    clean_build
    build_frontend
    build_backend
    create_deploy_package "production"
    
    # 创建部署说明
    cat > deploy-package/README.md << 'EOF'
# XRT生产环境部署包

## 部署步骤

1. 将此文件夹上传到服务器 `/opt/xrt/`
2. 配置数据库连接信息在 `config.yaml` 中
3. 配置Nginx反向代理
4. 运行 `./start.sh` 启动服务

## 文件说明

- `xrt-server`: 后端可执行文件
- `dist/`: 前端静态文件
- `config.yaml`: 配置文件
- `resource/`: 资源文件
- `start.sh`: 启动脚本
- `stop.sh`: 停止脚本

## 默认端口

- 后端API: 8888
- 前端需要通过Nginx代理访问

详细部署说明请参考: deploy/production-deploy.md
EOF
    
    log_success "生产环境部署包准备完成！"
    log_info "部署包位置: $(pwd)/deploy-package/"
    log_info "请参考 deploy/production-deploy.md 进行服务器部署"
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 检查Docker
    check_command "docker"
    check_command "docker-compose"
    
    log_info "使用Docker Compose部署..."
    cd deploy/docker-compose
    
    # 停止现有容器
    docker-compose down
    
    # 构建并启动
    docker-compose up -d --build
    
    log_success "Docker部署完成！"
    log_info "访问地址:"
    log_info "  前端: http://localhost:8080"
    log_info "  后端: http://localhost:8888"
    log_info "  MySQL: localhost:13306"
    log_info "  Redis: localhost:16379"
}

# 本地开发部署
deploy_local() {
    log_info "开始本地开发环境部署..."
    
    check_dependencies
    build_frontend
    build_backend
    
    log_success "本地构建完成！"
    log_info "启动方式:"
    log_info "  后端: cd server && ./xrt-server"
    log_info "  前端: cd web && npm run dev"
}

# 显示帮助信息
show_help() {
    echo "XRT项目部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  production  - 生产环境部署包"
    echo "  docker      - Docker容器部署"
    echo "  local       - 本地开发构建"
    echo "  help        - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 production   # 创建生产环境部署包"
    echo "  $0 docker       # 使用Docker部署"
    echo "  $0 local        # 本地开发构建"
}

# 主函数
main() {
    case "$1" in
        "production")
            deploy_production
            ;;
        "docker")
            deploy_docker
            ;;
        "local")
            deploy_local
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            log_warning "请指定部署类型"
            show_help
            exit 1
            ;;
        *)
            log_error "未知的部署类型: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
