# 🚀 XRT项目生产环境部署指南

## 📋 部署前准备

### 1. 服务器环境要求
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 7+)
- **内存**: 最低 2GB，推荐 4GB+
- **硬盘**: 最低 20GB 可用空间
- **网络**: 稳定的互联网连接

### 2. 必需软件
- **Nginx**: 用于前端静态文件服务和反向代理
- **MySQL**: 数据库服务 (5.7+ 或 8.0+)
- **SSL证书**: 用于HTTPS (可选但推荐)

## 📦 方式一：传统部署 (推荐)

### 步骤1: 准备部署文件

在本地项目根目录执行以下命令，已为您准备好：

```bash
# 前端已打包完成，位于: web/dist/
# 后端已编译完成，位于: server/xrt-server

# 创建部署包
mkdir -p deploy-package
cp -r web/dist deploy-package/
cp server/xrt-server deploy-package/
cp server/config.production.yaml deploy-package/config.yaml
cp -r server/resource deploy-package/
```

### 步骤2: 上传到服务器

将 `deploy-package` 文件夹上传到服务器，建议路径：`/opt/xrt/`

```bash
# 在服务器上创建目录
sudo mkdir -p /opt/xrt
sudo chown $USER:$USER /opt/xrt

# 上传文件 (使用scp或其他方式)
scp -r deploy-package/* user@your-server:/opt/xrt/
```

### 步骤3: 配置数据库

```bash
# 登录MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE xrt CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'xrt_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON xrt.* TO 'xrt_user'@'localhost';
FLUSH PRIVILEGES;
```

### 步骤4: 修改配置文件

编辑 `/opt/xrt/config.yaml`：

```yaml
mysql:
  path: "localhost"
  port: "3306"
  config: "charset=utf8mb4&parseTime=True&loc=Local"
  db-name: "xrt"
  username: "xrt_user"
  password: "your_secure_password"

# 修改其他必要配置...
```

### 步骤5: 配置Nginx

创建 `/etc/nginx/sites-available/xrt`：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名
    
    # 前端静态文件
    location / {
        root /opt/xrt/dist;
        try_files $uri $uri/ /index.html;
        index index.html;
    }
    
    # API代理到后端
    location /api {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://127.0.0.1:8888;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 文件上传大小限制
    client_max_body_size 100M;
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/xrt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 步骤6: 创建系统服务

创建 `/etc/systemd/system/xrt-server.service`：

```ini
[Unit]
Description=XRT Server
After=network.target mysql.service

[Service]
Type=simple
User=www-data
WorkingDirectory=/opt/xrt
ExecStart=/opt/xrt/xrt-server -c /opt/xrt/config.yaml
Restart=always
RestartSec=5
Environment=GIN_MODE=release

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable xrt-server
sudo systemctl start xrt-server
sudo systemctl status xrt-server
```

## 🐳 方式二：Docker部署

### 使用Docker Compose (最简单)

```bash
# 在项目根目录执行
cd deploy/docker-compose
docker-compose up -d
```

这将自动启动：
- MySQL数据库 (端口13306)
- Redis缓存 (端口16379)
- 后端服务 (端口8888)
- 前端服务 (端口8080)

### 单独Docker部署

```bash
# 构建镜像
docker build -t xrt-server:latest -f server/Dockerfile server/
docker build -t xrt-web:latest -f web/Dockerfile web/

# 运行容器
docker run -d --name xrt-server -p 8888:8888 xrt-server:latest
docker run -d --name xrt-web -p 8080:8080 xrt-web:latest
```

## 🔧 部署后配置

### 1. 数据库初始化

首次部署需要初始化数据库：

```bash
# 访问初始化页面
http://your-domain.com/init

# 或直接导入现有数据库
mysql -u xrt_user -p xrt < your_database_backup.sql
```

### 2. SSL证书配置 (推荐)

使用Let's Encrypt免费证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 防火墙配置

```bash
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 📊 监控和维护

### 日志查看

```bash
# 查看后端日志
sudo journalctl -u xrt-server -f

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 备份脚本

创建 `/opt/xrt/backup.sh`：

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/opt/xrt/backups"
mkdir -p $BACKUP_DIR

# 数据库备份
mysqldump -u xrt_user -p'your_password' xrt > $BACKUP_DIR/xrt_$DATE.sql

# 文件备份
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /opt/xrt/resource/

# 清理7天前的备份
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
```

### 更新部署

```bash
# 停止服务
sudo systemctl stop xrt-server

# 备份当前版本
cp /opt/xrt/xrt-server /opt/xrt/xrt-server.backup

# 上传新版本
# ... 上传新的可执行文件和静态文件

# 重启服务
sudo systemctl start xrt-server
sudo systemctl reload nginx
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :8888
   sudo kill -9 PID
   ```

2. **权限问题**
   ```bash
   sudo chown -R www-data:www-data /opt/xrt
   sudo chmod +x /opt/xrt/xrt-server
   ```

3. **数据库连接失败**
   - 检查MySQL服务状态
   - 验证配置文件中的数据库信息
   - 确认防火墙设置

### 性能优化

1. **Nginx优化**
   - 启用gzip压缩
   - 配置静态文件缓存
   - 调整worker进程数

2. **数据库优化**
   - 配置合适的缓冲池大小
   - 定期优化表结构
   - 监控慢查询

## 📞 技术支持

如遇到部署问题，请检查：
1. 服务器系统日志
2. 应用程序日志
3. 网络连接状态
4. 配置文件语法

---

**部署完成后访问地址：**
- 前端: http://your-domain.com
- 后端API: http://your-domain.com/api
- 管理后台: http://your-domain.com (默认账号admin/123456)
