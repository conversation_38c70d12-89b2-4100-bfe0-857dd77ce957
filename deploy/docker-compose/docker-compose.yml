version: '3.8'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:8.0
    container_name: xrt-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: Asdf1357
      MYSQL_DATABASE: xrt
      MYSQL_USER: xrt_user
      MYSQL_PASSWORD: xrt_password
      TZ: Asia/Shanghai
    ports:
      - "13306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
      - ./mysql/conf:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - xrt-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: xrt-redis
    restart: always
    ports:
      - "16379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass "redis_password"
    networks:
      - xrt-network

  # 后端API服务
  xrt-server:
    build:
      context: ../../server
      dockerfile: Dockerfile
    container_name: xrt-server
    restart: always
    ports:
      - "8888:8888"
    environment:
      - GIN_MODE=release
      - TZ=Asia/Shanghai
    volumes:
      - ./config/config.docker.yaml:/app/config.yaml
      - server_uploads:/app/uploads
      - server_logs:/app/log
    depends_on:
      - mysql
      - redis
    networks:
      - xrt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  xrt-web:
    build:
      context: ../../web
      dockerfile: Dockerfile
    container_name: xrt-web
    restart: always
    ports:
      - "8080:80"
    depends_on:
      - xrt-server
    networks:
      - xrt-network

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: xrt-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - xrt-web
      - xrt-server
    networks:
      - xrt-network

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  server_uploads:
    driver: local
  server_logs:
    driver: local
  nginx_logs:
    driver: local

# 网络
networks:
  xrt-network:
    driver: bridge
