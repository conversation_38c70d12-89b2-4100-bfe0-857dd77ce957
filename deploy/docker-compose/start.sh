#!/bin/bash

# XRT Docker Compose 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未启动，请启动Docker服务"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p mysql/init
    mkdir -p mysql/conf
    mkdir -p nginx/conf.d
    mkdir -p ssl
    mkdir -p config
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
    
    if [ ! -f "config/config.docker.yaml" ]; then
        log_warning "config.docker.yaml文件不存在，将使用默认配置"
    fi
    
    log_success "配置文件检查完成"
}

# 停止现有容器
stop_existing() {
    log_info "停止现有容器..."
    docker-compose down --remove-orphans
    log_success "现有容器已停止"
}

# 构建和启动服务
start_services() {
    log_info "构建和启动服务..."
    
    # 拉取最新镜像
    docker-compose pull
    
    # 构建自定义镜像
    docker-compose build --no-cache
    
    # 启动服务
    docker-compose up -d
    
    log_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待MySQL就绪
    log_info "等待MySQL服务..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            log_success "MySQL服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL服务启动超时"
        exit 1
    fi
    
    # 等待后端服务
    log_info "等待后端服务..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8888/health &> /dev/null; then
            log_success "后端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "后端服务可能未完全就绪，请检查日志"
    fi
    
    # 等待前端服务
    log_info "等待前端服务..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8080 &> /dev/null; then
            log_success "前端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "前端服务可能未完全就绪，请检查日志"
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态："
    docker-compose ps
    
    echo ""
    log_info "访问地址："
    echo "  前端: http://localhost:8080"
    echo "  后端API: http://localhost:8888"
    echo "  MySQL: localhost:13306"
    echo "  Redis: localhost:16379"
    
    echo ""
    log_info "默认账号："
    echo "  用户名: admin"
    echo "  密码: 123456"
}

# 显示日志
show_logs() {
    log_info "查看服务日志："
    echo "  查看所有日志: docker-compose logs -f"
    echo "  查看后端日志: docker-compose logs -f xrt-server"
    echo "  查看前端日志: docker-compose logs -f xrt-web"
    echo "  查看MySQL日志: docker-compose logs -f mysql"
}

# 主函数
main() {
    echo "🐳 XRT Docker Compose 启动脚本"
    echo "================================"
    
    check_docker
    create_directories
    check_config
    stop_existing
    start_services
    wait_for_services
    show_status
    show_logs
    
    echo ""
    log_success "🎉 XRT系统启动完成！"
}

# 脚本入口
main "$@"
