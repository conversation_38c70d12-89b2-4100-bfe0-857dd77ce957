# XRT Docker环境配置文件

# jwt配置
jwt:
  signing-key: 'qmPlus'
  expires-time: 7d
  buffer-time: 1d
  issuer: 'qmPlus'

# zap logger配置
zap:
  level: 'info'
  format: 'console'
  prefix: '[gin-vue-admin]'
  director: 'log'
  show-line: true
  encode-level: 'LowercaseColorLevelEncoder'
  stacktrace-key: 'stacktrace'
  log-in-console: true

# redis配置
redis:
  db: 0
  addr: 'redis:6379'
  password: 'redis_password'

# email配置
email:
  to: '<EMAIL>'
  port: 587
  from: '<EMAIL>'
  host: 'smtp.163.com'
  is-ssl: false
  secret: 'xxx'
  nickname: 'test'

# casbin配置
casbin:
  model-path: './resource/rbac_model.conf'

# 系统配置
system:
  env: 'docker'
  addr: 8888
  db-type: 'mysql'
  oss-type: 'local'
  use-multipoint: false
  use-redis: true
  iplimit-count: 15000
  iplimit-time: 3600

# captcha配置
captcha:
  key-long: 6
  img-width: 240
  img-height: 80
  open-captcha: 0
  open-captcha-timeout: 3600

# mysql配置
mysql:
  path: 'mysql:3306'
  port: '3306'
  config: 'charset=utf8mb4&parseTime=True&loc=Local'
  db-name: 'xrt'
  username: 'xrt_user'
  password: 'xrt_password'
  prefix: ''
  singular: false
  engine: ''
  max-idle-conns: 10
  max-open-conns: 100
  log-mode: 'error'
  log-zap: false

# local configuration
local:
  path: 'uploads/file'
  store-path: 'uploads/file'

# autocode configuration
autocode:
  transfer-restart: true
  root: ""
  server: /server
  server-plug: /plugin/%s
  server-api: /api/v1/%s
  server-initialize: /initialize
  server-model: /model/%s
  server-request: /model/%s/request/
  server-router: /router/%s
  server-service: /service/%s
  web: /web/src
  web-api: /api
  web-form: /view
  web-table: /view

# Timer task db clear table
Timer:
  start: true
  spec: "@daily"
  detail:
    tableName: "sys_operation_records"
    compareField: "created_at"
    interval: "2160h"

# 跨域配置
cors:
  mode: 'strict-whitelist'
  whitelist:
    - allow-origin: 'http://localhost:8080'
      allow-headers: 'Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id'
      allow-methods: 'POST, GET, OPTIONS, PUT, DELETE'
      expose-headers: 'Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type'
      allow-credentials: true
    - allow-origin: 'http://127.0.0.1:8080'
      allow-headers: 'Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id'
      allow-methods: 'POST, GET, OPTIONS, PUT, DELETE'
      expose-headers: 'Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type'
      allow-credentials: true
