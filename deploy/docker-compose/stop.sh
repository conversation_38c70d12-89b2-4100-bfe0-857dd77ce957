#!/bin/bash

# XRT Docker Compose 停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止XRT服务..."
    
    if [ -f "docker-compose.yml" ]; then
        docker-compose down
        log_success "服务已停止"
    else
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi
}

# 清理容器和网络
cleanup() {
    log_info "清理容器和网络..."
    
    # 删除停止的容器
    docker container prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    log_success "清理完成"
}

# 显示状态
show_status() {
    log_info "当前Docker状态："
    docker ps -a --filter "name=xrt"
}

# 主函数
main() {
    echo "🛑 XRT Docker Compose 停止脚本"
    echo "================================"
    
    case "${1:-stop}" in
        "stop")
            stop_services
            show_status
            ;;
        "clean")
            stop_services
            cleanup
            show_status
            ;;
        "remove")
            log_warning "这将删除所有容器、镜像和数据卷！"
            read -p "确认删除？(y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker-compose down -v --rmi all
                log_success "所有资源已删除"
            else
                log_info "操作已取消"
            fi
            ;;
        *)
            echo "用法: $0 [stop|clean|remove]"
            echo "  stop  - 停止服务（默认）"
            echo "  clean - 停止服务并清理容器和网络"
            echo "  remove - 停止服务并删除所有相关资源（包括数据）"
            exit 1
            ;;
    esac
    
    log_success "操作完成"
}

# 脚本入口
main "$@"
