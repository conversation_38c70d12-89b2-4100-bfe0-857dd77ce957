# 🐳 XRT Docker Compose 部署指南

## 📋 系统要求

### 服务器配置
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 20GB以上可用空间
- **操作系统**: Ubuntu 18.04+, CentOS 7+, 或其他支持Docker的Linux发行版

### 软件要求
- Docker 20.10+
- Docker Compose 1.29+

## 🚀 快速开始

### 1. 安装Docker和Docker Compose

#### Ubuntu/Debian系统
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
newgrp docker
```

#### CentOS/RHEL系统
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce docker-ce-cli containerd.io

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
newgrp docker
```

### 2. 验证安装
```bash
docker --version
docker-compose --version
docker run hello-world
```

### 3. 部署XRT系统

#### 方法一：使用自动化脚本（推荐）
```bash
# 上传项目文件到服务器
scp -r deploy/docker-compose/ user@your-server:/opt/xrt-docker/

# 登录服务器
ssh user@your-server

# 进入部署目录
cd /opt/xrt-docker

# 运行启动脚本
./start.sh
```

#### 方法二：手动部署
```bash
# 克隆或上传项目文件
cd /opt/xrt-docker

# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📁 目录结构

```
deploy/docker-compose/
├── docker-compose.yml      # Docker Compose配置文件
├── start.sh               # 启动脚本
├── stop.sh                # 停止脚本
├── config/
│   └── config.docker.yaml # 后端配置文件
├── mysql/
│   ├── init/              # MySQL初始化脚本
│   └── conf/              # MySQL配置文件
└── nginx/
    └── conf.d/            # Nginx配置文件
```

## 🔧 配置说明

### 端口映射
- **前端**: 8080 → 80
- **后端**: 8888 → 8888
- **MySQL**: 13306 → 3306
- **Redis**: 16379 → 6379
- **Nginx**: 80 → 80, 443 → 443

### 默认密码
- **MySQL Root**: Asdf1357
- **MySQL User**: xrt_user / xrt_password
- **Redis**: redis_password
- **系统管理员**: admin / 123456

### 数据持久化
所有重要数据都通过Docker卷进行持久化：
- `mysql_data`: MySQL数据
- `redis_data`: Redis数据
- `server_uploads`: 文件上传
- `server_logs`: 应用日志

## 🛠️ 管理命令

### 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d mysql redis
```

### 停止服务
```bash
# 停止所有服务
docker-compose down

# 停止特定服务
docker-compose stop xrt-server
```

### 查看状态
```bash
# 查看服务状态
docker-compose ps

# 查看资源使用情况
docker stats
```

### 查看日志
```bash
# 查看所有日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f xrt-server
docker-compose logs -f mysql
```

### 进入容器
```bash
# 进入后端容器
docker-compose exec xrt-server sh

# 进入MySQL容器
docker-compose exec mysql mysql -u root -p
```

### 备份和恢复
```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -pAsdf1357 xrt > backup.sql

# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -pAsdf1357 xrt < backup.sql
```

## 🔒 安全配置

### 1. 修改默认密码
```bash
# 修改MySQL密码
docker-compose exec mysql mysql -u root -p
ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';

# 修改Redis密码
# 编辑docker-compose.yml中的redis配置
```

### 2. 配置防火墙
```bash
# Ubuntu/Debian
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=22/tcp
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

### 3. 配置SSL证书
```bash
# 使用Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/
```

## 📊 监控和维护

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:8080/health
curl http://localhost:8888/health
```

### 性能监控
```bash
# 查看容器资源使用
docker stats

# 查看系统资源
htop
df -h
```

### 日志轮转
```bash
# 配置Docker日志轮转
sudo tee /etc/docker/daemon.json <<EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

sudo systemctl restart docker
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   sudo lsof -i :8080
   sudo kill -9 PID
   ```

2. **容器启动失败**
   ```bash
   docker-compose logs service-name
   docker-compose restart service-name
   ```

3. **数据库连接失败**
   ```bash
   docker-compose exec mysql mysql -u root -p
   docker-compose restart mysql
   ```

4. **磁盘空间不足**
   ```bash
   docker system prune -a
   docker volume prune
   ```

### 性能优化

1. **调整MySQL配置**
   - 编辑 `mysql/conf/my.cnf`
   - 根据服务器内存调整缓冲池大小

2. **优化Nginx配置**
   - 启用gzip压缩
   - 配置静态文件缓存
   - 调整worker进程数

3. **监控资源使用**
   - 定期检查CPU、内存、磁盘使用情况
   - 设置告警阈值

## 📞 技术支持

如遇到问题，请：
1. 查看服务日志：`docker-compose logs -f`
2. 检查服务状态：`docker-compose ps`
3. 参考故障排除章节
4. 联系技术支持团队

---

**🎉 恭喜！您的XRT系统已成功部署在Docker环境中！**
