-- XRT数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `xrt` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `xrt`;

-- 这里可以添加初始化表结构的SQL
-- 如果您有现有的数据库备份，可以在这里导入

-- 示例：创建一个基础的用户表（如果需要）
-- CREATE TABLE IF NOT EXISTS `users` (
--   `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
--   `created_at` datetime(3) DEFAULT NULL,
--   `updated_at` datetime(3) DEFAULT NULL,
--   `deleted_at` datetime(3) DEFAULT NULL,
--   `uuid` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   `username` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   `password` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   `nick_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'QMPlusUser',
--   `side_mode` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'dark',
--   `header_img` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT 'https://qmplusimg.henrongyi.top/gva_header.jpg',
--   `base_color` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '#fff',
--   `active_color` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT '#1890ff',
--   `authority_id` bigint(20) unsigned DEFAULT '888',
--   `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   `enable` bigint(20) DEFAULT '1',
--   PRIMARY KEY (`id`),
--   KEY `idx_users_deleted_at` (`deleted_at`),
--   KEY `idx_users_uuid` (`uuid`),
--   KEY `idx_users_username` (`username`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设置时区
SET time_zone = '+08:00';

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;
