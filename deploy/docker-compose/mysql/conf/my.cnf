[mysqld]
# 基本设置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 时区设置
default-time-zone='+08:00'

# 连接设置
max_connections=200
max_connect_errors=10

# 缓存设置
innodb_buffer_pool_size=256M
innodb_log_file_size=64M
innodb_log_buffer_size=16M

# 查询缓存
query_cache_type=1
query_cache_size=32M

# 慢查询日志
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=2

# 二进制日志
log-bin=mysql-bin
binlog_format=ROW
expire_logs_days=7

# 安全设置
sql_mode=STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
