# XRT项目Nginx配置文件
# 复制到 /etc/nginx/sites-available/xrt
# 然后执行: sudo ln -s /etc/nginx/sites-available/xrt /etc/nginx/sites-enabled/

# HTTP服务器配置
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # 请替换为您的域名
    
    # 重定向到HTTPS (如果使用SSL)
    # return 301 https://$server_name$request_uri;
    
    # 如果不使用HTTPS，请注释掉上面的重定向，启用下面的配置
    
    # 设置根目录
    root /opt/xrt/dist;
    index index.html index.htm;
    
    # 前端路由支持 (SPA应用)
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # API代理到后端
    location /api {
        # 移除/api前缀
        rewrite ^/api/(.*)$ /$1 break;
        
        # 代理到后端服务
        proxy_pass http://127.0.0.1:8888;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 文件上传接口特殊处理
    location /api/fileUploadAndDownload {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://127.0.0.1:8888;
        
        # 文件上传大小限制
        client_max_body_size 100M;
        client_body_timeout 120s;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 日志配置
    access_log /var/log/nginx/xrt_access.log;
    error_log /var/log/nginx/xrt_error.log;
}

# HTTPS服务器配置 (如果使用SSL证书)
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;  # 请替换为您的域名
    
    # SSL证书配置 (请替换为您的证书路径)
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # HSTS
    add_header Strict-Transport-Security "max-age=31536000" always;
    
    # 设置根目录
    root /opt/xrt/dist;
    index index.html index.htm;
    
    # 前端路由支持 (SPA应用)
    location / {
        try_files $uri $uri/ /index.html;
        
        # 静态文件缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            access_log off;
        }
    }
    
    # API代理到后端
    location /api {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://127.0.0.1:8888;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        proxy_buffering on;
        proxy_buffer_size 8k;
        proxy_buffers 8 8k;
    }
    
    # 文件上传接口特殊处理
    location /api/fileUploadAndDownload {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass http://127.0.0.1:8888;
        
        client_max_body_size 100M;
        client_body_timeout 120s;
        
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    server_tokens off;
    
    # 日志配置
    access_log /var/log/nginx/xrt_ssl_access.log;
    error_log /var/log/nginx/xrt_ssl_error.log;
}
