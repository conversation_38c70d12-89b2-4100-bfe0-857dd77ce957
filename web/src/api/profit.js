import service from '@/utils/request'

// @Tags OrderProfit
// @Summary 分页获取利润统计列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.OrderProfitSearch true "分页获取利润统计列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /profit/getProfitList [get]
export const getProfitList = (params) => {
  return service({
    url: '/profit/getProfitList',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 获取利润汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProfitAnalysisRequest true "获取利润汇总统计"
// @Success 200 {object} response.Response{data=order.ProfitSummary,msg=string} "获取成功"
// @Router /profit/getProfitSummary [get]
export const getProfitSummary = (params) => {
  return service({
    url: '/profit/getProfitSummary',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 按店铺统计利润
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProfitAnalysisRequest true "按店铺统计利润"
// @Success 200 {object} response.Response{data=[]order.ProfitByShop,msg=string} "获取成功"
// @Router /profit/getProfitByShop [get]
export const getProfitByShop = (params) => {
  return service({
    url: '/profit/getProfitByShop',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 计算订单利润
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProfitCalculateRequest true "计算订单利润"
// @Success 200 {object} response.Response{msg=string} "计算成功"
// @Router /profit/calculateProfit [post]
export const calculateOrderProfit = (data) => {
  return service({
    url: '/profit/calculateProfit',
    method: 'post',
    data
  })
}

// @Tags OrderProfit
// @Summary 获取月度利润统计列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.MonthlyProfitSearch true "获取月度利润统计列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /profit/getMonthlyProfitList [get]
export const getMonthlyProfitList = (params) => {
  return service({
    url: '/profit/getMonthlyProfitList',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 设置月度广告费用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.MonthlyAdvertisingRequest true "设置月度广告费用"
// @Success 200 {object} response.Response{msg=string} "设置成功"
// @Router /profit/setMonthlyAdvertising [post]
export const setMonthlyAdvertising = (data) => {
  return service({
    url: '/profit/setMonthlyAdvertising',
    method: 'post',
    data
  })
}

// @Tags OrderProfit
// @Summary 获取月度广告费用
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param month query string true "月份(YYYY-MM)"
// @Success 200 {object} response.Response{data=order.MonthlyAdvertising,msg=string} "获取成功"
// @Router /profit/getMonthlyAdvertising [get]
export const getMonthlyAdvertising = (params) => {
  return service({
    url: '/profit/getMonthlyAdvertising',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 刷新利润统计数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "刷新成功"
// @Router /profit/refreshProfitData [post]
export const refreshProfitData = () => {
  return service({
    url: '/profit/refreshProfitData',
    method: 'post'
  })
}

// @Tags OrderProfit
// @Summary 删除所有利润数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /profit/deleteAllProfitData [delete]
export const deleteAllProfitData = () => {
  return service({
    url: '/profit/deleteAllProfitData',
    method: 'delete'
  })
}

// @Tags OrderProfit
// @Summary 按货币分组获取利润汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProfitAnalysisRequest true "按货币分组获取利润汇总统计"
// @Success 200 {object} response.Response{data=[]order.ProfitSummaryByCurrency,msg=string} "获取成功"
// @Router /profit/getProfitSummaryByCurrency [get]
export const getProfitSummaryByCurrency = (params) => {
  return service({
    url: '/profit/getProfitSummaryByCurrency',
    method: 'get',
    params
  })
}

// @Tags OrderProfit
// @Summary 导出月度订单详细数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/octet-stream
// @Param month query string true "月份(YYYY-MM)"
// @Success 200 {file} file "Excel文件"
// @Router /profit/exportMonthlyOrdersDetail [get]
export const exportMonthlyOrdersDetail = (month) => {
  return service({
    url: '/profit/exportMonthlyOrdersDetail',
    method: 'get',
    params: { month },
    responseType: 'blob'
  })
}
