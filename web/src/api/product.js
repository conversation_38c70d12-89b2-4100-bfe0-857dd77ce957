import service from '@/utils/request'

// @Tags Product
// @Summary 创建产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.CreateProductRequest true "创建产品"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /product/createProduct [post]
export const createProduct = (data) => {
  return service({
    url: '/product/createProduct',
    method: 'post',
    data
  })
}

// @Tags Product
// @Summary 删除产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.DeleteProductRequest true "删除产品"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /product/deleteProduct [delete]
export const deleteProduct = (params) => {
  return service({
    url: '/product/deleteProduct',
    method: 'delete',
    params
  })
}

// @Tags Product
// @Summary 批量删除产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /product/deleteProductByIds [delete]
export const deleteProductByIds = (params) => {
  return service({
    url: '/product/deleteProductByIds',
    method: 'delete',
    params
  })
}

// @Tags Product
// @Summary 更新产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.UpdateProductRequest true "更新产品"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /product/updateProduct [put]
export const updateProduct = (data) => {
  return service({
    url: '/product/updateProduct',
    method: 'put',
    data
  })
}

// @Tags Product
// @Summary 用id查询产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.GetProductRequest true "用id查询产品"
// @Success 200 {object} response.Response{data=order.Product,msg=string} "查询成功"
// @Router /product/findProduct [get]
export const findProduct = (params) => {
  return service({
    url: '/product/findProduct',
    method: 'get',
    params
  })
}

// @Tags Product
// @Summary 分页获取产品列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.ProductSearch true "分页获取产品列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /product/getProductList [get]
export const getProductList = (params) => {
  return service({
    url: '/product/getProductList',
    method: 'get',
    params
  })
}

// @Tags Product
// @Summary 获取产品汇总统计
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=order.ProductSummary,msg=string} "获取成功"
// @Router /product/getProductSummary [get]
export const getProductSummary = (params) => {
  return service({
    url: '/product/getProductSummary',
    method: 'get',
    params
  })
}

// @Tags Product
// @Summary 根据SKU获取产品信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param sku query string true "SKU"
// @Success 200 {object} response.Response{data=order.Product,msg=string} "获取成功"
// @Router /product/getProductBySKU [get]
export const getProductBySKU = (params) => {
  return service({
    url: '/product/getProductBySKU',
    method: 'get',
    params
  })
}

// @Tags Product
// @Summary 批量更新产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.BatchUpdateProductRequest true "批量更新产品"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /product/batchUpdateProduct [put]
export const batchUpdateProduct = (data) => {
  return service({
    url: '/product/batchUpdateProduct',
    method: 'put',
    data
  })
}

// @Tags Product
// @Summary 从订单同步产品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SyncProductsFromOrdersRequest true "从订单同步产品"
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "同步成功"
// @Router /product/syncProductsFromOrders [post]
export const syncProductsFromOrders = (data) => {
  return service({
    url: '/product/syncProductsFromOrders',
    method: 'post',
    data
  })
}

// @Tags Product
// @Summary 从订单同步产品图片
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "同步成功"
// @Router /product/syncProductImagesFromOrders [post]
export const syncProductImagesFromOrders = () => {
  return service({
    url: '/product/syncProductImagesFromOrders',
    method: 'post'
  })
}


