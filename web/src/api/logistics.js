import service from '@/utils/request'

// @Tags Logistics
// @Summary 创建物流方式
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Logistics true "创建物流方式"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /logistics/createLogistics [post]
export const createLogistics = (data) => {
  return service({
    url: '/logistics/createLogistics',
    method: 'post',
    data
  })
}

// @Tags Logistics
// @Summary 删除物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Logistics true "删除物流信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /logistics/deleteLogistics [delete]
export const deleteLogistics = (params) => {
  return service({
    url: '/logistics/deleteLogistics',
    method: 'delete',
    params
  })
}

// @Tags Logistics
// @Summary 批量删除物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除物流信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /logistics/deleteLogisticsByIds [delete]
export const deleteLogisticsByIds = (params) => {
  return service({
    url: '/logistics/deleteLogisticsByIds',
    method: 'delete',
    params
  })
}

// @Tags Logistics
// @Summary 更新物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Logistics true "更新物流信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /logistics/updateLogistics [put]
export const updateLogistics = (data) => {
  return service({
    url: '/logistics/updateLogistics',
    method: 'put',
    data
  })
}

// @Tags Logistics
// @Summary 用id查询物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Logistics true "用id查询物流信息"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /logistics/findLogistics [get]
export const findLogistics = (params) => {
  return service({
    url: '/logistics/findLogistics',
    method: 'get',
    params
  })
}

// @Tags Logistics
// @Summary 分页获取物流信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取物流信息列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /logistics/getLogisticsList [get]
export const getLogisticsList = (params) => {
  return service({
    url: '/logistics/getLogisticsList',
    method: 'get',
    params
  })
}



// @Tags Logistics
// @Summary 获取物流统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /logistics/getLogisticsSummary [get]
export const getLogisticsSummary = () => {
  return service({
    url: '/logistics/getLogisticsSummary',
    method: 'get'
  })
}

// @Tags Logistics
// @Summary 同步Ozon物流信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"同步成功"}"
// @Router /logistics/syncOzonLogistics [post]
export const syncOzonLogistics = () => {
  return service({
    url: '/logistics/syncOzonLogistics',
    method: 'post'
  })
}

// @Tags Logistics
// @Summary 获取店铺仓库和物流方式层级数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /logistics/getShopWarehouseLogistics [get]
export const getShopWarehouseLogistics = () => {
  return service({
    url: '/logistics/getShopWarehouseLogistics',
    method: 'get'
  })
}
