import service from '@/utils/request'
// @Tags Order
// @Summary 创建订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Order true "创建订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /od/createOrder [post]
export const createOrder = (data) => {
  return service({
    url: '/od/createOrder',
    method: 'post',
    data
  })
}

// @Tags Order
// @Summary 删除订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Order true "删除订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /od/deleteOrder [delete]
export const deleteOrder = (params) => {
  return service({
    url: '/od/deleteOrder',
    method: 'delete',
    params
  })
}

// @Tags Order
// @Summary 批量删除订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /od/deleteOrder [delete]
export const deleteOrderByIds = (params) => {
  return service({
    url: '/od/deleteOrderByIds',
    method: 'delete',
    params
  })
}

// @Tags Order
// @Summary 更新订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Order true "更新订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /od/updateOrder [put]
export const updateOrder = (data) => {
  return service({
    url: '/od/updateOrder',
    method: 'put',
    data
  })
}

// @Tags Order
// @Summary 用id查询订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Order true "用id查询订单"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /od/findOrder [get]
export const findOrder = (params) => {
  return service({
    url: '/od/findOrder',
    method: 'get',
    params
  })
}

// @Tags Order
// @Summary 分页获取订单列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取订单列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /od/getOrderList [get]
export const getOrderList = (params) => {
  return service({
    url: '/od/getOrderList',
    method: 'get',
    params
  })
}

// @Tags Order
// @Summary 不需要鉴权的订单接口
// @Accept application/json
// @Produce application/json
// @Param data query orderReq.OrderSearch true "分页获取订单列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /od/getOrderPublic [get]
export const getOrderPublic = () => {
  return service({
    url: '/od/getOrderPublic',
    method: 'get',
  })
}

// @Tags Order
// @Summary 拉取订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "拉取成功"
// @Router /od/pullOrders [post]
export const pullOrders = () => {
  return service({
    url: '/od/pullOrders',
    method: 'post',
  })
}

// @Tags Order
// @Summary 拉取今年订单
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "拉取成功"
// @Router /od/pullYearOrders [post]
export const pullYearOrders = () => {
  return service({
    url: '/od/pullYearOrders',
    method: 'post',
  })
}
