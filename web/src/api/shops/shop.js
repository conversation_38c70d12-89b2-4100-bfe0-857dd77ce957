import service from '@/utils/request'
// @Tags Shop
// @Summary 创建商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Shop true "创建商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /shop/createShop [post]
export const createShop = (data) => {
  return service({
    url: '/shop/createShop',
    method: 'post',
    data
  })
}

// @Tags Shop
// @Summary 删除商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Shop true "删除商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /shop/deleteShop [delete]
export const deleteShop = (params) => {
  return service({
    url: '/shop/deleteShop',
    method: 'delete',
    params
  })
}

// @Tags Shop
// @Summary 批量删除商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /shop/deleteShop [delete]
export const deleteShopByIds = (params) => {
  return service({
    url: '/shop/deleteShopByIds',
    method: 'delete',
    params
  })
}

// @Tags Shop
// @Summary 更新商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Shop true "更新商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /shop/updateShop [put]
export const updateShop = (data) => {
  return service({
    url: '/shop/updateShop',
    method: 'put',
    data
  })
}

// @Tags Shop
// @Summary 用id查询商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Shop true "用id查询商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /shop/findShop [get]
export const findShop = (params) => {
  return service({
    url: '/shop/findShop',
    method: 'get',
    params
  })
}

// @Tags Shop
// @Summary 分页获取商店列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取商店列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /shop/getShopList [get]
export const getShopList = (params) => {
  return service({
    url: '/shop/getShopList',
    method: 'get',
    params
  })
}

// @Tags Shop
// @Summary 不需要鉴权的商店接口
// @Accept application/json
// @Produce application/json
// @Param data query shopsReq.ShopSearch true "分页获取商店列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /shop/getShopPublic [get]
export const getShopPublic = () => {
  return service({
    url: '/shop/getShopPublic',
    method: 'get',
  })
}
