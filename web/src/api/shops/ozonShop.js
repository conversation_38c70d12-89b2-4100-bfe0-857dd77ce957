import service from '@/utils/request'
// @Tags OzonShop
// @Summary 创建店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "创建店铺授权"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /ozonShop/createOzonShop [post]
export const createOzonShop = (data) => {
  return service({
    url: '/ozonShop/createOzonShop',
    method: 'post',
    data
  })
}

// @Tags OzonShop
// @Summary 删除店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "删除店铺授权"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ozonShop/deleteOzonShop [delete]
export const deleteOzonShop = (params) => {
  return service({
    url: '/ozonShop/deleteOzonShop',
    method: 'delete',
    params
  })
}

// @Tags OzonShop
// @Summary 批量删除店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除店铺授权"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ozonShop/deleteOzonShop [delete]
export const deleteOzonShopByIds = (params) => {
  return service({
    url: '/ozonShop/deleteOzonShopByIds',
    method: 'delete',
    params
  })
}

// @Tags OzonShop
// @Summary 更新店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "更新店铺授权"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /ozonShop/updateOzonShop [put]
export const updateOzonShop = (data) => {
  return service({
    url: '/ozonShop/updateOzonShop',
    method: 'put',
    data
  })
}

// @Tags OzonShop
// @Summary 用id查询店铺授权
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.OzonShop true "用id查询店铺授权"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /ozonShop/findOzonShop [get]
export const findOzonShop = (params) => {
  return service({
    url: '/ozonShop/findOzonShop',
    method: 'get',
    params
  })
}

// @Tags OzonShop
// @Summary 分页获取店铺授权列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取店铺授权列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ozonShop/getOzonShopList [get]
export const getOzonShopList = (params) => {
  return service({
    url: '/ozonShop/getOzonShopList',
    method: 'get',
    params
  })
}

// @Tags OzonShop
// @Summary 不需要鉴权的店铺授权接口
// @Accept application/json
// @Produce application/json
// @Param data query shopsReq.OzonShopSearch true "分页获取店铺授权列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /ozonShop/getOzonShopPublic [get]
export const getOzonShopPublic = () => {
  return service({
    url: '/ozonShop/getOzonShopPublic',
    method: 'get',
  })
}
