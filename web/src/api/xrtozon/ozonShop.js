import service from '@/utils/request'
// @Tags OzonShop
// @Summary 创建ozon商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "创建ozon商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /oShop/createOzonShop [post]
export const createOzonShop = (data) => {
  return service({
    url: '/oShop/createOzonShop',
    method: 'post',
    data
  })
}

// @Tags OzonShop
// @Summary 删除ozon商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "删除ozon商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /oShop/deleteOzonShop [delete]
export const deleteOzonShop = (params) => {
  return service({
    url: '/oShop/deleteOzonShop',
    method: 'delete',
    params
  })
}

// @Tags OzonShop
// @Summary 批量删除ozon商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ozon商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /oShop/deleteOzonShop [delete]
export const deleteOzonShopByIds = (params) => {
  return service({
    url: '/oShop/deleteOzonShopByIds',
    method: 'delete',
    params
  })
}

// @Tags OzonShop
// @Summary 更新ozon商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzonShop true "更新ozon商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /oShop/updateOzonShop [put]
export const updateOzonShop = (data) => {
  return service({
    url: '/oShop/updateOzonShop',
    method: 'put',
    data
  })
}

// @Tags OzonShop
// @Summary 用id查询ozon商店
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.OzonShop true "用id查询ozon商店"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /oShop/findOzonShop [get]
export const findOzonShop = (params) => {
  return service({
    url: '/oShop/findOzonShop',
    method: 'get',
    params
  })
}

// @Tags OzonShop
// @Summary 分页获取ozon商店列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ozon商店列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /oShop/getOzonShopList [get]
export const getOzonShopList = (params) => {
  return service({
    url: '/oShop/getOzonShopList',
    method: 'get',
    params
  })
}

// @Tags OzonShop
// @Summary 不需要鉴权的ozon商店接口
// @Accept application/json
// @Produce application/json
// @Param data query xrtozonReq.OzonShopSearch true "分页获取ozon商店列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /oShop/getOzonShopPublic [get]
export const getOzonShopPublic = () => {
  return service({
    url: '/oShop/getOzonShopPublic',
    method: 'get',
  })
}
