import service from '@/utils/request'
// @Tags OzoneOrderDetail
// @Summary 创建ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzoneOrderDetail true "创建ozone订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /ozoneOrderDetail/createOzoneOrderDetail [post]
export const createOzoneOrderDetail = (data) => {
  return service({
    url: '/ozoneOrderDetail/createOzoneOrderDetail',
    method: 'post',
    data
  })
}

// @Tags OzoneOrderDetail
// @Summary 删除ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzoneOrderDetail true "删除ozone订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ozoneOrderDetail/deleteOzoneOrderDetail [delete]
export const deleteOzoneOrderDetail = (params) => {
  return service({
    url: '/ozoneOrderDetail/deleteOzoneOrderDetail',
    method: 'delete',
    params
  })
}

// @Tags OzoneOrderDetail
// @Summary 批量删除ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ozone订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /ozoneOrderDetail/deleteOzoneOrderDetail [delete]
export const deleteOzoneOrderDetailByIds = (params) => {
  return service({
    url: '/ozoneOrderDetail/deleteOzoneOrderDetailByIds',
    method: 'delete',
    params
  })
}

// @Tags OzoneOrderDetail
// @Summary 更新ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.OzoneOrderDetail true "更新ozone订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /ozoneOrderDetail/updateOzoneOrderDetail [put]
export const updateOzoneOrderDetail = (data) => {
  return service({
    url: '/ozoneOrderDetail/updateOzoneOrderDetail',
    method: 'put',
    data
  })
}

// @Tags OzoneOrderDetail
// @Summary 用id查询ozone订单详情
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.OzoneOrderDetail true "用id查询ozone订单详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /ozoneOrderDetail/findOzoneOrderDetail [get]
export const findOzoneOrderDetail = (params) => {
  return service({
    url: '/ozoneOrderDetail/findOzoneOrderDetail',
    method: 'get',
    params
  })
}

// @Tags OzoneOrderDetail
// @Summary 分页获取ozone订单详情列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ozone订单详情列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /ozoneOrderDetail/getOzoneOrderDetailList [get]
export const getOzoneOrderDetailList = (params) => {
  return service({
    url: '/ozoneOrderDetail/getOzoneOrderDetailList',
    method: 'get',
    params
  })
}

// @Tags OzoneOrderDetail
// @Summary 不需要鉴权的ozone订单详情接口
// @Accept application/json
// @Produce application/json
// @Param data query ozoneOrderReq.OzoneOrderDetailSearch true "分页获取ozone订单详情列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /ozoneOrderDetail/getOzoneOrderDetailPublic [get]
export const getOzoneOrderDetailPublic = () => {
  return service({
    url: '/ozoneOrderDetail/getOzoneOrderDetailPublic',
    method: 'get',
  })
}
