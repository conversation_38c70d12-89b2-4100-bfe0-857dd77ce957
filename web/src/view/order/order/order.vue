
<template>
  <div class="order-container">
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="货件号" prop="postingNumber">
         <el-input v-model="searchInfo.postingNumber" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="订单号" prop="orderNumber">
         <el-input v-model="searchInfo.orderNumber" placeholder="搜索条件" />
        </el-form-item>
           <el-form-item label="配货状态" prop="distributionStatus">
            <el-select v-model="searchInfo.distributionStatus" clearable placeholder="请选择" @clear="()=>{searchInfo.distributionStatus=undefined}">
              <el-option v-for="(item,key) in distribution_statusOptions" :key="key" :label="item.label" :value="item.value" />
            </el-select>
            </el-form-item>
        <el-form-item label="国际单号" prop="trackingNumber">
         <el-input v-model="searchInfo.trackingNumber" placeholder="搜索条件" />
        </el-form-item>

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button type="success" icon="download" :loading="syncLoading" @click="syncOrders">同步订单</el-button>
            <el-button type="warning" icon="calendar" :loading="yearSyncLoading" @click="syncYearOrders">拉取今年订单</el-button>
            <el-button type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>

        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />

          <el-table-column align="left" label="订单信息" prop="postingNumber" min-width="180" :show-overflow-tooltip="false">
            <template #default="scope">
              <div class="order-info">
                <div class="order-info-row">
                  <span class="info-badge order-badge">订单</span>
                  <span
                    class="order-number"
                    @click="copyOrderNumber(scope.row.orderNumber)"
                    :title="'点击复制订单号: ' + scope.row.orderNumber"
                  >
                    {{ scope.row.orderNumber }}
                  </span>
                </div>
                <div class="posting-info">
                  <span class="info-badge posting-badge">货件</span>
                  <span
                    class="posting-number"
                    @click="copyPostingNumber(scope.row.postingNumber)"
                    :title="'点击复制货件号: ' + scope.row.postingNumber"
                  >
                    {{ scope.row.postingNumber }}
                  </span>
                </div>
                <!-- 只有当国际单号与货件号不同时才显示国际单号 -->
                <div v-if="shouldShowTrackingNumber(scope.row)" class="tracking-info">
                  <span class="info-badge tracking-badge">国际</span>
                  <span
                    class="tracking-number"
                    @click="copyTrackingNumber(scope.row.trackingNumber)"
                    :title="'点击复制国际单号: ' + scope.row.trackingNumber"
                  >
                    {{ scope.row.trackingNumber }}
                  </span>
                </div>
                <!-- 物流信息 -->
                <div class="logistics-info">
                  <span class="info-badge logistics-badge">物流</span>
                  <span
                    class="logistics-text"
                    :title="scope.row.tplProvider || '未知物流'"
                    :data-long="getLogisticsTextLength(scope.row.tplProvider) > 15"
                    :data-very-long="getLogisticsTextLength(scope.row.tplProvider) > 25"
                  >
                    {{ scope.row.tplProvider || '未知物流' }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>

        <el-table-column align="left" label="店铺" prop="shopName" min-width="180">
            <template #default="scope">
                <span class="shop-name" :title="scope.row.shopName || '未知店铺'">
                    {{ scope.row.shopName || '未知店铺' }}
                </span>
            </template>
        </el-table-column>


           <el-table-column label="产品信息" prop="orderImg" min-width="180">
              <template #default="scope">
                 <div class="products-container">
                    <div
                      v-for="(product, index) in getUniqueProducts(scope.row)"
                      :key="index"
                      class="product-row"
                    >
                      <!-- 产品图片和数量 -->
                      <div class="product-image-wrapper">
                        <el-image
                          v-if="product.image"
                          preview-teleported
                          style="width: 50px; height: 50px; cursor: pointer"
                          :src="getUrl(product.image)"
                          :preview-src-list="returnArrImg(scope.row.orderImg)"
                          :initial-index="product.imageIndex"
                          fit="cover"
                          @click="previewImage(scope.row.orderImg, product.imageIndex)"
                        />
                        <div v-else class="no-image">
                          <span>无图</span>
                        </div>
                        <!-- 数量显示 -->
                        <div class="product-quantity">
                          x{{ product.quantity || 1 }}
                        </div>
                      </div>
                      <!-- 产品信息 -->
                      <div class="product-info">
                        <div class="product-info-row">
                          <el-icon class="product-icon sku-icon" :size="12"><Goods /></el-icon>
                          <span
                            class="product-text product-sku"
                            @click="openProductPage(product.sku)"
                            :title="'点击查看产品: ' + (product.sku || '-')"
                          >
                            {{ product.sku || '-' }}
                          </span>
                        </div>
                        <div class="product-info-row">
                          <el-icon class="product-icon name-icon" :size="12"><Document /></el-icon>
                          <span
                            class="product-text product-name"
                            :class="getProductNameClass(product.name)"
                            :title="product.name"
                          >
                            {{ product.name }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <!-- 无产品时的占位 -->
                    <div v-if="getUniqueProducts(scope.row).length === 0" class="product-row">
                      <div class="product-image-wrapper">
                        <div class="no-image">
                          <span>无图</span>
                        </div>
                        <div class="product-quantity">
                          x-
                        </div>
                      </div>
                      <div class="product-info">
                        <div class="product-info-row">
                          <el-icon class="product-icon sku-icon" :size="12"><Goods /></el-icon>
                          <span class="product-text">-</span>
                        </div>
                        <div class="product-info-row">
                          <el-icon class="product-icon name-icon" :size="12"><Document /></el-icon>
                          <span class="product-text product-name">未知产品</span>
                        </div>
                      </div>
                    </div>
                 </div>
              </template>
           </el-table-column>

        <el-table-column align="left" label="配货状态" prop="distributionStatus" min-width="180">
            <template #default="scope">
                <span
                  class="status-text"
                  :class="getDistributionStatusClass(scope.row.distributionStatus)"
                >
                  {{ filterDict(scope.row.distributionStatus,distribution_statusOptions) }}
                </span>
            </template>
        </el-table-column>
        <el-table-column align="left" label="订单状态" prop="status" min-width="180">
            <template #default="scope">
                <span
                  class="status-text"
                  :class="getOrderStatusClass(scope.row.status)"
                >
                  {{ filterDict(scope.row.status,order_statusOptions) }}
                </span>
            </template>
        </el-table-column>

         <el-table-column align="left" label="时间" prop="inProcessAt" min-width="180">
            <template #default="scope">
              <div class="time-info">
                <div class="order-time">
                  <span class="time-badge order-time-badge">下单</span>
                  <span class="time-value">{{ formatShortDate(scope.row.inProcessAt) || '-' }}</span>
                </div>
                <div class="ship-time">
                  <span class="time-badge ship-time-badge">发货</span>
                  <span class="time-value">{{ formatShortDate(scope.row.shipmentDate) || '-' }}</span>
                </div>
              </div>
            </template>
         </el-table-column>

        <el-table-column align="left" label="操作" fixed="right" width="80">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
                  <el-icon style="margin-right: 3px"><InfoFilled /></el-icon>查看
                </el-button>
                <el-button type="primary" link icon="edit" class="table-button" @click="updateOrderFunc(scope.row)">
                  编辑
                </el-button>
                <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">
                  删除
                </el-button>
              </div>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="产品图:"  prop="orderImg" >
                <SelectImage
                 multiple
                 v-model="formData.orderImg"
                 file-type="image"
                 />
            </el-form-item>
            <el-form-item label="货件号:"  prop="postingNumber" >
              <el-input v-model="formData.postingNumber" :clearable="true"  placeholder="请输入货件号" />
            </el-form-item>
            <el-form-item label="下单时间:"  prop="inProcessAt" >
              <el-date-picker v-model="formData.inProcessAt" type="date" style="width:100%" placeholder="选择日期" :clearable="true"  />
            </el-form-item>
            <el-form-item label="订单号:"  prop="orderNumber" >
              <el-input v-model="formData.orderNumber" :clearable="true"  placeholder="请输入订单号" />
            </el-form-item>
            <el-form-item label="物流:"  prop="tplProvider" >
              <el-input v-model="formData.tplProvider" :clearable="true"  placeholder="请输入物流" />
            </el-form-item>
            <el-form-item label="订单状态:"  prop="status" >
              <el-select v-model="formData.status" placeholder="请选择订单状态" style="width:100%" :clearable="true" >
                <el-option v-for="(item,key) in order_statusOptions" :key="key" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="配货状态:"  prop="distributionStatus" >
              <el-select v-model="formData.distributionStatus" placeholder="请选择配货状态" style="width:100%" :clearable="true" >
                <el-option v-for="(item,key) in distribution_statusOptions" :key="key" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="国际单号:"  prop="trackingNumber" >
              <el-input v-model="formData.trackingNumber" :clearable="true"  placeholder="请输入国际单号" />
            </el-form-item>
            <el-form-item label="发货日期:"  prop="shipmentDate" >
              <el-date-picker v-model="formData.shipmentDate" type="date" style="width:100%" placeholder="选择日期" :clearable="true"  />
            </el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="产品图">
                            <el-image style="width: 50px; height: 50px; margin-right: 10px" :preview-src-list="returnArrImg(detailFrom.orderImg)" :initial-index="index" v-for="(item,index) in detailFrom.orderImg" :key="index" :src="getUrl(item)" fit="cover" />
                    </el-descriptions-item>
                    <el-descriptions-item label="货件号">
                        {{ detailFrom.postingNumber }}
                    </el-descriptions-item>
                    <el-descriptions-item label="下单时间">
                        {{ detailFrom.inProcessAt }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订单号">
                        {{ detailFrom.orderNumber }}
                    </el-descriptions-item>
                    <el-descriptions-item label="物流">
                        {{ detailFrom.tplProvider }}
                    </el-descriptions-item>
                    <el-descriptions-item label="订单状态">
                        
                        {{ filterDict(detailFrom.status,order_statusOptions) }}
                        
                    </el-descriptions-item>
                    <el-descriptions-item label="配货状态">
                        
                        {{ filterDict(detailFrom.distributionStatus,distribution_statusOptions) }}
                        
                    </el-descriptions-item>
                    <el-descriptions-item label="国际单号">
                        {{ detailFrom.trackingNumber }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发货日期">
                        {{ detailFrom.shipmentDate }}
                    </el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<style scoped>
/* 产品容器样式 */
.products-container {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 3px; /* 减少padding */
}

/* 每个产品行 */
.product-row {
  display: flex;
  align-items: flex-start;
  gap: 6px; /* 减少间距 */
  min-height: 45px; /* 减少最小高度 */
  padding: 3px 0; /* 减少上下padding */
  position: relative;
}

.product-row:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 58px; /* 从图片右边开始 */
  right: 0;
  height: 1px;
  background-color: #f0f0f0;
}

/* 产品图片包装器 */
.product-image-wrapper {
  flex-shrink: 0;
  width: 50px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.product-image-wrapper .el-image {
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

.product-image-wrapper .el-image:hover {
  border-color: #409eff;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.no-image {
  width: 50px;
  height: 50px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 10px;
  background-color: #f5f7fa;
}

/* 产品数量样式 */
.product-quantity {
  font-size: 11px;
  color: #409eff;
  font-weight: 700;
  text-align: center;
  line-height: 1;
  margin-top: 2px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 3px;
  padding: 1px 4px;
  min-width: 20px;
}

/* 产品信息区域 */
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1px; /* 减少行间距 */
  min-width: 0;
}

.product-info-row {
  display: flex;
  align-items: center;
  gap: 4px; /* 减少元素间距 */
  min-height: 16px; /* 减少最小高度 */
}

.product-badge {
  display: inline-block;
  padding: 1px 4px; /* 减少padding */
  border-radius: 3px;
  font-size: 9px; /* 缩小字体 */
  font-weight: 600;
  color: white;
  min-width: 24px; /* 减少最小宽度 */
  text-align: center;
  flex-shrink: 0;
}

.ozon-badge {
  background-color: #409eff;
}

.quantity-badge {
  background-color: #e6a23c;
}

.name-badge {
  background-color: #909399;
}

.product-text {
  font-size: 11px; /* 缩小字体 */
  color: #333;
  font-family: 'Courier New', monospace;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.product-name {
  font-family: inherit;
  line-height: 1.1;
  white-space: normal;
  overflow: visible; /* 允许完整显示 */
  display: block;
  font-size: 11px; /* 默认稍小的字体 */
  word-break: break-word;
  word-wrap: break-word;
  hyphens: auto;
  max-height: none; /* 移除高度限制 */
  text-overflow: clip; /* 不显示省略号 */
}

/* 中等长度文本缩小字体 */
.product-name.long-text {
  font-size: 9px;
  line-height: 1.0;
}

/* 超长文本进一步缩小字体 */
.product-name.very-long-text {
  font-size: 8px;
  line-height: 1.0;
}

/* SKU点击样式 */
.product-sku {
  color: #409eff !important;
  cursor: pointer;
  text-decoration: underline;
  text-decoration-color: #409eff;
  transition: all 0.3s ease;
  font-weight: 700;
}

.product-sku:hover {
  color: #337ecc !important;
  text-decoration-color: #337ecc;
  transform: scale(1.05);
}

/* 货件号样式 */
.posting-number {
  color: #409eff;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.3s ease;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  user-select: none;
  white-space: nowrap;
  text-decoration: underline;
  text-decoration-color: transparent;
  overflow: visible;
  text-overflow: clip;
  max-width: none;
  width: auto;
  display: inline-block;
}

.posting-number:hover {
  color: #337ecc;
  text-decoration-color: #337ecc;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  line-height: 1.2;
}

.order-time,
.ship-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 时间徽章样式 */
.time-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  min-width: 28px;
  text-align: center;
  flex-shrink: 0;
}

.order-time-badge {
  background-color: #909399;
}

.ship-time-badge {
  background-color: #f56c6c;
}

.time-value {
  color: #333;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

/* 订单信息样式 */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
  line-height: 1.2;
}

.posting-info,
.order-info-row,
.tracking-info,
.logistics-info {
  display: flex;
  align-items: center;
  gap: 6px;
  min-height: 18px;
}

/* 徽章样式 */
.info-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  min-width: 28px;
  text-align: center;
  flex-shrink: 0;
}

.order-badge {
  background-color: #409eff;
}

.posting-badge {
  background-color: #67c23a;
}

.tracking-badge {
  background-color: #e6a23c;
}

.logistics-badge {
  background-color: #67c23a;
}

.posting-number,
.order-number,
.tracking-number {
  color: #409eff;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.3s ease;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  user-select: none;
  white-space: nowrap;
  text-decoration: underline;
  text-decoration-color: transparent;
  overflow: visible;
  text-overflow: clip;
  max-width: none;
  width: auto;
  display: inline-block;
  flex: 1;
}

.posting-number:hover,
.order-number:hover,
.tracking-number:hover {
  color: #337ecc;
  text-decoration-color: #337ecc;
}

/* 国际单号为空时的样式 */
.tracking-number[title*="国际单号为空"] {
  color: #999;
  cursor: default;
  text-decoration: none;
}

.tracking-number[title*="国际单号为空"]:hover {
  color: #999;
  text-decoration: none;
}

/* 物流文本样式 */
.logistics-text {
  color: #606266;
  font-size: 12px;
  line-height: 1.2;
  max-width: 140px;
  max-height: 28.8px; /* 12px * 1.2 * 2行 = 28.8px */
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  display: block;
  flex: 1;
  white-space: normal;
  text-overflow: clip; /* 不显示省略号 */
}

/* 中等长度文本缩小字体 (15-25字符) */
.logistics-text[data-long="true"] {
  font-size: 10px;
  line-height: 1.15;
  max-height: 23px; /* 10px * 1.15 * 2行 = 23px */
}

/* 长文本进一步缩小字体 (25+字符) */
.logistics-text[data-very-long="true"] {
  font-size: 8px;
  line-height: 1.1;
  max-height: 17.6px; /* 8px * 1.1 * 2行 = 17.6px */
}

/* 物流文本悬停效果 */
.logistics-text:hover {
  color: #409eff;
}

/* 店铺名称样式 */
.shop-name {
  color: #606266;
  font-size: 12px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  max-width: 100%;
}

.shop-name:hover {
  color: #409eff;
}

/* 状态颜色样式 */
.status-text {
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
  min-width: 60px;
  text-align: center;
}

/* 正常状态 - 蓝色 */
.status-normal {
  color: #409eff;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
}

/* 异常状态 - 红色 */
.status-error {
  color: #f56c6c;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

/* 操作按钮纵向排布样式 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-start;
}

.operation-buttons .el-button {
  margin: 0;
  padding: 2px 8px;
  font-size: 12px;
  min-height: auto;
  height: auto;
  line-height: 1.2;
}

.operation-buttons .el-button + .el-button {
  margin-left: 0;
}

/* 页面布局优化 - 充分利用屏幕宽度并响应边栏变化 */
.gva-search-box {
  width: 100%;
  max-width: none;
  transition: all 0.3s ease;
}

.gva-table-box {
  width: 100%;
  max-width: none;
  transition: all 0.3s ease;
}

/* 表格自适应优化 - 响应边栏收起/展开 */
.el-table {
  width: 100% !important;
  transition: all 0.3s ease;
}

.el-table .el-table__body-wrapper {
  overflow-x: auto;
}

/* 确保容器充分利用空间并响应布局变化 */
.gva-card {
  margin: 0;
  width: 100%;
  transition: all 0.3s ease;
}

/* 页面根容器自适应 */
.order-container {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
}

/* 表格容器响应式处理 */
.el-table__body-wrapper,
.el-table__header-wrapper {
  transition: all 0.3s ease;
}

/* 强制表格重新计算布局 */
.el-table--fit {
  width: 100% !important;
}

/* 强制表格自适应容器宽度 */
.gva-table-box .el-table {
  width: 100% !important;
  min-width: auto !important;
}

/* 确保表格列能够自动分配宽度 */
.el-table .el-table__header-wrapper,
.el-table .el-table__body-wrapper {
  width: 100% !important;
}

/* 响应式优化 */
@media (min-width: 1200px) {
  .el-table-column {
    min-width: auto;
  }
}

/* 等宽列设计 - 移除响应式宽度变化以保持一致性 */

/* 自适应表格优化 - 响应边栏变化 */
.el-table {
  table-layout: auto !important;
  width: 100% !important;
}

.el-table .el-table__header-wrapper th,
.el-table .el-table__body-wrapper td {
  text-align: center;
  vertical-align: middle;
}

/* 确保内容在固定宽度内良好显示 */
.el-table .el-table__body-wrapper td .cell {
  padding: 8px 4px;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 特殊列保持左对齐 */
.el-table-column[label="产品信息"] .cell,
.el-table-column[label="订单信息"] .cell {
  text-align: left !important;
}

/* 确保表格能够充分利用可用空间 */
.el-table .el-table__body,
.el-table .el-table__header {
  width: 100% !important;
}

/* 表格列自适应分配剩余空间 */
.el-table th,
.el-table td {
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

<script setup>
import {
  createOrder,
  deleteOrder,
  deleteOrderByIds,
  updateOrder,
  findOrder,
  getOrderList,
  pullOrders,
  pullYearOrders
} from '@/api/order/order'
import { getUrl } from '@/utils/image'
// 图片选择组件
import SelectImage from '@/components/selectImage/selectImage.vue'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, watch } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'Order'
})

// 提交按钮loading
const btnLoading = ref(false)
// 同步订单loading
const syncLoading = ref(false)
// 拉取今年订单loading
const yearSyncLoading = ref(false)
const appStore = useAppStore()
const multipleTable = ref()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const order_statusOptions = ref([])
const distribution_statusOptions = ref([])
const formData = ref({
            orderImg: [],
            postingNumber: '',
            inProcessAt: new Date(),
            orderNumber: '',
            tplProvider: '',
            status: '',
            distributionStatus: '',
            trackingNumber: '',
            shipmentDate: new Date(),
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  // 移除了日期相关的验证规则，因为已经删除了日期搜索字段
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
// 从localStorage读取分页状态，如果没有则使用默认值
const savedPage = localStorage.getItem('orderListPage')
const savedPageSize = localStorage.getItem('orderListPageSize')

const page = ref(savedPage ? parseInt(savedPage) : 1)
const total = ref(0)
const pageSize = ref(savedPageSize ? parseInt(savedPageSize) : 10)
const tableData = ref([])

// 监听分页状态变化，自动保存到localStorage
watch(page, (newPage) => {
  localStorage.setItem('orderListPage', newPage.toString())
})

watch(pageSize, (newPageSize) => {
  localStorage.setItem('orderListPageSize', newPageSize.toString())
})
// 默认只显示未处理的订单
const searchInfo = ref({
  distributionStatus: 'unprocessed' // 默认过滤未处理的订单
})
// 重置
const onReset = () => {
  searchInfo.value = {
    distributionStatus: 'unprocessed' // 重置时保持默认过滤未处理的订单
  }
  // 重置搜索条件时不重置页码，保持用户当前的分页位置
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getOrderList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    // 不要用后端返回的page和pageSize覆盖前端状态，保持用户设置的分页状态
    // page.value = table.data.page
    // pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
    order_statusOptions.value = await getDictFunc('order_status')
    distribution_statusOptions.value = await getDictFunc('distribution_status')
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteOrderFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteOrderByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateOrderFunc = async(row) => {
    const res = await findOrder({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteOrderFunc = async (row) => {
    const res = await deleteOrder({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        orderImg: [],
        postingNumber: '',
        inProcessAt: new Date(),
        orderNumber: '',
        tplProvider: '',
        status: '',
        distributionStatus: '',
        trackingNumber: '',
        shipmentDate: new Date(),
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createOrder(formData.value)
                  break
                case 'update':
                  res = await updateOrder(formData.value)
                  break
                default:
                  res = await createOrder(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findOrder({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 同步订单
const syncOrders = async () => {
  try {
    syncLoading.value = true

    ElMessage({
      type: 'info',
      message: '正在从Ozon同步订单...'
    })

    const res = await pullOrders()

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: `同步成功！共拉取 ${res.data.count} 个订单`
      })
      // 刷新订单列表
      getTableData()
    } else {
      ElMessage({
        type: 'error',
        message: '同步失败: ' + res.msg
      })
    }
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '同步订单失败: ' + error.message
    })
  } finally {
    syncLoading.value = false
  }
}

// 拉取今年订单
const syncYearOrders = async () => {
  try {
    yearSyncLoading.value = true

    ElMessage({
      type: 'info',
      message: '正在拉取今年一整年的订单...'
    })

    const res = await pullYearOrders()

    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: `拉取成功！共拉取今年 ${res.data.count} 个订单`
      })
      // 刷新订单列表
      getTableData()
    } else {
      ElMessage({
        type: 'error',
        message: '拉取今年订单失败: ' + res.msg
      })
    }
  } catch (error) {
    ElMessage({
      type: 'error',
      message: '拉取今年订单失败: ' + error.message
    })
  } finally {
    yearSyncLoading.value = false
  }
}

// 图片预览功能
const previewImage = (images, index) => {
  // 这个方法主要是为了提供更好的用户体验
  // el-image组件的preview-src-list已经处理了预览功能
  // 这里可以添加额外的逻辑，比如统计点击次数等
  console.log('预览图片', { images, index })
}

// 复制货件号功能
const copyPostingNumber = async (postingNumber) => {
  if (!postingNumber) {
    ElMessage({
      type: 'warning',
      message: '货件号为空，无法复制'
    })
    return
  }

  try {
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(postingNumber)
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = postingNumber
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    ElMessage({
      type: 'success',
      message: `货件号已复制: ${postingNumber}`,
      duration: 2000
    })
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage({
      type: 'error',
      message: '复制失败，请手动复制'
    })
  }
}

// 复制订单号功能
const copyOrderNumber = async (orderNumber) => {
  if (!orderNumber) {
    ElMessage({
      type: 'warning',
      message: '订单号为空，无法复制'
    })
    return
  }

  try {
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(orderNumber)
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = orderNumber
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    ElMessage({
      type: 'success',
      message: `订单号已复制: ${orderNumber}`,
      duration: 2000
    })
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage({
      type: 'error',
      message: '复制失败，请手动复制'
    })
  }
}

// 格式化短日期 (25/06/01)
const formatShortDate = (dateString) => {
  if (!dateString) return ''

  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ''

    const year = date.getFullYear().toString().slice(-2) // 取年份后两位
    const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份补零
    const day = date.getDate().toString().padStart(2, '0') // 日期补零

    return `${year}/${month}/${day}`
  } catch (error) {
    return ''
  }
}

// 获取配货状态的CSS类
const getDistributionStatusClass = (status) => {
  // 需要处理的状态显示红色：未处理、异常状态等
  const needAttentionStatuses = ['unprocessed', 'cancelled', 'error', 'failed', 'rejected']
  return needAttentionStatuses.includes(status) ? 'status-error' : 'status-normal'
}

// 获取订单状态的CSS类
const getOrderStatusClass = (status) => {
  // 异常状态显示红色，其余显示蓝色
  const abnormalStatuses = [
    'cancelled', 'canceled', 'error', 'failed', 'rejected',
    'not_accepted', 'client_arbitration', 'arbitration'
  ]
  return abnormalStatuses.includes(status) ? 'status-error' : 'status-normal'
}

// 获取去重后的产品列表，每个产品包含图片和详细信息
const getUniqueProducts = (order) => {
  try {
    const products = []

    // 调试信息
    console.log('订单数据:', order)
    console.log('订单图片:', order.orderImg)

    if (order.jsonData) {
      // 解析jsonData获取产品信息
      const data = typeof order.jsonData === 'string' ? JSON.parse(order.jsonData) : order.jsonData
      console.log('解析后的JSON数据:', data)

      if (data.products && data.products.length > 0) {
        // 创建产品映射，用于去重
        const productMap = new Map()
        let imageIndex = 0

        data.products.forEach((product, index) => {
          const key = `${product.sku || 'unknown'}_${product.name || 'unknown'}`

          if (!productMap.has(key)) {
            // 获取对应的图片 - 确保图片与产品正确对应
            let productImage = null
            if (order.orderImg && order.orderImg.length > imageIndex) {
              productImage = order.orderImg[imageIndex]
              imageIndex++
            }

            productMap.set(key, {
              name: product.name || '未知产品',
              sku: product.sku || '',
              offerId: product.offer_id || '',
              quantity: product.quantity || 1,
              image: productImage,
              imageIndex: imageIndex - 1
            })
          } else {
            // 如果产品已存在，累加数量
            const existingProduct = productMap.get(key)
            existingProduct.quantity = (existingProduct.quantity || 1) + (product.quantity || 1)
          }
        })

        // 转换为数组，最多显示3个不同的产品
        return Array.from(productMap.values()).slice(0, 3)
      }
    }

    // 如果没有产品数据但有图片，创建默认产品
    if (order.orderImg && order.orderImg.length > 0) {
      const uniqueImages = [...new Set(order.orderImg)]
      return uniqueImages.slice(0, 3).map((img, index) => ({
        name: '未知产品',
        sku: '',
        offerId: '',
        image: img,
        imageIndex: index
      }))
    }

    return []
  } catch (error) {
    console.error('解析产品信息失败:', error)
    console.error('订单数据:', order)
    return []
  }
}

// 获取去重后的图片列表（保留兼容性）
const getUniqueImages = (order) => {
  if (!order.orderImg || order.orderImg.length === 0) {
    return []
  }

  // 使用Set去重，保持顺序
  const uniqueImages = [...new Set(order.orderImg)]

  // 最多显示3张不同的图片
  return uniqueImages.slice(0, 3)
}

// 根据产品名称长度返回CSS类
const getProductNameClass = (productName) => {
  if (!productName) return ''

  const length = productName.length

  if (length > 80) {
    return 'very-long-text'
  } else if (length > 50) {
    return 'long-text'
  }

  return ''
}

// 获取产品名称
const getProductName = (order) => {
  try {
    if (order.jsonData) {
      // 如果jsonData是字符串，需要解析
      const data = typeof order.jsonData === 'string' ? JSON.parse(order.jsonData) : order.jsonData

      // 从Ozon API数据中提取产品名称
      if (data.products && data.products.length > 0) {
        const productName = data.products[0].name || '未知产品'
        // 如果有多个产品，在名称后添加数量提示
        if (data.products.length > 1) {
          return `${productName} (+${data.products.length - 1}个产品)`
        }
        return productName
      }
    }
    return '未知产品'
  } catch (error) {
    console.error('解析产品名称失败:', error)
    return '未知产品'
  }
}

// 判断是否显示国际单号
const shouldShowTrackingNumber = (order) => {
  const trackingNumber = order.trackingNumber
  const postingNumber = order.postingNumber

  // 如果国际单号为空或者与货件号相同，则不显示
  if (!trackingNumber || trackingNumber === '-' || trackingNumber === postingNumber) {
    return false
  }

  return true
}

// 打开产品页面
const openProductPage = (sku) => {
  if (!sku || sku === '-') {
    ElMessage({
      type: 'warning',
      message: 'SKU为空，无法跳转'
    })
    return
  }

  try {
    // 构建Ozon产品页面URL
    const ozonProductUrl = `https://www.ozon.ru/product/${sku}/`

    // 在新标签页中打开
    window.open(ozonProductUrl, '_blank')

    ElMessage({
      type: 'success',
      message: `正在打开产品页面: ${sku}`,
      duration: 2000
    })
  } catch (error) {
    console.error('打开产品页面失败:', error)
    ElMessage({
      type: 'error',
      message: '打开产品页面失败'
    })
  }
}

// 复制国际单号功能
const copyTrackingNumber = async (trackingNumber) => {
  if (!trackingNumber || trackingNumber === '-') {
    ElMessage({
      type: 'warning',
      message: '国际单号为空，无法复制'
    })
    return
  }

  try {
    // 使用现代浏览器的 Clipboard API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(trackingNumber)
    } else {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = trackingNumber
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
    }

    ElMessage({
      type: 'success',
      message: `国际单号已复制: ${trackingNumber}`,
      duration: 2000
    })
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage({
      type: 'error',
      message: '复制失败，请手动复制'
    })
  }
}

// 获取物流文本长度，用于动态调整字体大小
const getLogisticsTextLength = (text) => {
  if (!text) return 0
  return text.length
}




</script>

<style>

</style>
