
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="产品图:" prop="orderImg">
           <SelectImage v-model="formData.orderImg" multiple file-type="image"/>
       </el-form-item>
        <el-form-item label="货件号:" prop="postingNumber">
          <el-input v-model="formData.postingNumber" :clearable="true"  placeholder="请输入货件号" />
       </el-form-item>
        <el-form-item label="下单时间:" prop="inProcessAt">
          <el-date-picker v-model="formData.inProcessAt" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="订单号:" prop="orderNumber">
          <el-input v-model="formData.orderNumber" :clearable="true"  placeholder="请输入订单号" />
       </el-form-item>
        <el-form-item label="物流:" prop="tplProvider">
          <el-input v-model="formData.tplProvider" :clearable="true"  placeholder="请输入物流" />
       </el-form-item>
        <el-form-item label="订单状态:" prop="status">
           <el-select v-model="formData.status" placeholder="请选择订单状态" style="width:100%" :clearable="true" >
              <el-option v-for="(item,key) in order_statusOptions" :key="key" :label="item.label" :value="item.value" />
           </el-select>
       </el-form-item>
        <el-form-item label="配货状态:" prop="distributionStatus">
           <el-select v-model="formData.distributionStatus" placeholder="请选择配货状态" style="width:100%" :clearable="true" >
              <el-option v-for="(item,key) in distribution_statusOptions" :key="key" :label="item.label" :value="item.value" />
           </el-select>
       </el-form-item>
        <el-form-item label="国际单号:" prop="trackingNumber">
          <el-input v-model="formData.trackingNumber" :clearable="true"  placeholder="请输入国际单号" />
       </el-form-item>
        <el-form-item label="发货日期:" prop="shipmentDate">
          <el-date-picker v-model="formData.shipmentDate" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createOrder,
  updateOrder,
  findOrder
} from '@/api/order/order'

defineOptions({
    name: 'OrderForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
// 图片选择组件
import SelectImage from '@/components/selectImage/selectImage.vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const order_statusOptions = ref([])
const distribution_statusOptions = ref([])
const formData = ref({
            orderImg: [],
            postingNumber: '',
            inProcessAt: new Date(),
            orderNumber: '',
            tplProvider: '',
            status: '',
            distributionStatus: '',
            trackingNumber: '',
            shipmentDate: new Date(),
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findOrder({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
    order_statusOptions.value = await getDictFunc('order_status')
    distribution_statusOptions.value = await getDictFunc('distribution_status')
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createOrder(formData.value)
               break
             case 'update':
               res = await updateOrder(formData.value)
               break
             default:
               res = await createOrder(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
