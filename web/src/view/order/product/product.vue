<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="SKU" prop="sku">
          <el-input v-model="searchInfo.sku" placeholder="搜索SKU" />
        </el-form-item>
        <el-form-item label="店铺" prop="shopName">
          <el-input v-model="searchInfo.shopName" placeholder="搜索店铺" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="searchInfo.brand" placeholder="搜索品牌" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-select v-model="searchInfo.isActive" placeholder="选择状态" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 产品汇总卡片 -->
    <div class="gva-card-box">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-card class="product-card">
            <div class="product-item">
              <div class="product-label">总产品数</div>
              <div class="product-value">{{ productSummary.totalProducts }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="product-card">
            <div class="product-item">
              <div class="product-label">启用产品</div>
              <div class="product-value">{{ productSummary.activeProducts }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card class="product-card">
            <div class="product-item">
              <div class="product-label">禁用产品</div>
              <div class="product-value">{{ productSummary.inactiveProducts }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增产品</el-button>
        <el-button type="success" icon="refresh" @click="loadProductSummary">刷新汇总</el-button>
        <el-button type="info" icon="download" @click="syncFromOrders">从订单同步</el-button>
        <el-button type="primary" icon="picture" @click="syncImagesFromOrders">同步图片</el-button>

        <el-button type="warning" icon="edit" :disabled="!multipleSelection.length" @click="openBatchDialog">批量编辑</el-button>
        <el-button type="danger" icon="delete" :disabled="!multipleSelection.length" @click="onDelete">批量删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="店铺" prop="shopName" width="120" />
        <el-table-column align="left" label="产品信息" prop="productInfo" min-width="280">
          <template #default="scope">
            <div style="display: flex; align-items: center; gap: 12px;">
              <!-- 产品图片 -->
              <div style="flex-shrink: 0;">
                <el-image
                  v-if="scope.row.imageUrl"
                  :src="scope.row.imageUrl"
                  :preview-src-list="[scope.row.imageUrl]"
                  fit="cover"
                  style="width: 60px; height: 60px; border-radius: 4px; cursor: pointer;"
                  :preview-teleported="true"
                >
                  <template #error>
                    <div style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center; flex-direction: column; font-size: 12px; color: #999;">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <div v-else style="width: 60px; height: 60px; background: #f5f5f5; border-radius: 4px; display: flex; align-items: center; justify-content: center; flex-direction: column; font-size: 12px; color: #999;">
                  <el-icon><Picture /></el-icon>
                  <span style="font-size: 10px; margin-top: 2px;">无图片</span>
                </div>
              </div>
              <!-- SKU和产品名称 -->
              <div style="flex: 1; min-width: 0;">
                <!-- SKU -->
                <div style="margin-bottom: 4px;">
                  <el-link type="primary" @click="goToProductDetail(scope.row.sku)" style="font-size: 13px; font-weight: 500;">
                    {{ scope.row.sku }}
                  </el-link>
                </div>
                <!-- 产品名称 -->
                <div :style="getProductNameStyle(scope.row.productName)" :title="scope.row.productName">
                  {{ scope.row.productName || '未设置产品名称' }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="实际重量(kg)" prop="actualWeight" width="120">
          <template #default="scope">
            <span v-if="scope.row.actualWeight" class="weight-highlight">{{ scope.row.actualWeight }}kg</span>
            <el-tag v-else type="warning" size="small">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="Ozon重量(kg)" prop="ozonWeight" width="120">
          <template #default="scope">
            <span v-if="scope.row.ozonWeight" class="weight-highlight">{{ scope.row.ozonWeight }}kg</span>
            <el-tag v-else type="info" size="small">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="成本价格" prop="costPrice" width="100">
          <template #default="scope">
            <span v-if="scope.row.costPrice" style="color: #0369a1; font-weight: 500;">
              ¥{{ parseFloat(scope.row.costPrice).toFixed(2) }}
            </span>
            <el-tag v-else type="warning" size="small">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="佣金比率" prop="commissionRate" width="100">
          <template #default="scope">
            <span v-if="scope.row.commissionRate" style="color: #dc2626; font-weight: 500;">
              {{ (parseFloat(scope.row.commissionRate) * 100).toFixed(1) }}%
            </span>
            <el-tag v-else type="warning" size="small">未设置</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="品牌" prop="brand" width="120" />
        <el-table-column align="left" label="状态" prop="isActive" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isActive ? 'success' : 'danger'">
              {{ scope.row.isActive ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="首次订单" prop="firstOrderDate" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.firstOrderDate) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="最后订单" prop="lastOrderDate" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.lastOrderDate) }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="订单数" prop="totalOrderCount" width="80">
          <template #default="scope">
            <el-tag type="info" size="small">
              {{ scope.row.totalOrderCount || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" width="160">
          <template #default="scope">
            <el-button type="primary" link icon="edit" @click="updateProductFunc(scope.row)">编辑</el-button>
            <el-button type="danger" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogFormVisible" :title="type === 'create' ? '新增产品' : '编辑产品'" width="600px">
      <el-form ref="elFormRef" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="SKU" prop="sku">
          <el-input v-model="formData.sku" placeholder="请输入SKU" :disabled="type === 'update'" />
        </el-form-item>
        <el-form-item label="实际重量(kg)" prop="actualWeight">
          <el-input-number v-model="formData.actualWeight" :precision="3" :min="0" placeholder="请输入实际重量" style="width: 100%" />
        </el-form-item>
        <el-form-item label="Ozon重量(kg)" prop="ozonWeight">
          <el-input-number v-model="formData.ozonWeight" :precision="3" :min="0" placeholder="请输入Ozon重量" style="width: 100%" :disabled="type === 'update'" />
        </el-form-item>
        <el-form-item label="成本价格" prop="costPrice">
          <el-input-number v-model="formData.costPrice" :precision="2" :min="0" placeholder="请输入成本价格" style="width: 100%" />
        </el-form-item>
        <el-form-item label="佣金比率" prop="commissionRate">
          <el-input-number v-model="formData.commissionRate" :precision="4" :min="0" :max="1" placeholder="请输入佣金比率(0-1)" style="width: 100%" />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            例如：0.12 表示 12%
          </div>
        </el-form-item>
        <el-form-item label="店铺" prop="shopName">
          <el-input v-model="formData.shopName" placeholder="请输入店铺名称" :disabled="type === 'update'" />
        </el-form-item>
        <el-form-item label="品牌" prop="brand">
          <el-input v-model="formData.brand" placeholder="请输入品牌" />
        </el-form-item>
        <el-form-item label="产品描述" prop="description">
          <el-input v-model="formData.description" type="textarea" placeholder="请输入产品描述" />
        </el-form-item>
        <el-form-item label="图片URL" prop="imageUrl">
          <el-input v-model="formData.imageUrl" placeholder="请输入图片URL" />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input v-model="formData.notes" type="textarea" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch v-model="formData.isActive" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="enterDialog">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量编辑对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量编辑产品" width="500px">
      <el-form ref="batchFormRef" :model="batchForm" label-width="120px">
        <el-form-item label="实际重量(kg)">
          <el-input-number v-model="batchForm.actualWeight" :precision="3" :min="0" placeholder="留空表示不修改" style="width: 100%" />
        </el-form-item>
        <el-form-item label="Ozon重量(kg)">
          <el-input-number v-model="batchForm.ozonWeight" :precision="3" :min="0" placeholder="留空表示不修改" style="width: 100%" />
        </el-form-item>
        <el-form-item label="成本价格">
          <el-input-number v-model="batchForm.costPrice" :precision="2" :min="0" placeholder="留空表示不修改" style="width: 100%" />
        </el-form-item>
        <el-form-item label="佣金比率">
          <el-input-number v-model="batchForm.commissionRate" :precision="4" :min="0" :max="1" placeholder="留空表示不修改" style="width: 100%" />
          <div style="font-size: 12px; color: #909399; margin-top: 4px;">
            例如：0.12 表示 12%
          </div>
        </el-form-item>
        <el-form-item label="店铺">
          <el-input v-model="batchForm.shopName" placeholder="留空表示不修改" />
        </el-form-item>
        <el-form-item label="品牌">
          <el-input v-model="batchForm.brand" placeholder="留空表示不修改" />
        </el-form-item>
        <el-form-item label="产品描述">
          <el-input v-model="batchForm.description" type="textarea" placeholder="留空表示不修改" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="batchForm.isActive" placeholder="留空表示不修改" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBatchUpdate">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 同步对话框 -->
    <el-dialog v-model="syncDialogVisible" title="从订单同步产品" width="400px">
      <el-form ref="syncFormRef" :model="syncForm" label-width="120px">
        <el-form-item label="强制更新">
          <el-switch v-model="syncForm.forceUpdate" active-text="是" inactive-text="否" />
          <div class="form-tip">开启后会更新已存在产品的名称等信息</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="syncDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitSync" :loading="syncLoading">开始同步</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createProduct,
  deleteProduct,
  deleteProductByIds,
  updateProduct,
  findProduct,
  getProductList,
  getProductSummary,
  batchUpdateProduct,
  syncProductsFromOrders,
  syncProductImagesFromOrders
} from '@/api/product.js'

import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { ref, reactive } from 'vue'

defineOptions({
  name: 'Product'
})

const elFormRef = ref()
const elSearchFormRef = ref()
const batchFormRef = ref()
const syncFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const searchRule = reactive({})

// 产品汇总数据
const productSummary = ref({
  totalProducts: 0,
  activeProducts: 0,
  inactiveProducts: 0,
  avgWeight: 0,
  totalOrders: 0
})

// 多选
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getProductList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
  }
}

// 加载产品汇总
const loadProductSummary = async() => {
  const result = await getProductSummary()
  if (result.code === 0) {
    productSummary.value = result.data
  }
}

// ============== 表格操作部分 ==============
const formData = ref({
  sku: '',
  actualWeight: null,
  ozonWeight: null,
  costPrice: null,
  commissionRate: null,
  shopName: '',
  brand: '',
  description: '',
  imageUrl: '',
  notes: '',
  isActive: true
})

// 验证规则
const rules = reactive({
  sku: [{ required: true, message: '请输入SKU', trigger: 'blur' }]
})

const dialogFormVisible = ref(false)
const type = ref('')

const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    sku: '',
    actualWeight: null,
    ozonWeight: null,
    costPrice: null,
    commissionRate: null,
    shopName: '',
    brand: '',
    description: '',
    imageUrl: '',
    notes: '',
    isActive: true
  }
}

const enterDialog = async() => {
  console.log('开始保存，当前类型:', type.value)
  console.log('表单数据:', formData.value)
  elFormRef.value?.validate(async(valid) => {
    if (!valid) {
      console.log('表单验证失败')
      return
    }
    console.log('表单验证通过')
    let res
    switch (type.value) {
      case 'create':
        console.log('执行创建产品')
        res = await createProduct(formData.value)
        break
      case 'update':
        console.log('执行更新产品')
        res = await updateProduct(formData.value)
        break
      default:
        console.log('默认执行创建产品')
        res = await createProduct(formData.value)
        break
    }
    console.log('API响应:', res)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
      loadProductSummary()
    } else {
      ElMessage({
        type: 'error',
        message: res.message || '操作失败'
      })
    }
  })
}

const updateProductFunc = async(row) => {
  console.log('开始编辑产品:', row)
  const res = await findProduct({ ID: row.ID })
  console.log('查询产品结果:', res)
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data
    console.log('设置表单数据:', formData.value)
    dialogFormVisible.value = true
  } else {
    ElMessage({
      type: 'error',
      message: '获取产品信息失败'
    })
  }
}

const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteProductFunc(row)
  })
}

const deleteProductFunc = async(row) => {
  const res = await deleteProduct({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
    loadProductSummary()
  }
}

// 批量删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteProductByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
      loadProductSummary()
    }
  })
}

// 批量编辑
const batchDialogVisible = ref(false)
const batchForm = ref({
  actualWeight: null,
  ozonWeight: null,
  costPrice: null,
  commissionRate: null,
  shopName: '',
  brand: '',
  description: '',
  isActive: null
})

const openBatchDialog = () => {
  batchForm.value = {
    actualWeight: null,
    ozonWeight: null,
    costPrice: null,
    commissionRate: null,
    shopName: '',
    brand: '',
    description: '',
    isActive: null
  }
  batchDialogVisible.value = true
}

const submitBatchUpdate = async() => {
  const IDs = multipleSelection.value.map(item => item.ID)
  const updateData = { ids: IDs }
  
  // 只添加非空字段
  if (batchForm.value.actualWeight !== null) updateData.actualWeight = batchForm.value.actualWeight
  if (batchForm.value.ozonWeight !== null) updateData.ozonWeight = batchForm.value.ozonWeight
  if (batchForm.value.shopName) updateData.shopName = batchForm.value.shopName
  if (batchForm.value.brand) updateData.brand = batchForm.value.brand
  if (batchForm.value.description) updateData.description = batchForm.value.description
  if (batchForm.value.isActive !== null) updateData.isActive = batchForm.value.isActive

  const res = await batchUpdateProduct(updateData)
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '批量更新成功'
    })
    batchDialogVisible.value = false
    getTableData()
    loadProductSummary()
  }
}

// 同步功能
const syncDialogVisible = ref(false)
const syncLoading = ref(false)
const syncForm = ref({
  forceUpdate: false
})

const syncFromOrders = () => {
  syncForm.value = {
    forceUpdate: false
  }
  syncDialogVisible.value = true
}

const submitSync = async() => {
  syncLoading.value = true
  try {
    const res = await syncProductsFromOrders(syncForm.value)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: res.msg
      })
      syncDialogVisible.value = false
      getTableData()
      loadProductSummary()
    }
  } finally {
    syncLoading.value = false
  }
}

// 同步图片功能
const syncImagesFromOrders = async() => {
  ElMessageBox.confirm('确定要从订单同步产品图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(async() => {
    const loading = ElMessage({
      message: '正在同步图片...',
      type: 'info',
      duration: 0
    })

    try {
      const res = await syncProductImagesFromOrders()
      loading.close()

      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: res.msg
        })
        getTableData()
        loadProductSummary()
      }
    } catch (error) {
      loading.close()
      ElMessage({
        type: 'error',
        message: '同步图片失败'
      })
    }
  })
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}





// 获取产品名称样式
const getProductNameStyle = (productName) => {
  if (!productName) {
    return {
      color: '#999',
      fontSize: '12px',
      fontStyle: 'italic'
    }
  }

  const length = productName.length
  let fontSize = '13px'

  if (length > 50) {
    fontSize = '11px'
  } else if (length > 30) {
    fontSize = '12px'
  }

  return {
    fontSize: fontSize,
    lineHeight: '1.3',
    color: '#333',
    wordBreak: 'break-word',
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden'
  }
}

// 跳转到产品详情页面
const goToProductDetail = (sku) => {
  // 这里可以根据需要实现跳转逻辑
  // 例如：router.push(`/product/detail/${sku}`)
  ElMessage({
    type: 'info',
    message: `查看产品详情: ${sku}`
  })
}

// 初始化
getTableData()
loadProductSummary()
</script>

<style scoped>
.gva-card-box {
  margin-bottom: 20px;
}

.product-card {
  text-align: center;
}

.product-item {
  padding: 10px;
}

.product-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.product-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.weight-highlight {
  font-weight: bold;
  color: #409eff;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.product-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
}

.product-image {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  cursor: pointer;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
  border-radius: 4px;
}

.no-image {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: #f5f7fa;
  color: #909399;
  font-size: 12px;
  border-radius: 4px;
}

.no-image .el-icon {
  font-size: 16px;
  margin-bottom: 2px;
}
</style>
