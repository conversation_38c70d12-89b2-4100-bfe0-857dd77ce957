<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
        <el-form-item label="物流方式名称" prop="name">
          <el-input v-model="searchInfo.name" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="物流服务商" prop="provider">
          <el-input v-model="searchInfo.provider" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="是否启用" prop="isActive">
          <el-select v-model="searchInfo.isActive" clearable placeholder="请选择">
            <el-option label="启用" value="true" />
            <el-option label="禁用" value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button type="success" icon="refresh" @click="syncOzonLogisticsFunc">同步Ozon物流</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>

      <!-- 店铺页签 -->
      <el-tabs v-model="activeShop" @tab-click="handleShopChange" class="shop-tabs">
        <el-tab-pane
          v-for="shop in shopList"
          :key="shop.name"
          :label="shop.name"
          :name="shop.name"
        >
          <!-- 仓库和物流方式展示 -->
          <div class="warehouse-container">
            <el-collapse v-model="expandedWarehouses" accordion>
              <el-collapse-item
                v-for="warehouse in shop.warehouses"
                :key="warehouse.id"
                :title="`${warehouse.name} (${warehouse.logistics.length}个物流方式)`"
                :name="warehouse.id.toString()"
              >
                <template #title>
                  <div class="warehouse-title">
                    <el-icon class="warehouse-icon"><House /></el-icon>
                    <span class="warehouse-name">{{ warehouse.name }}</span>
                    <el-tag size="small" type="info" class="logistics-count">
                      {{ warehouse.logistics.length }}个物流方式
                    </el-tag>
                    <el-tag
                      size="small"
                      :type="warehouse.status === 'created' ? 'success' : 'warning'"
                      class="warehouse-status"
                    >
                      {{ warehouse.status === 'created' ? '已创建' : warehouse.status }}
                    </el-tag>
                  </div>
                </template>

                <!-- 物流方式表格 -->
                <el-table
                  :data="getFilteredLogistics(warehouse.logistics)"
                  style="width: 100%"
                  size="small"
                  @selection-change="handleSelectionChange"
                >
                  <el-table-column type="selection" width="55" />
                  <el-table-column align="left" label="物流方式名称" prop="name" width="200" />
                  <el-table-column align="left" label="物流服务商" prop="provider" width="150" />
                  <el-table-column align="left" label="服务类型" prop="serviceType" width="120" />
                  <el-table-column align="left" label="是否启用" prop="isActive" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="small">
                        {{ scope.row.isActive ? '启用' : '禁用' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="基础价格" prop="basePrice" width="100">
                    <template #default="scope">
                      <span v-if="scope.row.basePrice">{{ scope.row.basePrice.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">未设置</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="每公斤价格" prop="pricePerKg" width="100">
                    <template #default="scope">
                      <span v-if="scope.row.pricePerKg">{{ scope.row.pricePerKg.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">未设置</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="预计天数" prop="estimatedDays" width="100" />
                  <el-table-column align="left" label="佣金比率" prop="commissionRate" width="100">
                    <template #default="scope">
                      <span v-if="scope.row.commissionRate">{{ (scope.row.commissionRate * 100).toFixed(2) }}%</span>
                      <span v-else class="text-gray-400">使用产品佣金</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="计价公式" prop="pricingFormula" width="200" show-overflow-tooltip />
                  <el-table-column align="left" label="操作" fixed="right" min-width="200">
                    <template #default="scope">
                      <div class="flex flex-col gap-1">
                        <el-button type="primary" link icon="view" size="small" @click="getDetails(scope.row)">查看</el-button>
                        <el-button type="primary" link icon="edit" size="small" @click="updateLogisticsFunc(scope.row)">编辑</el-button>
                        <el-button type="primary" link icon="delete" size="small" @click="deleteRow(scope.row)">删除</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>

                <!-- 如果没有物流方式，显示提示 -->
                <div v-if="warehouse.logistics.length === 0" class="no-logistics">
                  <el-empty description="该仓库暂无物流方式" :image-size="60" />
                </div>
              </el-collapse-item>
            </el-collapse>

            <!-- 如果没有仓库，显示提示 -->
            <div v-if="shop.warehouses.length === 0" class="no-warehouses">
              <el-empty description="该店铺暂无仓库信息" :image-size="80" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 详情/编辑对话框 -->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="type === 'create' ? '新增物流方式' : type === 'update' ? '修改物流方式' : '查看物流方式'">
      <el-form ref="elFormRef" :model="formData" label-position="right" label-width="120px" style="width: 90%">
        <el-form-item label="店铺名称">
          <el-input v-model="formData.shopName" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="物流方式名称">
          <el-input v-model="formData.name" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="物流服务商">
          <el-input v-model="formData.provider" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="服务类型">
          <el-input v-model="formData.serviceType" clearable :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="formData.description" type="textarea" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="是否启用">
          <el-switch v-model="formData.isActive" :disabled="type === 'look'" />
        </el-form-item>
        <el-form-item label="基础价格">
          <el-input-number v-model="formData.basePrice" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="每公斤价格">
          <el-input-number v-model="formData.pricePerKg" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="每立方米价格">
          <el-input-number v-model="formData.pricePerCubic" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="最小重量(kg)">
          <el-input-number v-model="formData.minWeight" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="最大重量(kg)">
          <el-input-number v-model="formData.maxWeight" :precision="2" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="预计送达天数">
          <el-input-number v-model="formData.estimatedDays" :readonly="type === 'look'" />
        </el-form-item>
        <el-form-item label="佣金比率">
          <el-input-number v-model="formData.commissionRate" :min="0" :max="1" :step="0.01" :precision="4" :readonly="type === 'look'" placeholder="如0.12表示12%，留空则使用产品佣金比率" />
        </el-form-item>
        <el-form-item label="计价公式">
          <el-input v-model="formData.pricingFormula" clearable :readonly="type === 'look'" placeholder="例如: basePrice + weight * pricePerKg" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="formData.sortOrder" :readonly="type === 'look'" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button v-show="type !== 'look'" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  createLogistics,
  deleteLogistics,
  deleteLogisticsByIds,
  updateLogistics,
  findLogistics,
  getLogisticsList,
  syncOzonLogistics,
  getShopWarehouseLogistics
} from '@/api/logistics'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict, filterDataSource, ReturnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { ref, reactive, computed, onMounted } from 'vue'
import { House } from '@element-plus/icons-vue'

defineOptions({
  name: 'Logistics'
})

// 新的数据结构
const shopList = ref([])
const activeShop = ref('')
const expandedWarehouses = ref([])
const searchInfo = ref({})
const multipleSelection = ref([])

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
  shopName: '',
  shopClientID: '',
  ozonDeliveryID: undefined,
  ozonWarehouseID: undefined,
  name: '',
  provider: '',
  serviceType: '',
  description: '',
  isActive: true,
  basePrice: undefined,
  pricePerKg: undefined,
  pricePerCubic: undefined,
  minWeight: undefined,
  maxWeight: undefined,
  estimatedDays: undefined,
  commissionRate: undefined,
  pricingFormula: '',
  formulaParams: undefined,
  sortOrder: 0,
  ozonData: undefined,
})

// 验证规则
const rule = reactive({})

const elSearchFormRef = ref()
const elFormRef = ref()

// 弹窗控制
const dialogFormVisible = ref(false)
const type = ref('')

// 获取店铺仓库物流数据
const getShopWarehouseData = async () => {
  try {
    const res = await getShopWarehouseLogistics()
    if (res.code === 0) {
      shopList.value = res.data || []
      if (shopList.value.length > 0 && !activeShop.value) {
        activeShop.value = shopList.value[0].name
      }
    }
  } catch (error) {
    console.error('获取店铺仓库数据失败:', error)
    ElMessage.error('获取数据失败')
  }
}

// 店铺切换
const handleShopChange = (tab) => {
  activeShop.value = tab.name
  expandedWarehouses.value = []
}

// 过滤物流方式
const getFilteredLogistics = (logistics) => {
  if (!logistics) return []

  return logistics.filter(item => {
    const nameMatch = !searchInfo.value.name || item.name?.includes(searchInfo.value.name)
    const providerMatch = !searchInfo.value.provider || item.provider?.includes(searchInfo.value.provider)
    const activeMatch = searchInfo.value.isActive === undefined ||
                       searchInfo.value.isActive === '' ||
                       item.isActive.toString() === searchInfo.value.isActive

    return nameMatch && providerMatch && activeMatch
  })
}

// 重置
const onReset = () => {
  searchInfo.value = {}
}

// 搜索
const onSubmit = () => {
  // 搜索时不需要重新获取数据，只需要触发过滤
}

// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 初始化数据
onMounted(() => {
  getShopWarehouseData()
})

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteLogisticsFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteLogisticsByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      // 重新获取数据
      getShopWarehouseData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
// const type = ref('') // 已在上面声明，删除重复声明

// 更新行
const updateLogisticsFunc = async(row) => {
  console.log('编辑物流方式:', row)
  try {
    const res = await findLogistics({ ID: row.ID })
    console.log('获取物流方式详情:', res)
    type.value = 'update'
    if (res.code === 0) {
      formData.value = res.data
      dialogFormVisible.value = true
    } else {
      ElMessage.error('获取物流方式详情失败: ' + res.msg)
    }
  } catch (error) {
    console.error('编辑物流方式失败:', error)
    ElMessage.error('编辑物流方式失败: ' + error.message)
  }
}

// 删除行
const deleteLogisticsFunc = async (row) => {
  const res = await deleteLogistics({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    // 重新获取数据
    getShopWarehouseData()
  }
}

// 弹窗控制标记
// const dialogFormVisible = ref(false) // 已在上面声明，删除重复声明

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    shopName: '',
    shopClientID: '',
    ozonDeliveryID: undefined,
    ozonWarehouseID: undefined,
    name: '',
    provider: '',
    serviceType: '',
    description: '',
    isActive: true,
    basePrice: undefined,
    pricePerKg: undefined,
    pricePerCubic: undefined,
    minWeight: undefined,
    maxWeight: undefined,
    estimatedDays: undefined,
    commissionRate: undefined,
    pricingFormula: '',
    formulaParams: undefined,
    sortOrder: 0,
    ozonData: undefined,
  }
}

// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createLogistics(formData.value)
        break
      case 'update':
        res = await updateLogistics(formData.value)
        break
      default:
        res = await createLogistics(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getShopWarehouseData()
    }
  })
}

// 查看详情控制标记
const detailFrom = ref({})

// 查看详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findLogistics({ ID: row.ID })
  if (res.code === 0) {
    formData.value = res.data
    type.value = 'look'
    dialogFormVisible.value = true
  }
}

// 同步Ozon物流信息
const syncOzonLogisticsFunc = async () => {
  ElMessageBox.confirm('确定要同步Ozon物流信息吗？这将从所有Ozon店铺拉取最新的物流方式信息。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(async () => {
    const loading = ElLoading.service({
      lock: true,
      text: '正在同步Ozon物流信息...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      const res = await syncOzonLogistics()
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '同步成功'
        })
        // 重新获取数据
        getShopWarehouseData()
      }
    } catch (error) {
      ElMessage({
        type: 'error',
        message: '同步失败: ' + error.message
      })
    } finally {
      loading.close()
    }
  })
}
</script>

<style scoped>
.text-gray-400 {
  color: #9ca3af;
}

.shop-tabs {
  margin-top: 20px;
}

.warehouse-container {
  padding: 10px 0;
}

.warehouse-title {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.warehouse-icon {
  color: #409eff;
}

.warehouse-name {
  font-weight: 500;
  flex: 1;
}

.logistics-count {
  margin-left: auto;
}

.warehouse-status {
  margin-left: 10px;
}

.no-logistics, .no-warehouses {
  padding: 40px 0;
  text-align: center;
}

.el-collapse-item__content {
  padding-bottom: 0;
}

.el-table {
  margin-top: 10px;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.gap-1 {
  gap: 4px;
}
</style>