<template>
  <div>
    <!-- 标签页切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="月度统计" name="monthly">
        <!-- 月度统计搜索框 -->
        <div class="gva-search-box">
          <el-form ref="elMonthlySearchFormRef" :inline="true" :model="monthlySearchInfo" class="demo-form-inline">
            <el-form-item label="年份">
              <el-select v-model="monthlySearchInfo.year" placeholder="选择年份" style="width: 120px">
                <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
              </el-select>
            </el-form-item>
            <el-form-item label="月份">
              <el-select v-model="monthlySearchInfo.month" placeholder="选择月份" style="width: 120px">
                <el-option label="全部" :value="null" />
                <el-option v-for="month in monthOptions" :key="month.value" :label="month.label" :value="month.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="店铺名称">
              <el-input v-model="monthlySearchInfo.shopName" placeholder="搜索条件" style="width: 200px" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="search" @click="onMonthlySubmit">查询</el-button>
              <el-button icon="refresh" @click="onMonthlyReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 月度统计表格 -->
        <div class="gva-table-box">
          <el-table
            ref="monthlyTable"
            style="width: 100%"
            tooltip-effect="dark"
            :data="monthlyTableData"
            row-key="month"
          >
            <el-table-column align="center" label="月份" prop="month" width="100" />
            <el-table-column align="center" label="订单数量" prop="orderCount" width="100" />
            <el-table-column align="center" label="总销售额" prop="totalSales" width="120">
              <template #default="scope">
                <span style="color: #0369a1; font-weight: 500;">
                  ¥{{ parseFloat(scope.row.totalSales || 0).toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="广告费用" prop="advertisingCost" width="120">
              <template #default="scope">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <span style="color: #dc2626; font-weight: 500; margin-right: 8px;">
                    ¥{{ parseFloat(scope.row.advertisingCost || 0).toFixed(2) }}
                  </span>
                  <el-button
                    type="primary"
                    link
                    icon="edit"
                    size="small"
                    @click="() => editAdvertising(scope.row.month, scope.row.advertisingCost)"
                  >
                    编辑
                  </el-button>
                </div>
              </template>
            </el-table-column>
            <el-table-column align="center" label="总成本" prop="totalCosts" width="120">
              <template #default="scope">
                <span style="color: #7c2d12; font-weight: 500;" :title="`成本价总计: ¥${parseFloat(scope.row.totalCosts || 0).toFixed(2)}`">
                  ¥{{ parseFloat(scope.row.totalCosts || 0).toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="物流费用" prop="totalShipping" width="120">
              <template #default="scope">
                <span style="color: #a16207; font-weight: 500;">
                  ¥{{ parseFloat(scope.row.totalShipping || 0).toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="佣金" prop="totalCommission" width="120">
              <template #default="scope">
                <span style="color: #9333ea; font-weight: 500;">
                  ¥{{ parseFloat(scope.row.totalCommission || 0).toFixed(2) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="净利润(¥)" prop="totalProfit" width="120">
              <template #default="scope">
                <span :style="{ color: scope.row.totalProfit >= 0 ? '#16a34a' : '#dc2626', fontWeight: '600' }">
                  ¥{{ parseFloat(scope.row.totalProfit || 0).toFixed(2) }}
                </span>
              </template>
            </el-table-column>

            <el-table-column align="center" label="利润率" prop="profitRate" width="100">
              <template #default="scope">
                <span :style="{ color: scope.row.profitRate >= 0 ? '#16a34a' : '#dc2626', fontWeight: '600' }">
                  {{ parseFloat(scope.row.profitRate || 0).toFixed(1) }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="120">
              <template #default="scope">
                <el-button
                  type="success"
                  link
                  icon="download"
                  size="small"
                  @click="() => exportMonthlyData(scope.row.month)"
                  :loading="exportLoading"
                >
                  导出Excel
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="gva-pagination">
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="monthlySearchInfo.page"
              :page-size="monthlySearchInfo.pageSize"
              :page-sizes="[10, 25, 50, 100]"
              :total="monthlyTotal"
              @current-change="handleMonthlyCurrentChange"
              @size-change="handleMonthlySizeChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="详细统计" name="detail">
        <!-- 详细统计搜索框 -->
        <div class="gva-search-box">
          <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker v-model="searchInfo.startDate" type="date" placeholder="选择开始日期"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker v-model="searchInfo.endDate" type="date" placeholder="选择结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item label="店铺名称" prop="shopName">
              <el-input v-model="searchInfo.shopName" placeholder="搜索条件" />
            </el-form-item>
            <el-form-item label="产品名称" prop="productName">
              <el-input v-model="searchInfo.productName" placeholder="搜索条件" />
            </el-form-item>
            <el-form-item label="SKU" prop="sku">
              <el-input v-model="searchInfo.sku" placeholder="搜索条件" />
            </el-form-item>
            <el-form-item label="货件号" prop="postingNumber">
              <el-input v-model="searchInfo.postingNumber" placeholder="搜索条件" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
              <el-button icon="refresh" @click="onReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 利润汇总卡片 -->
    <div class="gva-card-box">
      <el-row :gutter="20">
        <el-col :span="4">
          <el-card class="profit-card">
            <div class="profit-item">
              <div class="profit-label">总订单数</div>
              <div class="profit-value">{{ profitSummary.totalOrders }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="profit-card">
            <div class="profit-item">
              <div class="profit-label">总销售额</div>
              <div class="profit-value">¥{{ profitSummary.totalSales?.toFixed(2) || '0.00' }}</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="profit-card">
            <div class="profit-item">
              <div class="profit-label">总成本</div>
              <div class="profit-value" style="color: #7c2d12;" :title="`仅包含成本价，不含运费佣金等`">
                ¥{{ profitSummary.totalCosts?.toFixed(2) || '0.00' }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="profit-card">
            <div class="profit-item">
              <div class="profit-label">总净利润(¥)</div>
              <div class="profit-value" :class="profitSummary.totalNetProfit >= 0 ? 'profit-positive' : 'profit-negative'">
                ¥{{ profitSummary.totalNetProfit?.toFixed(2) || '0.00' }}
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="4">
          <el-card class="profit-card">
            <div class="profit-item">
              <div class="profit-label">平均利润率</div>
              <div class="profit-value" :class="profitSummary.avgProfitMargin >= 0 ? 'profit-positive' : 'profit-negative'">
                {{ profitSummary.avgProfitMargin?.toFixed(2) || '0.00' }}%
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 成本计算说明 -->
    <div class="gva-card-box">
      <el-alert
        title="利润统计说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <p style="margin: 0; font-size: 14px;">
            <strong>总成本</strong> = 产品成本价 × 数量<br>
            <strong>订单时间</strong> = 订单同步到系统的时间（接近实际下单时间）<br>
            <span style="color: #666; font-size: 12px;">
              注：总成本不包含运费、佣金等费用，因为这些费用由Ozon平台自动扣除，无需单独支付
            </span>
          </p>
        </template>
      </el-alert>
    </div>

    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openCalculateDialog">计算利润</el-button>
        <el-button type="success" icon="refresh" @click="refreshProfitDataHandler" :loading="refreshLoading">刷新汇总</el-button>
        <el-button type="danger" icon="delete" @click="openDeleteConfirmDialog" :loading="deleteLoading">删除所有数据</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
      >
        <el-table-column align="left" label="日期" prop="orderDate" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.orderDate) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="店铺名称" prop="shopName" width="120" />
        <el-table-column align="left" label="货件号" prop="postingNumber" width="150" />
        <el-table-column align="left" label="订单号" prop="orderNumber" width="150" />
        <el-table-column align="left" label="产品信息" width="200">
          <template #default="scope">
            <div>
              <div><strong>{{ scope.row.productName }}</strong></div>
              <div class="text-sm text-gray-500">SKU: {{ scope.row.sku }}</div>
              <div class="text-sm text-gray-500">数量: {{ scope.row.quantity }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="销售价格" prop="salePrice" width="100">
          <template #default="scope">
            ¥{{ scope.row.salePrice?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="成本价格" prop="costPrice" width="100">
          <template #default="scope">
            ¥{{ scope.row.costPrice?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="佣金" prop="commission" width="100">
          <template #default="scope">
            <span style="color: #9333ea; font-weight: 500;">
              ¥{{ scope.row.commission?.toFixed(2) || '0.00' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="物流费用" prop="shippingCost" width="100">
          <template #default="scope">
            <span style="color: #a16207; font-weight: 500;">
              ¥{{ scope.row.shippingCost?.toFixed(2) || '0.00' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="净利润(¥)" prop="netProfit" width="100">
          <template #default="scope">
            <span :class="scope.row.netProfit >= 0 ? 'profit-positive' : 'profit-negative'">
              ¥{{ scope.row.netProfit?.toFixed(2) || '0.00' }}
            </span>
          </template>
        </el-table-column>

        <el-table-column align="left" label="利润率" prop="profitMargin" width="100">
          <template #default="scope">
            <span :class="scope.row.profitMargin >= 0 ? 'profit-positive' : 'profit-negative'">
              {{ scope.row.profitMargin?.toFixed(2) || '0.00' }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="状态" prop="status" width="100" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 计算利润对话框 -->
    <el-dialog v-model="calculateDialogVisible" title="计算订单利润" width="500px">
      <!-- 利润计算公式说明 -->
      <el-alert
        title="利润计算公式：净利润 = 售价 - (成本价 + 运费 + 佣金 + 其他费用)"
        type="info"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>
      <el-form ref="calculateFormRef" :model="calculateForm" :rules="calculateRules" label-width="100px">
        <el-form-item label="货件号" prop="postingNumber">
          <el-input v-model="calculateForm.postingNumber" placeholder="请输入货件号" />
        </el-form-item>
        <el-form-item label="成本价格" prop="costPrice">
          <el-input-number v-model="calculateForm.costPrice" :precision="2" :min="0" placeholder="请输入成本价格" />
        </el-form-item>
        <el-form-item label="佣金" prop="commission">
          <el-input-number v-model="calculateForm.commission" :precision="2" :min="0" placeholder="请输入佣金" />
        </el-form-item>
        <el-form-item label="运费" prop="shippingCost">
          <el-input-number v-model="calculateForm.shippingCost" :precision="2" :min="0" placeholder="请输入运费" />
        </el-form-item>
        <el-form-item label="其他费用" prop="otherCosts">
          <el-input-number v-model="calculateForm.otherCosts" :precision="2" :min="0" placeholder="请输入其他费用" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="calculateDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCalculate">确定</el-button>
        </div>
      </template>
    </el-dialog>

      </el-tab-pane>
    </el-tabs>

    <!-- 广告费编辑对话框 -->
    <el-dialog v-model="advertisingDialogVisible" title="编辑广告费用" width="500px">
      <el-form ref="advertisingFormRef" :model="advertisingForm" :rules="advertisingRules" label-width="100px">
        <el-form-item label="月份" prop="month">
          <el-input v-model="advertisingForm.month" disabled />
        </el-form-item>
        <el-form-item label="广告费用" prop="advertisingCost">
          <el-input-number
            v-model="advertisingForm.advertisingCost"
            :precision="2"
            :min="0"
            placeholder="请输入广告费用"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="notes">
          <el-input
            v-model="advertisingForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="advertisingDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdvertising">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 删除确认对话框 -->
    <el-dialog v-model="deleteConfirmDialogVisible" title="删除确认" width="500px">
      <el-alert
        title="警告：此操作将永久删除所有利润数据和广告费用记录，且无法恢复！"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px;">
      </el-alert>
      <p style="margin-bottom: 20px; color: #606266;">
        确定要删除所有利润数据吗？这将包括：
      </p>
      <ul style="margin-bottom: 20px; color: #606266; padding-left: 20px;">
        <li>所有订单利润记录</li>
        <li>所有月度广告费用记录</li>
      </ul>
      <p style="color: #F56C6C; font-weight: bold;">
        此操作不可撤销，请谨慎操作！
      </p>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deleteConfirmDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDeleteAllData" :loading="deleteLoading">确认删除</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  getProfitList,
  getProfitSummary,
  calculateOrderProfit,
  getMonthlyProfitList,
  setMonthlyAdvertising,
  getMonthlyAdvertising,
  refreshProfitData,
  deleteAllProfitData,
  exportMonthlyOrdersDetail
} from '@/api/profit.js'

import { ElMessage } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'

defineOptions({
  name: 'OrderProfit'
})

const elSearchFormRef = ref()
const elMonthlySearchFormRef = ref()
const calculateFormRef = ref()
const advertisingFormRef = ref()

// 标签页控制
const activeTab = ref('monthly')

// 刷新汇总相关
const refreshLoading = ref(false)

// 删除相关
const deleteLoading = ref(false)
const deleteConfirmDialogVisible = ref(false)

// 导出相关
const exportLoading = ref(false)

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])

const searchInfo = ref({})
const searchRule = reactive({})

// =========== 月度统计部分 ===========
const monthlyTableData = ref([])
const monthlyTotal = ref(0)
const monthlySearchInfo = ref({
  page: 1,
  pageSize: 10,
  year: new Date().getFullYear(),
  month: null,
  shopName: ''
})

// 年份选项
const yearOptions = ref([])
for (let i = 2020; i <= new Date().getFullYear() + 1; i++) {
  yearOptions.value.push(i)
}

// 月份选项
const monthOptions = ref([
  { label: '1月', value: 1 },
  { label: '2月', value: 2 },
  { label: '3月', value: 3 },
  { label: '4月', value: 4 },
  { label: '5月', value: 5 },
  { label: '6月', value: 6 },
  { label: '7月', value: 7 },
  { label: '8月', value: 8 },
  { label: '9月', value: 9 },
  { label: '10月', value: 10 },
  { label: '11月', value: 11 },
  { label: '12月', value: 12 }
])

// 广告费编辑对话框
const advertisingDialogVisible = ref(false)
const advertisingForm = ref({
  month: '',
  advertisingCost: 0,
  notes: ''
})

const advertisingRules = reactive({
  advertisingCost: [
    { required: true, message: '请输入广告费用', trigger: 'blur' }
  ]
})

// 利润汇总数据
const profitSummary = ref({
  totalOrders: 0,
  totalSales: 0,
  totalNetProfit: 0,
  avgProfitMargin: 0
})

// 计算利润对话框
const calculateDialogVisible = ref(false)
const calculateForm = ref({
  postingNumber: '',
  costPrice: 0,
  commission: 0,
  shippingCost: 0,
  otherCosts: 0
})

const calculateRules = reactive({
  postingNumber: [
    { required: true, message: '请输入货件号', trigger: 'blur' }
  ]
})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getProfitList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
  }
}

// 加载利润汇总
const loadProfitSummary = async() => {
  const result = await getProfitSummary(searchInfo.value)
  if (result.code === 0) {
    profitSummary.value = result.data
  }
}

// 打开计算对话框
const openCalculateDialog = () => {
  calculateForm.value = {
    postingNumber: '',
    costPrice: 0,
    commission: 0,
    shippingCost: 0,
    otherCosts: 0
  }
  calculateDialogVisible.value = true
}

// 提交计算
const submitCalculate = async() => {
  calculateFormRef.value?.validate(async(valid) => {
    if (!valid) return
    
    const result = await calculateOrderProfit(calculateForm.value)
    if (result.code === 0) {
      ElMessage.success('利润计算成功')
      calculateDialogVisible.value = false
      getTableData()
      loadProfitSummary()
    } else {
      ElMessage.error(result.msg || '计算失败')
    }
  })
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('zh-CN')
}

// =========== 月度统计方法 ===========
// 获取月度统计数据
const getMonthlyTableData = async() => {
  const res = await getMonthlyProfitList(monthlySearchInfo.value)
  if (res.code === 0) {
    monthlyTableData.value = res.data.list
    monthlyTotal.value = res.data.total
  }
}

// 月度搜索
const onMonthlySubmit = () => {
  monthlySearchInfo.value.page = 1
  getMonthlyTableData()
}

// 月度重置
const onMonthlyReset = () => {
  monthlySearchInfo.value = {
    page: 1,
    pageSize: 10,
    year: new Date().getFullYear(),
    month: null,
    shopName: ''
  }
  getMonthlyTableData()
}

// 月度分页
const handleMonthlyCurrentChange = (val) => {
  monthlySearchInfo.value.page = val
  getMonthlyTableData()
}

const handleMonthlySizeChange = (val) => {
  monthlySearchInfo.value.pageSize = val
  getMonthlyTableData()
}

// 标签页切换
const handleTabClick = (tab) => {
  if (tab.name === 'monthly') {
    getMonthlyTableData()
  } else if (tab.name === 'detail') {
    getTableData()
    loadProfitSummary()
  }
}

// =========== 广告费管理方法 ===========
// 编辑广告费用
const editAdvertising = async(month, currentCost) => {
  advertisingForm.value = {
    month: month,
    advertisingCost: currentCost || 0,
    notes: ''
  }

  // 获取当前广告费用详情
  try {
    const res = await getMonthlyAdvertising({ month: month })
    if (res.code === 0 && res.data) {
      advertisingForm.value.advertisingCost = res.data.advertisingCost || 0
      advertisingForm.value.notes = res.data.notes || ''
    }
  } catch (error) {
    console.log('获取广告费用详情失败:', error)
  }

  advertisingDialogVisible.value = true
}

// 提交广告费用
const submitAdvertising = async() => {
  advertisingFormRef.value?.validate(async(valid) => {
    if (!valid) return

    try {
      const res = await setMonthlyAdvertising(advertisingForm.value)
      if (res.code === 0) {
        ElMessage.success('设置成功，净利润和利润率已重新计算')
        advertisingDialogVisible.value = false

        // 刷新所有相关数据
        if (activeTab.value === 'monthly') {
          getMonthlyTableData() // 刷新月度统计数据
        } else if (activeTab.value === 'detail') {
          getTableData() // 刷新详细统计数据
          loadProfitSummary() // 刷新利润汇总数据
        }
      } else {
        ElMessage.error(res.msg || '设置失败')
      }
    } catch (error) {
      ElMessage.error('设置失败: ' + error.message)
    }
  })
}

// 刷新利润汇总数据
const refreshProfitDataHandler = async() => {
  refreshLoading.value = true
  try {
    const res = await refreshProfitData()
    if (res.code === 0) {
      ElMessage.success(res.msg || '刷新成功')
      // 刷新当前显示的数据
      if (activeTab.value === 'monthly') {
        getMonthlyTableData()
      } else if (activeTab.value === 'detail') {
        getTableData()
        loadProfitSummary()
      }
    } else {
      ElMessage.error(res.msg || '刷新失败')
    }
  } catch (error) {
    ElMessage.error('刷新失败: ' + error.message)
  } finally {
    refreshLoading.value = false
  }
}

// 打开删除确认对话框
const openDeleteConfirmDialog = () => {
  deleteConfirmDialogVisible.value = true
}

// 确认删除所有数据
const confirmDeleteAllData = async() => {
  deleteLoading.value = true
  try {
    const res = await deleteAllProfitData()
    if (res.code === 0) {
      ElMessage.success(res.msg || '删除成功')
      deleteConfirmDialogVisible.value = false
      // 刷新当前显示的数据
      if (activeTab.value === 'monthly') {
        getMonthlyTableData()
      } else {
        getTableData()
        loadProfitSummary()
      }
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    ElMessage.error('删除失败: ' + error.message)
  } finally {
    deleteLoading.value = false
  }
}

// 导出月度数据
const exportMonthlyData = async(month) => {
  exportLoading.value = true
  try {
    const res = await exportMonthlyOrdersDetail(month)

    // 创建下载链接
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `月度订单详细数据_${month}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + (error.message || '未知错误'))
  } finally {
    exportLoading.value = false
  }
}

// 初始化
onMounted(() => {
  if (activeTab.value === 'monthly') {
    getMonthlyTableData()
  } else if (activeTab.value === 'detail') {
    getTableData()
    loadProfitSummary()
  }
})
</script>

<style scoped>
.gva-card-box {
  margin-bottom: 20px;
}

.profit-card {
  text-align: center;
}

.profit-item {
  padding: 10px;
}

.profit-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.profit-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.profit-positive {
  color: #67c23a;
}

.profit-negative {
  color: #f56c6c;
}

.text-sm {
  font-size: 12px;
}

.text-gray-500 {
  color: #6b7280;
}


</style>
