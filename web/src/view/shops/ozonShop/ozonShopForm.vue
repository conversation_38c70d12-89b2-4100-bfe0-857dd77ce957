
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="店铺名字:" prop="name">
          <el-input v-model="formData.name" :clearable="true"  placeholder="请输入店铺名字" />
       </el-form-item>
        <el-form-item label="店铺ID:" prop="clientID">
          <el-input v-model="formData.clientID" :clearable="true"  placeholder="请输入店铺ID" />
       </el-form-item>
        <el-form-item label="APIKey字段:" prop="APIKey">
          <el-input v-model="formData.APIKey" :clearable="true"  placeholder="请输入APIKey字段" />
       </el-form-item>
        <el-form-item label="店铺类型:" prop="shopType">
           <el-select v-model="formData.shopType" placeholder="请选择店铺类型" style="width:100%" :clearable="true" >
              <el-option v-for="(item,key) in Store_TypeOptions" :key="key" :label="item.label" :value="item.value" />
           </el-select>
       </el-form-item>
        <el-form-item label="结算货币:" prop="currency">
           <el-select v-model="formData.currency" placeholder="请选择结算货币" style="width:100%" :clearable="true" >
              <el-option label="人民币 (CNY)" value="CNY" />
              <el-option label="美元 (USD)" value="USD" />
              <el-option label="欧元 (EUR)" value="EUR" />
           </el-select>
       </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createOzonShop,
  updateOzonShop,
  findOzonShop
} from '@/api/shops/ozonShop'

defineOptions({
    name: 'OzonShopForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const Store_TypeOptions = ref([])
const formData = ref({
            name: '',
            clientID: '',
            APIKey: '',
            shopType: '',
            currency: 'CNY', // 默认人民币
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findOzonShop({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
    Store_TypeOptions.value = await getDictFunc('Store_Type')
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createOzonShop(formData.value)
               break
             case 'update':
               res = await updateOzonShop(formData.value)
               break
             default:
               res = await createOzonShop(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
