
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="收件人联系方式:" prop="addressee">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取
          {{ formData.addressee }}
       </el-form-item>
        <el-form-item label="分析数据:" prop="analytics_data">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取
          {{ formData.analytics_data }}
       </el-form-item>
        <el-form-item label="买家信息:" prop="customer">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取
          {{ formData.customer }}
       </el-form-item>
        <el-form-item label="货件条码:" prop="barcodes">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取
          {{ formData.barcodes }}
       </el-form-item>
        <el-form-item label="货件交付物流的时间:" prop="delivering_date">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取
          {{ formData.delivering_date }}
       </el-form-item>
        <el-form-item label="快递方式:" prop="delivery_method">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取
          {{ formData.delivery_method }}
       </el-form-item>
        <el-form-item label="有关商品成本、折扣幅度、付款和佣金的信息:" prop="financial_data">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取
          {{ formData.financial_data }}
       </el-form-item>
        <el-form-item label="开始处理货件的日期和时间:" prop="in_process_at">
          <el-date-picker v-model="formData.in_process_at" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="如果使用快速物流 Ozon Express —— true:" prop="is_express">
          <el-switch v-model="formData.is_express" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
       </el-form-item>
        <el-form-item label="带有附加特征的商品列表:" prop="optional">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取
          {{ formData.optional }}
       </el-form-item>
        <el-form-item label="货件所属订单的ID:" prop="order_id">
          <el-input v-model.number="formData.order_id" :clearable="true" placeholder="请输入" />
       </el-form-item>
        <el-form-item label="货件所属的订单号:" prop="order_number">
          <el-input v-model="formData.order_number" :clearable="true"  placeholder="请输入货件所属的订单号" />
       </el-form-item>
        <el-form-item label="快递母件编号，从该母件中拆分出了当前货件:" prop="parent_posting_number">
          <el-input v-model="formData.parent_posting_number" :clearable="true"  placeholder="请输入快递母件编号，从该母件中拆分出了当前货件" />
       </el-form-item>
        <el-form-item label="货件号:" prop="posting_number">
          <el-input v-model="formData.posting_number" :clearable="true"  placeholder="请输入货件号" />
       </el-form-item>
        <el-form-item label="货运商品列表:" prop="products">
          <ArrayCtrl v-model="formData.products" editable/>
       </el-form-item>
        <el-form-item label="需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:" prop="requirements">
          // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取
          {{ formData.requirements }}
       </el-form-item>
        <el-form-item label="必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:" prop="shipment_date">
          <el-date-picker v-model="formData.shipment_date" type="date" placeholder="选择日期" :clearable="true"></el-date-picker>
       </el-form-item>
        <el-form-item label="货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:" prop="status">
          <el-input v-model="formData.status" :clearable="true"  placeholder="请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。" />
       </el-form-item>
        <el-form-item label="发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:" prop="substatus">
          <el-input v-model="formData.substatus" :clearable="true"  placeholder="请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。" />
       </el-form-item>
        <el-form-item label="快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:" prop="tpl_integration_type">
          <el-input v-model="formData.tpl_integration_type" :clearable="true"  placeholder="请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。" />
       </el-form-item>
        <el-form-item label="货件跟踪号:" prop="tracking_number">
          <el-input v-model="formData.tracking_number" :clearable="true"  placeholder="请输入货件跟踪号" />
       </el-form-item>
        <el-form-item label="发运的计费信息:" prop="tariffication	">
          <ArrayCtrl v-model="formData.tariffication	" editable/>
       </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createOzoneOrderDetail,
  updateOzoneOrderDetail,
  findOzoneOrderDetail
} from '@/api/ozoneOrder/ozoneOrderDetail'

defineOptions({
    name: 'OzoneOrderDetailForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            addressee: {},
            analytics_data: {},
            customer: {},
            barcodes: {},
            delivering_date: {},
            delivery_method: {},
            financial_data: {},
            in_process_at: new Date(),
            is_express: false,
            optional: {},
            order_id: undefined,
            order_number: '',
            parent_posting_number: '',
            posting_number: '',
            products: [],
            requirements: {},
            shipment_date: new Date(),
            status: '',
            substatus: '',
            tpl_integration_type: '',
            tracking_number: '',
            tariffication	: [],
        })
// 验证规则
const rule = reactive({
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findOzoneOrderDetail({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createOzoneOrderDetail(formData.value)
               break
             case 'update':
               res = await updateOzoneOrderDetail(formData.value)
               break
             default:
               res = await createOzoneOrderDetail(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
