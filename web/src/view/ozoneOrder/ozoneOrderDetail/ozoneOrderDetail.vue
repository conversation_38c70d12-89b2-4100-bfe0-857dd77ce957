
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAt">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>
      <el-date-picker v-model="searchInfo.startCreatedAt" type="datetime" placeholder="开始日期" :disabled-date="time=> searchInfo.endCreatedAt ? time.getTime() > searchInfo.endCreatedAt.getTime() : false"></el-date-picker>
       —
      <el-date-picker v-model="searchInfo.endCreatedAt" type="datetime" placeholder="结束日期" :disabled-date="time=> searchInfo.startCreatedAt ? time.getTime() < searchInfo.startCreatedAt.getTime() : false"></el-date-picker>
      </el-form-item>
      

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">同步订单</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        >
        <el-table-column type="selection" width="55" />
        
        <el-table-column align="left" label="日期" prop="createdAt"width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        
          <el-table-column label="收件人联系方式" prop="addressee" width="200">
              <template #default="scope">
                  [JSON]
              </template>
          </el-table-column>
          <el-table-column label="分析数据" prop="analytics_data" width="200">
              <template #default="scope">
                  [JSON]
              </template>
          </el-table-column>
          <el-table-column label="买家信息" prop="customer" width="200">
              <template #default="scope">
                  [JSON]
              </template>
          </el-table-column>
          <el-table-column label="货件条码" prop="barcodes" width="200">
              <template #default="scope">
                  [JSON]
              </template>
          </el-table-column>
         
          <el-table-column label="快递方式" prop="delivery_method" width="200">
              <template #default="scope">
                  [JSON]
              </template>
          </el-table-column>       
       
          <el-table-column align="left" label="货件所属订单的ID" prop="order_id" width="120" />
          <el-table-column align="left" label="货件所属的订单号" prop="order_number" width="120" />
          <el-table-column align="left" label="货件号" prop="posting_number" width="120" />
           <el-table-column label="货运商品列表" prop="products" width="200">
               <template #default="scope">
                  <ArrayCtrl v-model="scope.row.products"/>
               </template>
           </el-table-column>
        
          
        <el-table-column align="left" label="操作" fixed="right" :min-width="appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateOzoneOrderDetailFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
       <template #header>
              <div class="flex justify-between items-center">
                <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
                <div>
                  <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                  <el-button @click="closeDialog">取 消</el-button>
                </div>
              </div>
            </template>

          <el-form :model="formData" label-position="top" ref="elFormRef" :rules="rule" label-width="80px">
            <el-form-item label="收件人联系方式:"  prop="addressee" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.addressee 后端会按照json的类型进行存取
              {{ formData.addressee }}
            </el-form-item>
            <el-form-item label="分析数据:"  prop="analytics_data" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.analytics_data 后端会按照json的类型进行存取
              {{ formData.analytics_data }}
            </el-form-item>
            <el-form-item label="买家信息:"  prop="customer" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.customer 后端会按照json的类型进行存取
              {{ formData.customer }}
            </el-form-item>
            <el-form-item label="货件条码:"  prop="barcodes" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.barcodes 后端会按照json的类型进行存取
              {{ formData.barcodes }}
            </el-form-item>
            <el-form-item label="货件交付物流的时间:"  prop="delivering_date" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivering_date 后端会按照json的类型进行存取
              {{ formData.delivering_date }}
            </el-form-item>
            <el-form-item label="快递方式:"  prop="delivery_method" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.delivery_method 后端会按照json的类型进行存取
              {{ formData.delivery_method }}
            </el-form-item>
            <el-form-item label="有关商品成本、折扣幅度、付款和佣金的信息:"  prop="financial_data" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.financial_data 后端会按照json的类型进行存取
              {{ formData.financial_data }}
            </el-form-item>
            <el-form-item label="开始处理货件的日期和时间:"  prop="in_process_at" >
              <el-date-picker v-model="formData.in_process_at" type="date" style="width:100%" placeholder="选择日期" :clearable="true"  />
            </el-form-item>
            <el-form-item label="如果使用快速物流 Ozon Express —— true:"  prop="is_express" >
              <el-switch v-model="formData.is_express" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
            </el-form-item>
            <el-form-item label="带有附加特征的商品列表:"  prop="optional" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.optional 后端会按照json的类型进行存取
              {{ formData.optional }}
            </el-form-item>
            <el-form-item label="货件所属订单的ID:"  prop="order_id" >
              <el-input v-model.number="formData.order_id" :clearable="true" placeholder="请输入货件所属订单的ID" />
            </el-form-item>
            <el-form-item label="货件所属的订单号:"  prop="order_number" >
              <el-input v-model="formData.order_number" :clearable="true"  placeholder="请输入货件所属的订单号" />
            </el-form-item>
            <el-form-item label="快递母件编号，从该母件中拆分出了当前货件:"  prop="parent_posting_number" >
              <el-input v-model="formData.parent_posting_number" :clearable="true"  placeholder="请输入快递母件编号，从该母件中拆分出了当前货件" />
            </el-form-item>
            <el-form-item label="货件号:"  prop="posting_number" >
              <el-input v-model="formData.posting_number" :clearable="true"  placeholder="请输入货件号" />
            </el-form-item>
            <el-form-item label="货运商品列表:"  prop="products" >
              <ArrayCtrl v-model="formData.products" editable/>
            </el-form-item>
            <el-form-item label="需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态:"  prop="requirements" >
              // 此字段为json结构，可以前端自行控制展示和数据绑定模式 需绑定json的key为 formData.requirements 后端会按照json的类型进行存取
              {{ formData.requirements }}
            </el-form-item>
            <el-form-item label="必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段:"  prop="shipment_date" >
              <el-date-picker v-model="formData.shipment_date" type="date" style="width:100%" placeholder="选择日期" :clearable="true"  />
            </el-form-item>
            <el-form-item label="货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。:"  prop="status" >
              <el-input v-model="formData.status" :clearable="true"  placeholder="请输入货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。" />
            </el-form-item>
            <el-form-item label="发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。:"  prop="substatus" >
              <el-input v-model="formData.substatus" :clearable="true"  placeholder="请输入发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。" />
            </el-form-item>
            <el-form-item label="快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。:"  prop="tpl_integration_type" >
              <el-input v-model="formData.tpl_integration_type" :clearable="true"  placeholder="请输入快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。" />
            </el-form-item>
            <el-form-item label="货件跟踪号:"  prop="tracking_number" >
              <el-input v-model="formData.tracking_number" :clearable="true"  placeholder="请输入货件跟踪号" />
            </el-form-item>
            <el-form-item label="发运的计费信息:"  prop="tariffication	" >
              <ArrayCtrl v-model="formData.tariffication	" editable/>
            </el-form-item>
          </el-form>
    </el-drawer>

    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="收件人联系方式">
                        {{ detailFrom.addressee }}
                    </el-descriptions-item>
                    <el-descriptions-item label="分析数据">
                        {{ detailFrom.analytics_data }}
                    </el-descriptions-item>
                    <el-descriptions-item label="买家信息">
                        {{ detailFrom.customer }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件条码">
                        {{ detailFrom.barcodes }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件交付物流的时间">
                        {{ detailFrom.delivering_date }}
                    </el-descriptions-item>
                    <el-descriptions-item label="快递方式">
                        {{ detailFrom.delivery_method }}
                    </el-descriptions-item>
                    <el-descriptions-item label="有关商品成本、折扣幅度、付款和佣金的信息">
                        {{ detailFrom.financial_data }}
                    </el-descriptions-item>
                    <el-descriptions-item label="开始处理货件的日期和时间">
                        {{ detailFrom.in_process_at }}
                    </el-descriptions-item>
                    <el-descriptions-item label="如果使用快速物流 Ozon Express —— true">
                        {{ detailFrom.is_express }}
                    </el-descriptions-item>
                    <el-descriptions-item label="带有附加特征的商品列表">
                        {{ detailFrom.optional }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件所属订单的ID">
                        {{ detailFrom.order_id }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件所属的订单号">
                        {{ detailFrom.order_number }}
                    </el-descriptions-item>
                    <el-descriptions-item label="快递母件编号，从该母件中拆分出了当前货件">
                        {{ detailFrom.parent_posting_number }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件号">
                        {{ detailFrom.posting_number }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货运商品列表">
                            <ArrayCtrl v-model="detailFrom.products"/>
                    </el-descriptions-item>
                    <el-descriptions-item label="需要上传商品制造国、货运报关单号（Cargo Customs Declaration）、商品批次注册号（Product Batch Registration Number）和“诚信标志”标签的产品列表，以更新货件状态">
                        {{ detailFrom.requirements }}
                    </el-descriptions-item>
                    <el-descriptions-item label="必须收取货件的日期和时间。 超出该时间后将适用新费率，相关信息请查看字段">
                        {{ detailFrom.shipment_date }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货运状态:  acceptance_in_progress —— 正在验收， arbitration —— 仲裁， awaiting_approve —— 等待确认， awaiting_deliver —— 等待装运， awaiting_packaging —— 等待包装， awaiting_registration —— 等待注册， awaiting_verification —— 已创建， cancelled —— 已取消， cancelled_from_split_pending——因货件拆分而取消， client_arbitration —— 快递客户仲裁， delivering —— 运输中， driver_pickup —— 司机处， not_accepted —— 分拣中心未接受， sent_by_seller —— 由卖家发送。">
                        {{ detailFrom.status }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发货子状态：  posting_acceptance_in_progress —— 正在验收， posting_in_arbitration —— 仲裁， posting_created —— 已创建， posting_in_carriage —— 在运输途中， posting_not_in_carriage —— 未在运输中， posting_registered —— 已登记， posting_transferring_to_delivery (status=awaiting_deliver) —— 移交给快递， posting_awaiting_passport_data —— 等待护照资料， posting_created —— 已创建， posting_awaiting_registration —— 等待注册， posting_registration_error —— 注册错误， posting_transferring_to_delivery (status=awaiting_registration) —— 交给快递员, posting_split_pending —— 已创建， posting_canceled —— 已取消， posting_in_client_arbitration —— 快递会员仲裁， posting_delivered —— 已送达， posting_received —— 已收到， posting_conditionally_delivered —— 暂时送到， posting_in_courier_service —— 快递员正在路上， posting_in_pickup_point —— 在取货点， posting_on_way_to_city —— 发往城市途中， posting_on_way_to_pickup_point —— 正发往取货点， posting_returned_to_warehouse —— 返回仓库， posting_transferred_to_courier_service —— 转交给快递员， posting_driver_pick_up —— 在司机那儿， posting_not_in_sort_center —— 集散中心未收到， sent_by_seller —— 由卖家发送。">
                        {{ detailFrom.substatus }}
                    </el-descriptions-item>
                    <el-descriptions-item label="快递服务集成类型：  ozon —— Ozon 快递服务。 3pl_tracking —— 集成服务快递。 non_integrated —— 第三方物流服务。 aggregator —— 通过Ozon合作物流伙伴交付。 hybryd—— 俄罗斯邮政配送方案。">
                        {{ detailFrom.tpl_integration_type }}
                    </el-descriptions-item>
                    <el-descriptions-item label="货件跟踪号">
                        {{ detailFrom.tracking_number }}
                    </el-descriptions-item>
                    <el-descriptions-item label="发运的计费信息">
                            <ArrayCtrl v-model="detailFrom.tariffication	"/>
                    </el-descriptions-item>
            </el-descriptions>
        </el-drawer>

  </div>
</template>

<script setup>
import {
  createOzoneOrderDetail,
  deleteOzoneOrderDetail,
  deleteOzoneOrderDetailByIds,
  updateOzoneOrderDetail,
  findOzoneOrderDetail,
  getOzoneOrderDetailList
} from '@/api/ozoneOrder/ozoneOrderDetail'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"




defineOptions({
    name: 'OzoneOrderDetail'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            addressee: {},
            analytics_data: {},
            customer: {},
            barcodes: {},
            delivering_date: {},
            delivery_method: {},
            financial_data: {},
            in_process_at: new Date(),
            is_express: false,
            optional: {},
            order_id: undefined,
            order_number: '',
            parent_posting_number: '',
            posting_number: '',
            products: [],
            requirements: {},
            shipment_date: new Date(),
            status: '',
            substatus: '',
            tpl_integration_type: '',
            tracking_number: '',
            tariffication	: [],
        })



// 验证规则
const rule = reactive({
})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.is_express === ""){
        searchInfo.value.is_express=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getOzoneOrderDetailList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteOzoneOrderDetailFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteOzoneOrderDetailByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateOzoneOrderDetailFunc = async(row) => {
    const res = await findOzoneOrderDetail({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteOzoneOrderDetailFunc = async (row) => {
    const res = await deleteOzoneOrderDetail({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        addressee: {},
        analytics_data: {},
        customer: {},
        barcodes: {},
        delivering_date: {},
        delivery_method: {},
        financial_data: {},
        in_process_at: new Date(),
        is_express: false,
        optional: {},
        order_id: undefined,
        order_number: '',
        parent_posting_number: '',
        posting_number: '',
        products: [],
        requirements: {},
        shipment_date: new Date(),
        status: '',
        substatus: '',
        tpl_integration_type: '',
        tracking_number: '',
        tariffication	: [],
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createOzoneOrderDetail(formData.value)
                  break
                case 'update':
                  res = await updateOzoneOrderDetail(formData.value)
                  break
                default:
                  res = await createOzoneOrderDetail(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findOzoneOrderDetail({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
